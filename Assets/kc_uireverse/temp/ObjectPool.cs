using System;
using System.Collections.Generic;
using Unity.VisualScripting;

public class ObjectPool<T>
{
    private Queue<T> m_Pool = new Queue<T>();
    private readonly Action<T> _cleanFunc;

    public ObjectPool(T obj, Action<T> cleanFunc)
    {
        _cleanFunc = cleanFunc;
    }

    public T Get()
    {
        if (m_Pool.Count > 0)
        { 
            return m_Pool.Dequeue();
        }
        return default(T);
    }
    
    public void Release(T toRelease, bool isClean = true)
    {
        m_Pool.Enqueue(toRelease);
        if (isClean)
        {
            _cleanFunc?.Invoke(toRelease);
        }
    }
}
