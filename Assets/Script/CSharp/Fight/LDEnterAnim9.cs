using UnityEngine;

namespace LD
{
    public class LDEnterAnim9 : MonoBehaviour
    {
       private enum EnterAnimStep
        {
            Step1,
            Step2,
            Step3,
            Step4,
            Step5,
            Step6,
            Step7,
        }
        private float m_CurTime = 0;
        private EnterAnimStep m_EnterAnimStep;
        private GameObject m_effect01;
        private GameObject m_effect02;
        private GameObject m_effect03;
        private float m_AnimTime = 1;
        private LDHeroPlayer m_LocalPlayer;
        public void StartAnim()
        {
            Global.gApp.CurFightScene.Pause();
            m_LocalPlayer = Global.gApp.CurFightScene.GetLocalHero();

            Transform heroModeNode = m_LocalPlayer.HeroAnimCtrol.DiyHandle.GetCreature().ModeNode;
            Transform effectNode1 = heroModeNode.Find("Bip001/Bip001 Prop1");
            m_effect01 = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.effect_mecha_011_chuchang01, ResSceneType.NormalRes, effectNode1);
            m_effect01.SetActive(false);
            LDFightTools.ResetTransform(m_effect01.transform);
            Transform effectNode2 = heroModeNode.Find("Bip001/Bip001 Pelvis");
            m_effect02 = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.effect_mecha_011_chuchang02, ResSceneType.NormalRes, effectNode2);
            m_effect02.SetActive(false);
            LDFightTools.ResetTransform(m_effect02.transform);
            m_effect03 = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.effect_mecha_011_chuchang03, ResSceneType.NormalRes, heroModeNode);
            m_effect03.SetActive(false);
            LDFightTools.ResetTransform(m_effect03.transform);

            Global.gApp.CurFightScene.gUnPauseTimeMgr.AddTimer(0.01f, 1, (a, b) =>
            {
                m_effect01.SetActive(true);
                m_effect02.SetActive(true);
                m_effect03.SetActive(true);
            });
            
            m_AnimTime = 2.85f;
            m_LocalPlayer.HeroAnimCtrol.PlayShowAnim();

            m_EnterAnimStep = EnterAnimStep.Step1;
            SetFightUIEnable(false);
        }
        public void SetFightUIEnable(bool hidFightNode)
        {
            Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.gameObject.SetActive(hidFightNode);
        }
        private void Update()
        {
            if(m_EnterAnimStep == EnterAnimStep.Step1)
            {
                m_CurTime += Time.deltaTime;
                if (m_CurTime >= m_AnimTime)
                {
                    m_CurTime = 0;
                    m_EnterAnimStep = EnterAnimStep.Step2;
                }
            }
            else if(m_EnterAnimStep == EnterAnimStep.Step2)
            {
                m_EnterAnimStep = EnterAnimStep.Step3;
                EndEnterAnim();
            }
        }
        private void EndEnterAnim()
        {
            SetFightUIEnable(true);
            Global.gApp.CurFightScene.Resume();
            Global.gApp.CurFightScene.gPassHandler.EndEnterAnim();
            
            m_effect01.SetActive(false);
            Global.gApp.gResMgr.DestroyGameObj(m_effect01);
            m_effect02.SetActive(false);
            Global.gApp.gResMgr.DestroyGameObj(m_effect02);
            m_effect03.SetActive(false);
            Global.gApp.gResMgr.DestroyGameObj(m_effect03);
        }
    } 
}