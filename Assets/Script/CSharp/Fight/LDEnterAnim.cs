using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;

namespace LD
{
    public class LDEnterAnim : MonoBehaviour
    {
        private enum EnterAnimStep
        {
            Step1,
            Step2,
            Step3,
            Step4,
            Step5,
            Step6,
            Step7,
        }
        private float m_CurTime = 0;
        private Vector3 m_AnimPos;
        private EnterAnimStep m_EnterAnimStep;
        private GameObject m_JiJiaGo;
        private float m_AnimTime = 1;
        private GameObject m_LocalPlayer;
        private Transform m_CreatureParentNode;
        public void StartAnim()
        {
            Global.gApp.CurFightScene.Pause();
            m_JiJiaGo = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.fx_jijiajinru, ResSceneType.NormalRes, Global.gApp.gRoleNode.transform);
            PlayableDirector playableDirector = m_JiJiaGo.GetComponent<PlayableDirector>();
            m_AnimTime = (float)playableDirector.duration;
            m_LocalPlayer = Global.gApp.CurFightScene.GetLocalHero().
                HeroAnimCtrol.DiyHandle.GetCreature().MechaNode;
            Vector3 recordHeroPos = m_LocalPlayer.transform.position;
            m_CreatureParentNode = m_LocalPlayer.transform.parent;
            m_JiJiaGo.transform.position = recordHeroPos;
            m_LocalPlayer.transform.SetParent(m_JiJiaGo.transform.Find("feiji/renwu"),true);
            m_LocalPlayer.transform.localPosition = Vector3.zero;

            m_AnimPos = recordHeroPos + Vector3.right * 5;
            m_EnterAnimStep = EnterAnimStep.Step1;

            Global.gApp.CurFightScene.GetLocalHero().gameObject.SetActive(false);
            Global.gApp.gAudioSource.PlayOneShot(AudioConfig.EnterAnimHelicopter, DYAudioType.DYUIAudio);
            SetFightUIEnable(false);
        }
        public void SetFightUIEnable(bool hidFightNode)
        {
            Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.gameObject.SetActive(hidFightNode);
        }
        private void Update()
        {
            if(m_EnterAnimStep == EnterAnimStep.Step1)
            {
                m_CurTime += Time.deltaTime;
                if (m_CurTime >= m_AnimTime)
                {
                    m_CurTime = 0;
                    m_EnterAnimStep = EnterAnimStep.Step2;
                }
            }
            else if(m_EnterAnimStep == EnterAnimStep.Step2)
            {
                m_EnterAnimStep = EnterAnimStep.Step3;
                EndEnterAnim();
            }
        }
        private void EndEnterAnim()
        {
            Global.gApp.CurFightScene.GetLocalHero().gameObject.SetActive(true);
            m_LocalPlayer.transform.SetParent(m_CreatureParentNode, false);
            m_LocalPlayer.transform.localPosition = Vector3.zero;
            m_LocalPlayer.transform.localEulerAngles = Vector3.zero;
            SetFightUIEnable(true);
            Global.gApp.CurFightScene.Resume();
            Global.gApp.CurFightScene.gPassHandler.EndEnterAnim();
            m_JiJiaGo.SetActive(false);
            Global.gApp.gResMgr.DestroyGameObj(m_JiJiaGo);
        }
    }
}
