using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;

namespace LD
{
    public class LDEnterAnim6 : MonoBehaviour
    {
        private enum EnterAnimStep
        {
            Step1,
            Step2,
            Step3,
            Step4,
            Step5,
            Step6,
            Step7,
        }
        private float m_CurTime = 0;
        private EnterAnimStep m_EnterAnimStep;
        private GameObject m_JiJiaGo;
        private GameObject m_chuChang;
        private float m_AnimTime = 1;
        private LDHeroPlayer m_LocalPlayer;
        public void StartAnim()
        {
            Global.gApp.CurFightScene.Pause();
            m_LocalPlayer = Global.gApp.CurFightScene.GetLocalHero();
            if (m_LocalPlayer.DIYData.BodySkinId > 0)
            {
                m_JiJiaGo = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.effect_mecha_S005_skin01_chuchang00, ResSceneType.NormalRes, Global.gApp.gRoleNode.transform);
                m_chuChang = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.effect_mecha_S005_skin01_chuchang01, ResSceneType.NormalRes, Global.gApp.gRoleNode.transform);
            }
            else
            {
                m_JiJiaGo = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.effect_mecha_S005_chuchang00, ResSceneType.NormalRes, Global.gApp.gRoleNode.transform);
                m_chuChang = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.effect_mecha_S005_chuchang01, ResSceneType.NormalRes, Global.gApp.gRoleNode.transform);
            }
            m_AnimTime = 3.34f;
            m_LocalPlayer.HeroAnimCtrol.PlayShowAnim();
            Transform bip001Tsf = m_LocalPlayer.HeroAnimCtrol.DiyHandle.GetCreature().ModeNode.Find("Bip001");

            m_JiJiaGo.transform.SetParent(bip001Tsf, false);
            m_JiJiaGo.transform.localPosition = Vector3.zero;
            m_chuChang.transform.SetParent(m_LocalPlayer.AtkFight.RotateNode,false);
            m_chuChang.transform.localPosition = Vector3.zero;
            GameObject.Destroy(m_chuChang,4);

            m_EnterAnimStep = EnterAnimStep.Step1;

            SetFightUIEnable(false);
        }
        public void SetFightUIEnable(bool hidFightNode)
        {
            Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.gameObject.SetActive(hidFightNode);
        }
        private void Update()
        {
            if(m_EnterAnimStep == EnterAnimStep.Step1)
            {
                m_CurTime += Time.deltaTime;
                if (m_CurTime >= m_AnimTime)
                {
                    m_CurTime = 0;
                    m_EnterAnimStep = EnterAnimStep.Step2;
                }
            }
            else if(m_EnterAnimStep == EnterAnimStep.Step2)
            {
                m_EnterAnimStep = EnterAnimStep.Step3;
                EndEnterAnim();
            }
        }
        private void EndEnterAnim()
        {
            SetFightUIEnable(true);
            Global.gApp.CurFightScene.Resume();
            Global.gApp.CurFightScene.gPassHandler.EndEnterAnim();
            m_JiJiaGo.SetActive(false);
            Global.gApp.gResMgr.DestroyGameObj(m_JiJiaGo);
        }
    }
}
