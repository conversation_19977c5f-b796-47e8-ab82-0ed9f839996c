using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public abstract class LDExpeditionTriggerBase : IUpdate
    {
        protected abstract void InitImp();
        protected abstract void DestroyImp();
        public abstract void AfterInit();
        protected LDExpeditionTriggerEvent m_ExpeditionTriggerEvent;
        protected LDExpeditionEventData m_ExpeditionEventData;
        protected int[] TriggerCond { get { return m_ExpeditionEventData.ExpeditionEventEffectItem.triggerCond; } }
        protected int m_EventType;
        protected LDMainRole m_MainRole;
        public void Init(LDExpeditionTriggerEvent expeditionTriggerEvent,LDMainRole mainRole)
        {
            m_MainRole = mainRole;
            m_ExpeditionTriggerEvent = expeditionTriggerEvent;
            m_ExpeditionEventData = m_ExpeditionTriggerEvent.ExpeditionEventData;
            m_EventType = m_ExpeditionEventData.ExpeditionEventEffectItem.triggerCond[0];
            RegListener(true);
            InitImp();
        }
        protected void TriggerSucess()
        {
            m_ExpeditionTriggerEvent.TriggerSucess(m_EventType);
        }
        public virtual void OnDUpdate(float dt)
        {
        }
        protected virtual void RegListener(bool reg)
        {
        }
        protected bool MatchCompare(int type,int curVal,int dstVal)
        {
            //1大于等于2小于等于
            if (type == 1)
            {
                return curVal >= dstVal;
            }
            else if(type == 2)
            {
                return curVal <= dstVal;
            }
            return false;
        }
        public void DestroyEvent()
        {
            RegListener(false);
            DestroyImp();
        }
    }
}
