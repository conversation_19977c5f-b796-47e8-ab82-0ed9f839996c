using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDExpeditionKillEnemyTrigger : LDExpeditionTriggerBase
    {
        private int m_TriggerTimes = 0;
        private int m_KillCount = 0;
        protected override void InitImp()
        {
        }
        public override void AfterInit()
        {
        }
        private void MonsterDeadth(LDAIMonster aIMonster)
        {
            if(m_TriggerTimes == 0)
            {
                if(TriggerCond[1] == 0 || TriggerCond[1] == aIMonster.MonsterItem.aiType)
                {
                    m_KillCount++;
                    TryTrigger();
                }
            }
        }
        private void TryTrigger()
        {
            if (m_KillCount >= TriggerCond[2])
            {
                m_KillCount -= TriggerCond[2];
                m_ExpeditionTriggerEvent.TriggerSucess(m_EventType);
            }
        }
        protected override void RegListener(bool reg)
        {
            base.RegListener(reg);
            Global.gApp.gMsgDispatcher.RegEvent<LDAIMonster>(MsgIds.MonsterDeadByDamage, MonsterDeadth, reg);
        }
        protected override void DestroyImp()
        {
        }
    }
}
