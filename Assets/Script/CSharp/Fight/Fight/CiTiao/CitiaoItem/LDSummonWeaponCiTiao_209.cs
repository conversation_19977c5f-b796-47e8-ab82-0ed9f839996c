
using LD.Skip;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDSummonWeaponCiTiao_209 : LDSubWeaponCiTiao
    {
        private double DamageParam = 0;
        private int BulletRate = -1;
        private int SummonId = -1;
        private int m_SummonCount = 1;
        private int m_LinkBulletId = -1;
        private double m_MinDisSqr = 4;
        private float m_SumDis = 6;
        private float m_MaxDis = 100;
        private bool m_LinkDirty = false;
        private LDSafeList<LDBaseSummon> m_Summers = new LDSafeList<LDBaseSummon>();
        private Dictionary<long, LDBaseSummon> m_SummersMap = new Dictionary<long, LDBaseSummon>();
        private List<Transform> m_SummonBornNodes = null;
        private int m_BornIndex = -1;

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            BulletRate = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            DamageParam = GetCitiaoParam()[2];
            SubWpnId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            SummonId = EZMath.IDDoubleToInt(GetCitiaoParam()[4]);
            m_SummonCount = EZMath.IDDoubleToInt(GetCitiaoParam()[5]);
            m_SumDis = GetCitiaoParam()[6];
            m_LinkBulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[7]);
            m_MaxDis = Mathf.Pow(GetCitiaoParam()[8],2);

            m_HeroSubWeapon = CiTiaoMgr.HeroPlayer.SubWpnMgr.GetSubWeapon(SubWpnId);
            if (m_HeroSubWeapon != null)
            {
                m_HeroSubWeapon.SummerCiTiao = this;
                LDWeaponData WeaponData = m_HeroSubWeapon.WeaponData;
                WeaponData.CitiaoBuffMgr = CitiaoBuffMgr;
                WeaponData.BulletId = -1;
                WeaponData.Curve = 1;
                WeaponData.DamageParam = DamageParam;
                m_HeroSubWeapon.FireDt = 1;
                m_HeroSubWeapon.InitByCiTiao(this);
            }
            m_TimeCondition = m_CitiaoCondition as LDTimeTriggerCondition_301;
            m_SummonBornNodes = Global.gApp.CurFightScene.gMapData.GetSummonBornNode();
        }
        public override void OnDUpdate(float dt)
        {
            if (m_LinkDirty)
            {
                m_LinkDirty = false;
                TryAddLinkLess();
            }
            m_Summers.OnDUpdate(dt);
            base.OnDUpdate(dt);
        }
        public override void TriggerSucess(int triggerType)
        {
            base.TriggerSucess(triggerType);
            if (!m_Restore)
            {
                if (m_HeroSubWeapon == null)
                {
                    return;
                }
                if (RandomMatch(BulletRate))
                {
                    SubSummon();
                }
            }
        }
        private void SubSummon()
        {
            int summonCount = m_SummonCount + CitiaoBuffMgr.GetBuffValInt(LDBuffType.SummonCountInc_701);
            for (int i = 0; i < summonCount; i++)
            {
                Vector3 bornPos = GetBornPos();
                SummonItem summItem = Summon.Data.Get(SummonId);
                LDBaseSummon baseSummon = LDSummonFactory.CreateSummon(summItem);
                baseSummon.transform.position = bornPos;
                baseSummon.SetGuid(CiTiaoMgr.GetGuid());
                baseSummon.Init(this, summItem);
                baseSummon.OnEnter();
                m_Summers.Add(baseSummon);
                m_SummersMap[baseSummon.GetGuid()] = baseSummon;
                m_HeroSubWeapon.SummerCount += 1;
            }
            m_LinkDirty = true;
        }
        public virtual void OnPreHitAI(LDTrackBullet m_TrackBullet, GameObject hitGo)
        {
            LDBaseSummon baseSummon = GetSummon(m_TrackBullet.AtkData.SummerGuid);
            if (baseSummon != null)
            {
                baseSummon.OnPreHitAI(m_TrackBullet, hitGo);
            }
        }
        public LDBaseSummon GetSummon(long guid)
        {
            if (m_SummersMap.ContainsKey(guid))
            {
                return m_SummersMap[guid];
            }
            else
            {
                return null;
            }
        }
        private void TryAddLinkLess()
        {
            if (m_LinkBulletId <= 0)
            {
                return;
            }
            if (!CitiaoBuffMgr.HasBuffByType(LDBuffType.SummonLink_706))
            {
                return;
            }
            m_HeroSubWeapon.SummerGuid = -1;
            List<LDBaseSummon> allSummer = m_Summers.GetAll();
            List<LDBaseSummon> linkSummer = new List<LDBaseSummon>();
            foreach (LDBaseSummon summer in allSummer)
            {
                if (summer.OnUsingState())
                {
                    linkSummer.Add(summer);
                }
            }
            TryStartLinkLess(linkSummer);
        }
        private void TryStartLinkLess(List<LDBaseSummon> linkSummer)
        {
            LDBaseSummon firstCanConnectSummon = null;
            foreach (LDBaseSummon item in linkSummer)
            {
                if (item.LinkBullet == null)
                {
                    firstCanConnectSummon = item;
                    break;
                }

            }
            if (firstCanConnectSummon != null)
            {
                TryAddLinkLess(firstCanConnectSummon, linkSummer);
            }
        }
        private void TryAddLinkLess(LDBaseSummon summer, List<LDBaseSummon> linkSummer)
        {
            Vector3 curPos = summer.SummerGo.transform.position;
            if (summer.LinkBullet == null)
            {
                LDBaseSummon targetSummer = null;
                float dis = 100000;
                foreach (LDBaseSummon otherSummer in linkSummer)
                {
                    if (summer != otherSummer)
                    {
                        if (otherSummer.BeLinkTarget == null && otherSummer.LinkTarget != summer)
                        {
                            bool jumpDis = false;
                            if (targetSummer != null)
                            {
                                if (otherSummer.LinkTarget == null && targetSummer.LinkTarget != null)
                                {
                                    //targetSummer = otherSummer;
                                    jumpDis = true;
                                }
                            }
                            float newDis = (curPos - otherSummer.SummerGo.transform.position).sqrMagnitude;
                            if (newDis < dis || jumpDis)
                            {
                                if (newDis < m_MaxDis)
                                {
                                    targetSummer = otherSummer;
                                    dis = newDis;
                                }
                            }
                        }
                    }
                }
                if (targetSummer != null)
                {
                    m_HeroSubWeapon.Fire(summer.SummerGo.transform,
                                          m_LinkBulletId, 1, 1, 0, 0, Vector3.zero);
                    if (m_HeroSubWeapon.AtkBullet.Count == 1)
                    {
                        LDAtkBullet bullet = m_HeroSubWeapon.AtkBullet[0];

                        Vector3 StartPos = summer.SummerGo.transform.position + Vector3.up * 0.3f;// Vector3.Lerp(m_LockStartPos, lockPos, dt);
                        Vector3 RealLocalPos = targetSummer.SummerGo.transform.position;
                        Vector3 dtPos = StartPos - RealLocalPos;

                        summer.LinkBullet = bullet;
                        targetSummer.BeLinkTarget = summer;
                        summer.LinkTarget = targetSummer;

                        float scale = LDFightTools.GetIntF((dtPos).magnitude);
                        Vector3 localScale = bullet.transform.localScale;
                        localScale.z = scale;
                        bullet.transform.SetLocalPoisition((StartPos + RealLocalPos) / 2);
                        bullet.AtkData.BulletScale = localScale;
                        bullet.transform.SetLocalScale(localScale);
                        bullet.transform.SetForward(dtPos);
                        (bullet.BulletMove as LDRayLesserMoveMode)?.RecycleEffect();
                        LDFightTools.UnifiedTransform(bullet.transform);
                    }
                    TryAddLinkLess(targetSummer, linkSummer);
                }
                else
                {
                    linkSummer.Remove(summer);
                    TryStartLinkLess(linkSummer);
                }
            }
            else
            {
                TryStartLinkLess(linkSummer);
            }
        }
        private Vector3 GetBornPos()
        {
            if(m_SummonBornNodes != null && m_SummonBornNodes.Count > 0)
            {
                m_BornIndex++;
                return m_SummonBornNodes[m_BornIndex % m_SummonBornNodes.Count].position;
            }
            int splitCount = 12;
            float angle = LDFightTools.GetIntF(2 * LDMath.PI / splitCount);
            Vector3 heroPos = CiTiaoMgr.HeroPlayer.transform.position;
            int startIndex = RandomUtil.NextInt(0, splitCount);
            for (int i = startIndex; i < splitCount + startIndex; i++)
            {
                int newIndex = i % splitCount;
                Vector3 newPos = heroPos + new Vector3(m_SumDis * LDMath.Cos(angle * newIndex), 0, m_SumDis * LDMath.Sin(angle * newIndex));
                newPos = LDFightTools.GetV3IntF(newPos);
                bool posAvalid = true;
                if (!LDFightTools.RaycastPropBlockSphere(newPos, 0.5f))
                {
                    foreach (LDBaseSummon item in m_Summers.GetAll())
                    {
                        float maxDisSqr = (item.SummerGo.transform.GetPoisition() - newPos).sqrMagnitude;
                        if (maxDisSqr < m_MinDisSqr)
                        {
                            if (item.OnUsingState())
                            {
                                posAvalid = false;
                                break;
                            }
                        }
                    }
                }
                else
                {
                    posAvalid = false;
                }
                if (posAvalid)
                {
                    return newPos;
                }
            }
            return CiTiaoMgr.HeroPlayer.transform.position;
        }
        public void RemoveSummonLinkBullet(LDBaseSummon baseSummon)
        {
            m_HeroSubWeapon.SummerCount -= 1;

            RmLinkBullet(baseSummon);
            RmLinkBullet(baseSummon.BeLinkTarget);

            if (baseSummon.LinkTarget != null)
            {
                baseSummon.LinkTarget.BeLinkTarget = null;
            }
            if (baseSummon.BeLinkTarget != null)
            {
                baseSummon.BeLinkTarget.LinkTarget = null;
            }
        }
        public void RemoveSummon(LDBaseSummon baseSummon)
        {
            baseSummon.OnExit();
            m_Summers.Remove(baseSummon);
            m_SummersMap.Remove(baseSummon.GetGuid());

            if (baseSummon.SummerGo != null)
            {
                baseSummon.SummerGo.SetActive(false);
                GameObject.Destroy(baseSummon.SummerGo, 0.1f);
            }
        }
        private void RmLinkBullet(LDBaseSummon baseSummon)
        {
            if (baseSummon != null && baseSummon.LinkBullet != null)
            {
                baseSummon.LinkBullet.RecycleBulletOnly(LDRecycleReason.ForceRecycle);
                baseSummon.LinkBullet = null;
            }
        }  
        public override void TryCacheEffect()
        {
            CacheSummonEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[4]));
        }
        public override void Destroy()
        {
            CiTiaoMgr.HeroPlayer.SubWpnMgr.ReturnSubWeapon(m_HeroSubWeapon);
            foreach (LDBaseSummon suomonItem in m_Summers.GetAll())
            {
                suomonItem.OnExit();
                if (suomonItem.SummerGo != null)
                {
                    suomonItem.SummerGo.SetActive(false);
                    GameObject.Destroy(suomonItem.SummerGo, 0.1f);
                }
            }
            m_Summers.Clear();
            base.Destroy();
        }
    }
}
