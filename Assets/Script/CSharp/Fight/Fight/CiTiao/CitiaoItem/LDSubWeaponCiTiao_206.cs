
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDSubWeaponCiTiao_206 : LDSubWeaponCiTiao
    {
        private double DamageParam = 0;
        private int BulletRate = -1;
        private int m_BulletCount = 0;
        private float m_FireDt = 0;

        private float m_Radius = 0;
        private float m_RSpeed = 0;
        private LDSyncTsfPos m_BulletRootNode;
        List<LDBaseBullet> m_FrisbeeBullet = new List<LDBaseBullet>(4);

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            BulletRate = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            DamageParam = GetCitiaoParam()[2];
            SubWpnId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            SubWpnBulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[4]);
            m_BulletCount = EZMath.IDDoubleToInt(GetCitiaoParam()[5]);

            m_Radius = GetCitiaoParam()[6];
            m_RSpeed = GetCitiaoParam()[7];

            m_HeroSubWeapon = CiTiaoMgr.HeroPlayer.SubWpnMgr.GetSubWeapon(SubWpnId);
            if(m_HeroSubWeapon != null)
            {
                LDWeaponData WeaponData = m_HeroSubWeapon.WeaponData;
                WeaponData.CitiaoBuffMgr = CitiaoBuffMgr;
                WeaponData.BulletId = (SubWpnBulletId);
                WeaponData.Curve = m_BulletCount;
                WeaponData.DamageParam = DamageParam;
                m_HeroSubWeapon.FireDt = m_FireDt;
                m_HeroSubWeapon.InitByCiTiao(this);
            }
            m_TimeCondition = m_CitiaoCondition as LDTimeTriggerCondition_301;

            GameObject go = new GameObject("Frisbee");
            m_BulletRootNode = go.AddComponent<LDSyncTsfPos>();
            m_BulletRootNode.Offset = Vector3.up;
            m_BulletRootNode.transform.SetParent(Global.gApp.gBulletNode.transform);
        }
        public override void TriggerSucess(int triggerType)
        {
            base.TriggerSucess(triggerType);
            if (!m_Restore)
            {
                if(m_HeroSubWeapon == null)
                {
                    return;
                }
                if (RandomMatch(BulletRate))
                {
                    AddBullet();
                }
            }
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            int curCount = m_FrisbeeBullet.Count;
            if (curCount > 0)
            {
                int bulletCount = GetBulletCount();
                if (bulletCount != curCount)
                {
                    AddBullet();
                }
            }
            if (m_BulletRootNode != null)
            {
                m_BulletRootNode.SyncTsf = CiTiaoMgr.HeroPlayer.transform;
                m_BulletRootNode.transform.Rotate(Vector3.up, GetRSpeed() * dt);
            }
        }
        private float GetRSpeed()
        {
            return m_RSpeed * (1 + (float)CitiaoBuffMgr.GetBuffVal(LDBuffType.CiTiao206RSpeed_163));
        }

        private void RecycleFrisbeeBullet()
        {
            foreach (LDBaseBullet bullet in m_FrisbeeBullet)
            {
                bullet.RecycleBullet(LDRecycleReason.TimeOver);
            }
            m_FrisbeeBullet.Clear();
        }
        private int GetBulletCount()
        {
            if (m_HeroSubWeapon != null)
            {
                return m_HeroSubWeapon.GetCurve();
            }
            else
            {
                return 0;
            }
        }
        private void AddBullet()
        {
            RecycleFrisbeeBullet();
            int bulletCount = GetBulletCount();
            m_HeroSubWeapon.WeaponData.angleOffset = 360 / bulletCount;
            m_HeroSubWeapon.Fire(SubWpnBulletId);
            m_BulletRootNode.transform.SetForward(Vector3.forward);
            foreach (LDAtkBullet bullet in m_HeroSubWeapon.AtkBullet)
            {
                bullet.transform.SetParent(m_BulletRootNode.transform, false);
                bullet.transform.localPosition = Vector3.zero;
                Vector3 forward = bullet.transform.GetForward();
                forward.y = 0;
                bullet.transform.SetForward(forward);
                bullet.transform.SetLocalPoisition(bullet.transform.GetForward() * (m_Radius * (1 + (float)CitiaoBuffMgr.GetBuffVal(LDBuffType.CiTIao206Radius_165))));
                m_FrisbeeBullet.Add(bullet);
            }
        }
        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[4]));
        }
        public override void Destroy()
        {
            RecycleFrisbeeBullet();
            CiTiaoMgr.HeroPlayer.SubWpnMgr.ReturnSubWeapon(m_HeroSubWeapon);
            base.Destroy();
            Global.gApp.gResMgr.DestroyBehaviour(m_BulletRootNode);
            m_BulletRootNode = null;
        }
    }
}
