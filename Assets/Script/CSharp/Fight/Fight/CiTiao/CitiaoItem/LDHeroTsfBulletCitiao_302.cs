
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDHeroTsfBulletCitiao_302 : LDEventCitiao
    {
        private double m_DamageParam = 1;
        private int m_BulletCount = 1;
        private float m_StartAngleMin = 0;
        private float m_StartAngleMax = 0;
        private float m_DtAngle = 0;
        private int m_AddToParent = 0;
        private int m_LockPosType = 0;

        private float m_TriggerTimes = 1;
        private float m_CurTriggerTimes = 0;
        private float m_TriggerDt = 0.1f;
        private float m_CurTime = 0;
        private int m_BindHeroForward = 0;
        private float m_Scale = 1;
        private List<int> m_BulletIds = new List<int>(4);


        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_DamageParam = GetCitiaoParam()[1];
            m_BulletCount = EZMath.IDDoubleToInt(GetCitiaoParam()[2]);
            m_StartAngleMin = (float)(GetCitiaoParam()[3]);
            m_StartAngleMax = (float)(GetCitiaoParam()[4]);
            m_DtAngle = (float)(GetCitiaoParam()[5]);
            m_TriggerTimes = (float)(GetCitiaoParam()[6]);
            m_TriggerDt = (float)(GetCitiaoParam()[7]);
            m_Scale = (float)(GetCitiaoParam()[8]);
            m_LockPosType = EZMath.IDDoubleToInt(GetCitiaoParam()[9]);
            m_BindHeroForward = EZMath.IDDoubleToInt(GetCitiaoParam()[10]);
            m_AddToParent = EZMath.IDDoubleToInt(GetCitiaoParam()[11]);
            m_CurTriggerTimes = m_TriggerTimes;
        }

        public override void OnTriggerByWeaponSucess(LDBaseWeapon heroWeapon)
        {
            if (!m_Restore && heroWeapon != null)
            {
                int bulletId = heroWeapon.GetBulletId();
                if (bulletId > 0)
                {
                    m_BulletIds.Add(bulletId);
                }
            }
        }
        public override void TriggerSucess(int triggerType)
        {
            if (!m_Restore)
            {
                base.TriggerSucess(triggerType);
                if (m_CurTriggerTimes == m_TriggerTimes)
                {
                    m_CurTime = m_TriggerDt;
                    m_CurTriggerTimes = 0;
                }
                else
                {
                    m_CurTriggerTimes -= 1;
                }
            }
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if (m_CurTriggerTimes < m_TriggerTimes)
            {
                m_CurTime += dt;
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime , m_TriggerDt))
                {
                    m_CurTriggerTimes++;
                    m_CurTime -= m_TriggerDt;
                    TryAddBullet();
                }
            }
        }
        private int GetBulletId()
        {
            if (m_BulletIds.Count > 0)
            {
                int bulletId = m_BulletIds[0];
                m_BulletIds.RemoveAt(0);
                return bulletId;
            }
            else
            {
                return -1;
            }
        }
        private void TryAddBullet()
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;

            double atk = heroPlayer.MechaMgr.GetAtk();
            int bulletId = GetBulletId();
            if(bulletId < 0)
            {
                return;
            }
            double damageParam = GetDamageParam(m_DamageParam);
            Vector3 vector3 = LDFightNodeTools.GetNodePos(m_LockPosType, heroPlayer);
            float startAngle = RandomUtil.NextFloat(m_StartAngleMin , m_StartAngleMax);
            float dtAngle = -m_DtAngle * (m_BulletCount - 1) / 2 + startAngle;
            if (m_BindHeroForward == 0)
            {
                for (int i = 0; i < m_BulletCount; i++)
                {

                    LDHeroBulletData heroBulletData = heroPlayer.BulletEmitter.
                        SummonDelayBullet(bulletId, atk, CitiaoBuffMgr, vector3, dtAngle, 0);
                    heroBulletData.DamageParam = damageParam;
                    heroBulletData.ParentAdd = m_AddToParent;
                    heroBulletData.Scale = m_Scale;
                    dtAngle += m_DtAngle;
                }
            }
            else if(m_BindHeroForward == 1)
            {
                for (int i = 0; i < m_BulletCount; i++)
                {

                    LDHeroBulletData heroBulletData = heroPlayer.BulletEmitter.
                        SummonDelayBulletBaseOnForward(bulletId, atk, CitiaoBuffMgr, vector3, dtAngle, 0);
                    heroBulletData.DamageParam = damageParam;
                    heroBulletData.ParentAdd = m_AddToParent;
                    dtAngle += m_DtAngle;
                }
            }
            else if(m_BindHeroForward == 2)
            {
                for (int i = 0; i < m_BulletCount; i++)
                {

                    LDHeroBulletData heroBulletData = heroPlayer.BulletEmitter.
                        SummonDelayBulletBaseOnMechaFireForward(bulletId, atk, CitiaoBuffMgr, vector3, dtAngle, 0);
                    heroBulletData.DamageParam = damageParam;
                    heroBulletData.ParentAdd = m_AddToParent;
                    dtAngle += m_DtAngle;
                }
            }
        }
    }
}
