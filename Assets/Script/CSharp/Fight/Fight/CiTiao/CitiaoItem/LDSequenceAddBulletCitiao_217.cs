using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDSequenceAddBulletCitiao_217Content : IUpdate
    {
        private LDSequenceAddBulletCitiao_217 m_Citiao;
        private float m_CurTime;
        private int m_CurBulletCount;
        private float m_StartOffet;

        public LDSequenceAddBulletCitiao_217Content(LDSequenceAddBulletCitiao_217 citiao, float startOffet)
        {
            m_Citiao = citiao;
            m_StartOffet = startOffet;
            m_CurTime = citiao.GetTriggerDt() - citiao.GetFirstDt();
        }

        public void OnDUpdate(float dt)
        {
            m_CurTime += dt;
            if (LDFightTools.GreaterOrEqualThenZero(m_CurTime, m_Citiao.GetTriggerDt()))
            {
                m_CurTime -= m_Citiao.GetTriggerDt();
                m_CurBulletCount++;
                m_Citiao.TryAddBullet(m_CurBulletCount, m_StartOffet);
                if (m_CurBulletCount >= m_Citiao.GetBulletCount())
                {
                    RemoveThis();
                }
            }
        }

        private void RemoveThis()
        {
            m_Citiao.OnceOver(this);
        }
    }

    public class LDSequenceAddBulletCitiao_217 : LDEventCitiao
    {
        private int m_TriggerRata = 0;
        private double m_DamageParam = 1;
        private int m_BulletId = 1;
        private int m_BulletCount = 1;
        private float m_StartAngleMin = 0;
        private float m_StartAngleMax = 0;
        private float m_DtAngle = 0;
        public float m_TriggerDt = 1;
        private int m_LockPosType = 0;
        private int m_BindHeroForward = 0;
        private int m_AddToParent = 0;
        private float m_FirstDt = 0.1f;
        private float m_Offset = 0;

        private LDSafeList<LDSequenceAddBulletCitiao_217Content> m_ContentList = new LDSafeList<LDSequenceAddBulletCitiao_217Content>();

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_TriggerRata = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            m_DamageParam = GetCitiaoParam()[2];
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            m_BulletCount = EZMath.IDDoubleToInt(GetCitiaoParam()[4]);
            m_StartAngleMin = GetCitiaoParam()[5];
            m_StartAngleMax = GetCitiaoParam()[6];
            m_DtAngle = GetCitiaoParam()[7];
            m_TriggerDt = GetCitiaoParam()[8];
            m_LockPosType = EZMath.IDDoubleToInt(GetCitiaoParam()[9]);
            m_BindHeroForward = EZMath.IDDoubleToInt(GetCitiaoParam()[10]);
            m_AddToParent = EZMath.IDDoubleToInt(GetCitiaoParam()[11]);
            m_FirstDt = GetCitiaoParam()[12];
            m_Offset = GetCitiaoParam()[13];

            m_ContentList.Clear();
        }

        public override void TriggerSucess(int triggerType)
        {
            if (m_Restore)
            {
                BulletItem bulletItem = Bullet.Data.Get(m_BulletId);
                if (bulletItem != null)
                {
                    if (bulletItem.livetime < 999)
                    {
                        return;
                    }
                }
            }

            if (!RandomMatch(m_TriggerRata)) return;

            float startOffet = RandomUtil.NextFloat(m_StartAngleMin, m_StartAngleMax);
            LDSequenceAddBulletCitiao_217Content content = new LDSequenceAddBulletCitiao_217Content(this, startOffet);
            m_ContentList.Add(content);

            base.TriggerSucess(triggerType);
        }

        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            m_ContentList.OnDUpdate(dt);
        }

        public void TryAddBullet(int curBulletCount, float startOffet)
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;

            double atk = heroPlayer.MechaMgr.GetAtk();
            int bulletId = m_BulletId;
            if (bulletId < 0) return;

            double damageParam = GetDamageParam(m_DamageParam);
            Vector3 vector3 = LDFightNodeTools.GetNodePos(m_LockPosType, heroPlayer);

            float offet = startOffet + (curBulletCount - 1) * m_DtAngle;

            LDHeroBulletData heroBulletData = null;
            if (m_BindHeroForward == 0)
            {
                heroBulletData = heroPlayer.BulletEmitter.SummonDelayBullet(bulletId, atk, CitiaoBuffMgr, vector3, offet, m_Offset);
            }

            if (m_BindHeroForward == 1)
            {
                heroBulletData = heroPlayer.BulletEmitter.SummonDelayBulletBaseOnForward(bulletId, atk, CitiaoBuffMgr, vector3, offet, m_Offset);
            }

            if (m_BindHeroForward == 2)
            {
                heroBulletData = heroPlayer.BulletEmitter.SummonDelayBulletBaseOnMechaFireForward(bulletId, atk, CitiaoBuffMgr, vector3, offet, m_Offset);
            }

            if (heroBulletData != null)
            {
                heroBulletData.DamageParam = damageParam;
                heroBulletData.ParentAdd = m_AddToParent;
                heroBulletData.CiTiaoItem = this;
            }
        }

        public void OnceOver(LDSequenceAddBulletCitiao_217Content content)
        {
            m_ContentList.Remove(content);
        }

        public int GetBulletCount()
        {
            return (m_BulletCount + CitiaoBuffMgr.GetBuffValInt(LDBuffType.CiitaoCurve_151)) * (1 + CitiaoBuffMgr.GetBuffValInt(LDBuffType.CiitaoCurveTimes_152)); ;
        }

        public float GetTriggerDt()
        {
            return m_TriggerDt + (float)CitiaoBuffMgr.GetBuffVal(LDBuffType.CiTiaoCurveAngle_156);
        }

        public float GetFirstDt()
        {
            return m_FirstDt;
        }

        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[3]));
        }
    }
}