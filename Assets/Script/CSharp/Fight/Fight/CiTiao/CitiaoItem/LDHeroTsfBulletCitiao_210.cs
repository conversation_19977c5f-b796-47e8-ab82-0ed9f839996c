
using UnityEngine;

namespace LD
{
    public class LDHeroTsfBulletCitiao_210 : LDEventCitiao
    {
        private double m_DamageParam = 1;
        private int m_TargetEffectId = 19006;
        private int m_LineEffectId = 19007;
        private int m_BulletId = 1;
        private float m_TriggerTimes = 1;
        private float m_CurTriggerTimes = 0;
        private float m_TriggerDt = 0.1f;
        private float m_CurTime = 0;
        private LDEffectNodeContent m_LinkEffect = null;
        private LDEffectNodeContent m_LineEffect = null;
        private LDSceneObj m_LockObj = null;
        private Vector3  m_LockObjPos;
        private long LockGuid = -1;


        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_DamageParam = GetCitiaoParam()[1];
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[2]);
            m_TriggerTimes = (float)(GetCitiaoParam()[3]);
            m_TriggerDt = (float)(GetCitiaoParam()[4]);

            m_CurTriggerTimes = m_TriggerTimes;

        }

        public override void TriggerSucess(int triggerType)
        {
            if (m_Restore)
            {
                return;
            }
            base.TriggerSucess(triggerType);
            m_CurTriggerTimes -= 1;
            m_CurTriggerTimes = Mathf.Max(m_CurTriggerTimes, 0);
            CalcLockObj();
        }
        private void CalcLockObj()
        {
            if (m_CurTriggerTimes < m_TriggerTimes)
            {
                if (m_LockObj != null)
                {
                    if (LockGuid != m_LockObj.GetGuid())
                    {
                        m_LockObj = null;
                        RecycleEffectNode();
                    }
                }
                if (m_LockObj == null)
                {
                    m_LockObj = Global.gApp.CurFightScene.gPlayerMgr.SearchUtils.
                                   FindNearestHeroObj(8, CiTiaoMgr.HeroPlayer.transform.GetPoisition(), LDFightConstVal.AIMask, CiTiaoMgr.HeroPlayer.TeamId);
                    if (m_LockObj != null)
                    {
                        Transform bodyNode = LDFightNodeTools.GetBodyNode(m_LockObj);
                        m_LockObjPos = bodyNode.position;
                        AddEffectNode();
                        m_LinkEffect.transform.SetParent(bodyNode.transform, false);
                        m_LinkEffect.transform.localPosition = Vector3.zero;      
                        
                        m_LineEffect.transform.SetParent(GetFirePoint(), false);
                        Vector3 linePos = Vector3.zero;
                        linePos.y = m_LockObjPos.y;
                        m_LineEffect.transform.localPosition = linePos;
                    }
                    else
                    {
                        return;
                    }
                }
                LockGuid = m_LockObj.GetGuid();
            }
            else
            {
                LockGuid = -1;
                m_LockObj = null;
                RecycleEffectNode();
            }
            UpdateLinkData();
        }
        private  Transform GetFirePoint()
        {
            //LDHeroWeapon heroWeapon = CiTiaoMgr.HeroPlayer.MechaMgr.GetHeroWeapon();
            //if (heroWeapon != null)
            //{
            //    return heroWeapon.WeapenNode.FirePoint;
            //}
            //else
            {
                return  CiTiaoMgr.HeroPlayer.transform;
            }
        }
        private void UpdateLinkData()
        {
            if (m_LockObj == null)
            {
                return;
            }
            Transform bodyNode = LDFightNodeTools.GetBodyNode(m_LockObj);
            m_LockObjPos = bodyNode.GetPoisition();
            Vector3 lockPos = m_LockObjPos;
            Vector3 curPos = m_LineEffect.transform.position;
            Vector3 dtPos = lockPos - curPos;
            float scale = dtPos.magnitude;
            m_LineEffect.transform.localScale = new Vector3(1, 1, scale);
            m_LineEffect.transform.SetForward(dtPos);
        }


        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if (m_CurTriggerTimes < m_TriggerTimes)
            {
                m_CurTime += dt;
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime, m_TriggerDt))
                {
                    m_CurTriggerTimes++;
                    m_CurTime -= m_TriggerDt;
                    RecycleEffectNode();
                    TryAddBullet();
                }
            }
            CalcLockObj();
        }
        private void TryAddBullet()
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;

            double atk = heroPlayer.MechaMgr.GetAtk();
            int bulletId = m_BulletId;
            if (bulletId <= 0)
            {
                return;
            }
            RecycleEffectNode();
            double damageParam = GetDamageParam(m_DamageParam);

            LDTrackBullet bullet = heroPlayer.BulletEmitter.SummonBullet(bulletId, atk, CitiaoBuffMgr);
            bullet.Init(GetFirePoint(), 0, 0);
            bullet.AtkData.MulCiTiaoParam(damageParam);
            bullet.AtkData.CitiaoItem = this;
            if (m_LockObj != null)
            {
                m_LockObjPos = m_LockObj.transform.position;
            }
            Vector3 forward = m_LockObjPos - bullet.transform.GetPoisition();
            bullet.SetBezierEndPosCalcController(m_LockObjPos);
            bullet.SyncForward(forward);
        }
        private void RecycleEffectNode()
        {
            if(m_LinkEffect != null)
            {
                m_LinkEffect.StopAndRecycleEffect();
                m_LinkEffect = null;
            }       
            if(m_LineEffect != null)
            {
                m_LineEffect.StopAndRecycleEffect();
                m_LineEffect = null;
            }
        }      
        private void AddEffectNode()
        {
            if(m_LinkEffect == null)
            {
                m_LinkEffect = Global.gApp.CurFightScene.gEffectMgr.GetFreeEffectNode(m_TargetEffectId);
                m_LinkEffect.PlayParticle();
            }       
            if(m_LineEffect == null)
            {
                m_LineEffect = Global.gApp.CurFightScene.gEffectMgr.GetFreeEffectNode(m_LineEffectId);
                m_LineEffect.PlayParticle();
            }
        }

        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[2]));
            CacheEffect(m_TargetEffectId);
            CacheEffect(m_LineEffectId);
        }
        public override void Destroy()
        {
            RecycleEffectNode();
            base.Destroy();
        }
    }
}
