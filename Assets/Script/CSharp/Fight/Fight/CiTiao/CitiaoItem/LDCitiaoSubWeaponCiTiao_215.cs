using System.Collections.Generic;
using LD.Skip;
using UnityEngine;

namespace LD
{
    public class LDCitiaoSubWeaponCiTiao_215 : LDSubWeaponCiTiao
    {
        private int m_BulletRate = -1;
        private float m_DamageParam;
        private int m_BulletCurve;
        private float m_CurveAngle;
        private int m_MaxTriggerTimes;
        private float m_DistanceDt;
        private float m_FireDt;
        private int m_FirePointType;
        private int m_BulletPos;
        private int m_LockType;
        private int m_FireEffectId;
        private int m_LinkBulletId;
        
        
        private int m_TriggerTimes;
        private float m_CurTime;

        private Transform m_FirePointTf;
        private Vector3 m_FirePointPos;
        private List<Vector3> m_FireForwards = new List<Vector3>(4);

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_BulletRate = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            m_DamageParam = GetCitiaoParam()[2];
            SubWpnId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            SubWpnBulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[4]);
            m_BulletCurve = EZMath.IDDoubleToInt(GetCitiaoParam()[5]);
            m_CurveAngle = GetCitiaoParam()[6];
            m_MaxTriggerTimes = EZMath.IDDoubleToInt(GetCitiaoParam()[7]);
            m_DistanceDt = GetCitiaoParam()[8];
            m_FireDt = GetCitiaoParam()[9];
            m_FirePointType = EZMath.IDDoubleToInt(GetCitiaoParam()[10]);
            m_BulletPos = EZMath.IDDoubleToInt(GetCitiaoParam()[11]);
            m_LockType = EZMath.IDDoubleToInt(GetCitiaoParam()[12]);
            m_FireEffectId = EZMath.IDDoubleToInt(GetCitiaoParam()[13]);
            m_LinkBulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[14]);

            InitSubWpnData();

            m_TriggerTimes = 9999;
        }

        private void InitSubWpnData()
        {
            m_HeroSubWeapon = CiTiaoMgr.HeroPlayer.SubWpnMgr.GetSubWeapon(SubWpnId);
            if(m_HeroSubWeapon != null)
            {
                LDWeaponData WeaponData = m_HeroSubWeapon.WeaponData;
                WeaponData.CitiaoBuffMgr = CitiaoBuffMgr;
                WeaponData.BulletId = SubWpnBulletId;
                WeaponData.Curve = m_BulletCurve;
                WeaponData.DamageParam = m_DamageParam;
                WeaponData.angleOffset = m_CurveAngle;
                
                m_FirePointTf = WeaponData.FirePoint;
                if (m_BulletPos == 1)
                {
                    m_HeroSubWeapon.SetBindParent(true);
                }
                m_HeroSubWeapon.SetRotateMode(m_LockType);
                m_HeroSubWeapon.InitByCiTiao(this);
                if (m_FirePointType == 1)
                {
                    m_HeroSubWeapon.SetFirePoint(m_HeroSubWeapon.HeroPlayer.AtkFight.RotateNode.transform);
                }
            }
            
            m_TimeCondition = m_CitiaoCondition as LDTimeTriggerCondition_301;
            CiTiaoMgr.FreshSubWeaponCD(SubWpnId);
            if (m_TimeCondition != null)
            {
                m_TimeCondition.ForceClearCD();
            }
        }

        public override void TriggerSucess(int triggerType)
        {
            if (m_Restore) return;
            if (m_HeroSubWeapon == null) return;

            if (!RandomMatch(m_BulletRate)) return;

            m_TriggerTimes = 0;
            m_CurTime = 0;
            m_FirePointPos = m_HeroSubWeapon.GetFirePoint().position;
            m_FirePointPos.y = 0;
            m_FireForwards.Clear();
            base.TriggerSucess(triggerType);
        }

        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            
            if (m_TriggerTimes >= GetSubWpnTriggerTimes(m_MaxTriggerTimes)) return;
            
            m_CurTime += dt;
            if (LDFightTools.GreaterOrEqualThenZero(m_CurTime, m_FireDt))
            {
                m_CurTime -= m_FireDt;
                Fire();
            }
        }

        private void Fire()
        {
            m_TriggerTimes++;
            Global.gApp.CurFightScene.gEffectMgr.AddDelayEffectNode(m_FireEffectId, m_FirePointTf.position);
            m_HeroSubWeapon.Fire(SubWpnBulletId);
            
            // 把第一波的子弹方向存下来  后面方向保持一致
            List<LDAtkBullet> atkBullets = m_HeroSubWeapon.AtkBullet;
            if (m_FireForwards.Count <= 0)
            {
                foreach (LDAtkBullet t in atkBullets)
                {
                    m_FireForwards.Add(t.transform.forward);
                }
            }
            
            for (int i = 0; i < m_HeroSubWeapon.AtkBullet.Count; i++)
            {
                LDAtkBullet atkBullet = m_HeroSubWeapon.AtkBullet[i];
                Vector3 forward = atkBullet.transform.forward;
                if (m_FireForwards.Count > i)
                {
                    forward = m_FireForwards[i];
                }
                Vector3 pos = m_TriggerTimes * m_DistanceDt * forward;
                pos += m_FirePointPos;
                atkBullet.SyncPos(pos);
            }

            if(CitiaoBuffMgr.GetBuffValInt(LDBuffType.LightningChainCitiao215_168) > 0)
            {
                TryLightningChain();
            }
        }

        private void TryLightningChain()
        {
            List<LDAtkBullet> atkBullets = m_HeroSubWeapon.AtkBullet;
            if (atkBullets.Count < 2) return;
            for (int i = 0; i < atkBullets.Count - 1; i++)
            {
                if (i == atkBullets.Count - 1) break;
                
                LDTrackBullet bullet = m_HeroSubWeapon.GetBullet(m_LinkBulletId);
                bullet.Init(atkBullets[i].transform, 0, 0);
                bullet.AtkData.MulCiTiaoParam(m_HeroSubWeapon.GetCiTiaoDamageParam());
                if (bullet.BulletMove is LDRayLesserMoveMode rayLesserMoveMode)
                {
                    rayLesserMoveMode.SetLockObj(atkBullets[i], atkBullets[i+1], 0);
                    rayLesserMoveMode.RecycleEffect();
                }
            }
        }

        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[4]));
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[14]));
            CacheEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[13]));
        } 
        
        public override void Destroy()
        {
            CiTiaoMgr.HeroPlayer.SubWpnMgr.ReturnSubWeapon(m_HeroSubWeapon);
            base.Destroy();
        }
    }
}