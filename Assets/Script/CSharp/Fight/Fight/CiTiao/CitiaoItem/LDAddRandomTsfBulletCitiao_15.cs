
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDAddRandomTsfBulletCitiao_15 : LDEventCitiao
    {
        private double m_TriggerRata = 0;
        private double m_DamageParam = 1;
        private int m_BulletCount = 1;
        private int m_BulletId = 1;
        private float m_StartAngleMin = 0;
        private float m_StartAngleMax = 0;
        private float m_DtAngle = 0;
        private float m_Radius = 1;
        private float m_TriggerTimes = 1;
        private float m_CurTriggerTimes = 0;
        private float m_TriggerDt = 0.1f;
        private float m_CurTime = 0;

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_TriggerRata = GetCitiaoParam()[1];
            m_DamageParam = GetCitiaoParam()[2];
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            m_BulletCount = EZMath.IDDoubleToInt(GetCitiaoParam()[4]);
            m_StartAngleMin = (float)(GetCitiaoParam()[5]);
            m_StartAngleMax = (float)(GetCitiaoParam()[6]);
            m_DtAngle = (float)(GetCitiaoParam()[7]);
            m_Radius = (float)(GetCitiaoParam()[8]);
            m_TriggerTimes = (float)(GetCitiaoParam()[9]);
            m_TriggerDt = (float)(GetCitiaoParam()[10]);
            m_CurTriggerTimes = m_TriggerTimes;
        }
        public override void TriggerSucess(int triggerType)
        {
            if(!m_Restore)
            {
                if (RandomMatch(m_TriggerRata))
                {
                    base.TriggerSucess(-1);
                    m_CurTime = m_TriggerDt;
                    m_CurTriggerTimes = 0;
                }
            }
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if(m_CurTriggerTimes < m_TriggerTimes)
            {
                m_CurTime += dt;
                if(LDFightTools.GreaterOrEqualThenZero(m_CurTime,m_TriggerDt))
                {
                    m_CurTriggerTimes++;
                    m_CurTime -= m_TriggerDt;
                    TriggerAddBullet();
                }
            }
        }
        private void TriggerAddBullet()
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
            List<LDSceneObj> colliderData = Global.gApp.CurFightScene.gPlayerMgr.SearchUtils.FindRangeObj(m_Radius, heroPlayer.transform.GetPoisition(),
             LDFightConstVal.AIMask,heroPlayer.TeamId);
            int rangeCount = colliderData.Count;
            if (rangeCount > 0)
            {
                int index = RandomUtil.NextInt(0, rangeCount);
                LDSceneObj collider = colliderData[index];
                TryAddBullet(collider.transform);
            }
        }
        private void TryAddBullet(Transform aiMonster)
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
            double atk = heroPlayer.MechaMgr.GetAtk();
            int bulletId = m_BulletId;
            if (bulletId <= 0)
            {
                return;
                //bulletId = heroPlayer.BattleWpnMgr.GetWeaponBulletCfg().ItemId;
            }
            Vector3 vector3 = LDFightNodeTools.GetBodyNode(aiMonster).position;
            float startAngle = RandomUtil.NextFloat(m_StartAngleMin, m_StartAngleMax);
            float dtAngle = m_DtAngle;
            double damageParam = GetDamageParam(m_DamageParam);
            for (int i = 0; i < m_BulletCount; i++)
            {
                float offAngle = startAngle + dtAngle * i;
                LDHeroBulletData heroBulletData = heroPlayer.BulletEmitter.SummonDelayBullet(bulletId, atk, CitiaoBuffMgr, vector3, offAngle, 0);
                heroBulletData.DamageParam = damageParam;
                heroBulletData.CiTiaoItem = this;
            }
        }
        public override void TryCacheEffect()
        {
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            CacheBulletEffect(m_BulletId);
        }
    }
}
