using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public class LDAddRandomCiTiao_118 : LDEventCitiao
    {
        private int m_RandomCount = 1;
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_RandomCount = EZMath.IDFloatToInt(GetCitiaoParam()[1]);
        }
        public override void TriggerSucess(int triggerType)
        {
            if (!m_Restore)
            {
                List<LDCiTiaoItemData> citiaoDatas = new List<LDCiTiaoItemData>();
                for (int i = 0; i < m_RandomCount; i++)
                {
                    List<LDCiTiaoItemData> ciTiaoItemDatas = CiTiaoMgr.GetNormalValidCiTiao();
                    if (ciTiaoItemDatas.Count > 0)
                    {
                        int maxWeight = 0;
                        foreach (LDCiTiaoItemData item in ciTiaoItemDatas)
                        {
                            maxWeight += item.GetWeight();
                        }
                        int curWeight = 0;
                        int dstWeight = RandomUtil.NextInt(1, maxWeight);
                        foreach (LDCiTiaoItemData ciTiaoItemData in ciTiaoItemDatas)
                        {
                            curWeight += ciTiaoItemData.GetWeight();
                            if (curWeight >= dstWeight)
                            {
                                ciTiaoItemData.SetItemUsed();
                                citiaoDatas.Add(ciTiaoItemData);
                                break;
                            }
                        }
                    }

                }
                if (citiaoDatas.Count > 0 && CanShowCiTiaoInfoUI())
                {
                    if (CiTiaoMgr.HeroPlayer.HeroMgr.MainRole.GetGuid() == Global.gApp.CurFightScene.LocalPlayerGuid)
                    {
                        // 这里先按同步写  等UIMgr的异步逻辑完善了 再改
                        if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.UICitiaoGainTips) is UICitiaoGainTips uiCitiaoGainTips)
                        {
                            uiCitiaoGainTips.InitData(citiaoDatas);
                        }
                        else
                        {
                            Global.gApp.gUiMgr.OpenUIAsync<UICitiaoGainTips>(LDUICfg.UICitiaoGainTips)?.SetLoadedCall(baseUI =>
                            {
                                baseUI.InitData(citiaoDatas);
                            });    
                        }
                        
                    }
                    base.TriggerSucess(triggerType);
                }
            }
        }
    }
}
