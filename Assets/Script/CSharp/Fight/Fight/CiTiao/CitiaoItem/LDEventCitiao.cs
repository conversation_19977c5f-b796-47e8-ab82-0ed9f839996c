using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{

    public abstract partial class LDEventCitiao : LDCiTiaoItemBase
    {
        protected LDTriggerCitiaoConditionBase m_CitiaoCondition;
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            CreateCondition();
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            //看情况 conditin 也需要。比如没间隔多长时间 
            m_CitiaoCondition?.OnDUpdate(dt);
        }
        private void CreateCondition()
        {
            if (GetCitiaoTriggerParam().Length < 1)
            {
                UnityEngine.Debug.LogWarning("DYTriggerEventCiTiao.triggerParam.Length == 0" + CiTiaoItem.id);
                return;
            }
            int eventType = EZMath.IDDoubleToInt(GetCitiaoTriggerParam()[0]);
            m_CitiaoCondition = LDTriggerCitiaoConditionFactory.CreateCondition(eventType, CiTiaoMgr);
            m_CitiaoCondition?.Init(this);
        }
        public override void RecordCitiaoData(LDRecordCitiaoData passCitiaoDTOItem)
        {
            base.RecordCitiaoData(passCitiaoDTOItem);
            m_CitiaoCondition?.RecordCitiaoData(passCitiaoDTOItem);
        }
        public override void RestoreCitiaoData(LDRecordCitiaoData passCitiaoDTOItem)
        {
            base.RestoreCitiaoData(passCitiaoDTOItem);
            m_CitiaoCondition?.RestoreCitiaoData(passCitiaoDTOItem);
        }
        public void TryClearCD(float cdTime)
        {
            (m_CitiaoCondition as LDTriggerConditionTimeBase)?.ClearCD(cdTime);
        }
        public override void Destroy()
        {
            m_CitiaoCondition?.OnDestroy();
            m_CitiaoCondition = null;
            base.Destroy();
        }
    }
}
