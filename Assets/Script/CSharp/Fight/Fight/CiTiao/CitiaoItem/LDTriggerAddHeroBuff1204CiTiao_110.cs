using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public class LDTriggerAddHeroBuff1204CiTiao_110 : LDEventCitiao 
    {
        private List<LDBaseBuff> m_BaseBuffs = new List<LDBaseBuff>();
        public override void TriggerSucess(int triggerType)
        {
            //if(!m_Restore)
            {
                TryAddBuff();
            }
        }
        private void TryRmBuff()
        {
            if (m_BaseBuffs.Count > 0)
            {
                foreach (LDBaseBuff buffItem in m_BaseBuffs)
                {
                    buffItem.RemoveBuff();
                }
                m_BaseBuffs.Clear();
            }
        }
        private void TryAddBuff()
        {
            base.TriggerSucess(-1);
            float[] citiaoParam = GetCitiaoParam();
            for (int i = 1; i < citiaoParam.Length; i++)
            {
                List<LDBaseBuff> baseBuffs = TryAddTriggerRoleBuffById(citiaoParam[i]);
                if (baseBuffs != null)
                {
                    m_BaseBuffs.AddRange(baseBuffs);
                }
            }
        }
        public override void TryCacheEffect()
        {
            float[] citiaoParam = GetCitiaoParam();
            for (int i = 1; i < citiaoParam.Length; i++)
            {
                CacheBuffEffect(citiaoParam[i]);
            }
        }
        public override void Destroy()
        {
            TryRmBuff();
            base.Destroy();
        }
        private void HeroInjured(LDHeroPlayer heroPlayer)
        {
            if (heroPlayer == CiTiaoMgr.HeroPlayer)
            {
                TryRmBuff();
            }
        }
        protected override void RegListener(bool addListener)
        {
            base.RegListener(addListener);
            Global.gApp.gMsgDispatcher.RegEvent<LDHeroPlayer>(MsgIds.HeroInjured, HeroInjured, addListener);
        }
    }
}
