using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDNormalCiTiaoRandom_1 : LDEventCitiao
    {
        private int m_TriggerTimes = 0;

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
        }

        public override void TriggerSucess(int triggerType)
        {
            base.TriggerSucess(triggerType);
            TryAddBuff();
        }

        private float GetBuffVal()
        {
            LDRoleData roleData = CiTiaoMgr.HeroPlayer.HeroMgr.MainRole.RoleData;
            if (CiTiaoBaseGroup != null)
            {
                foreach (LDNetAttrEffect attrEffect in roleData.AttrEffect)
                {
                    AttrEffectItem attrEffectItem = AttrEffect.Data.Get(attrEffect.EffectId);
                    if (!string.IsNullOrEmpty(attrEffectItem.citiaoEffect))
                    {
                        string[] citiaoEffect = LDCommonTools.SplitStringList(attrEffectItem.citiaoEffect);
                        int groupID = LDParseTools.IntParse(citiaoEffect[0]);
                        int citiaoId = LDParseTools.IntParse(citiaoEffect[1]);
                        if (groupID == CiTiaoBaseGroup.CitiaoGroupItem.id && citiaoId == CiTiaoItem.id)
                        {
                            return (float)attrEffect.EffectValue;
                        }
                    }
                }
            }
            else
            {
                foreach (LDNetAttrEffect attrEffect in roleData.AttrEffect)
                {
                    AttrEffectItem attrEffectItem = AttrEffect.Data.Get(attrEffect.EffectId);
                    if (!string.IsNullOrEmpty(attrEffectItem.citiaoEffect))
                    {
                        string[] citiaoEffect = LDCommonTools.SplitStringList(attrEffectItem.citiaoEffect);
                        int groupID = LDParseTools.IntParse(citiaoEffect[0]);
                        int citiaoId = LDParseTools.IntParse(citiaoEffect[1]);
                        if (groupID <= 0 && citiaoId == CiTiaoItem.id)
                        {
                            return (float)attrEffect.EffectValue;
                        }
                    }
                }
            }

            return 0;
        }

        private void TryAddBuff()
        {
            // if (CiTiaoBaseGroup == null)
            // {
            //     return;
            // }

            int triggerTimes = m_TriggerTimes * 2;
            m_TriggerTimes = Mathf.Max(1, triggerTimes - m_TriggerTimes);
            for (int j = 0; j < m_TriggerTimes; j++)
            {
                float[] citiaoParam = GetCitiaoParam();
                for (int i = 1; i < citiaoParam.Length; i++)
                {
                    List<LDBaseBuff> buffs = TryAddTriggerRoleBuffById(citiaoParam[i]);
                    if (buffs != null)
                    {
                        foreach (LDBaseBuff buff in buffs)
                        {
                            buff.FreshBuffVal(GetBuffVal());
                        }
                    }
                }
            }
        }

        public override void TryCacheEffect()
        {
            float[] citiaoParam = GetCitiaoParam();
            for (int i = 1; i < citiaoParam.Length; i++)
            {
                CacheBuffEffect(citiaoParam[i]);
            }
        }
    }
}