
using UnityEngine;

namespace LD
{
    public class LDAddBulletBullet_205 : LDEventCitiao
    {
        private int m_TriggerRata = 0;
        private int m_BulletId = 1;
        private int m_BulletTimeSnyc = 0;

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_TriggerRata = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[2]);
            m_BulletTimeSnyc = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
        }

        public override void OnTriggerByWeaponSucess(LDBaseWeapon heroWeapon, LDSceneObj monster, LDAtkBullet atkBullet) 
        {
            if (!m_Restore)
            {
                if (RandomMatch(m_TriggerRata))
                {
                    base.TriggerSucess(-1);
                    TryAddBullet(atkBullet);
                }
            }
        }
        private void TryAddBullet(LDAtkBullet AtkBullet)
        {
            LDTrackBullet externBullet =  AtkBullet.CreateCiTiaoBullet(AtkBullet.transform, m_BulletId,0,this);
            if (m_BulletTimeSnyc == 1)
            {

                float leftTime = -1;
                if (AtkBullet is LDTrackBullet trackBullet)
                {
                    if (trackBullet.BulletMove != null)
                    {
                        leftTime = trackBullet.BulletMove.GetLeftLiveTime();
                    }
                }
                if (leftTime > 0)
                {
                    if (externBullet.BulletMove != null)
                    {
                        float externLeft = externBullet.BulletMove.GetLeftLiveTime();
                        externBullet.BulletMove.SetLiveTime(Mathf.Min(externLeft, leftTime));
                    }
                }
            }

        }

        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[2]));
        }
    }
}
