using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDChangeChangeTsf_305 : LDEventCitiao
    {
        private bool m_ChangeHeroAnim = false;
        private bool m_Changed = false;
        private bool m_Backing = false;
        private float m_CurTime = 0;
        private float m_BackCurTime = 0;

        private double m_DamageParam = 1;
        private int m_BulletId = 1;
        private float m_ChangeTime = 10;
        private float m_DtAddTime = 0.1f;
        private float m_MaxAddTime = 10;

        private float m_ChangeExternTime = 0;

        private  LDBaseBullet m_ChangeBullet;
        private List<LDBaseBuff> m_Buffs = new List<LDBaseBuff>();
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_Changed = false;
            m_DamageParam = GetCitiaoParam()[1];
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[2]);
            m_ChangeTime = (float)(GetCitiaoParam()[3]);
            m_DtAddTime = (float)(GetCitiaoParam()[4]);
            m_MaxAddTime = (float)(GetCitiaoParam()[5]);
        }
        public override void TriggerSucess(int triggerType)
        {
            base.TriggerSucess(triggerType);
            TryChange();
        }
        private void AddBuff()
        {
            float[] citiaoParam = GetCitiaoParam();
            for (int i = 6; i < citiaoParam.Length; i += 1)
            {
                m_Buffs.AddRange(TryAddTriggerRoleBuffById(citiaoParam[i]));
            }
        }
        private void TryRmBuff()
        {
            if (m_Buffs.Count > 0)
            {
                foreach (LDBaseBuff buffItem in m_Buffs)
                {
                    buffItem.RemoveBuff();
                }
                m_Buffs.Clear();
            }
        }
        private void AddChangeBullet()
        {
            RecycleBullet();
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
            double atk = heroPlayer.MechaMgr.GetAtk();
            m_ChangeBullet = heroPlayer.BulletEmitter.SummonBullet(m_BulletId, atk, CitiaoBuffMgr);
            m_ChangeBullet.Init(heroPlayer.AtkFight.RotateNode.transform, 0, 0);
            m_ChangeBullet.transform.SetParent(heroPlayer.AtkFight.RotateNode, false);
            m_ChangeBullet.transform.localPosition = Vector3.zero;
            m_ChangeBullet.transform.localEulerAngles = Vector3.zero;
            m_ChangeBullet.AtkData.MulCiTiaoParam(m_DamageParam);
            m_ChangeBullet.AtkData.CitiaoItem = this;
        }
        private void RecycleBullet()
        {
            if (m_ChangeBullet != null)
            {
                m_ChangeBullet.RecycleBullet(LDRecycleReason.TimeOver);
                m_ChangeBullet = null;
            }
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if(m_ChangeHeroAnim)
            {
                m_CurTime += dt;
                float radios = m_CurTime / (m_ChangeTime + Mathf.Min(m_ChangeExternTime, m_MaxAddTime));
                 Global.gApp.gMsgDispatcher.Broadcast<long, float>(MsgIds.ManaProgressChanged,
                    CiTiaoMgr.HeroPlayer.GetGuid(), 1 - radios);
                if (LDFightTools.GreaterOrEqualThenZero(radios,1))
                {
                    TransformBack();
                }
            }    
            if(m_Backing)
            {
                m_BackCurTime += dt;
                if(LDFightTools.GreaterOrEqualThenZero(m_BackCurTime,1))
                {
                    m_Backing = false;
                    RemoveStateInfo();
                }
            }
        }
        private void TransformBack()
        {
            if (m_ChangeHeroAnim)
            {
                m_ChangeHeroAnim = false;
                m_Backing = true;
                CiTiaoMgr.HeroPlayer.HeroAnimCtrol.PlayIdleAnim();
                CiTiaoMgr.HeroPlayer.HeroStateMgr.HeroStateMachine.TryChangeToTransformBackState();
            }
        }
        private void TryChange()
        {
            if (!m_Changed)
            {
                m_CurTime = 0;
                m_ChangeExternTime = 0;
                m_ChangeHeroAnim = true;
                m_Changed = true;
                CiTiaoMgr.HeroPlayer.MechaMgr.SetLockRotation_305(true);
                m_BackCurTime = 0;
                CiTiaoMgr.HeroPlayer.HeroStateMgr.HeroStateMachine.TryChangeToTransformState();
                CiTiaoMgr.HeroPlayer.gameObject.layer = LDFightConstVal.CarrierLayer;
                CiTiaoMgr.HeroPlayer.MoveCtroller.MRigidbody.mass = 1000;
                AddChangeBullet();
                AddBuff();
            }
        }
        private void RemoveStateInfo()
        {
            if (m_Changed)
            {
                m_Backing = false;
                m_Changed = false;
                LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
                heroPlayer.MechaMgr.SetLockRotation_305(false);
                heroPlayer.gameObject.layer = LDFightConstVal.HeroLayer;
                heroPlayer.MoveCtroller.MRigidbody.mass = 1;

                Global.gApp.gMsgDispatcher.Broadcast<long, float>(MsgIds.ManaProgressChanged,
                    heroPlayer.GetGuid(), 0);
                RecycleBullet();
                TryRmBuff();
            }
        }
        private void MonsterDeadth(LDAIMonster aIMonster)
        {
            if(m_ChangeHeroAnim)
            {
                m_ChangeExternTime += m_DtAddTime;
            }
        }
        protected override void RegListener(bool reg)
        {
            base.RegListener(reg);
            Global.gApp.gMsgDispatcher.RegEvent<LDAIMonster>(MsgIds.MonsterDeadByDamage, MonsterDeadth, reg);
        }
        public override void TryCacheEffect()
        {
            float[] citiaoParam = GetCitiaoParam();
            for (int i = 6; i < citiaoParam.Length; i += 1)
            {
                CacheBuffEffect(citiaoParam[i]);
            }
        }
        public override void Destroy()
        {
            TransformBack();
            RemoveStateInfo();
            base.Destroy();
        }
    }
}

