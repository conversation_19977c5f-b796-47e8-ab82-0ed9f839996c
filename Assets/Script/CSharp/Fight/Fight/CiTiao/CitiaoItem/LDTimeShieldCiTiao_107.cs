namespace LD
{
    public class LDTimeShieldCiTiao_107: LDCiTiaoItemBase
    {
        private float m_DtTime;
        private int m_BuffId;

        private float m_CurTime = 0;
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_DtTime = (float)GetCitiaoParam()[1];
            m_BuffId = EZMath.IDDoubleToInt(GetCitiaoParam()[2]);
            m_CurTime = GetDt();
        }
        private float GetDt()
        {
            return m_DtTime * (1 - (float)CiTiaoMgr.HeroPlayer.BuffMgr.GetBuffVal(LDBuffType.ShiedCD_308));
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            m_CurTime += dt;
            if(LDFightTools.GreaterOrEqualThenZero(m_CurTime,GetDt()))
            {
                m_CurTime -= GetDt();
                TryAddTriggerRoleBuffById(m_BuffId);
                base.TriggerSucess(-1);
            }
        }
        public void ReduceCurCD(float reduceCd)
        {
            m_CurTime += reduceCd;
        }
    }
}
