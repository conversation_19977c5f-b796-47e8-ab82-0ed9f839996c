using System.Collections.Generic;

namespace LD
{
    public class LDSelectCiTiao_115 : LDCiTiaoItemBase
    {
        private int m_TriggerTimes;
        private int m_MaxTriggerTimes = 1;
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_MaxTriggerTimes = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            m_TriggerTimes = 0;
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if(m_TriggerTimes < m_MaxTriggerTimes)
            {
                m_TriggerTimes += 1;
                LDCiTiaoInfo ciTiaoInfo = CiTiaoMgr.MainRole.ExpUtils.OpenNormalCiTiao(1);
                if (ciTiaoInfo != null)
                {
                    if (CiTiaoBaseGroup != null && CiTiaoBaseGroup.GroupInfo != null)
                    {
                        ciTiaoInfo.MechaId = CiTiaoBaseGroup.GroupInfo.MechaId;
                    }
                }
                base.TriggerSucess(-1);
            }
        }
        public override void RestoreCitiaoData(LDRecordCitiaoData passCitiaoDTOItem)
        {
            base.RestoreCitiaoData(passCitiaoDTOItem);
            m_TriggerTimes = EZMath.IDDoubleToInt(passCitiaoDTOItem.P1);
        }
        public override void RecordCitiaoData(LDRecordCitiaoData passCitiaoDTOItem)
        {
            base.RecordCitiaoData(passCitiaoDTOItem);
            passCitiaoDTOItem.P1 = m_TriggerTimes;
        }
    }
}
