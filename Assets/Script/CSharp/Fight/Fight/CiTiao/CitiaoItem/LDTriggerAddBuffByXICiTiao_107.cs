using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public class LDTriggerAddBuffByXICiTiao_107 : LDEventCitiao
    {
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
        }
        public override void TriggerSucess(int triggerType)
        {
            base.TriggerSucess(triggerType);
            TryAddBuff();
        }

        private void TryAddBuff()
        {
            float[] citiaoParam = GetCitiaoParam();
            for (int i = 1; i < citiaoParam.Length; i += 3)
            {
                int partXi = EZMath.IDFloatToInt(citiaoParam[i]);
                int partXiDtCount = EZMath.IDFloatToInt(citiaoParam[i + 1]);
                int buffId = EZMath.IDFloatToInt(citiaoParam[i + 2]);

                int curXiCount = CiTiaoMgr.HeroPlayer.GetXiCount(partXi);
                int addBuffTimes = curXiCount / partXiDtCount;

                for (int j = 0; j < addBuffTimes; j++)
                {
                    TryAddTriggerRoleBuffById(buffId);
                }
            }
 
        }
        public override void TryCacheEffect()
        {
            float[] citiaoParam = GetCitiaoParam();
            for (int i = 1; i < citiaoParam.Length; i += 3)
            {
                CacheBuffEffect(citiaoParam[i + 2]);
            }
        }
    }
}
