namespace LD
{
    public class LDAddBulletAndBullet_111 : LDEventCitiao
    {
        private bool m_FirstTrigger = false;
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if (!m_FirstTrigger)
            {
                m_FirstTrigger = true;
                if (CiTiaoMgr.HeroPlayer.DIYData.BodyId == 100002)
                {
                    Global.gApp.gMsgDispatcher.Broadcast(MsgIds.FreshTankGrayTime, CiTiaoMgr.HeroPlayer.GetGuid(), GetLeftTime());
                }
            }
        }

        public override void TriggerSucess(int triggerType)
        {
            base.TriggerSucess(triggerType);
            if (!m_Restore)
            {
                float[] citiaoParam = GetCitiaoParam();
                int bulletId = EZMath.IDFloatToInt(citiaoParam[1]);
                if (bulletId > 0)
                {
                    LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
                    double atk = heroPlayer.MechaMgr.GetAtk();
                    LDBaseBullet bullet = heroPlayer.BulletEmitter.SummonBullet(bulletId, atk, CitiaoBuffMgr);
                    bullet.transform.SetParent(heroPlayer.transform, false);
                    bullet.Init(heroPlayer.transform, 0, 0);
                    bullet.AtkData.CitiaoItem = this;
                    LDFightTools.ResetTransform(bullet.transform);
                }

                for (int i = 2; i < citiaoParam.Length; i++)
                {
                    TryAddTriggerRoleBuffById(citiaoParam[i]);
                }
                if (CiTiaoMgr.HeroPlayer.DIYData.BodyId == 100002)
                {
                    Global.gApp.gMsgDispatcher.Broadcast(MsgIds.FreshTankGrayTime, CiTiaoMgr.HeroPlayer.GetGuid(), GetLeftTime());
                }
            }
        }
        private float GetLeftTime()
        {
            LDTimeTriggerCondition_301 timeCondition = m_CitiaoCondition as LDTimeTriggerCondition_301;
            if(timeCondition != null)
            {
                return timeCondition.GetLeftTime();
            }
            else
            {
                return 0;
            }
        }
        public override void TryCacheEffect()
        {
            float[] citiaoParam = GetCitiaoParam();
            int bulletId = EZMath.IDFloatToInt(citiaoParam[1]);
            CacheBulletEffect(bulletId);
            for (int i = 2; i < citiaoParam.Length; i++)
            {
                CacheBuffEffect(citiaoParam[i]);
            }
        }
    }
}
