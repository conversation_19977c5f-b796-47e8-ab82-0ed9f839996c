using System;
using System.Collections.Generic;

namespace LD
{
    public class LDCiTiaoConditionMonsterHasDeBuff_204 : LDBaseExternCondition
    {
        private int m_MatchType;
        private Func<double,bool> m_MatchAction;
        public override void Init(float[] conditionPara)
        {
            base.Init(conditionPara);
            m_MatchType = EZMath.IDDoubleToInt(m_ConditionParam[1]);
            if (m_MatchType == 1)
            {
                m_MatchAction = MathchUp;
            }
            else
            {
                m_MatchAction = MathchDow;
            }
        }
        public override bool MatchCondition(LDHeroPlayer heroPlayer, LDAIMonster Monster)
        {
            if(Monster != null)
            {
                for (int i = 1; i < m_ConditionParam.Length; i++)
                {
                    int buffType = EZMath.IDDoubleToInt(m_ConditionParam[i]);
                    if (m_MatchAction(Monster.BuffMgr.GetBuffVal(buffType)))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool MathchUp(double val)
        {
            return LDFightTools.GreaterThenZero(val);
        }
        private bool MathchDow(double val)
        {
            return !LDFightTools.GreaterThenZero(val);
        }
    }
}
