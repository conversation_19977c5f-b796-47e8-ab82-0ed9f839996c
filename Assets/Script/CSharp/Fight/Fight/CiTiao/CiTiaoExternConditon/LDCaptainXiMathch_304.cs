using System.Collections.Generic;

namespace LD
{
    public class LDCaptainXiMathch_304 : LDBaseExternCondition
    {
        private int m_DstPartType = 0;
        private int m_Rarity = 0;
        private bool m_MatchCondition = false;
        public override void Init(float[] conditionPara)
        {
            base.Init(conditionPara);
            m_DstPartType = EZMath.IDDoubleToInt(conditionPara[1]);
            m_Rarity = EZMath.IDDoubleToInt(conditionPara[2]);
            int sCount = 0;
            int bodyId = m_HeroPlayer.DIYData.BodyId;
            MechaItem mechaItem = Mecha.Data.Get(bodyId);
            if (mechaItem.DamageSystem == m_DstPartType)
            {
                if (m_Rarity == LDItemRarityType.Normal || mechaItem.rarity == m_Rarity)
                {
                    sCount++;
                }
            }
            m_MatchCondition = sCount >= 1;
        }
        public override bool MatchCondition()
        {
            return m_MatchCondition;
        }
    }
}
