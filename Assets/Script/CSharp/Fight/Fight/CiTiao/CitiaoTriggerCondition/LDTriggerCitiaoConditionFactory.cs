namespace LD
{
    public class LDTriggerCiTiaoConditionType
    {
        // 默认触发
        public static int DefaultTrigger = 1;                  
        //101.冷却（总次数，初始冷却，常规冷却）
        public static int TimeLimit_101 = 101;                  //时间间隔
        public static int ExpLvUp_102 = 102;// 升级

        public static int HeroInjured_103 = 103;// 角色受伤 
        public static int HeroInRevive_104 = 104;// 角色 复活
        public static int ExpLvMax_105 = 105;// 满级升级
        public static int HeroMoveDis_106 = 106;// 移动距离
        public static int HeroBuffJudge_107 = 107;// 角色身上buff 判定
        public static int HeroShieldJudge_108 = 108;// 角色身上护盾值
        public static int HeroHpPercent_109 = 109;// 角色血量百分比
        public static int UseAssistMech_110 = 110;// 召唤助战机甲
        public static int CiTiaoGroupLvUp_111 = 111;// 词条组升级 
        public static int TeamOnlyCurMechaLive_112 = 112;// 我方队伍仅剩当前机甲

        //怪物死于指定子弹时（总次数，每x次，子弹ID，子弹ID…)
        public static int MonsterDeadDstBullet_201 = 201;// 怪物死亡
        public static int MonsterHitByCaptainMainBullet_203 = 202;// 怪物 被队长主武器打
        public static int MonsterCreate_203 = 203;              //怪物创建   
        

        public static int MonsterAddBuff_204 = 204;    //Monster 加buff

        public static int MonsteCount_205 = 205;    //Monster Count
        public static int WaveComming_206 = 206;    //怪群来袭
        public static int AIBuffValChange_207 = 207;    //战争女生专属的 叠满之后。需要 删除
        public static int HeroBulletTriggerBlockHole_208 = 208;    // 子弹穿过黑洞
        public static int MonsterExit_209 = 209;    // 子弹穿过黑洞
        public static int MonsterHitByHeroBullet_210 = 210; // 子弹命中怪物 在同一个怪物身上触发n次
        public static int BulletHitCount = 211; // 子弹命中前X个怪物
        public static int GoddessTagCount_205 = 22222;    //Monster Count
        // 机甲开火时（每x次）
        public static int CaptainFireBullet_301 = 301;    //队长开火
        public static int CaptainSubWpnFireBullet_302 = 302;    //队长开火

        public static int CaptainStartReload_303 = 303;    //换弹前
        public static int CaptainReloadEnd_304 = 304;       // 换弹后
        public static int CaptainXiPartCount_305 = 305;       // 携带某类型部件到指定数量触发
        public static int CaptainGainNewCiTiao_306 = 306;       // 获得特定词条
        public static int SameMechCurve_307 = 307;       // 获得特定词条
        public static int CaptainFireBullet_308 = 308;       // 获得特定词条

        public static int CaptainXiMecha_309 = 309;       // 指定系机甲
        public static int CaptainGainDstNewCiTiao_310 = 310;       // 获得所有特定词条
        public static int MechaDeformation_311 = 311;       // 机甲变形
        public static int CaptainXiPartCount_312 = 312;       // 携带某类型部件多少个就触发多少次
        public static int CaptainClipFireCount_313 = 313;       // 主角弹夹消耗数
        
        public static int CaptainTriggerCrit_401 = 401;    //触发暴击的时候
       //===========================

        public static int CaptainAddBuff_103 = 9999;    //队长加buff
        public static int CaptainTryAddBuff_104 = 9999;    //队长尝试加buff


        public static int GainExp_108 = 9999;// 加经验的
        public static int CaptainTryDeadth_109 = 9999;// 队长死亡之前


        public static int MonsterDead_201 = 9999;// 怪物死亡
        public static int MonsterHitByCaptainBullet_202 = 9999;// 怪物 被 队长 子弹 打

  
        public static int MonsterTryAddBuff_205 = 9999;    //怪物尝试加buff
        public static int MonsterDeadDstBuff_206 = 9999;// 怪物死亡


     
         
        public static int OnSumCaptainBullet_End_303 = 9999;     //进入战斗的时候            
        public static int OnCaptainEnterFight_304 = 9999;        //进入战斗的时候
        public static int OnMonsterDeadIncEnergy_305 = 9999;     //杀怪回能
        public static int TimeLimit_306 = 9999;                  //时间间隔

        public static int TimeLimitSkill_401 = 99999;    //进入战斗的时候
           
    }
    public class LDTriggerCitiaoConditionFactory
    {
        public static LDTriggerCitiaoConditionBase CreateCondition(int conditionType, LDCiTiaoMgr ciTiaoMgr)
        {
            LDTriggerCitiaoConditionBase citiaoConditionBase = null;
            if(conditionType == LDTriggerCiTiaoConditionType.HeroInjured_103)
            {
                citiaoConditionBase = new LDHeroInjuredTriggerCondition_105();
            }        
            if(conditionType == LDTriggerCiTiaoConditionType.HeroInRevive_104)
            {
                citiaoConditionBase = new LDHeroRevivewTriggerCondition_104();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterDead_201)
            {
                citiaoConditionBase = new LDMonsterDeadthTriggerCondition_201();
            }          
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterDeadDstBuff_206)
            {
                citiaoConditionBase = new LDMonsterDeadthByDstBuffTriggerCondition_206();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.ExpLvUp_102)
            {
                citiaoConditionBase = new LDExpLvUpTriggerCondition_102();
            }         
            else if(conditionType == LDTriggerCiTiaoConditionType.ExpLvMax_105)
            {
                citiaoConditionBase = new LDCiTiaoGroupMaxCondition_105();
            }             
            else if(conditionType == LDTriggerCiTiaoConditionType.HeroMoveDis_106)
            {
                citiaoConditionBase = new LDMoveDisTriggerCondition_107();
            }              
            else if(conditionType == LDTriggerCiTiaoConditionType.HeroBuffJudge_107)
            {
                citiaoConditionBase = new LDCaptainGainBuffTriggerCondition_107();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.HeroShieldJudge_108)
            {
                citiaoConditionBase = new LDCaptainShieldCondition_108();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.HeroHpPercent_109)
            {
                citiaoConditionBase = new LDHeroHpPercentCondition_109();
            }               
            else if(conditionType == LDTriggerCiTiaoConditionType.UseAssistMech_110)
            {
                citiaoConditionBase = new LDUseAssistMech_110();
            }               
            else if (conditionType == LDTriggerCiTiaoConditionType.CiTiaoGroupLvUp_111)
            {
                citiaoConditionBase = new LDCiTiaoGroupLvUp_111();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.TeamOnlyCurMechaLive_112)
            {
                citiaoConditionBase = new LDTeamOnlyCurMechaLive_112();
            }        
            else if(conditionType == LDTriggerCiTiaoConditionType.GainExp_108)
            {
                citiaoConditionBase = new LDGainExpTriggerCondition_108();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterHitByCaptainBullet_202)
            {
                citiaoConditionBase = new LDAIHitByCaptainBulletTriggerCondition_202();
            }          
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterHitByCaptainMainBullet_203)
            {
                citiaoConditionBase = new LDAIHitByCaptainMainBulletTriggerCondition_203();
            }            
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainFireBullet_301)
            {
                citiaoConditionBase = new LDCaptainFireTriggerCondition_301();
            }        
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainFireBullet_308)
            {
                citiaoConditionBase = new LDCaptainFireTriggerCondition_308();
            }
            else if (conditionType == LDTriggerCiTiaoConditionType.CaptainSubWpnFireBullet_302)
            {
                citiaoConditionBase = new LDCaptainSubFireTriggerCondition_302();
            }             
            else if (conditionType == LDTriggerCiTiaoConditionType.CaptainTriggerCrit_401)
            {
                citiaoConditionBase = new LDCaptainTriggerCritCondition_401();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.TimeLimit_101)
            {
                citiaoConditionBase = new LDTimeTriggerCondition_301();
            }             
            else if(conditionType == LDTriggerCiTiaoConditionType.DefaultTrigger)
            {
                citiaoConditionBase = new LDDefaultTriggerCondition_1();
            }
            else if (conditionType == LDTriggerCiTiaoConditionType.TimeLimit_306)
            {
                citiaoConditionBase = new LDTimeTriggerCondition_306();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.TimeLimitSkill_401)
            {
                citiaoConditionBase = new LDTimeSkillTriggerCondition_401();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainStartReload_303)
            {
                citiaoConditionBase = new LDCaptainStartReloadTriggerCondition_303();
            }          
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainReloadEnd_304)
            {
                citiaoConditionBase = new LDCaptainEndReloadTriggerCondition_304();
            }        
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainXiPartCount_305)
            {
                citiaoConditionBase = new LDCaptainXiPartCount_305();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainGainNewCiTiao_306)
            {
                citiaoConditionBase = new LDCaptainGainNewCiTiao_306();
            }         
            else if(conditionType == LDTriggerCiTiaoConditionType.SameMechCurve_307)
            {
                citiaoConditionBase = new LDSameMechCurve_307();
            }        
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainXiMecha_309)
            {
                citiaoConditionBase = new LDCaptainXiMecha_309();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainGainDstNewCiTiao_310)
            {
                citiaoConditionBase = new LDCaptainGainDstNewCiTiao_310();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.MechaDeformation_311)
            {
                citiaoConditionBase = new LDMechaDeformation_311();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainXiPartCount_312)
            {
                citiaoConditionBase = new LDCaptainXiPartCount_312();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainClipFireCount_313)
            {
                citiaoConditionBase = new CaptainClipFireCount_313();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainAddBuff_103)
            {
                citiaoConditionBase = new LDCaptainBuffTriggerCondition_103();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterAddBuff_204)
            {
                citiaoConditionBase = new LDAIBuffTriggerCondition_204();
            }      
            else if(conditionType == LDTriggerCiTiaoConditionType.GoddessTagCount_205)
            {
                citiaoConditionBase = new LDAIBuffTriggerCondition_206();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainTryAddBuff_104)
            {
                citiaoConditionBase = new LDCaptainTryAddBuffTriggerCondition_104();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterTryAddBuff_205)
            {
                citiaoConditionBase = new LDAITryAddBuffTriggerCondition_205();
            }            
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterDeadDstBullet_201)
            {
                citiaoConditionBase = new LDMonsterDeadthByDstBulletCondition_207();
            }            
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterCreate_203)
            {
                citiaoConditionBase = new LDMonsterCreateCondition_302();
            }         
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsterExit_209)
            {
                citiaoConditionBase = new LDMonsterExitCondition_209();
            }        
            else if (conditionType == LDTriggerCiTiaoConditionType.MonsterHitByHeroBullet_210)
            {
                citiaoConditionBase = new LDMonsterHitByHeroBullet_210();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.BulletHitCount)
            {
                citiaoConditionBase = new LDBulletHitCount_211();
            }        
            else if(conditionType == LDTriggerCiTiaoConditionType.HeroBulletTriggerBlockHole_208)
            {
                citiaoConditionBase = new LDHeroBulletTriggerBlockHole_208();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.MonsteCount_205)
            {
                citiaoConditionBase = new LDMonsterCountCondition_205();
            }      
            else if(conditionType == LDTriggerCiTiaoConditionType.WaveComming_206)
            {
                citiaoConditionBase = new LDWaveCommingCondition_206();
            }           
            else if(conditionType == LDTriggerCiTiaoConditionType.AIBuffValChange_207)
            {
                citiaoConditionBase = new LDAIBuffValChangedTriggerCondition_207();
            }                
            else if(conditionType == LDTriggerCiTiaoConditionType.HeroBulletTriggerBlockHole_208)
            {
                citiaoConditionBase = new LDHeroBulletTriggerBlockHole_208();
            }               
            else if(conditionType == LDTriggerCiTiaoConditionType.CaptainTryDeadth_109)
            {
                citiaoConditionBase = new LDCaptainTryDeadthTriggerCondition_109();
            }
            else if (conditionType == LDTriggerCiTiaoConditionType.OnSumCaptainBullet_End_303)
            {
                citiaoConditionBase = new LDCaptainSumBullet_End_303();
            }
            else if(conditionType == LDTriggerCiTiaoConditionType.OnCaptainEnterFight_304)
            {
                citiaoConditionBase = new LDCaptainEnterFight_304();
            }            
            else if(conditionType == LDTriggerCiTiaoConditionType.OnMonsterDeadIncEnergy_305)
            {
                citiaoConditionBase = new LDMonsterDeadIncEnergyCondition_305();
            }
            else
            {
                UnityEngine.Debug.Log("Event Type not Match" + conditionType);
            }
            if(citiaoConditionBase != null)
            {
                citiaoConditionBase.SetBaseInfo(conditionType, ciTiaoMgr);
            }
            return citiaoConditionBase;
        }

    }
}
