using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDMonsterDeadthByDstBuffTriggerCondition_206 : LDTriggerCitiaoConditionBase
    {
        private int m_DstCount = 1; 
        private int m_CurCount = 0; 
        private int m_TriggerTimes = 0; 
        private int m_BuffId = -1; 
        public override void Init(LDEventCitiao ciTiaoItemBase)
        {
            base.Init(ciTiaoItemBase);
            m_TriggerTimes = 0;
            m_DstCount = EZMath.IDDoubleToInt(ciTiaoItemBase.GetCitiaoTriggerParam()[2]);
            m_BuffId = EZMath.IDDoubleToInt(ciTiaoItemBase.GetCitiaoTriggerParam()[3]);
        }
        public override void RecordCitiaoData(LDRecordCitiaoData citiaoData)
        {
            base.RecordCitiaoData(citiaoData);
            citiaoData.CP1 = m_CurCount;
            citiaoData.CP2 = m_TriggerTimes;
        }
        public override void RestoreCitiaoData(LDRecordCitiaoData citiaoData)
        {
            base.RestoreCitiaoData(citiaoData);
            m_CurCount = EZMath.IDDoubleToInt(citiaoData.CP1);
            m_TriggerTimes = EZMath.IDDoubleToInt(citiaoData.CP2);
            for (int i = 0; i < m_TriggerTimes; i++)
            {
                TriggerSucess(null,null);
            }
        }
        private void MonsterDeadthByBuff(LDAIMonster aIMonster, LDBaseBuff buff)
        {
            if (!m_CitiaoMgr.HeroPlayer.Live)
            {
                return;
            }
            if (m_TriggerTimes < m_MaxTriggerTimes)
            {
                if (m_BuffId <= 0 || m_BuffId == buff.BuffItem.id)
                {
                    if (MatchCondition(aIMonster))
                    {
                        m_CurCount += 1;
                        if (m_CurCount >= m_DstCount)
                        {
                            m_CurCount -= m_DstCount;
                            m_TriggerTimes++;
                            TriggerSucess(aIMonster, buff);
                        }
                    }
                }
            }
        }
        private void TriggerSucess(LDAIMonster aIMonster, LDBaseBuff buff)
        {
            m_EventCiTiao.TriggerSucess(m_EventType);
            // 最后一个 触发的怪物
            m_EventCiTiao.OnTriggerAIDeadthByBuffSucess(aIMonster, buff);
        }

        protected override void RegEvent(bool addListener)
        {
            base.RegEvent(addListener);
            Global.gApp.gMsgDispatcher.RegEvent<LDAIMonster, LDBaseBuff>(MsgIds.MonsterDeadByBuff, MonsterDeadthByBuff, addListener);
        }
    }
}
