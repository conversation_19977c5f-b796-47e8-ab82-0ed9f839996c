namespace LD
{
    public class LDDefaultTriggerCondition_1 : LDTriggerCitiaoConditionBase
    {
        private int m_TriggerTimes = 0;
        public override void Init(LDEventCitiao ciTiaoItemBase)
        {
            base.Init(ciTiaoItemBase);

        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if(m_TriggerTimes == 0)
            {
                if (MatchWeaponCondition(m_CitiaoMgr.HeroPlayer.MechaMgr.GetHeroWeapon()))
                {
                    m_TriggerTimes++;
                    m_EventCiTiao.TriggerSucess(m_EventType);
                }
            }
        }
        public override void RecordCitiaoData(LDRecordCitiaoData citiaoData)
        {
            base.RecordCitiaoData(citiaoData);
            citiaoData.CP1 = m_TriggerTimes;
        }
        public override void RestoreCitiaoData(LDRecordCitiaoData citiaoData)
        {
            base.RestoreCitiaoData(citiaoData);
            m_TriggerTimes = EZMath.IDDoubleToInt(citiaoData.CP1);
            for (int i = 0; i < m_TriggerTimes; i++)
            {
                m_EventCiTiao.TriggerSucess(m_EventType);
            }
        }
    }
}
