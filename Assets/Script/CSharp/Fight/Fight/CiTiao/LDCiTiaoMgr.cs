
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    /// <summary>
    /// 词条在恢复过程中如果发生卡顿。就酌情处理一些。有的词条条件。只需恢复参数 。不需要恢复 行为 
    /// </summary>
    public class LDCiTiaoMgr: IUpdate
    {
        public LDMainRole MainRole { private set; get; }
        public LDHeroPlayer HeroPlayer { private set; get; }
        public bool Resotre { set; get; }
        public long MainGuid { private set; get; }
        // 这个词条不会remove 不用担心data 删除 dirty问题 
        private Dictionary<int, LDCiTiaoBaseGroup> m_AllCiTiaoGroups = new Dictionary<int, LDCiTiaoBaseGroup>();
        // 已经选择的词条
        private LDSafeList<LDCiTiaoItemBase> m_SelectedCiTiao = new LDSafeList<LDCiTiaoItemBase>();
        // 可以 选择的词条
        private List<LDCiTiaoItemData> m_ValidCiTiaoTmp = new List<LDCiTiaoItemData>();

        public LDRandomUtil RandomUtil { private set; get; }
        public long CiTiaoGuid { private set; get; } = 100000000;
        public void Init(LDMainRole mainRole, LDRoleData roleData,LDHeroPlayer heroPlayer)
        {
            HeroPlayer = heroPlayer;
            MainRole = mainRole;
            MainGuid = mainRole.GetGuid();
            RandomUtil = new LDRandomUtil(mainRole.RandomUtil.NextSeed());

            if(heroPlayer.ObjType == LDSceneObjType.AssistHero || heroPlayer.HeroInfo.LeagueMechaAI)
            {
                return;
            }
            List<int> skillInfo = Global.gApp.gSystemMgr.gEnhancementMgr.GetCitiaoIds(heroPlayer.HeroInfo.Skill_id,(int)Global.gApp.CurFightScene.PassType);
            // skill 外围对接的时候需要整理一下
            foreach (int skillId in skillInfo)
            {
                if (skillId > 0)
                {
                    ForceAddCiTiao(skillId);
                }
            }            
            List<int> exclusiveSkillInfo = Global.gApp.gSystemMgr.gDIYMgr.GetExclusiveSkill(heroPlayer.DIYData,Global.gApp.CurFightScene.PassType);
            // skill 外围对接的时候需要整理一下
            foreach (int skillId in exclusiveSkillInfo)
            {
                if (skillId > 0)
                {
                    ForceAddCiTiao(skillId);
                }
            }          
            foreach (int skillId in heroPlayer.HeroInfo.Skill_id_Extern)
            {
                if (skillId > 0)
                {
                    ForceAddCiTiao(skillId);
                }
            }        
            foreach (LDDriverBattleInfo driverInfo in heroPlayer.HeroInfo.DriveInfo)
            {
                foreach (int skillId in driverInfo.Citiaos)
                {
                    if (skillId > 0)
                    {
                        LDCiTiaoItemBase CiTiaoItemBase = ForceAddCiTiao(skillId);
                        if(CiTiaoItemBase != null)
                        {
                            CiTiaoItemBase.DriverBattleInfo = driverInfo;
                        }
                    }
                }
            }
            TryForceAddMonthCardCiTiao();
            // 是否需要自定义词条组 
            if (Global.gApp.CurFightScene.gPassHandler.CustomInitCiTiaoGroup())
            {
                return;
            }
            // 关卡词条
            int[] CGId = Global.gApp.CurFightScene.gPassData.cgId;
            foreach (int groupId in CGId)
            {
                AddCiTiaoGroup(groupId,null);
            }

            List<LDCitiaoGroupInfo> CitiaoGroup_Info = new List<LDCitiaoGroupInfo>(heroPlayer.HeroInfo.CitiaoGroup_Info);

            List<LDNetAttrEffect> CiTiaoAttrEffect = new List<LDNetAttrEffect>();

            foreach (LDNetAttrEffect attrEffect in roleData.AttrEffect)
            {
                AttrEffectItem attrEffectItem = AttrEffect.Data.Get(attrEffect.EffectId);
                if (!string.IsNullOrEmpty(attrEffectItem.citiaoEffect))
                {
                    string[] citiaoEffect = LDCommonTools.SplitStringList(attrEffectItem.citiaoEffect);
                    int groupID = LDParseTools.IntParse(citiaoEffect[0]);
                    int citiaoId = LDParseTools.IntParse(citiaoEffect[1]);
                    LDNetAttrEffect targetAttrEffect = null;
                    foreach (LDNetAttrEffect item in CiTiaoAttrEffect)
                    {
                        if(item.GroupId == groupID && item.CiTiaoId == citiaoId)
                        {
                            targetAttrEffect = item;
                            break;
                        }
                    }

                    if (groupID <= 0 && targetAttrEffect == null)
                    {
                        ForceAddCiTiao(citiaoId);
                    }
                    if(targetAttrEffect ==  null)
                    {
                        targetAttrEffect = new LDNetAttrEffect(attrEffect.EffectId);
                        targetAttrEffect.EffectValue = 0;
                        CiTiaoAttrEffect.Add(targetAttrEffect);
                    }
                    targetAttrEffect.EffectValue += attrEffect.EffectValue;
                    targetAttrEffect.GroupId = groupID;
                    targetAttrEffect.CiTiaoId = citiaoId;
                }
            }

            roleData.AttrEffect = CiTiaoAttrEffect;
            
            foreach (LDNetAttrEffect attrEffect in CiTiaoAttrEffect)
            {
                AttrEffectItem attrEffectItem = AttrEffect.Data.Get(attrEffect.EffectId);
                if (!string.IsNullOrEmpty(attrEffectItem.citiaoEffect))
                {
                    // 组里没有就不加了。加不上
                    foreach (LDCitiaoGroupInfo citiaoGroupInfo in CitiaoGroup_Info)
                    {
                        if (citiaoGroupInfo.CitiaoGroupId == attrEffect.GroupId)
                        {
                             if (!citiaoGroupInfo.CiTiaoIds.Contains(attrEffect.CiTiaoId))
                            {
                                citiaoGroupInfo.CiTiaoIds.Add(attrEffect.CiTiaoId);
                            }
                        }
                    }
                }
            }
            // diy 词条
            AddDIYCitiaoGroups(CitiaoGroup_Info);

        }
        public void AddDIYCitiaoGroups(List<LDCitiaoGroupInfo> CitiaoGroup_Info)
        {
            List<LDCitiaoGroupInfo> citiaoGroup = CitiaoGroup_Info;
            foreach (LDCitiaoGroupInfo item in citiaoGroup)
            {
                TryAddCiTiaoGroup(item.CitiaoGroupId, item);
            }
            CalcGroupCiTiaoRate();
        }
        public long GetGuid()
        {
            CiTiaoGuid++;
            return CiTiaoGuid;
        }
        private void CalcGroupCiTiaoRate()
        {
            int[] rateData = Global.gApp.CurFightScene.gPassData.PassItem.cgRecommend;
            List<int> externWeightRate = Global.gApp.CurFightScene.gPassData.ExternCitiaoWeightRate;
            if (rateData.Length > 0 || externWeightRate.Count > 0)
            {
                foreach (KeyValuePair<int, LDCiTiaoBaseGroup> item in m_AllCiTiaoGroups)
                {
                    for (int i = 0; i < rateData.Length; i += 2)
                    {
                        int groupId = rateData[i];
                        int rate = rateData[i + 1];
                        if (groupId == item.Value.CitiaoGroupItem.id)
                        {
                            item.Value.SetWeightRate(rate);
                            break;
                        }
                    }
                    
                    for (int i = 0; i < externWeightRate.Count; i += 2)
                    {
                        int groupId = externWeightRate[i];
                        int rate = externWeightRate[i + 1];
                        if (groupId == item.Value.CitiaoGroupItem.id)
                        {
                            item.Value.SetWeightRate(rate); // 策划需求 都是直接覆盖
                            break;
                        }
                    } 
                }
            }
        }
        public void TryCacheEffect()
        {
            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> citiaoGroupData in m_AllCiTiaoGroups)
            {
                List<LDCiTiaoItemData> allData = citiaoGroupData.Value.GetAllCiTiaoData();
                foreach (LDCiTiaoItemData item in allData)
                {
                    item.BattleCiTiaoItem.TryCacheEffects();
                }
            }
            foreach (LDCiTiaoItemBase citiaoItem in m_SelectedCiTiao.GetAll())
            {
                citiaoItem.TryCacheEffects();
            }
        }
        public LDCiTiaoBaseGroup TryAddCiTiaoGroup(int groupId, LDCitiaoGroupInfo citiaoGroupInfo)
        {
            if(m_AllCiTiaoGroups.ContainsKey(groupId))
            {
                return m_AllCiTiaoGroups[groupId];
            }
            else
            {
                return AddCiTiaoGroup(groupId, citiaoGroupInfo);
            }
        }
        public LDCiTiaoItemBase SetCiTiaoUsed(int groupId,int citiaoId, bool applyLvUp = true)
        {
            if(citiaoId <= 0 || citiaoId <= 0)
            {
                Debug.LogError(" paran error " + groupId +  " citiaoId" + citiaoId);
                return null;
            }
            LDCiTiaoBaseGroup ciTiaoGroup = TryAddCiTiaoGroup(groupId,null);
            return ciTiaoGroup.SetCitiaoUsed(citiaoId,applyLvUp);
        }
        private LDCiTiaoBaseGroup AddCiTiaoGroup(int groupId,LDCitiaoGroupInfo ciTiaoInfo)
        {
            CitiaoGroupItem citiaoGroupItem = CitiaoGroup.Data.Get(groupId);
            if (citiaoGroupItem != null)
            {
                LDCiTiaoBaseGroup ciTiaoGroup = LDCiTiaoFactory.GetCiTiaoGroup(citiaoGroupItem);
                if (ciTiaoGroup.Init(this,citiaoGroupItem, ciTiaoInfo))
                {
                    m_AllCiTiaoGroups[citiaoGroupItem.id] = ciTiaoGroup;
                }
                return ciTiaoGroup;
            }
            else
            {
                UnityEngine.Debug.LogError(" citiao groupId error" + groupId);
                return null;
            }
        }
        public void OnDUpdate(float dt)
        {
            m_SelectedCiTiao.OnDUpdate(dt);
        }
        /// <summary>
        /// 强插词条
        /// </summary>
        /// <param name="citiaoId"></param>
        /// <returns></returns>
        public LDCiTiaoItemBase TryForceAddMonthCardCiTiao(bool force = false)
        {
            if(MainRole.RoleData.MonthCardNormal || force)
            {
                int citiaoId = Global.gApp.gSystemMgr.gMonthlyCardMgr.GetMonthlyCardCiTiao();
                LDCiTiaoItemBase item = GetSelectedCiTiaoById(citiaoId);
                if (item == null)
                {
                    return ForceAddCiTiao(citiaoId);
                }
                else
                {
                    return item;
                }
            }
            return null;
        }
        public LDCiTiaoItemBase ForceAddCiTiao(int citiaoId)
        {
            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            if (citiaoItem != null)
            {
                LDCiTiaoItemBase battleCiTiaoBaseItem = LDCiTiaoFactory.GetCitiaoItem(citiaoItem,this);
                if (battleCiTiaoBaseItem != null)
                {
                    battleCiTiaoBaseItem.SpecialInit(citiaoItem);
                    AddUsedCiTiao(battleCiTiaoBaseItem);
                    return battleCiTiaoBaseItem;
                }
            }
            else
            {
                UnityEngine.Debug.LogError("addCiTIao id wrong " + citiaoId);
            }
            return null;
        }
        public void RemoveUsedCiTiao(LDCiTiaoItemBase citiaoItem)
        {
            if (citiaoItem != null)
            {
                citiaoItem.Destroy();
                // 生效词条会移除，但是所有选过的词条还在
                m_SelectedCiTiao.Remove(citiaoItem);
            }
        }
        public void AddUsedCiTiao(LDCiTiaoItemBase citiaoItem)
        {
            m_SelectedCiTiao.Add(citiaoItem);
        }
        public void FreshSubWeaponCD(int subWpnId)
        {
            foreach (LDCiTiaoItemBase item in m_SelectedCiTiao.GetAll())
            {
                if(item is LDSubWeaponCiTiao subWpnCiTiao)
                {
                    subWpnCiTiao.ResetTimeCondition(subWpnId);
                }
            }
        }      
        
        public List<LDCiTiaoItemBase> GetAllUsedCiTiao()
        {
            return m_SelectedCiTiao.GetAll();
        }    
         public LDCiTiaoItemBase GetSelectedCiTiaoById(int citiaoId)
        {
            foreach (LDCiTiaoItemBase item in m_SelectedCiTiao.GetAll())
            {
                if(item.CiTiaoItem.id == citiaoId)
                {
                    return item;
                }
            }
            return null;
        }

        public List<LDCiTiaoItemData> GetRandomValidCiTiao(int uiCiTiaoType)
        {
            //用哪些词条
            List<LDCiTiaoItemData> tmp = null;
            if (uiCiTiaoType == LDCiTiaoType.Normal)
            {
                tmp = GetNormalValidCiTiao();
            }
            else if (uiCiTiaoType == LDCiTiaoType.Power)
            {
                tmp = GeBossValidCiTiao();
            }       
            else if (uiCiTiaoType == LDCiTiaoType.EliteBook)
            {
                tmp = GeEliteBookValidCiTiao();
            }
            return tmp;
        }
        public List<LDCiTiaoItemData> GetNormalValidCiTiao()
        {
            m_ValidCiTiaoTmp.Clear();
            foreach (var group in m_AllCiTiaoGroups.Values)
            {
                 List<LDCiTiaoItemData> ciTiaoItemData = group.GetNormalValidCiTiao();
                if (ciTiaoItemData != null)
                {
                    m_ValidCiTiaoTmp.AddRange(ciTiaoItemData);
                }
            }
            return m_ValidCiTiaoTmp;
        }       

        private List<LDCiTiaoItemData> GeBossValidCiTiao()
        {
            m_ValidCiTiaoTmp.Clear();
            foreach (var group in m_AllCiTiaoGroups.Values)
            {
                List<LDCiTiaoItemData> ciTiaoItemData = group.GeNormal2ValidCiTiao();
                if (ciTiaoItemData != null)
                {
                    m_ValidCiTiaoTmp.AddRange(ciTiaoItemData);
                }
            }
            return m_ValidCiTiaoTmp;
        }       
        public List<LDCiTiaoItemData> GeEliteBookValidCiTiao()
        {
            m_ValidCiTiaoTmp.Clear();
            foreach (var group in m_AllCiTiaoGroups.Values)
            {
                List<LDCiTiaoItemData> ciTiaoItemData = group.GeEliteBookValidCiTiao();
                if (ciTiaoItemData != null)
                {
                    m_ValidCiTiaoTmp.AddRange(ciTiaoItemData);
                }
            }
            return m_ValidCiTiaoTmp;
        }
        public List<LDCiTiaoBaseGroup> GetMaxCiTiaoGroup()
        {
            List<LDCiTiaoBaseGroup> ciTiaoBaseGroups = new List<LDCiTiaoBaseGroup>();
            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> item in m_AllCiTiaoGroups)
            {
                if(item.Value.IsLvMaxed())
                {
                    ciTiaoBaseGroups.Add(item.Value);
                }
            }
            return ciTiaoBaseGroups;
        }
        public bool CiTiaoGrouMax(int groupId)
        {
            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> item in m_AllCiTiaoGroups)
            {
                if (item.Key == groupId)
                {
                    if (item.Value.IsLvMaxed())
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        public LDCiTiaoBaseGroup GetCiTiaoGroup(int grouId)
        {
            if (m_AllCiTiaoGroups.ContainsKey(grouId))
            {
                return m_AllCiTiaoGroups[grouId];
            }
            else
            {
                return null;
            }
        }
        public Dictionary<int, LDCiTiaoBaseGroup> GetAllCiTiaoGroup()
        {
            return m_AllCiTiaoGroups;

        }
        public void CitiaoDestroy()
        {
            // 删除特效啥的
            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> keyValuePair in m_AllCiTiaoGroups)
            {
                List<LDCiTiaoItemData> allCiTiaoItems = keyValuePair.Value.GetAllCiTiaoData();
                foreach (var item in allCiTiaoItems)
                {
                    m_SelectedCiTiao.Remove(item.BattleCiTiaoItem);
                }
                keyValuePair.Value.Destroy();
            }

            m_SelectedCiTiao.Foreach((LDCiTiaoItemBase citiaoBase) =>
            {
                citiaoBase.Destroy();
            });
            m_AllCiTiaoGroups.Clear();
        }
    }
}
