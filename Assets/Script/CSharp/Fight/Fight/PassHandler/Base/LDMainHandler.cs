using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public abstract class LDMainHandler : LDPassHandler
    {
        private int m_AutoSaveTimerId = -1;
        public override void PreInit() { }
        public override void AfterInit() 
        {

            //Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.m_btn_pause.gameObject.SetActive(true);
            //Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.m_img_warFog.gameObject.SetActive(false);
            //Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.m_DIYBtn.gameObject.SetActive(false);
            Global.gApp.CurFightScene.TimerMgrRender.AddTimer(1, 1, TryOpenAdUI);

            if (LDFightModeOpenCfg.CanContiuneFight())
            {
                m_AutoSaveTimerId = Global.gApp.CurFightScene.TimerMgrRender.AddTimer(120, 1, TrySaveData);
                LDRecordData.TryRestoreData();
            }
        }

        public override void TrySaveContinueFightData()
        {
            if (Global.gApp.CurFightScene.Started)
            {
                if (LDFightModeOpenCfg.CanContiuneFight())
                {
                    Global.gApp.CurFightScene.TimerMgrRender.RemoveTimer(m_AutoSaveTimerId);
                    m_AutoSaveTimerId = Global.gApp.CurFightScene.TimerMgrRender.AddTimer(2, 1, TrySaveData);
                }
            }
        }
        protected override void TrySaveData(float a, bool b)
        {
            LDRecordData.TryRecordData();
            Global.gApp.CurFightScene.TimerMgrRender.AddTimer(60, 1, TrySaveData);
        }
        private void TryOpenAdUI(float a, bool b)
        {
            if (Global.gApp.gSystemMgr.gMonthlyCardMgr.GetMonthlyNormal())
            {
                return;
            }
            int passId = GlobalCfg.Data.Get(LDGlobalConfigId.MagnetTrialHide).valueIntarray[0];
            if (Global.gApp.CurFightScene.gPassData.PassItem.id == passId)
            {
                if (Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.magnet.gameObject.activeSelf)
                {
                    Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.UImagnet);
                }
            }
        }
        public override void OnDUpdate(float dt) 
        {
        }
        public override void DestroyHandler() { }
        public override void FindAILockObj(LDAIMonster aiMonster)
        {
            FindAILockObjMain(aiMonster);
        }

        public override List<string> GetGameEndGainItems()
        {
            return Global.gApp.CurFightScene.GetLocalRole().GetGainItems();
        }
        public override void TryChangePursueState(LDAIStateMachine AIStateMachine)
        {
            AIStateMachine.ChangeState(LDAIStateType.Pursue);
        }
        public override void TryGameWin(LDGameEndReason reason)
        {
            Global.gApp.CurFightScene.gPassInfo.GameWin(reason);
        }
        public override void TryGameLose(LDGameEndReason reason)
        {
            Global.gApp.CurFightScene.gPassInfo.GameLose(reason);
        }
        public override bool TryHeroDeadth(LDHeroPlayer heroPlayer)
        {
            return true;
        }
        public override bool ShowSkillEffect()
        {
            return true;
        }
        public override void OpenFightWinUI()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.MainFightWinUI);
        }
        public override void OpenFightLoseUI()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.MainFightLoseUI);
        }
    }
}
