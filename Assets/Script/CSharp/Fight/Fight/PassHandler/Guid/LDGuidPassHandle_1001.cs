using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDGuidPassHandle_1001: LDMainHandler
    {
        private LDGuidStepTools m_GuidStepTools = new LDGuidStepTools();
        private int m_PartCiTiaoGroupId = 2;
        private Dictionary<int, List<int>> m_MechaCitiaos = new Dictionary<int, List<int>>()
        {
            {11,new List<int>(){14,15}},
            {12,new List<int>(){14,15}},
            {13,new List<int>(){14,15}},
        };
        private List<int> m_InsertWave = new List<int>()
        {
            10102,10103,10104
        };

        private GameObject m_ForceGuidTipsNode;
        private LDFightUI m_FightUI_Nodes;
        private int m_PartAId = 1;
        private LDProp m_PartA;
        private bool m_DIYStartNextStep = false;
        public override void AfterInit()
        {
            base.AfterInit();
            LDSDKEvent.SendBattleGuidEvent("30101010");
            RegListener(true);
            LDMainRole mainRole = Global.gApp.CurFightScene.GetLocalRole();
            mainRole.RoleData.MaxCiTiaoFreshTimes = 0;

            m_FightUI_Nodes = Global.gApp.CurFightScene.gFightUI;
            m_FightUI_Nodes.FightUI_Nodes.btn_pause.gameObject.SetActive(false);
            m_FightUI_Nodes.SetDIYCall(OnDIYCall);
            Global.gApp.CurFightScene.AddUnScaleUpAct(m_GuidStepTools.OnDUpdate);
            GenerateForceActUpAct();
        }
        private void OnDIYCall()
        {
            if (m_DIYStartNextStep)
            {
                Global.gApp.gUiMgr.OpenUIAsync<MechaDiyUI>(LDUICfg.MechaDiyUI).SetLoadedCall(mechaDiyUI =>
                {
                    if (mechaDiyUI != null)
                    {
                        Global.gApp.gUICameraCmpt.cullingMask = LDFightConstVal.UICameraMask;
                        Global.gApp.gRoleNode.SetActive(false);
                        Global.gApp.CurFightScene.Pause();
                        mechaDiyUI.btn_mechaSkin.gameObject.SetActive(false);
                        mechaDiyUI.btn_diy.gameObject.SetActive(false);
                        mechaDiyUI.btn_skin_part.gameObject.SetActive(false);
                        mechaDiyUI.ForceGuide();
                        foreach (int waveId in m_InsertWave)
                        {
                            Global.gApp.CurFightScene.gWaveMgr.InsertWave(waveId);
                        }
                        m_InsertWave.Clear();
                        DelForceTipsNode();
                    }
                } );
            }
            else
            {
                Global.gApp.gToastMgr.ShowGameTips(97601);
            }
        }
        public override void DestroyHandler()
        {
            base.DestroyHandler();
            RegListener(false);
            Global.gApp.CurFightScene.RemoveUnScaleUpAct(m_GuidStepTools.OnDUpdate);
        }
        public override void EndEnterAnim()
        {
            m_GuidStepTools.StartNextAct();
        }
        public override bool TryHeroDeadth(LDHeroPlayer heroPlayer)
        {
            return false;
        }

        private void GenerateForceActUpAct()
        {
            LDGuidStepItem guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep20_21;
            guidStepItem.DelyTime = 7.5f;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep22;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep23;
            guidStepItem.LoopCall = true;
            guidStepItem.AutoNext = false;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep24;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep24_1;
            guidStepItem.LoopCall = true;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep24_2;
            m_GuidStepTools.AddAct(guidStepItem);     
            
            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep25_1;
            guidStepItem.DelyTime = 10;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuidStep25_2;
            m_GuidStepTools.AddAct(guidStepItem);

        }
        //19	播放机甲入场动画
        //20	刷新敌人
        //21    刷新空投
        private bool GuidStep20_21()
        {
            Debug.Log("GuidStep20_21 ");
            Vector3 propPos = GetDstPropPos();

            if (m_PartAId > 0)
            {
                m_PartA = Global.gApp.CurFightScene.gPropMgr.AddProp(propPos, m_PartAId);
            }

            LDSDKEvent.SendBattleGuidEvent("30101020");
            return true;
        }
        private float m_WaitATime = 0;
        //22 指引向空投移动
        private bool GuidStep22()
        {
            LDSDKEvent.SendBattleGuidEvent("30101030");
            Debug.Log("GuidStep22 ");
            Global.gApp.gUiMgr.OpenUIAsync<UIDialog>(LDUICfg.UIDialog).SetLoadedCall(uIDialog =>
            {
                uIDialog.InitData(new List<int>() { 97301, 97302 });
                uIDialog.ChangeIcon();
            });
            return false;
        }
        private bool GuidStep23()
        {
            if (m_PartA != null)
            {
                m_WaitATime += Time.deltaTime;
                if (m_WaitATime >= 10)
                {
                    LDHeroPlayer heroPlayer = GetLocalHero();
                    m_PartA.SetGainNode(heroPlayer, heroPlayer.transform, true);
                }
                return false;
            }
            else
            {
                LDSDKEvent.SendBattleGuidEvent("30101050");
                Debug.Log("GuidStep23 ");
                Global.gApp.CurFightScene.Pause();
                MapPropItem mapPropItem = MapProp.Data.Get(m_PartAId);
                LDCommonItem commonItem = new LDCommonItem(mapPropItem.param[1]);
                Global.gApp.gUiMgr.OpenUIAsync<GetNewWeaponUI>(LDUICfg.GetNewWeaponUI).SetLoadedCall(getNewWeaponUI =>
                {
                    getNewWeaponUI.InitData(null, commonItem);
                    getNewWeaponUI.SetCheckFalse();
                });
                return true;
            }
        }
        private float m_DiyFlyTime = 0;
        private float m_DiyMaxFlyTime = 0.5f;
        private Vector2 m_DstDiyPos;
        private Vector2 m_StartDiyPos;
        //关闭UI后，部件光芒从主角处飞到右侧，DIY入口UI出现
        private bool GuidStep24()
        {
            LDSDKEvent.SendBattleGuidEvent("30101060");
            Global.gApp.CurFightScene.Pause();
            Debug.Log("GuiStep24 ");
            m_FightUI_Nodes.FightUI_Nodes.FlyEffectNode.SetActive(true);

            Vector3 heroPos = GetLocalHero().transform.GetPoisition();
            m_StartDiyPos = UiTools.WorldToRectPos(m_FightUI_Nodes.FightUI_Nodes.FlyEffectNode.gameObject,
              heroPos);
            m_DstDiyPos = m_FightUI_Nodes.FightUI_Nodes.DIYBtn.rectTransform.anchoredPosition;
            m_DiyFlyTime = 0;
            m_FightUI_Nodes.FightUI_Nodes.FlyEffectNode.rectTransform.anchoredPosition = m_StartDiyPos;
            // 创建特效 
            return true;
        }
        private bool GuidStep24_1()
        {
            m_DiyFlyTime += Time.deltaTime;
            float radio = m_DiyFlyTime / m_DiyMaxFlyTime;
            if (radio < 1)
            {
                m_FightUI_Nodes.FightUI_Nodes.FlyEffectNode.rectTransform.anchoredPosition =
                    Vector2.Lerp(m_StartDiyPos, m_DstDiyPos, radio);
                return false;
            }
            else
            {
                m_DiyFlyTime = 0;
                //要学习装备部件增强自己！）
                Debug.Log("GuiStep24_1 ");
                return true;
            }
        }

        //指引点击DIY入口（光圈、文字、点击动画）
        private bool GuidStep24_2()
        {
            Debug.Log("GuiStep24_2 ");
            CreateForceTipsNode();

            m_FightUI_Nodes.FightUI_Nodes.FlyEffectNode.SetActive(false);
            m_FightUI_Nodes.FightUI_Nodes.DIYBtn.gameObject.SetActive(true);
            m_FightUI_Nodes.FightUI_Nodes.MaskNode.gameObject.SetActive(true);
            AddDIYBaoEffect(m_FightUI_Nodes.FightUI_Nodes.DIYBtn.rectTransform.anchoredPosition);

            UIGuidTipsNode guidTipsNode = m_ForceGuidTipsNode.GetComponent<UIGuidTipsNode>();
            guidTipsNode.BgNode.gameObject.SetActive(true);
            guidTipsNode.Text.text.SetTips(97303);
            Vector2 dipBtnPos = m_FightUI_Nodes.FightUI_Nodes.DIYBtn
                .rectTransform.anchoredPosition;
            guidTipsNode.BgNode.rectTransform.anchoredPosition = dipBtnPos + new Vector2(-155, 150);
            guidTipsNode.AdaptClipNode(dipBtnPos, dipBtnPos, 0.2f);
            m_DIYStartNextStep = true;
            return false;
        }
        //升级前文本
        private bool GuidStep25_1()
        {
            LDSDKEvent.SendBattleGuidEvent("30101100");
            Debug.Log("GuidStep25_1 ");

            Global.gApp.gUiMgr.OpenUIAsync<UIDialog>(LDUICfg.UIDialog).SetLoadedCall(uIDialog =>
            {
                uIDialog.InitData(new List<int>() { 97304, 97305 });
                uIDialog.ChangeIcon();
            });
            return true;
        }
        private bool GuidStep25_2()
        {
            Debug.Log("GuidStep25_2 ");
            LDMainRole MainRole = Global.gApp.CurFightScene.GetLocalRole();
            Dictionary<int, LDCiTiaoBaseGroup> allGroup = MainRole.GetCaptainPlayer().CiTiaoMgr.GetAllCiTiaoGroup();
            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> item in allGroup)
            {
                if (item.Key != m_PartCiTiaoGroupId)
                {
                    foreach (LDCiTiaoItemData allItem in item.Value.GetAllCiTiaoData())
                    {
                        allItem.CanBeSelect = false;
                    }
                }
            }

            LDSDKEvent.SendBattleGuidEvent("30101110");
            Global.gApp.CurFightScene.GetLocalRole().ExpUtils.OpenBuffUI(LDCiTiaoType.Normal);
            return false;
        }

        private void AddDIYBaoEffect(Vector2 pos)
        {
            GameObject baoEffect = GameObject.Instantiate(m_FightUI_Nodes.FightUI_Nodes.DIYBao.gameObject, m_FightUI_Nodes.FightUI_Nodes.DIYBao.rectTransform.parent);
            baoEffect.GetComponent<RectTransform>().anchoredPosition = pos;
            baoEffect.SetActive(true);
            GameObject.Destroy(baoEffect, 1.5f);
        }
        private void CreateForceTipsNode()
        {
            DelForceTipsNode();
            m_ForceGuidTipsNode = Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.UIGuidTipsNode, ResSceneType.NormalRes, m_FightUI_Nodes.FightUI_Nodes.RootNode.rectTransform);
            m_ForceGuidTipsNode.transform.SetAsLastSibling();
        }
        private void DelForceTipsNode()
        {
            if (m_ForceGuidTipsNode != null)
            {
                Global.gApp.gResMgr.DestroyGameObj(m_ForceGuidTipsNode);
            }
            m_ForceGuidTipsNode = null;

        }
        public Vector3 GetDstPropPos()
        {
            Vector3 startPos = GetLocalHero().transform.GetPoisition();
            float radius = 5;
            float angle = 2 * LDMath.PI / 8;
            for (int i = 0; i < 8; i++)
            {
                Vector3 newPos = startPos + radius * new Vector3(LDMath.Sin(angle * i), 0, LDMath.Cos(angle * i));
                if (!LDFightTools.RaycastPropBlockSphere(newPos, 0.5f))
                {
                    return newPos;
                }
            }
            return startPos;
        }
        private void OnCloseOtherUI(string uiName)
        {
            if (uiName == LDUICfg.MechaDiyUI)
            {
                m_FightUI_Nodes.FightUI_Nodes.MaskNode.gameObject.SetActive(false);
                Global.gApp.gUICameraCmpt.cullingMask = LDFightConstVal.UICameraFightMask;

                LDHeroPlayer heroPlayer = Global.gApp.CurFightScene.GetLocalHero();
                LDMainRole mainRole = Global.gApp.CurFightScene.GetLocalRole();

                LDRoleData RoleData = Global.gApp.gGameCtrl.CreateRoleData(Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId());
                heroPlayer.HeroInfo.DIYData = RoleData.HeroInfos[0].DIYData;
                heroPlayer.HeroInfo.CitiaoGroup_Info = RoleData.HeroInfos[0].CitiaoGroup_Info;
                heroPlayer.HeroInfo.AllPart = RoleData.HeroInfos[0].AllPart;
                heroPlayer.HeroInfo.PartInfo = RoleData.HeroInfos[0].PartInfo;
                heroPlayer.DIYData = RoleData.HeroInfos[0].DIYData;
                heroPlayer.AllPart = RoleData.HeroInfos[0].AllPart;

                mainRole.GetCaptainPlayer().CiTiaoMgr.CitiaoDestroy();
                heroPlayer.SubWpnMgr.OnDestroySelf();
                heroPlayer.MechaMgr.OnDestroySelf();

                heroPlayer.HeroAnimCtrol.DiyHandle.AdujestCreatureNormal();


                heroPlayer.MechaMgr.InitMechaWeaponNode();
                heroPlayer.SubWpnMgr.InitPartSubWeapon(heroPlayer.HeroInfo.AllPart);

                LDCitiaoGroupInfo citiaoGroupInfo = new LDCitiaoGroupInfo();
                citiaoGroupInfo.SetCiTiaoGroupId(m_PartCiTiaoGroupId);
                citiaoGroupInfo.MechaId = heroPlayer.HeroInfo.MechaId;

                heroPlayer.HeroInfo.CitiaoGroup_Info.Add(citiaoGroupInfo);


                mainRole.GetCaptainPlayer().CiTiaoMgr.AddDIYCitiaoGroups(heroPlayer.HeroInfo.CitiaoGroup_Info);


                Global.gApp.gRoleNode.SetActive(true);
                Global.gApp.CurFightScene.ClearPause();
                if (m_DIYStartNextStep)
                {
                    m_DIYStartNextStep = false;
                    m_GuidStepTools.StartNextAct();
                }
            }
            else if (uiName == LDUICfg.UIDialog)
            {
                m_GuidStepTools.StartNextAct();
            }      
            else if (uiName == LDUICfg.GetNewWeaponUI)
            {
                Global.gApp.CurFightScene.ClearPause();
                m_GuidStepTools.StartNextAct();
            }
        }
        public override void TryGameLose(LDGameEndReason reason)
        {
            LDSDKEvent.SendBattleGuidEvent("30101120");
            base.TryGameLose(reason);
        }
        public override void TryGameWin(LDGameEndReason reason)
        {
            LDSDKEvent.SendBattleGuidEvent("30101120");
            base.TryGameWin(reason);
        }
        private void OnHeroLvUp(LDHeroPlayer heroPlayer, LDCiTiaoItemData itemData)
        {
            List<int> externAddCiTiao;
            if (m_MechaCitiaos.TryGetValue(itemData.CitiaoItem.id, out externAddCiTiao))
            {
                foreach (int citiaoId in externAddCiTiao)
                {
                    heroPlayer.CiTiaoMgr.ForceAddCiTiao(citiaoId);
                }
                m_GuidStepTools.StartNextAct();
                m_MechaCitiaos.Remove(itemData.CitiaoItem.id);
            }
        }

        protected void RegListener(bool addListener)
        {
            Global.gApp.gMsgDispatcher.RegEvent<string>(MsgIds.OnCloseUI, OnCloseOtherUI, addListener);
            Global.gApp.gMsgDispatcher.RegEvent<LDHeroPlayer, LDCiTiaoItemData>(MsgIds.CiTiaoLvUp, OnHeroLvUp, addListener);
        }
        public override List<string> GetGameEndGainItems()
        {
            return new List<string>();
        }
        public override LDDIYData GetDIYData()
        {
            LDMainRole mainRole = Global.gApp.CurFightScene.GetLocalRole();
            return mainRole.GetCaptainPlayer().HeroInfo.DIYData;
        }
        public override bool CanDropProp()
        {
            return false;
        }
    }
}
