using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDPveHandle : LDMainHandler
    {
        private double m_ExternAtkInc = 0;
        public override void PreInit()
        {
            base.PreInit();
            m_ExternAtkInc = GlobalCfg.Data.Get(LDGlobalConfigId.PveTeamAtkInc).valueInt / 10000.0d;
        }
        public override bool TryHeroDeadth(LDHeroPlayer heroPlayer)
        {
            return false;
        }
        public override void FindAILockObj(LDAIMonster aiMonster)
        {
            //FindAILockObjMain(aiMonster);
            //LDSceneObj lockHeroObj = null;
            //;
            //float disSqr = 1000000;
            //Vector3 monsterPos = aiMonster.transform.GetPoisition();
            //List<LDMainRole> allRole = Global.gApp.CurFightScene.gPlayerMgr.GetMainRole();
            //foreach (LDMainRole mainRole in allRole)
            //{
            //    List<LDHeroPlayer> heros = mainRole.HeroMgr.GetAllHeroPlayer();
            //    foreach (LDHeroPlayer hero in heros)
            //    {
            //        if (hero.Live)
            //        {
            //            float newDisSqr = (monsterPos - hero.transform.GetPoisition()).sqrMagnitude;
            //            if (newDisSqr < disSqr)
            //            {
            //                disSqr = newDisSqr;
            //                lockHeroObj = hero;
            //            }
            //        }
            //    }
            //}

            aiMonster.AIMgr.LockHeroObj = Global.gApp.CurFightScene.gElementMgr.FangXianElement;
        }

        public override void SetCameraTarget(Transform target)
        {
        }
        public override double GetExternAtkInc()
        {
            return m_ExternAtkInc;
        }
        public override bool CollectEndProps()
        {
            return true;
        }

        public override bool AssistHeroNavMeshEnable()
        {
            return false;
        }

        public override void OpenFightWinUI()
        {
            List<LDNetCBattleDamage> roleDamage = new List<LDNetCBattleDamage>();
            List<LDMainRole> roles = Global.gApp.CurFightScene.gPlayerMgr.GetMainRole();
            if (roles.Count == 2)
            {
                var role1 = roles[0];
                var role2 = roles[1];
                var role1Id = role1.RoleData.RoleGuid;
                var role2Id = role2.RoleData.RoleGuid;
                var role1Damage = role1.DamageRecord.TotalDamage;
                var role2Damage = role2.DamageRecord.TotalDamage;
                roleDamage.Add(new LDNetCBattleDamage(role1Id, (long)role1Damage));
                roleDamage.Add(new LDNetCBattleDamage(role2Id, (long)role2Damage));
            }

            LDNetGVECBattleResult NetGVECBattleResult = new LDNetGVECBattleResult((int)LDGameEnd.GameWin, roleDamage, (long)Global.gApp.CurFightScene.gPassInfo.GetTime());
            Global.gApp.CurFightScene.gKCPNetMgr.SendGVEMissionEndRequest(NetGVECBattleResult);

            //string pveLogMd5 = LDPveLog.WriteLog();
            //PveFightWinUI pveFightWinUI = Global.gApp.gUiMgr.OpenUI(LDUICfg.PveFightWinUI) as PveFightWinUI;
            //pveFightWinUI.MD5.text.text = pveLogMd5;
        }

        public override void OpenFightLoseUI()
        {
            List<LDNetCBattleDamage> roleDamage = new List<LDNetCBattleDamage>();
            List<LDMainRole> roles = Global.gApp.CurFightScene.gPlayerMgr.GetMainRole();
            if (roles.Count == 2)
            {
                var role1 = roles[0];
                var role2 = roles[1];
                var role1Id = role1.RoleData.RoleGuid;
                var role2Id = role2.RoleData.RoleGuid;
                var role1Damage = role1.DamageRecord.TotalDamage;
                var role2Damage = role2.DamageRecord.TotalDamage;
                roleDamage.Add(new LDNetCBattleDamage(role1Id, (long)role1Damage));
                roleDamage.Add(new LDNetCBattleDamage(role2Id, (long)role2Damage));
            }

            LDNetGVECBattleResult NetGVECBattleResult = null;
            if (Global.gApp.CurFightScene.gPassInfo.GetGameEndReason() == LDGameEndReason.GiveUp)
            {
                NetGVECBattleResult = new LDNetGVECBattleResult((int)LDGameEnd.GameGiveUp, roleDamage, (long)Global.gApp.CurFightScene.gPassInfo.GetTime());
            }
            else
            {
                NetGVECBattleResult = new LDNetGVECBattleResult((int)LDGameEnd.GameLose, roleDamage, (long)Global.gApp.CurFightScene.gPassInfo.GetTime());
            }

            Global.gApp.CurFightScene.gKCPNetMgr.SendGVEMissionEndRequest(NetGVECBattleResult);


            //string pveLogMd5 = LDPveLog.WriteLog();
            //PveFightLoseUI pveFightLoseUI = Global.gApp.gUiMgr.OpenUI(LDUICfg.PveFightLoseUI) as PveFightLoseUI;
            //pveFightLoseUI.MD5.text.text = pveLogMd5;
        }

        public override List<string> GetGameEndGainItems()
        {
            return new List<string>();
        }
        public override bool CanDropProp()
        {
            return false;
        }
        public override bool ShowBossFen()
        {
            return false;
        }
        public override void ShowEnterAnim()
        {
        }
        public override bool ShowBossWarning()
        {
            return false;
        }
    }
}