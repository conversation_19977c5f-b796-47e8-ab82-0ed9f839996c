
using UnityEngine;

namespace LD
{
    //public class LDGuidFlyNodes : IUpdate
    //{
    //    public bool Ended = false;
    //    public int PropId = 102;
    //    public Vector3 DstWorldPos;
    //    public RectTransform FlyEffect;
    //    public float m_CurFlyTime;
    //    public float m_MaxFlyTime;
    //    public int m_NeedMaxGrouId = 1;
    //    public Vector2 StartPos;
    //    public LDProp Prop;
    //    //private LDGuideHandler m_GuideHandler;
    //    //public LDGuidFlyNodes(LDGuideHandler guideHandler)
    //    //{
    //    //    m_MaxFlyTime = UnityEngine.Random.Range(0.4f, 0.8f);
    //    //    m_GuideHandler = guideHandler;
    //    //}
    //    //public void OnDUpdate(float dt)
    //    //{
    //    //    if (!Ended)
    //    //    {
    //    //        m_CurFlyTime += dt;
    //    //        float radio = m_CurFlyTime / m_MaxFlyTime;
    //    //        Vector2 dstPos = UiTools.WorldToRectPos(FlyEffect.gameObject,
    //    //      DstWorldPos);
    //    //        FlyEffect.anchoredPosition =
    //    //                Vector2.Lerp(StartPos, dstPos, radio);
    //    //        Ended = radio > 1;
    //    //        if (Ended)
    //    //        {
    //    //            Prop = Global.gApp.CurFightScene.gPropMgr.AddProp(DstWorldPos, PropId);
    //    //            m_GuideHandler.AddBaoEffect(DstWorldPos);
    //    //        }
    //    //    }
    //    //}
    //}
}