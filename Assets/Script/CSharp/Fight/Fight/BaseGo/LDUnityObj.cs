using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public enum LDGameObj
    {
        None = 0,
        Hero = 3,                   // 主角
        Enemy = 4,                  // 怪物
        BigEnemy = 5,               // 大怪物
        StaticMap = 6,              // 场景地图
        ElementNormal = 7,          // 普通 场景元素
        ElementFence = 8,          // 栅栏
        GasBucketFence = 9,          // 汽油桶
        Crystal = 10,          // 水晶
        RoleBullet = 12,            // 角色子弹
        EnemyBullet = 13,           // 怪物子弹
        AllBullet = 14,             // 所有的子弹
        HeroBullet = 15,            //角色子弹
        TrapBullet = 16,            //场景元素子弹
        EnemyHBullet = 17,           //带血的子弹

        ElementBox = 18,          // 宝箱
        SignalTower = 19,          // 信号塔
        ElementDoor = 20,          // 门
        SwitchElement = 21,          //开关
        GoldCar = 22,          //金币运输车 包括 组队 防线
        EnemyBullet2 = 23,           //碰到空气墙 立刻消失
        EnemyBulletBlock = 24,           //吃子弹的
        BlackHoleBullet = 25,           //吃子弹的
        HeroBullet2 = 26,            // 跟地面碰撞
        Survivor = 27,              // 幸存者
        TrapBullet2 = 28,              // 场景元素子弹  打人

        AllPutBullet = 130,         //放置 子弹 这种子弹 不跟 之类的地面碰撞
        EnemyPutBullet = 131,       //怪物 放置 子弹 这种子弹 不跟 之类的地面碰撞
        RolePutBullet = 132,        //角色 放置 子弹 这种子弹 不跟 之类的地面碰撞
        TimeBullet = 133,           //不与任何物体碰撞 时间到了消失
        TrapPutBullet = 134,        // 之类的地面碰撞
        HeroShildBullet = 135,      // 主角子弹
        EnemyPutBullet2 = 136,       //怪物 放置 子弹 这种子弹 不跟 主角
        
        ElementHeroTrigger = 201, // 触发器 -- 角色
        ElementAITrigger = 202,  // 触发器 -- 怪物
        ElementRoleTrigger = 203, // 触发器 -- 角色 + 怪物
        

    }
    public class LDUnityObj : MonoBehaviour
    {
        // 穿透值 跟格挡值 需要的 对象需要自行 去处理
        [SerializeField] private int MMaxBlockVal = 10; // 之所以要 序列化是因为担心有的 没有具体逻辑 但是 可能需要 格挡值
        [SerializeField] private LDGameObj m_LHObject = LDGameObj.None;
        public void SetMaxBlockVal(int blockVal)// 基础格挡值
        {
            MMaxBlockVal = blockVal;
        }
        public LDGameObj DYObject { get { return m_LHObject; } protected set { m_LHObject = value; } }
        public string DYObjectStr { get; protected set; }
        public int MaxPenetrationVal { get; protected set; }
        public int MaxBlockVal { get { return MMaxBlockVal; } protected set { MMaxBlockVal = value; } }

        public static Dictionary<LDGameObj, HashSet<LDGameObj>> TriggerMatrix = new Dictionary<LDGameObj, HashSet<LDGameObj>>()
        {
            {LDGameObj.RoleBullet,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy,LDGameObj.GasBucketFence,LDGameObj.EnemyHBullet,LDGameObj.ElementBox,LDGameObj.ElementDoor }},
            {LDGameObj.RolePutBullet,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy,LDGameObj.GasBucketFence}},
            {LDGameObj.TrapBullet,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy}},
            {LDGameObj.TrapBullet2,new HashSet<LDGameObj>(){ LDGameObj.Hero}},
            {LDGameObj.TrapPutBullet,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy}},
            {LDGameObj.EnemyBullet,new HashSet<LDGameObj>(){ LDGameObj.Hero, LDGameObj.HeroShildBullet, LDGameObj.ElementFence,LDGameObj.Crystal,LDGameObj.GoldCar}},
            {LDGameObj.EnemyBullet2,new HashSet<LDGameObj>(){ LDGameObj.Hero, LDGameObj.HeroShildBullet, LDGameObj.ElementFence,LDGameObj.Crystal,LDGameObj.GoldCar,
            LDGameObj.EnemyBulletBlock}},
            {LDGameObj.EnemyHBullet,new HashSet<LDGameObj>(){ LDGameObj.Hero,LDGameObj.ElementFence,LDGameObj.Crystal,LDGameObj.GoldCar}},
            {LDGameObj.HeroBullet,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy, LDGameObj.ElementBox,LDGameObj.ElementDoor,LDGameObj.Hero }},
            {LDGameObj.HeroBullet2,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy, LDGameObj.ElementBox,LDGameObj.ElementDoor ,LDGameObj.Hero}},
            {LDGameObj.BlackHoleBullet,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy, LDGameObj.ElementBox,LDGameObj.ElementDoor,LDGameObj.Hero}},
            {LDGameObj.HeroShildBullet,new HashSet<LDGameObj>(){ LDGameObj.Enemy,LDGameObj.BigEnemy}},
            {LDGameObj.AllBullet,new HashSet<LDGameObj>(){ LDGameObj.Hero,LDGameObj.Enemy,LDGameObj.BigEnemy,LDGameObj.GoldCar}},
            {LDGameObj.EnemyPutBullet,new HashSet<LDGameObj>(){ LDGameObj.Hero,LDGameObj.ElementFence,LDGameObj.Crystal,LDGameObj.GoldCar}},
            {LDGameObj.EnemyPutBullet2,new HashSet<LDGameObj>(){LDGameObj.ElementFence,LDGameObj.Crystal,LDGameObj.GoldCar}},
            {LDGameObj.TimeBullet,new HashSet<LDGameObj>(){}},
            {LDGameObj.ElementHeroTrigger,new HashSet<LDGameObj>(){LDGameObj.Hero}},
            {LDGameObj.ElementAITrigger,new HashSet<LDGameObj>(){LDGameObj.Enemy,LDGameObj.BigEnemy}},
            {LDGameObj.ElementRoleTrigger,new HashSet<LDGameObj>(){LDGameObj.Hero,LDGameObj.Enemy,LDGameObj.BigEnemy}},
        };
        public static bool IsEnemyHitObj(LDGameObj obj)
        {
            return LDUnityObj.TriggerMatrix[LDGameObj.EnemyBullet].Contains(obj) |
                LDUnityObj.TriggerMatrix[LDGameObj.EnemyHBullet].Contains(obj) |
                LDUnityObj.TriggerMatrix[LDGameObj.EnemyPutBullet].Contains(obj) |
                LDUnityObj.TriggerMatrix[LDGameObj.EnemyPutBullet2].Contains(obj) |
                LDUnityObj.TriggerMatrix[LDGameObj.EnemyBullet2].Contains(obj);

        }

        public static HashSet<LDGameObj> MainRoleBulletObj = new HashSet<LDGameObj>()
        {
            LDGameObj.RoleBullet,LDGameObj.RolePutBullet
        };

        public static HashSet<LDGameObj> EnemyBulletObj = new HashSet<LDGameObj>()
        {
            LDGameObj.EnemyBullet,LDGameObj.EnemyPutBullet,LDGameObj.EnemyHBullet,LDGameObj.EnemyPutBullet2,LDGameObj.EnemyBullet2
        };
        public static HashSet<LDGameObj> HeroBulletObj = new HashSet<LDGameObj>()
        {
            LDGameObj.HeroBullet,LDGameObj.HeroShildBullet,LDGameObj.BlackHoleBullet,LDGameObj.HeroBullet2
        };
        public static HashSet<LDGameObj> HeroShildBulletObj = new HashSet<LDGameObj>()
        {
            LDGameObj.HeroShildBullet
        };

        public static HashSet<LDGameObj> ElementBulletObj = new HashSet<LDGameObj>()
        {
            LDGameObj.TrapBullet,LDGameObj.TrapPutBullet
        };


        public static HashSet<LDGameObj> PutBulletObj = new HashSet<LDGameObj>()
        {
            LDGameObj.RolePutBullet,LDGameObj.EnemyPutBullet,LDGameObj.TrapPutBullet,LDGameObj.EnemyPutBullet2
        };

        public static HashSet<LDGameObj> AllBulletObj = new HashSet<LDGameObj>()
        {
            LDGameObj.AllPutBullet,LDGameObj.AllBullet
        };


        public virtual bool OnHittedN(LDAtkBullet bullet) { return false; }

        public Vector3 GetPos()
        {
            return transform.GetPoisition();
        }
    }
}
