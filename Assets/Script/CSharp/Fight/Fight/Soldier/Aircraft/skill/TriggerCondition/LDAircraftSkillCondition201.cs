using System.Collections.Generic;

namespace LD
{
    public class LDAircraftSkillCondition201 : LDAircraftSkillConditionBase
    {
        private int m_TargetType;
        private List<int> m_TrapId = new List<int>();
        

        private List<LDBaseElement> m_Elements = new List<LDBaseElement>();

        protected override void InitImp()
        {
            m_TargetType = EZMath.IDDoubleToInt(GetParam(1));
            for (int i = 2; i < GetParams().Length; i++)
            {
                m_TrapId.Add(EZMath.IDDoubleToInt(GetParam(i)));
            }
        }

        protected override void UpdateImp(float dt)
        {
        }

        public override void RegListener(bool reg)
        {
            Global.gApp.gMsgDispatcher.RegEvent<LDBaseElement>(MsgIds.CraftSummonCreate, OnCraftSummonCreate, reg);
            Global.gApp.gMsgDispatcher.RegEvent<LDBaseElement>(MsgIds.CraftSummonDeath, OnCraftSummonDeath, reg);
        }

        private void OnCraftSummonCreate(LDBaseElement element)
        {
            if (m_TargetType == 1 && element.TeamId != m_Aircraft.TeamId) return;
            if (m_TargetType == 2 && element.TeamId == m_Aircraft.TeamId) return;

            TrapItem trapItem = element.TryGetTrapItem();
            if (trapItem == null) return;
            if (!m_TrapId.Contains(trapItem.id)) return;

            if (TriggerSuccess())
            {
                m_Elements.Add(element);
            }
        }

        private void OnCraftSummonDeath(LDBaseElement element)
        {
            if (m_Elements.Contains(element))
            {
                m_Skill.RemoveSkillLogicEffect();
                m_Elements.Remove(element);
            }
        }

        protected override void TryTriggerImp()
        {
        }
    }
}