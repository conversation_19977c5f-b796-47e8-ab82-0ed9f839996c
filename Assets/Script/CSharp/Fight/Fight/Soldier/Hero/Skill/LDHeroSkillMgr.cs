using System.Collections;
using System.Collections.Generic;

namespace LD.Skill
{
    public class LDHeroSkillMgr : IDYDestroy
    {
        private LDHeroBehaviorMgr m_HeroStateMgr;
        private LDHeroPlayer m_HeroPlayer;
        private List<float> m_CountDown = new List<float>();
        private List<LDHeroSkillBase> m_Skills;
        public LDHeroSkillMgr(LDHeroBehaviorMgr aiMgr, LDHeroPlayer heroPlayer)
        {
            m_HeroStateMgr = aiMgr;
            m_HeroPlayer = heroPlayer;
            m_Skills = new List<LDHeroSkillBase>();
            //m_Skills = new List<LDAISkillBase>(heroPlayer.HeroItem.skill.Length);
            //foreach (int skillId in heroPlayer.MonsterItem.skill)
            //{
            //    MonsterSkillItem aISkillItem = MonsterSkill.Data.Get(skillId);
            //    m_CountDown.Add(aISkillItem.skillCd);
            //    m_Skills.Add(CreatSkill(aISkillItem));
            //}
        }
        public void Init()
        {
            ReSetCd();
        }
        private void ReSetCd()
        {
            //for (int i = 0; i < m_Skills.Count; i++)
            //{
            //    float cdTime = m_Skills[i].AISkillItem.skillCd;
            //    m_CountDown[i] = UnityEngine.Random.Range(cdTime * 0.8f, cdTime * 1.5f);
            //}
        }
        private void CdUpdate(float dt)
        {
            for (int i = 0; i < m_CountDown.Count; i++)
            {
                m_CountDown[i] -= dt;
            }
        }
        private LDAISkillBase CreatSkill(LuaConfigItem skillItem)
        {
            LDAISkillBase aiSkill = null;
            //if (skillItem.skillType == LDAISkillTypes.deadthSkill)
            //{
            //    aiSkill = new LDAIWaitDeadthSkill();

            //}
            //else if (skillItem.skillType == LDAISkillTypes.normalAtk)
            //{
            //    aiSkill = new LDAINormalAtkSkill();
            //}
            //aiSkill.SetBaseInfo(m_HeroStateMgr, this, m_HeroPlayer, skillItem);
            return aiSkill;
        }

        public void OnDUpdate(float dt)
        {
            CdUpdate(dt);
            //if (!m_HeroStateMgr.AIStateMachine.InSkillState())
            //{
            //    for (int i = 0; i < m_Skills.Count; i++)
            //    {
            //        if (m_CountDown[i] <= 0)
            //        {
            //            LDAISkillBase aiSkillBase = m_Skills[i];
            //            if (aiSkillBase.MatchCondition())
            //            {
            //                float cdTime = aiSkillBase.AISkillItem.skillCd;
            //                m_CountDown[i] = UnityEngine.Random.Range(cdTime * 0.8f, cdTime * 1.5f);
            //                m_HeroStateMgr.AIStateMachine.UseSkill(aiSkillBase);
            //                return;
            //            }
            //        }
            //    }
            //}
        }

        public void OnDestroySelf()
        {
        }
    }
}


