using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
	public class LDNormalHero : LDHeroPlayer
	{
        protected override void TryAddHpNode()
        {
            Transform hpNode = transform.Find(LDFightConstVal.HpNode);
            if (hpNode != null)
            {
                Global.gApp.gMsgDispatcher.Broadcast(MsgIds.AddNormalHP, GetGuid(), true, LDUICfg.HeroHP, hpNode);
            }
        }
    }
}
