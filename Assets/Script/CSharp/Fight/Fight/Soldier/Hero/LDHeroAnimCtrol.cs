using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDHeroAnimCtrol:IUpdate
    {
        public static string UpSpeedKey = "UpBody";
        public static string DownSpeedKey = "DownBody";
        public LDDiyHandle DiyHandle { private set; get; }
        private Transform m_AnimNode = null;
        private Animator m_Anim = null;

        protected string m_UAnim = string.Empty;
        protected string m_DAnim = string.Empty;

        private float m_TimeScale = 1;
        private LDHeroPlayer m_Hero;
        private float m_UpSpeed = 1;
        private float m_DownSpeed = 1;
        public LDHeroAnimCtrol(LDHeroPlayer heroPlayer, Transform animNode)
        {
            m_Hero = heroPlayer;
            m_AnimNode = animNode;
            m_Anim = m_AnimNode.GetComponentInChildren<Animator>();
            DiyHandle = new LDDiyHandle();
            DiyHandle.CreateBody(m_Hero, animNode);
        }
     
        public void AdujestCreature()
        {
            DiyHandle?.AdujestCreatureNormal();
        }       
        public void OnDUpdate(float dt)
        {
            DiyHandle?.OnDUpdate(dt);
        }
        public void PlayUAnim(string anim, bool forece = false, float normalized = 0)
        {
            string newAnim = DiyHandle.DiyAnim[anim];
            if (string.IsNullOrEmpty(newAnim))
            {
                return;
            }
            // 上半身 idle 的时候 下半身如果在移动 那么上半身动画不播放idle  与下半身保持一致 等同于播放整体动作
            if (m_UAnim == LDAnimName.Idle && LDAnimName.PosAnim.Contains(m_DAnim))
            {
                anim = m_DAnim;
            }
            if (!string.IsNullOrEmpty(anim) && (!m_UAnim.Equals(anim) || forece))
            {
                m_UAnim = anim;
                PlayAnim(anim, 0, normalized);
            }
        }

        public void PlayDAnim(string anim, bool forece = false, float normalized = 0)
        {
            string newAnim = DiyHandle.DiyAnim[anim];
            if (string.IsNullOrEmpty(newAnim))
            {
                return;
            }
            int weight = CalcDAnimWeight(anim);
            SetLayerWeight(1,weight);
            if (weight > 0)
            {
                if (!m_DAnim.Equals(anim) || forece)
                {
                    SetDAnim(anim);
                    PlayAnim(anim, 1, normalized);
                }
                if (LDAnimName.PosAnim.Contains(m_DAnim) && m_UAnim == LDAnimName.Idle)
                {
                    PlayUAnim(m_DAnim);
                }
            }
            else
            {
                SetDAnim(string.Empty);
                if (LDAnimName.MoveAnim.Contains(m_UAnim))
                {
                    PlayUAnim(LDAnimName.Idle);
                }
                if (m_UAnim == LDAnimName.Idle)
                {
                    PlayAnim(anim, 1, normalized);
                }
                else
                {
                    PlayAnim(m_UAnim, 1, normalized);
                }
            }
        }
        public void PlayIdleAnim()
        {
            PlayUAnim(LDAnimName.Idle);
            m_Hero.Move(0, 0, 0.1f);
        }
        public void PlayRunAnim()
        {
            m_Hero.Move(0, 0, 0.1f);
            PlayUAnim(LDAnimName.Run);
            PlayDAnim(LDAnimName.Run);
        }
        public void PlayWinAnim()
        {
            m_Hero.Move(0, 0, 0.1f);
            PlayUAnim(LDAnimName.Dance);
            PlayDAnim(LDAnimName.Dance);
            if (!Global.gApp.CurFightScene.PVEMode)
            {
                m_Hero.AtkFight.SetForwardByAngle(new Vector3(0, 210, 0));
            }
        }
        public void PlayShowAnim()
        {
            PlayUAnim(LDAnimName.MechaShow);
            PlayDAnim(LDAnimName.MechaShow);

            //LDFightTools.SampleAnimator(DiyHandle.GetCreature().GetAnimator(),0.033f);
            SetAnimTimeScale(1);
        }
        private void SetDAnim(string dAnim)
        {
            m_DAnim = dAnim;
        }
        private void SetLayerWeight(int layerIndex, float weight)
        {
            if (m_Anim != null)
            {
                m_Anim.SetLayerWeight(layerIndex, weight);
            }
            else if (DiyHandle != null)
            {
                DiyHandle.SetLayerWeight(layerIndex, weight);
            }
        }
        private void PlayAnim(string anim, int layer, float normalized)
        {
            if(string.IsNullOrEmpty(anim))
            {
                return;
            }
            if (m_Anim != null)
            {
                m_Anim.Play(anim, layer, normalized);
            }
            else if (DiyHandle != null)
            {
                DiyHandle.PlayAnim(anim, layer, normalized);
            }
        }
        private int CalcDAnimWeight(string anim)
        {
            if (LDAnimName.MoveAnim.Contains(anim))
            {
                return 1;
            }
            else
            {
                return 0;
            }
        }

        public void StartSampleAnim()
        {
            Animator[] animators = DiyHandle.GetMechaModeNode().GetComponentsInChildren<Animator>(true);
            foreach (Animator animator in animators)
            {
                animator.Play(LDAnimName.DiyNormalAnim[LDAnimName.Idle], -1, 0);
                //animator.Update(0);
                animator.speed = 0;
                LDFightTools.SampleAnimator(animator);
            }
            DiyHandle.GetCreature().PauseAnimator();
        }
        public void EndSampleAnim()
        {
            Animator[] animators = DiyHandle.GetMechaModeNode().GetComponentsInChildren<Animator>(true);
            foreach (Animator animator in animators)
            {
                animator.enabled = true;
                animator.speed = 1;
            }
            //    Animator [] animators = DiyHandle.GetMechaModeNode().GetComponentsInChildren<Animator>(true);
            //foreach (Animator animator in animators)
            //{
            //    animator.Play(LDAnimName.DiyNormalAnim[LDAnimName.Idle], 0, 0);
            //    animator.Update(0);
            //    animator.speed = 1;
            //}
        }
        public void SetAnimUpSpeed(float animSpeed)
        {
            m_UpSpeed = animSpeed;
            if (m_Anim != null)
            {
                //m_Anim.speed = animSpeed * m_TimeScale;
                m_Anim.SetFloat(m_UAnim, animSpeed);
            }
            else if (DiyHandle != null)
            {
                DiyHandle.SetAnimSpeed(UpSpeedKey, animSpeed);
            }
        }
        public void SetAnimDownSpeed(float animSpeed)
        {
            m_DownSpeed = animSpeed;
            if (m_Anim != null)
            {
                //m_Anim.speed = animSpeed * m_TimeScale;
                m_Anim.SetFloat(m_DAnim, animSpeed);
            }
            else if (DiyHandle != null)
            {
                DiyHandle.SetAnimSpeed(DownSpeedKey, animSpeed);
            }
        }
        public void SetAnimSpeed(float animSpeed)
        {
            if (m_Anim != null)
            {
                m_Anim.speed = animSpeed * m_TimeScale;
            }
            else if (DiyHandle != null)
            {
                DiyHandle.SetSpeedScale(animSpeed);
            }
        }
        public void SetAnimTimeScale(float timeScale)
        {
            if (m_Anim != null)
            {
                m_Anim.speed = m_TimeScale;
            }
            else if (DiyHandle != null)
            {
                DiyHandle.SetTimeScale(timeScale);
            }
        }
        public void SetRenderTimeScale(float timeScale)
        {
            m_TimeScale = timeScale;
            SetAnimTimeScale(m_TimeScale);
        }
        public void OnDestroySelf()
        {
            DiyHandle.OnDestroySelf();
        }
    }
}

