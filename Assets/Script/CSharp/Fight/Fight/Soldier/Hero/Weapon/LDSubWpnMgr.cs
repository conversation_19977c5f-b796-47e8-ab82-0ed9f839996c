using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public enum SubWeaponType
    {
        None = 0,
        Normal = 1,
        Lesser = 2,
    }

	public class LDSubWpnMgr : IUpdate
    {
        public LDHeroPlayer HeroPlayer { get; private set; }
        public List<LDHeroSubWeapon> AllWeapon { get; private set; } = new List<LDHeroSubWeapon>(); 
        public List<LDHeroSubWeapon> AllWeaponForCiTiao { get; private set; } = new List<LDHeroSubWeapon>();
        public Transform UAVNode { get; private set; }
        public LDSubWpnMgr(LDHeroPlayer soldierPlayer)
        {
            HeroPlayer = soldierPlayer;
            CreateUAVNode();
            InitPartSubWeapon(soldierPlayer.AllPart);
        }
        public void InitPartSubWeapon(List<int> AllPart)
        {
            Dictionary<int, int> diyDatas = new Dictionary<int, int>();
            foreach (int diyPartId in AllPart)
            {
                if (diyDatas.ContainsKey(diyPartId))
                {
                    diyDatas[diyPartId]++;
                }
                else
                {
                    diyDatas[diyPartId] = 1;
                }
            }
            foreach (KeyValuePair<int, int> diyItem in diyDatas)
            {
                List<LDBodyBlock> bodyBlocks = HeroPlayer.HeroAnimCtrol.DiyHandle.GetBlockEnableByBlockItemId(diyItem.Key);
                int diyCount = Mathf.Min(bodyBlocks.Count, diyItem.Value);
                DIYPartCfgItem luaConfigItem = DIYPartCfg.Data.Get(diyItem.Key);
                int pos = luaConfigItem.pos;
                if (pos == LDDIYPartItemType.PartWeapon)
                {
                    for (int i = 0; i < diyCount; i++)
                    {
                        InitWeapon(diyItem.Key, bodyBlocks[i],luaConfigItem.fireCount);
                    }
                    if (bodyBlocks.Count != diyItem.Value)
                    {
                        Debug.LogError("SubWpn BodyBlock Data Error " + diyItem.Key + " data  " + bodyBlocks.Count + "  : " + diyItem.Value);
                    }
                }
            }
        }
        public void InitWeapon(int diyPartId, LDBodyBlock bodyBlock,int fireCount)
        {
            for (int i = 0; i < fireCount; i++)
            {
                LDWeaponData weaponData = new LDWeaponData();
                weaponData.InitByDIYID(this, diyPartId, bodyBlock,i,fireCount);
                InitWeapon(weaponData);
            }
        }
        public void InitWeapon(LDWeaponData weaponData)
        {
            GameObject wpnNode = new GameObject("MechaSubWpn");
            wpnNode.transform.SetParent(HeroPlayer.transform,false);
            LDHeroSubWeapon curWeapon = GetWeapon(wpnNode, weaponData.PartId);
            curWeapon.Init(this, weaponData, HeroPlayer);
            AllWeapon.Add(curWeapon);
            AllWeaponForCiTiao.Add(curWeapon);
        }
        private LDHeroSubWeapon GetWeapon(GameObject wpnNode,int partId)
        {
            DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(partId);
            SubWeaponType subWeaponType = (SubWeaponType)(partCfgItem.wpnType);
            if (subWeaponType == SubWeaponType.Normal)
            {
                return wpnNode.AddComponent<LDNormalSubWeapon>();
            }
            else if(subWeaponType == SubWeaponType.Lesser)
            {
                return wpnNode.AddComponent<LDLesserSubWeapon>();
            }
            else
            {
                return wpnNode.AddComponent<LDNormalSubWeapon>();
            }
        }
        public void OnDUpdate(float dt)
        {
            foreach (LDHeroSubWeapon weapon in AllWeapon)
            {
                weapon.OnDUpdate(dt);
            }
            UAVNode.Rotate(Vector3.up, dt * 60, Space.Self);
        }

        public double GetAtk()
        {
            //if (curWeapon != null)
            //{
            //    return curWeapon.GetReCalcAtkVal();
            //}
            //else
            {
                return HeroPlayer.HeroData.GetHeroAtk();
            }
        }

        public void SetWpnEnalble(bool enable)
        {

        }
        public LDHeroSubWeapon GetSubWeapon(int subWeaponId)
        {
            LDHeroSubWeapon subWeapon = null; ;
            foreach (LDHeroSubWeapon weapon in AllWeaponForCiTiao)
            {
                if(weapon.SubWeaponId == subWeaponId)
                {
                    subWeapon = weapon;
                    break;
                }
            }
            AllWeaponForCiTiao.Remove(subWeapon);
            return subWeapon;
        }
        public void ReturnSubWeapon(LDHeroSubWeapon heroSubWeapon)
        {
            if (heroSubWeapon != null)
            {
                AllWeaponForCiTiao.Insert(0, heroSubWeapon);
            }
        }
        public void AddBuff(int buffId, int weaponId, ref List<LDBaseBuff> tempBuff)
        {

            foreach (LDHeroSubWeapon weapon in AllWeapon)
            {
                if (weapon.PartId == weaponId || weaponId == -1)
                {
                    if (weapon.BuffMgr != null)
                    {
                        LDBaseBuff baseBuff = weapon.BuffMgr.AddBuff(buffId, weapon);
                        if (baseBuff != null)
                        {
                            tempBuff.Add(baseBuff);
                        }
                    }
                }
            }
        }
        // 根据系别添加buff
        public void AddBuffByElement(int buffId, int element, ref List<LDBaseBuff> tempBuff)
        {
            foreach (LDHeroSubWeapon weapon in AllWeapon)
            {
                DIYPartCfgItem cfgItem = DIYPartCfg.Data.Get(weapon.PartId);
                if (cfgItem.element == element ||  element == -1)
                {
                    if (weapon.BuffMgr != null)
                    {
                        LDBaseBuff baseBuff = weapon.BuffMgr.AddBuff(buffId, weapon);
                        if (baseBuff != null)
                        {
                            tempBuff.Add(baseBuff);
                        }
                    }
                }
            }
        }
        public void DelUAVNode(GameObject UAV)
        {
            Global.gApp.gResMgr.DestroyGameObj(UAV);
            CalcUAVLayout();
        }
        public void AddUAVNode(Transform UAV)
        {
            if (UAV != null)
            {
                UAV.SetParent(UAVNode, false);
            }
            CalcUAVLayout();
        }        
        private void CalcUAVLayout()
        {
            int UAVCount = UAVNode.childCount;
            if (UAVCount > 0)
            {
                float StartAngle = LDMath.PI;
                float dtAngle = LDFightTools.GetIntF(LDMath.PI * 2 / UAVCount);
                float radius = 2.5f;
                for (int i = 0; i < UAVCount; i++)
                {
                    float angle = StartAngle + dtAngle * i;
                    UAVNode.GetChild(i).SetLocalPoisition(radius * new Vector3(LDMath.Cos(angle), 0, LDMath.Sin(angle)));
                }
            }
        }
        private void CreateUAVNode()
        {
            UAVNode = new GameObject("UAVNode").transform;
            UAVNode.SetParent(HeroPlayer.BodyNode, false);
            UAVNode.transform.SetLocalPoisition(new Vector3(0, 3, 0));
        }

        public void ReclcWpnByGuid(List<int> DIYList)
        {
            InitPartSubWeapon(DIYList);
        }
        public void DestroyWpnByGuid()
        {
            DestroyWpn();
        }
        private void DestroyWpn()
        {
            foreach (LDHeroSubWeapon weapon in AllWeapon)
            {
                weapon.DestroySubWpn();
            }
            AllWeapon.Clear();
            AllWeaponForCiTiao.Clear();
        }
        public void OnDestroySelf()
        {
            DestroyWpn();
        }
    }
}