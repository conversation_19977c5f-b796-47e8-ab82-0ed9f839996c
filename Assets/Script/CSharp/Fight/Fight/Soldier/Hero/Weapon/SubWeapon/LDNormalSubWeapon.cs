using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDNormalSubWeapon : LDHeroSubWeapon
    {
        private GameObject m_FlyPet;
        public void TryAddFlyPet(GameObject flyPet)
        {
            if(m_FlyPet == null)
            {
                m_FlyPet = flyPet;
                SubWpnMgr.AddUAVNode(flyPet.transform);
            }
        }
        public GameObject GetUAVPet()
        {
            return m_FlyPet;
        }
        public override void DestroySubWpn()
        {
            if(m_FlyPet != null)
            {
                SubWpnMgr.DelUAVNode(m_FlyPet);
                m_FlyPet = null;
            }
            base.DestroySubWpn();
        }
    }
}
