using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public enum LDFireStep
    {
        None = 0,
        Firing = 1,
        Reload = 2,
        WaitFire = 3,
    }
    public enum LDFiringStep
    {
        None = 0,
        PlayAnim_Step1 = 1,
        Fire_Step2 = 2,
        EndFire_Step3 = 3,
    }
    public class LDFireData
    {
        public int ClipSize = 0;
    }
    
    public abstract class LDHeroWeapon : LDBaseWeapon, IUpdate
    {
        public BulletItem BulletCfgItem { private set; get; }
        public LDMechaWpnMgr BattleWpnMgr { private set; get; }
        public LDRandomUtil RandomUtil { private set; get; }

        protected LDEffectNodeContent m_FireEffect = null;
        protected LDFireStep m_FireStep = LDFireStep.Firing;
        protected LDFiringStep m_FiringStep = LDFiringStep.None;
        protected LDFireData m_FireData = null;
        protected Vector3 m_LockPos;
        protected float m_CurTime = 0;
        protected int m_FireTimes = 0;
        protected int m_FireDic = 0;
        protected MechaItem m_MechaItem;
        protected Vector3 m_StartAngle;
        protected string m_SoundName = string.Empty;
        public LDWeapenNode WeapenNode { set; get; } = new LDWeapenNode();
        private Animator m_WpnAnimator;
        public int Index { set; get; }
        private int m_ShootAnimIndex = 0;
        private float m_FireDtRadio = 0;
        private bool m_LoopAudio = false;
        public bool AutoFire { set; get; } = true;
        private AudioSource m_LoopAudioSource = null;

        private int m_InitBroadHeroClip = 2;


        public void InitWeaponNode(Transform rootNode, List<Transform> firePoints, Transform rotateNode)
        {
            m_WpnAnimator = rotateNode.GetComponentInChildren<Animator>();
            foreach (Transform firePoint in firePoints)
            {
                WeapenNode.InitNode(rootNode, firePoint,rotateNode,false);
            }
        }
        public virtual void Init(LDMechaWpnMgr battleWpnMgr, LDWeaponData weaponData, LDHeroPlayer heroPlayer)
        {
            base.ObjLive(false);
            AutoFire = true;
            RandomUtil = new LDRandomUtil(heroPlayer.GetRandomSeed());
            m_MechaItem = Mecha.Data.Get(heroPlayer.HeroInfo.MechaId);
            m_FireDtRadio = m_MechaItem.shootAnimRate;
            WeaponData = weaponData;
            PartId = weaponData.PartId;
            WeaponType = LDDIYPartItemType.Mecha;
            m_CurTime = 0;
            m_FireTimes = 0;
            m_FiringStep = LDFiringStep.None;
            WeaponData = weaponData;
            WeaponAttrData = new LDWeaponDataAttr();
            m_FireData = new LDFireData();
            BattleWpnMgr = battleWpnMgr;
            BuffMgr = battleWpnMgr.MechaBuffMgr;
            ChangeBulletId(weaponData.BulletId);
            HeroPlayer = heroPlayer;
            transform.localPosition = Vector3.zero;
            m_StartAngle = WeapenNode.RotateBone.localEulerAngles;

            WeaponAttrData.InitAttrData(this, weaponData, heroPlayer);

            m_FireDic = weaponData.faceDir;
            if (m_MechaItem.shootSound.Length == 2)
            {
                m_SoundName = m_MechaItem.shootSound[1];
                m_LoopAudio = LDParseTools.IntParse(m_MechaItem.shootSound[0]) == 2;
                Global.gApp.gAudioSource.PreloadClip(m_SoundName);
            }

            SetState(LDFireStep.WaitFire);
            AddFireEffect();
            ReloadGun();
        }
        /// <summary>
        /// 计算规则待定
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public float GetFireDt()
        {
            float passHandleDt = Global.gApp.CurFightScene.gPassHandler.MainWpnFireDtCoef();
            return (float)WeaponAttrData.GetFireDt() * passHandleDt;
        }
        private float GetReoladDt()
        {
            float passHandleDt = Global.gApp.CurFightScene.gPassHandler.MainWpnReloadDtCoef();
            return (float)WeaponAttrData.GetReloadDt() * passHandleDt;
        }
        public int GetMaxClipCount()
        {
            return WeaponAttrData.GetMaxClipCount();
        }
        public int GetCurClipConsumeCount()
        {
            return m_FireData.ClipSize;
        }
        public virtual void OnDUpdate(float dt)
        {
            if (m_InitBroadHeroClip > 0)
            {
                BroadHeroClip(0);
                m_InitBroadHeroClip--;
            }
            
            //BuffMgr.OnDUpdate(dt);
            if(!AutoFire){return;}
            if (!LDFightTools.GreaterThenZero(dt))
            {
                return;
            }
            AdujestFirePoint(dt);
            if (m_FireStep == LDFireStep.Firing)
            {
                float fireDT = GetFireDt();
                float cdTimeScale = WeaponAttrData.GetCdTimeScale();
                m_CurTime += dt * cdTimeScale;
                if(m_FiringStep == LDFiringStep.PlayAnim_Step1)
                {
                    TryPlayAnim(cdTimeScale);
                    m_FiringStep = LDFiringStep.Fire_Step2;
                    m_CurTime = dt;
                }
                else if( m_FiringStep == LDFiringStep.Fire_Step2)
                {
                    if(m_CurTime >= fireDT * m_FireDtRadio)
                    {
                        m_CurTime -= fireDT * m_FireDtRadio;
                        ConsumeClip();
                        Fire();
                        m_FiringStep = LDFiringStep.EndFire_Step3;
                    }
                }
                else if(m_FiringStep == LDFiringStep.EndFire_Step3)
                {
                    if (m_CurTime >= fireDT * (1 - m_FireDtRadio))
                    {
                        m_CurTime = 0;
                        m_FiringStep = LDFiringStep.PlayAnim_Step1;
                       if (m_FireData.ClipSize >= GetMaxClipCount())
                        {
                            ChangeToReload();
                        }
                        else if(!AreFire)
                        {
                            SetState(LDFireStep.WaitFire); 
                        }
                    }
                }
            }
            else if (m_FireStep == LDFireStep.WaitFire)
            {
                if (m_FireData.ClipSize < GetMaxClipCount())
                {
                    if (AreFire)
                    {
                        m_CurTime = 0;
                        m_FiringStep = LDFiringStep.PlayAnim_Step1;
                        SetState(LDFireStep.Firing);
                    }
                }
                else
                {
                    // 万一buff 改变的 子弹数。。做个兜底
                    ChangeToReload();
                }
                
            }
            else if (m_FireStep == LDFireStep.Reload)
            {
                m_CurTime += dt;
                float reloadDT = GetReoladDt();
                if (m_CurTime >= reloadDT)
                {
                    ReloadGun();
                    m_CurTime = 0;// GetFireDt();
                    if (AreFire)
                    {
                        SetState(LDFireStep.Firing);
                    }
                    else
                    {
                        SetState(LDFireStep.WaitFire);
                    }
                    BroadManaProgress(1);
                    BroadHeroClip(0);
                }
                else
                {
                    BroadManaProgress(m_CurTime / reloadDT);
                    BroadHeroClip(m_CurTime / reloadDT);
                }
            }
        }
        private void ChangeToReload()
        {
            m_CurTime = 0;
            m_WpnAnimator?.Play(LDAnimName.Run, 0, 0);
            SetState(LDFireStep.Reload) ;
            // 多武器需要 重新设定
            Global.gApp.gMsgDispatcher.Broadcast<LDHeroPlayer, LDHeroWeapon, int>(MsgIds.HeroWeaponStartReload, HeroPlayer,this, m_FireData.ClipSize);
            //Global.gApp.gAudioSource.ZMWave_PlayReolad();
        }
        public void AddBulletClip(int clipCount)
        {
            if (m_FireStep != LDFireStep.Reload)
            {
                SetConsumeClip(m_FireData.ClipSize - clipCount);
            }
        }
        public void ChangeBulletId(int bulletId)
        {
            if (bulletId != WeaponData.BulletId)
            {
                BulletCfgItem = Bullet.Data.Get(bulletId);
            }
            WeaponData.BulletId = bulletId;
        }
        private void SetConsumeClip(int consumeClip)
        {
            m_FireData.ClipSize = Mathf.Max(0, consumeClip);
            BroadHeroClip(0);
            //多武器需要重构
            //Global.gApp.gMsgDispatcher.Broadcast<int,int>(MsgIds.HeroConsumeBullet, m_FireData.ClipSize,GetMaxClipCount());
        }

        private void ConsumeClip()
        {

            float unConsumeRata = (float)WeaponData.CitiaoBuffMgr.GetBuffVal(LDBuffType.CiTiaoFireUnConsumBullet_181) * 10000;
            if(EZMath.IDFloatToInt(unConsumeRata) > 0)
            {
                if(RandomUtil.RandomMatchFloat(unConsumeRata))
                {
                    return;
                }
            }
            SetConsumeClip(m_FireData.ClipSize + 1);
            if(m_FireStep == LDFireStep.Firing)
            {
                int maxClipCount = GetMaxClipCount();
                BroadManaProgress(1.0f * (maxClipCount - m_FireData.ClipSize) / maxClipCount);
            }
        }
        private void BroadManaProgress(float progress)
        {
            //多武器需要重构
            //Global.gApp.gMsgDispatcher.Broadcast<long, float,bool>(MsgIds.ManaProgressChanged, m_HeroPlayer.GetGuid(),
           //   progress,m_FireStep == LDFireStep.Firing);
        }
        private void BroadHeroClip(float progress)
        {
            int maxClipCount = GetMaxClipCount();
            Global.gApp.gMsgDispatcher.Broadcast<long, float,int,int>(MsgIds.HeroClipInfo, HeroPlayer.GetGuid(),
              progress, maxClipCount - m_FireData.ClipSize, maxClipCount);
        }
        protected virtual void FireCall()
        {
            m_FireTimes++;
            //多武器需要重构
            //Global.gApp.gMsgDispatcher.Broadcast<LDHeroPlayer,int, int>(MsgIds.HeroFire,m_HeroPlayer, WeaponItem.BulletId, m_FireTimes);
            Global.gApp.gMsgDispatcher.Broadcast<LDHeroPlayer, LDHeroWeapon, int>(MsgIds.HeroWeaponFire, HeroPlayer,this, m_FireTimes);
            PlayOnceShot();
        }
        private void PlayOnceShot()
        {
            if (!m_LoopAudio)
            {
                if (!string.IsNullOrEmpty(m_SoundName))
                {
                    Global.gApp.gAudioSource.PlayOneShot(m_SoundName, DYAudioType.DYGunAudio);
                }
            }
        }
        private void TryPlayLoopShotAudio()
        {
            if(m_LoopAudio)
            {
                if (!string.IsNullOrEmpty(m_SoundName))
                {
                    if (m_LoopAudioSource == null)
                    {
                        m_LoopAudioSource = Global.gApp.gAudioSource.PlayLoopSource(m_SoundName, gameObject);
                    }
                    m_LoopAudioSource.Play();
                }
            }
        }
        private void TryStopLoopShootAudio()
        {
            if (m_LoopAudio)
            {
                if (!string.IsNullOrEmpty(m_SoundName))
                {
                    if(m_LoopAudioSource != null)
                    {
                        m_LoopAudioSource.Stop();
                    }
                }
            }
        }
        protected virtual void Fire()
        {
            LDWeaponFirePoint weaponFirePoint = WeapenNode.GetNodeData();
            if (m_FireEffect != null)
            {
                m_FireEffect.transform.SetParent(weaponFirePoint.FirePointRender, false);
                m_FireEffect.transform.localPosition = Vector3.zero;
                m_FireEffect?.PlayParticle();
            }
            FireCall();
            InstanceNormalBullet(weaponFirePoint);
        }
        private void TryPlayAnim(float animSpeed)
        {
            if (m_FireStep == LDFireStep.Firing)
            {

                string animNameKey = m_MechaItem.shootAnim[m_ShootAnimIndex % m_MechaItem.shootAnim.Length];
                HeroPlayer.HeroAnimCtrol.PlayUAnim(animNameKey, true);
                HeroPlayer.HeroAnimCtrol.SetAnimUpSpeed(animSpeed);
                m_WpnAnimator?.Play(animNameKey, 0, 0);
                m_ShootAnimIndex++;
            }
        }
        protected virtual void AdujestFirePoint(float dt)
        {
        }
        protected void InstanceNormalBullet(LDWeaponFirePoint weaponFirePoint )
        {
            Global.gApp.gMsgDispatcher.Broadcast<LDBulletData,LDHeroWeapon ,LDHeroPlayer>(MsgIds.HeroFire, HeroPlayer.BulletEmitter.BulletData,this, HeroPlayer);
            int bulletId = WeaponData.BulletId;
            int bulletCount = GetBulletCount();
            int bulletCurves = GetBulletCurves();
            float angleOffset = GetAngleOffset();
            float posOffset = GetPosOffset();
            float dtAngleZ = -angleOffset * (bulletCurves - 1) / 2;
            int mIndex = 0;

            int totalCount = bulletCount * bulletCurves;
            float bezireOffset = 2;
            float OffsetBezire = LDFightTools.GetIntF(- bezireOffset * (totalCount - 1) / 2);

            HeroPlayer.BulletEmitter.BulletData.BulletId = bulletId;
            HeroPlayer.BulletEmitter.BulletData.ClipSize = GetMaxClipCount();
            HeroPlayer.BulletEmitter.BulletData.ClipCount = m_FireData.ClipSize;
            //多武器需要重构 
            //Global.gApp.gMsgDispatcher.Broadcast<LDBulletData, LDHeroPlayer>(MsgIds.HeroFireBullet, m_HeroPlayer.BulletEmitter.BulletData, m_HeroPlayer);
            Global.gApp.gMsgDispatcher.Broadcast<LDBulletData,LDHeroWeapon ,LDHeroPlayer>(MsgIds.HeroFireBullet, HeroPlayer.BulletEmitter.BulletData,this, HeroPlayer);
            double atk = GetReCalcAtkVal();
            bulletId = HeroPlayer.BulletEmitter.BulletData.BulletId;

            double damageParam = WeaponData.GetDamageParam();
            for (int index = 0; index < bulletCurves; index = index + 1)
            {
                float Offset = -posOffset * (bulletCount - 1) / 2; ;
                for (int pIndex = 0; pIndex < bulletCount; pIndex = pIndex + 1)
                {
                    LDTrackBullet bullet = HeroPlayer.BulletEmitter.FireByWeapon(bulletId,atk, WeaponData.CitiaoBuffMgr, this);
                    bullet.AtkData.MulCiTiaoParam(damageParam);
                    InitNormalBullet(weaponFirePoint, bullet, dtAngleZ, Offset);
                    if (WeaponData.BindParent > 0)
                    {
                        bullet.transform.SetParent(weaponFirePoint.FirePoint, true);
                    }
                    bullet.SetBezierEndPosCalcController(GetTargetPos(), OffsetBezire);
                    OffsetBezire += bezireOffset;

                    Offset = Offset + posOffset;
                    mIndex++;
                }
                dtAngleZ = dtAngleZ + angleOffset;
            }
        }
        private Vector3 GetTargetPos()
        {
            return m_LockPos;
        }
        public double GetReCalcAtkVal()
        {
            return BattleWpnMgr.GetAtk();
        }
        protected virtual void InitNormalBullet(LDWeaponFirePoint WeaponFirePoint,LDAtkBullet bullet, float dtAngleZ, float OffsetX)
        {
            bullet.Init(WeaponFirePoint.FirePoint, dtAngleZ, OffsetX);
        }
        protected virtual float GetPosOffset()
        {
            return WeaponAttrData.GetPosOffset();
        }
        protected virtual float GetAngleOffset()
        {
            return WeaponAttrData.GetMainWpnAngleOffSet();
        }

        public virtual int GetBulletCount()
        {
            return WeaponAttrData.GetBulletCount(Index);
        }
        public virtual int GetBulletCurves()
        {
            return WeaponAttrData.GetBulletCurves();
        }
        private void ReloadGun()
        {
            SetConsumeClip(0);
            m_FireTimes = 0;
            if (HeroPlayer.MechaItem.animReset == 1)
            {
                m_ShootAnimIndex = 0;
            }
            BuffMgr.RemoveTypeBuff(LDBuffType.ReloadClipSpeedAndClear_2101, HeroPlayer.BuffMgr);
            BuffMgr.RemoveTypeBuff(LDBuffType.Crit_2191, HeroPlayer.BuffMgr);
            // 多武器需要重构
            //Global.gApp.gMsgDispatcher.Broadcast<LDHeroPlayer, int>(MsgIds.HeroReload, m_HeroPlayer, m_FireData.ClipSize);
            Global.gApp.gMsgDispatcher.Broadcast<LDHeroPlayer,LDHeroWeapon,int>(MsgIds.HeroWeaponReload, HeroPlayer,this, m_FireData.ClipSize);
        }
        public override void StartFire()
        {
            base.StartFire();
        }
        private void SetState(LDFireStep fireStep)
        {
            m_FireStep = fireStep;
            HeroPlayer.HeroAnimCtrol.PlayUAnim(LDAnimName.Idle, false);
            HeroPlayer.HeroAnimCtrol.SetAnimUpSpeed(1);
            if(fireStep == LDFireStep.Firing)
            {
                TryPlayLoopShotAudio();
            }
            else
            {
                TryStopLoopShootAudio();
            }
        }
        public override void EndFire()
        {
            base.EndFire();
        }
        public bool TrySetLockPos(Vector3 lockPos)
        {
            //if(m_FireStep != LDFireStep.Firing)
            {
                m_LockPos = lockPos;
                return true;
            }
            //return false;
        }

        public void SetTimeScale(float timeScale)
        {

        }
        private void AddFireEffect()
        {
            RecycleFireEffect();
            if (WeaponData.FireEffect > 0)
            {
                m_FireEffect = Global.gApp.CurFightScene.gEffectMgr.GetFreeEffectNode(WeaponData.FireEffect);
                m_FireEffect.transform.SetParent(WeapenNode.FirePointRender, false);
                m_FireEffect.transform.localPosition = Vector3.zero;
            }
        }
        public bool InFirStep()
        {
            return m_FireStep == LDFireStep.Firing;
        }
        private void RecycleFireEffect()
        {
            if (m_FireEffect != null)
            {
                Global.gApp.CurFightScene.gEffectMgr.RecycleEffectNode(m_FireEffect);
                m_FireEffect = null;
            }
        }
        public override int GetBulletId()
        {
            return BulletCfgItem.id;
        }
        public virtual void DestroyWpn()
        {
            base.Destroy();
            BuffMgr.BuffMgrDestroy();
            RecycleFireEffect();
            EndFire();
            //m_WpnFireData?.Destroy();
            if (m_LoopAudioSource != null)
            {
                Global.gApp.gAudioSource.DestroyLoopSource(m_LoopAudioSource);
                m_LoopAudioSource = null;
            }
            Global.gApp.gResMgr.DestroyGameObj(gameObject);
        }
    }
}
