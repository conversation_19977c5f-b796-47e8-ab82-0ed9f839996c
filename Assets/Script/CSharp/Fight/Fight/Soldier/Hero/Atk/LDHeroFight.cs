using UnityEngine;

namespace LD
{
    public class LDHeroFight : LDFight,IUpdate
    {
        LDEffectNodeContent m_AtkRange;
        private float m_PickRangeScale = 1;
        public LDHeroFight(LDHeroPlayer player)
        {
            m_HeroPlayer = player;
            RotateNode = player.transform.Find(LDFightConstVal.ModelNodeName);
            ModeRoleNode = player.transform.Find(LDFightConstVal.ModelNodeName);
        }
        public void Init(LDHeroPlayer player)
        {
            m_HeroPlayer.HeroAnimCtrol.PlayIdleAnim();
            //m_AtkRange = Global.gApp.CurFightScene.gEffectMgr.GetFreeEffectNode(1006);
            //m_AtkRange.transform.SetParent(m_HeroPlayer.UnScaleNode);
            //m_AtkRange.transform.localPosition = Vector3.zero;
        }
        public void DisableAtkRange()
        {
            m_AtkRange.gameObject.SetActive(false);
        }
        public void OnDUpdate(float dt)
        {
            TryPickProps();
            //float atkRange = m_HeroPlayer.BattleWpnMgr.GetAtkRange();
            //m_AtkRange.transform.localScale = Vector3.one * (atkRange);
            //Global.gApp.CurBattleScene.PlayerMgr.SearchNeareastAI(atkRange,dt);
            CalcWpnState(dt);
        }
        private void TryPickProps()
        {
            if(m_HeroPlayer.InputController)
            {
                Global.gApp.CurFightScene.gPlayerMgr.SearchUtils.GainInRangeProps(GetPickRange(), 
                    m_HeroPlayer.transform.GetPoisition(),m_HeroPlayer, m_HeroPlayer.transform,true);
            }
        }
        private float GetPickRange()
        {
            return (float)m_HeroPlayer.HeroData.GetPickRange() * m_PickRangeScale;
        }
        public void SetPickScale(float pickScale)
        {
            m_PickRangeScale = pickScale;
        }
        private Vector3 m_LockPos;
        private void CalcWpnState(float dt)
        {
            LDSceneObj lockObj = m_HeroPlayer.HeroStateMgr.GetLockObj();
            if (lockObj != null)
            {
                Vector3 lockPos = LDFightNodeTools.GetBodyNode(lockObj).position;
                m_HeroPlayer.MechaMgr.SetWpnEnalble(true, lockObj.AI_Hero_SqrDis);
                if(m_HeroPlayer.MechaMgr.TrySetLockPos(lockPos))
                {
                    m_LockPos = lockPos;
                }
            }
            else
            {
                m_HeroPlayer.MechaMgr.SetWpnEnalble(false);
            }

            if (!m_HeroPlayer.MechaMgr.LockRotationBy_305())
            {
                if (m_HeroPlayer.MechaMgr.LockMoveRef > 0 && lockObj != null)
                {
                    CalcFireRotation(m_LockPos, dt);
                }
            }
        }
        /// <summary>
        /// 指令 控制移动
        /// </summary>
        /// <param name="move"></param>
        /// <param name="speedVal"></param>
        public void Move(Vector3 move, float speedVal, float dt)
        {
            // 设置速度
            m_HeroPlayer.SetSpeed(move, speedVal);
            MoveOnAct(move, speedVal, dt);
        }
        public void MoveNoSpeed(Vector3 move, float speedVal, float dt)
        {
            // 设置速度
            m_HeroPlayer.SetSpeed(move, speedVal);
            MoveOnAct(move, speedVal, dt);
        }
        private void MoveOnAct(Vector3 move, float speedVal, float dt)
        {
            // 根据朝向 与速度放心计算 动作
            CalcCurAnim(move.x, move.z);
            CalcMoveRotation(move, dt);
        }

    }
}