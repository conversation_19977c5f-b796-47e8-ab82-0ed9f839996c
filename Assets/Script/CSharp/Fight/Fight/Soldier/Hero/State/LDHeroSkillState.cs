using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDHeroSkillState : LDHeroBaseState
    {
        private LDHeroSkillBase m_CurSkill;
        public void SetCurSkill(LDHeroSkillBase skillBase)
        {
            m_CurSkill = skillBase;
            skillBase.SetExcuteState(this);
        }
         public void UseSkillEnd(LDHeroSkillBase heroSkill)
        {
            m_AIStateMachine.ExitSkill();

        }
        protected override void OnEnterImp()
        {
            m_CurSkill.OnEnter();
        }
        protected override void OnRUpdate(float dt)
        {
            m_CurSkill.OnDUpdate(dt);
        }
        protected override void OnExitImp()
        {
            m_CurSkill.OnExit();
        }
    }
}
