using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

namespace LD
{
	public class LDHeroDMoveState :LDHeroBaseState 
	{
        private NavMeshAgent m_Agent;
        protected override void OnEnterImp()
        {
            m_Agent = m_HeroPlayer.NavMeshAgent;
        }
        public NavMeshAgent GetAgent()
        {
            return m_Agent;
        }
        public void SetAgentEnable(bool agentState)
        {
            m_Agent.enabled = agentState;
        }
        protected override void OnRUpdate(float dt)
        {
            //if (m_HeroPlayer.InputController != null)
            if(m_HeroPlayer.InputController)
            {
                //m_HeroPlayer.InputController.OnDUpdate(dt);
                // �ƶ�
                if (!m_HeroPlayer.HeroMgr.MainRole.IsMoving())
                {
                    //// Ѱ·���� ���� �����ٶ�
                    //if (m_Agent.enabled)
                    //{
                    //    Vector3 moveVec = m_Agent.desiredVelocity;
                    //    m_HeroPlayer.HeroMgr.Move(moveVec.x, moveVec.z, dt);
                    //    m_HeroPlayer.SetSpeed(Vector3.zero,0);
                    //}
                    //else
                    {
                        m_HeroPlayer.HeroMgr.Move(0,0, dt);
                    }
                }
                else // �ֶ����Ƶ�ʱ����Ҫ�ر� Ѱ·
                {
                    SetAgentEnable(false);
                    m_HeroPlayer.HeroMgr.Move(m_HeroPlayer.HeroMgr.MainRole.MoveX / 10000f, m_HeroPlayer.HeroMgr.MainRole.MoveY / 10000f, dt);
                }
            }
            else if(m_HeroPlayer.ObjType == LDSceneObjType.CrossArenaHero)
            {
                LDSceneObj targetObj = HeroStateMgr.GetLockObj();
                if(targetObj == null)
                {
                    LDMainRole nextMainRole = Global.gApp.CurFightScene.gPlayerMgr.GetNextRole(m_HeroPlayer.HeroMgr.MainRole.GetGuid());
                    targetObj = nextMainRole.GetCaptainPlayer();
                    if(targetObj == null)
                    {
                        return;
                    }
                }
                m_Agent.speed = 1.5f;
                Vector3 targetPos = targetObj.transform.position;
                m_Agent.SetDestination(targetPos);

                Vector3 moveVec = m_Agent.desiredVelocity;
                //moveVec.y = 0;

                //Vector3 moveVecNormal = moveVec.normalized;
                //LDHeroPlayer captainPlayer = m_HeroPlayer.HeroMgr.MainRole.GetCaptainPlayer();
                //m_HeroPlayer.transform.position = m_HeroPlayer.transform.position + moveVecNormal * (captainPlayer.GetMoveSpeed() *
                //    captainPlayer.GetMoveSpeedScale()) * dt;
                m_HeroPlayer.AtkFight.MoveNoSpeed(moveVec, 0, dt);
                //m_HeroPlayer.SetSpeed(Vector3.zero, 0);

                m_Agent.stoppingDistance = 5;
            }
            else if(m_Agent.enabled)
            {
                LDHeroPlayer captainPlayer = m_HeroPlayer.HeroMgr.MainRole.GetCaptainPlayer();
                m_Agent.speed = 0.01f;
                Vector3 targetPos = captainPlayer.transform.position;
                float disSqrt = (m_HeroPlayer.transform.position - targetPos).sqrMagnitude;
                m_Agent.SetDestination(targetPos);

                Vector3 moveVec = m_Agent.desiredVelocity;
                moveVec.y = 0;
                
                Vector3 moveVecNormal = moveVec.normalized;
                m_HeroPlayer.transform.position += moveVecNormal * (captainPlayer.GetMoveSpeed() * captainPlayer.GetMoveSpeedScale() * dt);
                m_HeroPlayer.Move(moveVecNormal.x, moveVecNormal.z, dt);
                m_HeroPlayer.SetSpeed(Vector3.zero, 0);

                if (disSqrt >= 25)
                {
                    m_Agent.stoppingDistance = 1;
                }
                else if(disSqrt <= 10)
                {
                    m_Agent.stoppingDistance = 5;
                } 
            }
            else if (Global.gApp.CurFightScene.gPassHandler.AssistHeroFollowNoNavMesh())
            {
                LDHeroPlayer captainPlayer = m_HeroPlayer.HeroMgr.MainRole.GetCaptainPlayer();
                Vector3 targetPos = captainPlayer.transform.position;
                Vector3 dtPos = targetPos - m_HeroPlayer.transform.position;
                float disSqrt = dtPos.sqrMagnitude;
                if (disSqrt > Mathf.Pow(m_Agent.stoppingDistance, 2))
                {
                    float speed = captainPlayer.GetMoveSpeed() * captainPlayer.GetMoveSpeedScale() * dt;
                    dtPos.y = 0;

                    Vector3 moveVecNormal = dtPos.normalized;
                    m_HeroPlayer.transform.position += moveVecNormal * speed;
                    m_HeroPlayer.Move(moveVecNormal.x, moveVecNormal.z, dt);
                    m_HeroPlayer.SetSpeed(Vector3.zero, 0);
                }
                else
                {
                    m_HeroPlayer.Move(0f, 0f, dt);
                    m_HeroPlayer.SetSpeed(Vector3.zero, 0); 
                }

                if (disSqrt >= 25)
                {
                    m_Agent.stoppingDistance = 1;
                }
                else if(disSqrt <= 10)
                {
                    m_Agent.stoppingDistance = 5;
                }  
            }
            
        }
        protected override void OnExitImp()
        {
            SetAgentEnable(false);
        }
    }
}
