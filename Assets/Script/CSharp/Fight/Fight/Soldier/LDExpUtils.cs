using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    //  先不考虑 额外问题。目前任务 Frame 是不重复的
    public class LDCiTiaoInfo
    {
        public int CitiaoType = -1;
        public int ExternEffect = -1;
        public int MechaId = -1;
        public int CurFrame = 0;
        public int PlayerLv = 0;
        public int Sequence = 0;
        public LDCiTiaoBaseGroup CiTiaoGroup = null;

    }
    public class LDExpUtils
    {
        public List<LDCiTiaoItemData> FreshCitiaoInfo { set; get; } = new List<LDCiTiaoItemData>();
        // 回头存到 服务器
        public List<LDCiTiaoItemData> RankCitiaoInfo { set; get; } = new List<LDCiTiaoItemData>();
        private List<LDCiTiaoInfo> m_CitiaoInfo = new List<LDCiTiaoInfo>();


        private LDMainRole m_MainRole;
        //总经验
        private int m_CurLv = 1;
        private int m_MaxLv = 0;
        private double m_CurExp = 0;
        private double m_CurLvNeedExp = 0;
        private float m_ExpInc = 0;

        public void InitExpInfo(LDMainRole mainRole)
        {
            m_MainRole = mainRole;
            m_MaxLv = BattleExp.Data.items.Length;
            SetLv(1);
            Debug.Log("MaxLv " + m_MaxLv);
        }
        public void UpExpInc()
        {
            m_ExpInc = (float)m_MainRole.GetCaptainPlayer().HeroData.GetExpInc();
            BroadExpInfo();
        }
        public void AddExp(float exp)
        {
            //if (!m_Ended)
            {
                exp = exp * (1 + m_ExpInc);
                AddExpImp(exp);
                Global.gApp.gMsgDispatcher.Broadcast<long,float>(MsgIds.GainExp, m_MainRole.GetGuid(), exp);
            }
        }

        private void AddExpImp(float exp)
        {
            m_CurExp += exp;
            CalcExpInfo();
            //Global.gApp.gMsgDispatcher.Broadcast<int, float, float>(MsgIds.GainExp, Mathf.Min(m_CurLv + 1, m_MaxLv), m_CurLvExp, m_CurLvNeedExp);
        }
        private void CalcExpInfo()
        {
            // 满级不做计算
            if (m_CurLv >= m_MaxLv) { return; }

            if (m_CurExp >= m_CurLvNeedExp)
            {
                ExpLvUp();
            }
            BroadExpInfo();
        }
        public void ExpLvUpByCiTiao()
        {
            if (m_CurLv >= m_MaxLv) { return; }
            ExpLvUp();
        }

        public LDCiTiaoInfo OpenNormalCiTiao(int externEffect = -1)
        {
            if (m_CurLv >= m_MaxLv) { return null; }
            return OpenBuffUI(LDCiTiaoType.Normal, externEffect);
        }
        public LDCiTiaoInfo OpenNormalCiTiaoInGroup(LDCiTiaoBaseGroup ciTiaoBaseGroup)
        {
            if (m_CurLv >= m_MaxLv) { return null; }
            LDCiTiaoInfo CiTiaoInfo = OpenBuffUI(LDCiTiaoType.Normal);
            if (CiTiaoInfo != null)
            {
                CiTiaoInfo.ExternEffect = 1;
                CiTiaoInfo.CiTiaoGroup = ciTiaoBaseGroup;
            }
            return CiTiaoInfo;
        }
        public void SetLv(int lv)
        {
            m_CurLv = lv;
            CalcNeedLvExp();
            UpExpInc();
            if(m_CurLv > 1)
            {
                Global.gApp.gMsgDispatcher.Broadcast<long, int>(MsgIds.ExpLvUp, m_MainRole.GetGuid(), m_CurLv);
            }
        }
        private void ExpLvUp()
        {
            // 当前lv
            m_CurLv++;
            m_CurExp -= m_CurLvNeedExp;
            if (m_CurExp <= 0)
            {
                m_CurExp = 0;
            }
            CalcNeedLvExp();
            if (m_CurLv >= 1)
            {
                OpenBuffUI(LDCiTiaoType.Normal,-1,true);
                Global.gApp.gMsgDispatcher.Broadcast<long, int>(MsgIds.ExpLvUp, m_MainRole.GetGuid(), m_CurLv);
                LDHeroPlayer heroPlayer = m_MainRole.GetCaptainPlayer();
                LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.AddEffectNode(LDEffectCfg.LvUpEffect, heroPlayer.transform.GetPoisition());
                effectNode.SetTimeScale(1);
                effectNode.transform.SetParent(heroPlayer.transform,true);
                Global.gApp.CurFightScene.gPlayerMgr.SetTimeScale(1);
            }
        }

        public bool CanShowCiTiao()
        {
            return Global.gApp.CurFightScene.gPassHandler.ShowCitiaoInfoUI();
        }
        private void AITryUseCitiao(int uitype)
        {
            int aiSequence = Global.gApp.CurFightScene.Orders.GetCiTiaoSequence();
            if (Global.gApp.CurFightScene.Orders.CheckOrderLegal(m_MainRole.GetGuid(), aiSequence))
            {
                List<LDCiTiaoItemData> CiTiaoItemDatas = m_MainRole.GetCaptainPlayer().CiTiaoMgr.GetRandomValidCiTiao(uitype);
                if (CiTiaoItemDatas.Count > 0)
                {
                    int maxWeight = 0;
                    foreach (LDCiTiaoItemData item in CiTiaoItemDatas)
                    {
                        maxWeight += item.GetWeight();
                    }

                    int dstWeight = Random.Range(1, maxWeight);
                    int curWeight = 0;
                    int index = 0;
                    foreach (LDCiTiaoItemData item in CiTiaoItemDatas)
                    {
                        curWeight += item.GetWeight();
                        if (curWeight > dstWeight)
                        {
                            break;
                        }
                        else
                        {
                            index++;
                        }
                    }
                    int realIndex = index % CiTiaoItemDatas.Count;
                    LDCiTiaoItemData data = CiTiaoItemDatas[realIndex];

                    Global.gApp.CurFightScene.Orders.SendCiTiaoSelect(m_MainRole.GetGuid(),
                                   m_CurLv, aiSequence, data.CiTiaoGroup.CitiaoGroupItem.id, data.CitiaoItem.id);
                }
            }
        }
        public LDCiTiaoInfo OpenBuffUI(int uitype,int externEffect = -1,bool lvUp = false)
        {
            if(Global.gApp.gGameCtrl.RecordFight)
            {
                return null;
            }

            if (!CanShowCiTiao())
            {
                return null;
            }
            if (m_MainRole.GetGuid() != Global.gApp.CurFightScene.LocalPlayerGuid)
            {
                if (m_MainRole.RoleData.AIRole)
                {
                    AITryUseCitiao(uitype);
                }
                return null;
            }
            int sequence = Global.gApp.CurFightScene.Orders.GetCiTiaoSequence();
            if (!Global.gApp.CurFightScene.Orders.CheckOrderLegal(m_MainRole.GetGuid(), sequence))
            {
                Debug.LogError("=========OpenBuffUI=========");
                return null;
            }
            int curFrame = Global.gApp.CurFightScene.Frame;
            LDCiTiaoInfo ciTiaoInfo = new LDCiTiaoInfo();
            ciTiaoInfo.CurFrame = curFrame;
            ciTiaoInfo.PlayerLv = m_CurLv;
            ciTiaoInfo.Sequence = sequence;
            ciTiaoInfo.CitiaoType = uitype;
            ciTiaoInfo.ExternEffect = externEffect;
            m_CitiaoInfo.Add(ciTiaoInfo);
            if(lvUp)
            {
                Global.gApp.CurFightScene.TryDelyOpen(15);
            }
            else
            {
                Global.gApp.CurFightScene.TryDelyOpen();
            }
            Global.gApp.gGameData.CheckData();
            return ciTiaoInfo;
        }

        public void TryOpenCitiaoUI()
        {
            if (m_CitiaoInfo.Count == 0 || Global.gApp.CurFightScene.Paused() ||
                Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.UIFightCitiaoView) != null ||
                Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.UIFightCitiao4View) != null ||
                Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.UIFightEliteCitiaoView) != null)
            {
                return;
            }
            LDCiTiaoInfo citiaoInfo = m_CitiaoInfo[0];
            m_CitiaoInfo.RemoveAt(0);
            List<LDCiTiaoItemData> CiTiaoItemDatas = null;
            if (citiaoInfo.CiTiaoGroup == null)
            {
                CiTiaoItemDatas = m_MainRole.GetCaptainPlayer().CiTiaoMgr.GetRandomValidCiTiao(citiaoInfo.CitiaoType);
            }
            else
            {
               CiTiaoItemDatas = citiaoInfo.CiTiaoGroup.GetNormalValidCiTiao();
            }
            if (CiTiaoItemDatas.Count > 0)
            {
                if (citiaoInfo.CitiaoType == LDCiTiaoType.Normal ||
                       citiaoInfo.CitiaoType == LDCiTiaoType.Power)
                {
                    LDCitiaoUIData citiaoUIData = new LDCitiaoUIData();
                    citiaoUIData.CitiaoType = citiaoInfo.CitiaoType;
                    citiaoUIData.ExternEffect = citiaoInfo.ExternEffect;
                    citiaoUIData.CiTiaoItemDatas = CiTiaoItemDatas;
                    //SoundManger.Get().PlayEffect(LDFightConstVal.LevelUpClip, SoundManger.SoundType.SHOOT_EFFECT);
                    //Global.gApp.gLuaDataMsg.SendShowCitiaoUI(citiaoType, citiaoInfo.ExternEffect, CiTiaoItemDatas);
                    LDPlayModeBase playMode = Global.gApp.CurFightScene.gPlayMode;
                    if(Global.gApp.CurFightScene is LDCoinPassFightScene or LDGuildFightScene || playMode is LDBreakoutPlayMode || playMode is LDSurvivorPlayMode)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<UIFightCitiao4View>(LDUICfg.UIFightCitiao4View).SetLoadedCall(
                           uIFightCitiaoView =>
                           {
                               uIFightCitiaoView?.InitCitiao(this, m_MainRole, citiaoUIData, citiaoInfo);
                           }
                          );

                    }
                    else
                    {
                        if(Global.gApp.CurFightScene is LDSeasonFightScene)
                        {

                            if (Global.gApp.gSystemMgr.gSeasonMgr.IsEndlessMode(Global.gApp.CurFightScene.gPassData.MisssionId))
                            {
                                Global.gApp.gUiMgr.OpenUIAsync<UIFightCitiao4View>(LDUICfg.UIFightCitiao4View).SetLoadedCall(uIFightCitiao4View =>
                                {
                                    uIFightCitiao4View?.InitCitiao(this, m_MainRole, citiaoUIData, citiaoInfo);
                                });

                                return;
                            }
                        }
                        Global.gApp.gUiMgr.OpenUIAsync<UIFightCitiaoView>(LDUICfg.UIFightCitiaoView).SetLoadedCall(uIFightCitiaoView =>
                        {
                            uIFightCitiaoView?.InitCitiao(this, m_MainRole, citiaoUIData, citiaoInfo);
                        });
                    }

                }
                else if(citiaoInfo.CitiaoType == LDCiTiaoType.EliteBook)
                {
                    LDCitiaoUIData citiaoUIData = new LDCitiaoUIData();
                    citiaoUIData.CitiaoType = citiaoInfo.CitiaoType;
                    citiaoUIData.ExternEffect = citiaoInfo.ExternEffect;
                    citiaoUIData.CiTiaoItemDatas = CiTiaoItemDatas;
                    //SoundManger.Get().PlayEffect(LDFightConstVal.LevelUpClip, SoundManger.SoundType.SHOOT_EFFECT);
                    //Global.gApp.gLuaDataMsg.SendShowCitiaoUI(citiaoType, citiaoInfo.ExternEffect, CiTiaoItemDatas);
                    Global.gApp.gUiMgr.OpenUIAsync<UIFightEliteCitiaoView>(LDUICfg.UIFightEliteCitiaoView).SetLoadedCall(uIFightCitiaoView =>
                    {
                        uIFightCitiaoView?.InitCitiao(this, m_MainRole, citiaoUIData, citiaoInfo);
                    });
                }
            }
            else
            {
                TryOpenCitiaoUI();
            }
        }
        public int GetLv()
        {
            return m_CurLv;
        }     
        public void SetExp(double exp)
        {
            m_CurExp = exp;
        }       
        public double GetExp()
        {
            return m_CurExp;
        }

        private void CalcNeedLvExp()
        {
            int nextLv = m_CurLv + 1;
            nextLv = Mathf.Min(nextLv, m_MaxLv);
            nextLv = Mathf.Max(nextLv, 1);
            BattleExpItem itemItem = BattleExp.Data.Get(nextLv - 1);
            if (Global.gApp.CurFightScene.PassType != LDPassType.PveTeam)
            {
                m_CurLvNeedExp = itemItem.exp;
            }
            else
            {
                m_CurLvNeedExp = itemItem.expTeam;
            }
            CalcExpInfo();// 递归处理一下 不能递归处理担心多次出现词条问题。
            BroadExpInfo();
        }
        private bool m_BroadExpInfo = true;
        public void SetBroadExpInfoState(bool state)
        {
            m_BroadExpInfo = state;
            if (state)
            {
                BroadExpInfo();
            }
        }
        private void BroadExpInfo()
        {
            if (m_BroadExpInfo)
            {
                Global.gApp.gMsgDispatcher.Broadcast<long,int, double, double>(MsgIds.ExpInfoChanged, m_MainRole.GetGuid(), m_CurLv, m_CurExp, m_CurLvNeedExp);
            }
        }
    }
}
