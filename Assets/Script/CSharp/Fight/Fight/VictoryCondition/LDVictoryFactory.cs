using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public class LDVictoryType
    {
        public static int KillSpecifiedTypeAI = 1;
        public static int KillSpecifiedIDAI = 2;
        public static int KillAllAI = 3;
        public static int TimeCountDown = 4;
        public static int ActiveTower = 5;
        public static int KillSpecifiedTypeAITimeLimt = 6;
        public static int RescueSurvivor = 7;
        public static int AircraftRound = 8;
        public static int ActScore = 9;
    }
    public class LDVictoryFactory
    {
        public static LDVictoryBase CreateVictoryCondition(LDVictoryMgr victoryMgr,int[] info )
        {
            LDVictoryBase victoryBase = null;
            int conditionType = (info[0]);

            if(conditionType == LDVictoryType.KillSpecifiedTypeAI)
            {
                victoryBase = new LDKillSpecifiedTypeAI_1();
            }       
            else if(conditionType == LDVictoryType.KillSpecifiedIDAI)
            {
                victoryBase = new LDKillSpecifiedIdAI_2();
            }
            else if (conditionType == LDVictoryType.KillAllAI)
            {
                victoryBase = new LDKillAllAI_3();
            }
            else if(conditionType == LDVictoryType.TimeCountDown)
            {
                victoryBase = new LDCountDown_4();
            }
            else if(conditionType == LDVictoryType.ActiveTower)
            {
                victoryBase = new LDActiveSiginTower_5();
            }
            else if (conditionType == LDVictoryType.KillSpecifiedTypeAITimeLimt)
            {
                victoryBase = new LDKillSpecifiedTypeAITimeLimt();
            }
            else if (conditionType == LDVictoryType.RescueSurvivor)
            {
                victoryBase = new LDRescueSurvivor_7();
            }
            else if (conditionType == LDVictoryType.AircraftRound)
            {
                victoryBase = new LDAircraftRound_8();
            }
            else if (conditionType == LDVictoryType.ActScore)
            {
                victoryBase = new LDVicActScore_9();
            }
            if (victoryBase != null)
            {
                victoryBase.SetBaseInfo(victoryMgr, info);
            }
            else
            {
                Debug.LogError("====conditionType not imp====" + conditionType);
            }
            return victoryBase;
        }
    }
}
