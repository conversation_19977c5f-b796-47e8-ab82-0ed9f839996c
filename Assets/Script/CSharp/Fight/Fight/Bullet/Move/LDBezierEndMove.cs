using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD.Skip
{
    public class LDBezierEndMove : LDBaseMoveMode
    {
        float m_T;
        public override void Init(LDTrackBullet bullet)
        {
            base.Init(bullet);
            InitBezierData();
        }
        public override void SyncPos(Vector3 poisition)
        {
            base.SyncPos(poisition);
            InitBezierData();
        }
        private void InitBezierData()
        {
            m_StartPos = m_Bullet.transform.GetPoisition();
            m_T = 0f;
        }
        public override void SetBezierEndPosCalcController(Vector3 endPos,float offsetX)
        {
            base.SetBezierEndPosCalcController(endPos);
            m_EndPos = endPos;
            float height = (float) m_Bullet.AtkData.BulletChangeUtil().CalcMoveParam(m_Bullet.BulletItem)[0];
            m_StartPos = endPos + new Vector3(0, height, 0); 
            m_ControlPos = (m_StartPos + m_EndPos) / 2;
        }

        public override void OnDUpdate(float dt)
        {
            if (!m_Bullet.Sleeped)
            {
                m_CurTime = m_CurTime + dt;
                if (LDFightTools.GreaterOrEqualThenZero(m_LiveTime, m_CurTime))
                {
                    m_T += dt / (float)m_LiveTime;
                    m_Bullet.transform.SetPoisition(GetBezierPoint(m_T));
                    LDFightTools.UnifiedTransform(m_Bullet.transform);
                    TryRayCast();
                }
                else
                {
                    m_Bullet.RecycleBullet(LDRecycleReason.TimeOver);
                }
            }
        }
    }
}

