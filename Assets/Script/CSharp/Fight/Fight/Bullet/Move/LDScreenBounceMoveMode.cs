using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD.Skip
{
    public class LDScreenBounceMoveMode : LDBaseMoveMode
    {
        private SphereCollider m_SphereCollider;

        private float m_MinY = 0.4f;
        private float m_MaxY = 1.5f;
        private Vector3 m_Forward = Vector3.forward;
        private Vector3 m_Back = Vector3.back;
        private Vector3 m_Left = Vector3.left;
        private Vector3 m_Right = Vector3.right;
        private float m_BounceAddTime = 0;
        private int m_BounceTimes = 0;
        private float m_BounceMaxAddTime = 0;
        private bool m_PveMode = false;
        private bool m_KcpMode = false;
        private float m_CosVal;
        private float m_SinVal;

        public override void Init(LDTrackBullet bullet)
        {
            base.Init(bullet);
            m_PveMode = Global.gApp.CurFightScene.PVEMode;
            m_KcpMode = Global.gApp.CurFightScene.UseKcp;
            List<float> moveParam = m_Bullet.AtkData.BulletChangeUtil().CalcMoveParam(m_Bullet.BulletItem);
            m_BounceAddTime = (float)moveParam[0];
            m_BounceMaxAddTime = (float)moveParam[1];
            m_BounceTimes = 0;
            m_SphereCollider = m_Bullet.Collider as SphereCollider;
            if (m_SphereCollider != null)
            {
                m_MinY = m_SphereCollider.radius * m_Bullet.transform.localScale.x;
            }
            Vector3 forward = m_Bullet.transform.GetForward();
            forward.y = 0;
            m_Bullet.transform.SetForward(forward);
            LDFightTools.UnifiedTransform(m_Bullet.transform);


            float DtAngle = Global.gApp.CurFightScene.gPassData.CameraAngle;
             DtAngle = (float)(DtAngle * Mathf.Deg2Rad);
            m_CosVal = LDMath.Cos(DtAngle);
            m_SinVal = LDMath.Sin(DtAngle);
            m_Forward = LDInputHandler.CalcVec(Vector2.up,m_CosVal,m_SinVal);
            m_Back = LDInputHandler.CalcVec(Vector2.down, m_CosVal,m_SinVal);
            m_Left = LDInputHandler.CalcVec(Vector2.left,m_CosVal,m_SinVal);
            m_Right = LDInputHandler.CalcVec(Vector2.right,m_CosVal,m_SinVal);

            // 向量跟坐标的计算是反向的，向量正常算。坐标反向 才能转进去
            DtAngle = -DtAngle;
            m_CosVal = LDMath.Cos(DtAngle);
            m_SinVal = LDMath.Sin(DtAngle);
            ForwardMove(0);

        }
        public override void OnDUpdate(float dt)
        {
            if (!m_Bullet.Sleeped)
            {
                m_CurTime = m_CurTime + dt;
                if (LDFightTools.GreaterOrEqualThenZero(m_LiveTime + Mathf.Min(m_BounceTimes * m_BounceAddTime, m_BounceMaxAddTime), m_CurTime))
                {
                    if (m_Speed > 0)
                    {
                        CalcForward();
                        ForwardMove(dt);
                    }
                    TryRayCast();
                }
                else
                {
                    m_Bullet.RecycleBullet(LDRecycleReason.TimeOver);
                }
            }
        }
        private void CalcForward()
        {
            if (!m_PveMode)
            {

                // 这个地方 记得看看
                Vector2 vectorCurPos = UiTools.WorldToScreen(m_Bullet.transform.GetPoisition());

                if (vectorCurPos.x >= Screen.width || vectorCurPos.x <= 0 ||
                   vectorCurPos.y >= Screen.height || vectorCurPos.y <= 0)
                {
                    Vector3 dtPos = m_Bullet.transform.GetPoisition();
                    if (m_Bullet.AtkData.SrcObj != null)
                    {
                        dtPos = m_Bullet.AtkData.SrcObj.transform.GetPoisition() - dtPos;
                    }
                    m_Bullet.transform.SetForward(dtPos);
                    return;
                }

                Vector2 vector2 = UiTools.WorldToScreen(m_Bullet.transform.GetPoisition() + m_Bullet.transform.GetForward());
                if (vector2.x >= Screen.width)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Back));
                    Bounce();
                }
                else if (vector2.x <= 0)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Forward));
                    Bounce();
                }
                else if (vector2.y >= Screen.height)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Right));
                    Bounce();
                }
                else if (vector2.y <= 0)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Left));
                    Bounce();
                }
            }
            else if(m_KcpMode)
            {
                Vector3 newPos = m_Bullet.transform.GetPoisition() + m_Bullet.transform.GetForward();
                newPos = LDInputHandler.CalcVec(newPos, m_CosVal, m_SinVal);
                if (newPos.x > 7)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Back));
                    Bounce();
                }
                else if (newPos.x < -7)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Forward));
                    Bounce();
                }
                else if (newPos.z > 15)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Right));
                    Bounce();
                }
                else if (newPos.z < -17)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Left));
                    Bounce();
                }
                LDFightTools.UnifiedTransform(m_Bullet.transform);
            }
            else
            {
                Vector3 newPos = m_Bullet.transform.GetPoisition() + m_Bullet.transform.GetForward();
                if (m_Bullet.AtkData.SrcObj != null)
                {
                    newPos = newPos - m_Bullet.AtkData.SrcObj.transform.GetPoisition();
                }
                newPos = LDInputHandler.CalcVec(newPos, m_CosVal, m_SinVal);
                if (newPos.x > 7.5f)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Back));
                    Bounce();
                }
                else if(newPos.x < -7.5f)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Forward));
                    Bounce();
                }
                else if(newPos.z > 19)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Right));
                    Bounce();
                }
                else if(newPos.z < -12)
                {
                    m_Bullet.transform.SetForward(Vector3.Reflect(-m_Bullet.transform.GetForward(), m_Left));
                    Bounce();
                }
                LDFightTools.UnifiedTransform(m_Bullet.transform);


            }
        }
        private void Bounce()
        {
            m_BounceTimes++;
            m_Bullet.TriggerScreenBounce();
        }
        private void ForwardMove(float dt)
        {
            m_Bullet.transform.TranslateForwardM(m_Speed * dt);
            Vector3 pos = m_Bullet.transform.GetPoisition();
            pos.y = Mathf.Max(pos.y, m_MinY);
            pos.y = Mathf.Min(pos.y, m_MaxY);
            m_Bullet.transform.SetPoisition(pos);
            LDFightTools.UnifiedTransformPos(m_Bullet.transform);
        }
    }
}
