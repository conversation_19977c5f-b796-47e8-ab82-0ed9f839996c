using LD.Skip;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public  abstract partial class LDAtkBullet : LDBaseBullet
    {
        public LDBaseMoveMode BulletMove { set; get; }
        private Dictionary<LDSceneObj, bool> m_TriggerObj = new Dictionary<LDSceneObj, bool>();
        private int m_DeadthEffectId = -1;
        private double m_CurHP = 0;
        private double m_MaxHp = 0;
        private bool m_HasHpUI = false;
        private LDBlinkUtil m_AIBlinkUtil = null;
        public override void Init(Transform firePoint, float dtAngleZ, float offset)
        {
            base.Init(firePoint, dtAngleZ, offset);
            m_CurHP = -1;
            m_MaxHp = 1;
        }
        public void OnTriggerEnter(Collider other)
        {
            OnTriggerEnter(other.gameObject) ;
        }      
        public override void OnDUpdate(float dt)
        {
            m_BulletEffect?.OnDUpdateSnake(dt);
            m_AIBlinkUtil?.OnDUpdate(dt);
            m_TriggerObj.Clear();
        }
        private void OnTriggerEnter(GameObject go)
        {
            // 倒计时子弹不做碰撞
            if (Sleeped || !m_Trigger || DYObject == LDGameObj.TimeBullet)
            {
                return;
            }
            TriggerHandleGameObj(go);
        }
        public void SetInfo(double hp,int deadEffectId)
        {
            m_DeadthEffectId = deadEffectId;
            m_CurHP = hp;
            m_MaxHp = hp;
            if (m_CurHP > 0)
            {
                 if (m_AIBlinkUtil == null)
                {
                    m_AIBlinkUtil = new LDBlinkUtil();
                }
                m_AIBlinkUtil.InitByBullet(gameObject);
                m_HasHpUI = true;
                //Global.gApp.gMsgDispatcher.Broadcast(MsgIds.AddNormalHP, GetGuid(), true, LDUICfg.BulletHp, transform.Find(LDFightConstVal.HpNode));
            }
        }
        public override bool OnHittedN(LDAtkBullet bullet)
        {
            if (!Sleeped && m_CurHP > 0)
            {
                m_AIBlinkUtil?.StartBlink();
                m_CurHP -= bullet.AtkData.BulletAtk;
                Global.gApp.gMsgDispatcher.Broadcast<long, float, long, long>(MsgIds.HpChanged, GetGuid(), (float)(m_CurHP / m_MaxHp),
                    Convert.ToInt64(m_CurHP), Convert.ToInt64(m_CurHP));
                if (m_CurHP <= 0)
                {
                    if (m_DeadthEffectId > 0)
                    {
                        Global.gApp.CurFightScene.gEffectMgr.AddDelayEffectNode(m_DeadthEffectId, transform.GetPoisition());
                    }
                    RecycleBullet(LDRecycleReason.Deadth);
                    return true;
                }
            }
            return base.OnHittedN(bullet);
        }
        public override void RecycleBullet(LDRecycleReason recycleReason)
        {
            base.RecycleBullet(recycleReason);
            if (m_HasHpUI)
            {
                m_HasHpUI = false;
                m_AIBlinkUtil?.ClearBlink();
                Global.gApp.gMsgDispatcher.Broadcast<long>(MsgIds.RemoveNormalHp, GetGuid());
            }
        }
    }
}
