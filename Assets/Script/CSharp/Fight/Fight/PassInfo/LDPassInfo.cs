
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDPassInfo:IUpdate
    {
        public LDPassData PassData { private set; get; }
        public LDFightData FightData { private set; get; }
        public double GoldCount { private set; get; }
        public bool UseKcp { set; get; } = false;
        private LDGameEndReason m_GameEndReason = LDGameEndReason.None;
        private bool m_Ended = false;
        private float m_FightTime = 0;
        private float m_MaxFightTime = 10;
        public LDPassInfo(LDFightData fightData)
        {
            FightData = fightData;
            m_MaxFightTime = GlobalCfg.Data.Get(LDGlobalConfigId.BattleTimeLimit).valueInt;
        }
        public void CreateMainPassInfo()
        {
            PassData = LDPassData.CreateDataByMain(FightData);
        }        
        public void CreatPveTeamPassInfo()
        {
            UseKcp = true;
            PassData = LDPassData.CreateDataByPveTeam(FightData);
        }      
        public void CreateGuidPassInfo()
        {
            PassData = LDPassData.CreateGuidPassInfo(FightData);
        }       
        public void CreateSessionTowerGuidPassInfo()
        {
            PassData = LDPassData.CreateSessionTowerGuidPassInfo(FightData);
        }
        public void CreateTowerPassInfo()
        {
            PassData = LDPassData.CreateDataByTower(FightData);
        }
        public void CreateCoinPassInfo()
        {
            PassData = LDPassData.CreateDataByCoin(FightData);
        }
        public void CreateStrategyPassInfo()
        {
            PassData = LDPassData.CreateDataByExpedition(FightData);
        }
        public void CreateSeasonPassInfo()
        {
            PassData = LDPassData.CreateDataBySeason(FightData);
        }
        public void CreateGuildPassInfo()
        {
            PassData = LDPassData.CreateDataByGuild(FightData);
        }
        public void CreateArenaPassInfo()
        {
            PassData = LDPassData.CreateDataByArena(FightData);
        }
        public void CreateGuildLeaguePassInfo()
        {
            PassData = LDPassData.CreateDataByGuildLeague(FightData);
        }
        public void CreateLeaguePvePassInfo()
        {
            PassData = LDPassData.CreateDataByGuildLeaguePve(FightData);
        }
        public void CreateCSArenaPassInfo()
        {
            PassData = LDPassData.CreateDataByCSArena(FightData);
        }
        public void CreateActScorePassInfo()
        {
            PassData = LDPassData.CreateDataByActScore(FightData);
        }
        public void OnDUpdate(float dt)
        {
            m_FightTime += dt;
            if(m_FightTime >= m_MaxFightTime)
            {
                Global.gApp.CurFightScene.gPassHandler.TryGameLose(LDGameEndReason.TimeLimit);
            }
        }
        public void UpTime(float time)
        {
            m_FightTime = time;
            OnDUpdate(0);
        }
        public float GetTime()
        {
            return m_FightTime;
        }

        public void GainGold(double goldCount)
        {
            GoldCount += goldCount;
        }
        public bool GameEnded()
        {
            return m_Ended;
        }
        public void GameWin(LDGameEndReason gameEndReason)
        {
            if (m_Ended) { return; }

            Time.timeScale = 1;
            LDRecordData.ClearRecordData();

            Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.MaskAllNode.gameObject.SetActive(true);
            m_GameEndReason = gameEndReason;
            m_Ended = true;
            Global.gApp.CurFightScene.GameEndDeal1();
            Global.gApp.CurFightScene.PauseOnly();
            Global.gApp.CurFightScene.gWaveMgr.SetRenderTimeScale(1);
            float timeSale = 1;
            if (gameEndReason == LDGameEndReason.Guid1050)
            {
                timeSale = 0.1f;
            }

            Global.gApp.gFrameTimerMgrRender.AddFrameTimer(1, 1, (float a,bool b) =>
            {
                Global.gApp.CurFightScene.gWaveMgr.AllAIDead();
                for (int i = 0; i < 10; i++)
                {
                    Global.gApp.CurFightScene.gPlayerMgr.OnAICacheUpdate(LDFightScene.DtUpdate);
                    Global.gApp.CurFightScene.gEffectMgr.OnDUpdate(LDFightScene.DtUpdate);
                }

                // 让死亡动画播完
                Global.gApp.CurFightScene.gWaveMgr.SetRenderTimeScale(1);
                Global.gApp.CurFightScene.GetLocalHero().HeroAnimCtrol.PlayWinAnim();
                Global.gApp.gTimerMgr.AddTimer(1f * timeSale, 1, DelayGainProp);
                Global.gApp.gTimerMgr.AddTimer(2.5f * timeSale, 1, DelayWin);
            });
        }
        private void DelayGainProp(float dt, bool end)
        {
            Global.gApp.CurFightScene.gPropMgr.TryGainEndtems(false);

        }
        private void DelayWin(float dt,bool end)
        {
            Global.gApp.gAudioSource.Pause(true);
            Global.gApp.CurFightScene.GameEndDeal2();
            Global.gApp.CurFightScene.gPassHandler.OpenFightWinUI();
            LDSDKEvent.SendGameEnd(LDGameEnd.GameWin);
        }
        public void MonsterDeadth()
        {
            int killMonsterCount = Global.gApp.CurFightScene.gWaveMgr.KillAICount;
            Global.gApp.CurFightScene.gFightUI.FreshMonsterDeadthText(killMonsterCount);
        }
        public LDGameEndReason GetGameEndReason()
        {
            return m_GameEndReason;
        }

        public void GameLose(LDGameEndReason gameEndReason)
        {
            if (m_Ended) { return; }
            Time.timeScale = 1;
            LDRecordData.ClearRecordData();
            Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.MaskAllNode.gameObject.SetActive(true);

            m_GameEndReason = gameEndReason;
            m_Ended = true;
            Global.gApp.CurFightScene.GameEndDeal1();
            Global.gApp.CurFightScene.PauseOnly();

            Global.gApp.gFrameTimerMgrRender.AddFrameTimer(1, 1, (float a, bool b) =>
            {
                if (m_GameEndReason != LDGameEndReason.GiveUp)
                {
                    Global.gApp.CurFightScene.gPropMgr.TryGainEndtems(false);
                    LDSDKEvent.SendGameEnd(LDGameEnd.GameLose);
                    Global.gApp.gTimerMgr.AddTimer(2, 1, DelayLose);
                }
                else
                {
                    Global.gApp.CurFightScene.gPropMgr.TryGainEndtems(true);
                    LDSDKEvent.SendGameEnd(LDGameEnd.GameGiveUp);
                    Global.gApp.gTimerMgr.AddTimer(0.2f, 1, DelayLose);
                }
            });
        }
        private void DelayLose(float dt,bool end)
        {
            Global.gApp.gAudioSource.Pause(true);
            Global.gApp.CurFightScene.GameEndDeal2();
            Global.gApp.CurFightScene.gPassHandler.OpenFightLoseUI();
        }
    }
}