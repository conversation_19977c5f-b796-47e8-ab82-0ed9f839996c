using LD.Skill;

namespace LD
{
    public class LDAISkillState : LDAIBaseState
    {
        private LDAISkillBase m_CurSkill;
        public void SetCurSkill(LDAISkillBase skillBase)
        {
            m_CurSkill = skillBase;
            skillBase.SetExcuteState(this);
        }
        /// <summary>
        /// 是正常技能，普通也算成了 技能，但是能打断
        /// </summary>
        /// <returns></returns>
        public bool IsNormalSkill()
        {
            return m_CurSkill.AISkillItem.skillType != LDAISkillTypes.normalAtk;
        }
        public string GetSkillType()
        {
            return m_CurSkill.AISkillItem.skillType;
        }

        public void UseSkillEnd(LDAISkillBase dYAISkillBase)
        {
            m_AIStateMachine.ExitSkill();
        }
        public override void OnHitted(LDDamageDataTemp damageDataTemp)
        {
            if(m_CurSkill != null)
            {
                m_CurSkill.OnHitted(damageDataTemp);
            }
        }
        protected override void OnEnterImp()
        {
            m_CurSkill.OnEnter();
        }
        protected override void OnRUpdate(float dt)
        {
            m_CurSkill.OnDUpdate(dt);
        }
        public override bool CanBeatBack()
        {
            return false;
        }
        protected override void OnExitImp()
        {
            m_Monster.MoveCtroller.StopMove();
            m_CurSkill.OnExit();
        }

    }
}
