using LD.Skip;
using UnityEngine;

namespace LD.Skill
{
    public class LDShootAtkSkill : LDAISkillBase
    {
        enum LDState
        {
            Step1,
            Step2,
            Step3,
            End,
        }
        private float m_CurTime = 0;
        private int m_BulletId = -1;
        private int m_WarningEffect = -1;
        private int m_AIMEffect = -1;// 瞄准
        private int m_FireEffect = -1;
        private double m_EffectExternLiveTime = 0;

        private LDState m_State = LDState.Step1;
        private Transform m_FirePoint = null;
        private Vector3 m_TargetPos;
        public override void OnEnter()
        {
            base.OnEnter();
            m_CurTime = 0;
            StopMoveAndResetAnimSpeed();;
            PlayAnim(0);
            ParseSkillParamInt(0, out m_BulletId);
            ParseSkillParam(1, out m_EffectExternLiveTime);
            ParseWarningParamInt(0, out m_WarningEffect);
            ParseWarningParamInt(1, out m_FireEffect);
            ParseWarningParamInt(2, out m_AIMEffect);
            if (m_FirePoint == null)
            {
                m_FirePoint = GameObject.Instantiate(m_FirePoints[0], m_FirePoints[0].transform.parent).transform;
            }
            m_State = LDState.Step1;
            SkillState.FaceTarget();
            m_TargetPos = m_AIMgr.GetLockPos();
            AddWarningEffect();
        }
        public override void OnDUpdate(float dt)
        {
            m_CurTime += dt;
            if (m_State == LDState.Step1)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[0]))
                {
                    m_CurTime -= (float)AISkillItem.totalTime[0];
                    PlayAnim(1);
                    m_State = LDState.Step2;
                    AddAIMEffect();
                }
            }
            else if (m_State == LDState.Step2)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[1]))
                {
                    PlayAnim(2);
                    m_CurTime -= (float)AISkillItem.totalTime[1];
                    m_State = LDState.Step3;
                    AddBullet();
                }
            }
            else if(m_State == LDState.Step3)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[2]))
                {
                    m_State = LDState.End;
                    m_CurTime = 0;
                    ReleaseSkillEnd();
                }
            }

        }
        private void AddWarningEffect()
        {
            if(m_WarningEffect > 0)
            {
                LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(m_WarningEffect);
                effectNode.SetLiveTime((float)(AISkillItem.totalTime[0] + m_EffectExternLiveTime));
                //effectNode.transform.localPosition = m_TargetPos;
                effectNode.transform.SetPoisition(m_Monster.transform.GetPoisition());
                effectNode.transform.SetForward(m_Monster.MoveCtroller.RotateNode);
                AddEffectNode(effectNode);
            }

        }
        //private void AdapterFirePoint()
        //{
        //    LDSceneObj lockObj = m_AIMgr.GetLockObj();
        //    if (lockObj != null)
        //    {
        //        m_FirePoint.transform.forward = lockObj.transform.GetPoisition() + Vector3.up - m_FirePoint.transform.GetPoisition();
        //    }
        //}
        private void AddBullet()
        {
            if (m_BulletId > 0)
            {
                LDTrackBullet trackBullet = m_Monster.BulletEmitter.GetBullet(m_BulletId, m_Monster.AIData.GetAtk());
                trackBullet.AtkData.MulSkillParam(AISkillItem.DamageParam);
                trackBullet.Init(m_FirePoint, 0, 0);
                trackBullet.SetBezierEndPosCalcController(m_TargetPos);
            }
            if (m_FireEffect > 0)
            {
                LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(m_FireEffect);
                effectNode.transform.SetParent(m_FirePoint);
                effectNode.transform.localEulerAngles = Vector3.zero;
                effectNode.transform.localPosition = Vector3.zero;
                AddEffectNode(effectNode);
            }
        }
        private void AddAIMEffect()
        {
            if (m_AIMEffect > 0)
            {
                LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(m_AIMEffect);
                effectNode.transform.SetParent(m_FirePoint);
                effectNode.transform.localEulerAngles = Vector3.zero;
                effectNode.transform.localPosition = Vector3.zero;
                AddEffectNode(effectNode);
            }
        }
        public override void OnExit()
        {
            m_Monster.MoveCtroller.StopMove();
            base.OnExit();
        }
    }
}
