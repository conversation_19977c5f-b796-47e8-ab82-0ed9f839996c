using UnityEngine;

namespace LD.Skill
{
    public class LDAIShootMultipleSkill : LDAISkillBase
    {
        enum LDState
        {
            Step1,
            Step2,
            Step3,
            End,
        }
        private LDState m_State = LDState.Step1;
        private float m_CurTime = 0;
        private int m_FireEffect = -1;

        private int m_BulletID = -1;
        private int m_Curves = -1;
        private double m_CurvesDtAngle = 1;
        private int m_BulletCount = 1;
        private double m_PosOffset = 1;

        public override void OnEnter()
        {
            base.OnEnter();
            m_CurTime = 0;
            StopMoveAndResetAnimSpeed();
            PlayAnim(0); 
            ParseWarningParamInt(0, out m_FireEffect);

            ParseSkillParamInt(0, out m_BulletID);
            ParseSkillParamInt(1, out m_Curves);
            ParseSkillParam(2, out m_CurvesDtAngle);
            ParseSkillParamInt(3, out m_BulletCount);
            ParseSkillParam(4, out m_PosOffset);

            m_State = LDState.Step1;
            SkillState.FaceTarget();
        }
        public override void OnDUpdate(float dt)
        {
            m_CurTime += dt;
            if (m_State == LDState.Step1)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[0]))
                {
                    m_CurTime -= (float)AISkillItem.totalTime[0];
                    PlayAnim(1);
                    m_State = LDState.Step2;
                }
            }
            else if (m_State == LDState.Step2)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[1]))
                {
                    m_CurTime -= (float)AISkillItem.totalTime[1];
                    m_State = LDState.Step3;
                    PlayAnim(2);
                    AddBullet();
                }
            }
            else if(m_State == LDState.Step3)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[2]))
                {
                    m_State = LDState.End;
                    m_CurTime = 0;
                    ReleaseSkillEnd();
                }
            }

        }
        private void AddBullet()
        {
            foreach (Transform firePoint in m_FirePoints)
            {
                if (m_FireEffect > 0)
                {
                    LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(m_FireEffect);
                    effectNode.transform.SetParent(firePoint);
                    effectNode.transform.localEulerAngles = Vector3.zero;
                    effectNode.transform.localPosition = Vector3.zero;
                }
                float dtAngleZ = -(float)m_CurvesDtAngle * (m_Curves - 1) / 2;
                double atk = m_Monster.AIData.GetAtk();
                for (int index = 0; index < m_Curves; index = index + 1)
                {
                    float Offset = -(float)m_PosOffset * (m_BulletCount - 1) / 2; ;
                    for (int pIndex = 0; pIndex < m_BulletCount; pIndex = pIndex + 1)
                    {
                        LDTrackBullet trackBullet = m_Monster.BulletEmitter.GetBullet(m_BulletID, atk);
                        trackBullet.AtkData.MulSkillParam(AISkillItem.DamageParam);
                        trackBullet.Init(firePoint, dtAngleZ, Offset);
                        Offset = Offset + (float)m_PosOffset;
                    }
                    dtAngleZ = dtAngleZ + (float)m_CurvesDtAngle;
                }
            }
        }
        public override void OnExit()
        {
            m_Monster.MoveCtroller.StopMove();
            base.OnExit();
        }
    }
}
