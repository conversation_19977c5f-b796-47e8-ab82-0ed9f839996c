using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD.Skill
{
    public class LDAIDashDeathSkill : LDAISkillBase
    {
        private enum DashStep
        {
            None,
            Start,
            Dash,
            End,
        }
        private float m_CurTime = 0;
        private float m_StartSpeed = 0;
        private float m_EndSpeed = 0;
        private int m_DashBulletId = 0;
        private int m_WarninEffect = -1;
        private int m_ChargeEffect = -1;
        private int m_DashEffect = -1;
        private Vector3 m_SpeedVec;
        private DashStep m_State = DashStep.Start;

        public override void OnEnter()
        {
            base.OnEnter();
            m_CurTime = 0;
            m_State = DashStep.Start;
            ParseSkillParam(0, out m_StartSpeed);
            ParseSkillParam(1, out m_EndSpeed);
            ParseSkillParamInt(2, out m_DashBulletId);
            ParseWarningParamInt(0, out m_WarninEffect);
            ParseWarningParamInt(1, out m_ChargeEffect);
            ParseWarningParamInt(2, out m_DashEffect);

            StopMoveAndResetAnimSpeed();
            m_Monster.SetBodyBulletEnable(false);
            SkillState.FaceTargetByBornNode();

            AddWarningEffect();
            PlayAnim(0);
            m_SpeedVec = m_Monster.MoveCtroller.RotateNode.GetForward();
            m_Monster.MoveCtroller.SetMoveState(false);
        }
        public override void OnDUpdate(float dt)
        {
            m_CurTime += dt;
            if (m_State == DashStep.Start)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[0]))
                {
                    m_CurTime -= (float)AISkillItem.totalTime[0];
                    m_State = DashStep.Dash;
                    m_Monster.MoveCtroller.SetMoveState(true);
                    AddBullet();
                    PlayAnim(1);
                }
            }
            else if (m_State == DashStep.Dash)
            {
                float time1 = (float)AISkillItem.totalTime[1];
                float rate = m_CurTime / time1;
                m_Monster.MoveCtroller.MoveSimple(m_SpeedVec, Mathf.Lerp(m_StartSpeed, m_EndSpeed, rate));
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,time1))
                {
                    m_Monster.MoveCtroller.StopMove();
                    m_CurTime -= time1;
                    m_State = DashStep.End;
                    PlayAnim(2);
                }
            }
            else if (m_State == DashStep.End)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime,AISkillItem.totalTime[2]))
                {
                    m_CurTime -= (float)AISkillItem.totalTime[2];
                    m_CurTime = 0;
                    m_State = DashStep.None;
                    m_Monster.OnDeathBySelf();
                }
            }

        }
        private void AddWarningEffect()
        {
            if (m_WarninEffect > 0)
            {
                LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(m_WarninEffect);
                effectNode.transform.SetPoisition(m_Monster.transform.GetPoisition());
                effectNode.transform.SetForward(m_Monster.MoveCtroller.RotateNode);
                effectNode.SetLiveTime((float)(AISkillItem.totalTime[0]));
            }
            if(m_ChargeEffect > 0)
            {
                LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(m_ChargeEffect);
                //if (m_FirePoints.Count > 0)
                //{
                //    effectNode.transform.SetParent(m_FirePoints[0], false);
                //    effectNode.transform.localPosition = Vector3.zero;
                //}
                //else
                {
                    effectNode.transform.SetParent(m_Monster.transform, false);
                    effectNode.transform.localPosition = Vector3.zero;
                }
                effectNode.transform.SetForward(m_Monster.MoveCtroller.RotateNode);
                effectNode.SetLiveTime((float)(AISkillItem.totalTime[0]));
            }
        }
        private void AddBullet()
        {
            foreach (Transform firePont in m_FirePoints)
            {
                LDTrackBullet trackBullet = m_Monster.BulletEmitter.GetBullet(m_DashBulletId, m_Monster.AIData.GetAtk());
                trackBullet.AtkData.MulSkillParam(AISkillItem.DamageParam);
                trackBullet.Init(firePont, 0, 0);
                trackBullet.transform.localEulerAngles = Vector3.zero;
                trackBullet.transform.SetParent(firePont.transform,true);
            }

            if (m_DashEffect > 0)
            {
                LDEffectNodeContent effectNode = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(m_DashEffect);
                //if (m_FirePoints.Count > 0)
                //{
                //    effectNode.transform.SetParent(m_FirePoints[0], false);
                //    effectNode.transform.localPosition = Vector3.zero;
                //}
                //else
                {
                    effectNode.transform.SetParent(m_Monster.transform, false);
                    effectNode.transform.localPosition = Vector3.zero;
                }
                effectNode.transform.SetForward(m_Monster.MoveCtroller.RotateNode);
                effectNode.SetLiveTime((float)(AISkillItem.totalTime[1]));
            }
        }
        public override void OnExit()
        {
            m_Monster.SetBodyBulletEnable(true);
            m_Monster.MoveCtroller.StopMove();
            base.OnExit();
        }
    }
}

