using System.Collections.Generic;
using UnityEngine;
namespace LD
{

    public class LDAIAnimCtrol : IUpdate
    {
        private Transform m_AnimNode = null;
        private Animator m_Animator = null;
        private float m_AnimSpeed = 1;
        private GPUSkinningPlayerMono m_GpuAnimator = null;
        private string m_AnimName = string.Empty;
        private float m_TimeScale = 1;
        private bool m_AnimatorManual = false;
        public LDAIAnimCtrol(Transform animNode)
        {
            m_AnimNode = animNode;
            GenerateAnim();
            SetRenderTimeScale(1);
        }
        private void GenerateAnim()
        {
            m_GpuAnimator = m_AnimNode.gameObject.GetComponentInChildren<GPUSkinningPlayerMono>(true);
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.Init();
                m_GpuAnimator.ShadowViewStateChanged(true);
                m_GpuAnimator.SetLightDic(Global.gApp.CurFightScene.LightDic);
                return;
            }
            m_Animator = m_AnimNode.gameObject.GetComponent<Animator>();
            if (m_AnimatorManual)
            {
                m_Animator.playableGraph.SetTimeUpdateMode(UnityEngine.Playables.DirectorUpdateMode.Manual);
                RandomUpDt();
            }
        }
        public void Init()
        {
            if(m_Animator != null)
            {
                m_Animator.enabled = Global.gApp.CurFightScene.ShowAnimator;
            }
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.enabled = true;
                m_GpuAnimator.Born();
            }
            Resume();
            PlayAnim(LDAnimName.Idle, -1, Random.Range(0.0f, 1.0f));
            OnDUpdate(0.033f);
        }
        public GPUSkinningPlayerMono GetGpuAnimator()
        {
            return m_GpuAnimator;
        }
        public void SetAnimSpeedBySpeed(float speed)
        {
            if (m_AnimSpeed > -0.01f)
            {
                SetAnimSpeed(speed);
            }
        }
        public void SetAnimSpeed(float speed)
        {
            m_AnimSpeed = speed;
            speed = Mathf.Max(0, speed);
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.SetSpeed(speed);
            }
            else if (m_Animator != null)
            {
                m_Animator.speed = speed;
            }
        }
        public void PlayAnim(string anim, int layer = -1, float normalize = 0)
        {
            if (string.IsNullOrEmpty(anim))
            {
                return;
            }
            if(m_AnimName.Equals(anim))
            {
                return;
            }
            m_AnimName = anim;
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.PlayAnim(anim, normalize);
            }
            else if (m_Animator != null)
            {
                m_Animator.Play(anim, layer, normalize);
            }
        }
        public void CrossAnim(string anim,float fadeTime, int layer = -1, float normalize = 0)
        {
            if (m_AnimName.Equals(anim))
            {
                return;
            }
            m_AnimName = anim;
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.PlayAnim(anim, normalize);
            }
            else if (m_Animator != null)
            {
                m_Animator.CrossFade(anim, fadeTime, layer, normalize);
            }
        }
        public void PlayBeatBack()
        {

        }


        // 先简单实现 不做引用计数。后面有需求在改 也不用 moveKey 弄了。
        public void Pause()
        {
            SetAnimSpeed(-1);
 
        }
        public void Resume()
        {
            SetAnimSpeed(1);
            SetRenderTimeScale(m_TimeScale);
        }
        public void RecysleSelf()
        {
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.enabled = false;
            }
        }
        public void SetRenderTimeScale(float timeScale)
        {
            m_TimeScale = timeScale;
            SetAnimSpeed(timeScale);
        }

        public void ShadowViewStateChanged(bool showView)
        {
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.ShadowViewStateChanged(showView);
            }
        }
        public Animator GetAnimator()
        {
            return m_Animator;
        }
        private float m_CurTime = 0;
        private float m_DtUpTime = LDFightScene.DtUpdate;
        private void RandomUpDt()
        {
            int randomVal = Random.Range(0,10000);
            m_DtUpTime = randomVal % 2 * (0.0166667f);
        }
        public void OnDUpdate(float dt)
        {
            if (m_AnimatorManual)
            {
                m_CurTime += dt;
                if (m_CurTime >= m_DtUpTime)
                {
                    m_CurTime -= m_DtUpTime;
                    m_Animator.Update(m_DtUpTime);
                }
            }
            if (m_GpuAnimator != null)
            {
                m_GpuAnimator.OnDUpdate(dt);
            }
        }
        public void OnDestroySelf()
        {
            if (m_Animator != null)
            {
                m_Animator.enabled = false;
            }
        }
    }
}
