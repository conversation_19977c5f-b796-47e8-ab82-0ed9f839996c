using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDGasBucketElement : LDLockElement
    {
        public override void Init(LDElementMgr elementMgr, TrapItem citiaoTrapItem)
        {
            base.Init(elementMgr, citiaoTrapItem);
            DYObject = LDGameObj.GasBucketFence;
        }
        public override bool OnHittedN(LDAtkBullet bullet)
        {
            base.OnHittedN(bullet);
            OnDamage((bullet.AtkData.BulletAtk + bullet.AtkData.Param_SkillDamage + bullet.AtkData.Param_SkillDamageBase )* bullet.AtkData.DamageParam);
            if(m_HP <= 0)
            {
                OnDeadth();
            }
            return true;
        }
    }
}
