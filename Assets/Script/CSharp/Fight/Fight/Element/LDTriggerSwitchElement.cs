using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class LDTriggerSwitchElement : LDLockElement
    {
        public List<int> Effect = new List<int>();
        public int AddTime = 0;
        public float ProgressTime = 1;
        private float m_CurTime = 0;
        private Transform m_TriggerNode;
        private Transform m_OnNode;
        private Transform m_OffNode;
        private Transform m_OffInNode;
        private Transform m_OffOutNode;
        private SphereCollider m_SphereCollider;
        private BoxCollider m_BoxCollider;
        private SwitchElement m_SwitchElementUI;
        private Transform m_FlowNode;
        public override void Init(LDElementMgr elementMgr, TrapItem citiaoTrapItem)
        {
            base.Init(elementMgr, citiaoTrapItem);
            DYObject = LDGameObj.SwitchElement;

            m_FlowNode = transform.Find(LDFightConstVal.HpNode);
            m_OnNode = transform.Find("On");
            m_OffNode = transform.Find("Off");
            m_OffInNode = m_OffNode.Find("In");
            m_OffOutNode = m_OffNode.Find("Out");
            m_TriggerNode = transform.Find("Trigger");
            m_OnNode.gameObject.SetActive(false);
            m_OffNode.gameObject.SetActive(true);

            m_SphereCollider = m_TriggerNode.gameObject.GetComponent<SphereCollider>();
            m_BoxCollider = m_TriggerNode.gameObject.GetComponent<BoxCollider>();
            if (ProgressTime > 0)
            {
                GameObject towerProgress = Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.SwitchElement,
                  ResSceneType.NormalRes, Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.HpNode.rectTransform);

                m_SwitchElementUI = towerProgress.GetComponent<SwitchElement>();
                m_SwitchElementUI.Init(-1, m_FlowNode, Global.gApp.CurFightScene.gFightUI.ParentCanvas);
                m_SwitchElementUI.gameObject.SetActive(true);
            }
            ProgressTime = Mathf.Max(ProgressTime,0.01f);
            OnDUpdate(0);
        }
        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            if(m_CurTime >= ProgressTime)
            {
                return;
            }
            bool inRange = false;
            if (m_SphereCollider != null)
            {
                if (LDFightTools.RaycastHeroSphere(transform.GetPoisition(), m_SphereCollider.radius) > 0)
                {
                    inRange = true;
                }
            }
            else if(m_BoxCollider != null)
            {
                if (LDFightTools.RaycastHeroBox(m_BoxCollider) > 0)
                {
                    inRange = true;
                }
            }
            if(inRange)
            {
                m_CurTime += dt;
                if (m_SwitchElementUI != null)
                {
                    m_SwitchElementUI.progress_in.gameObject.SetActive(true);
                    m_SwitchElementUI.icon.gameObject.SetActive(false);
                    m_SwitchElementUI.icon_in.gameObject.SetActive(true);
                }
                if(m_OffInNode != null)
                {
                    m_OffInNode.gameObject.SetActive(true);
                }
                if(m_OffOutNode != null)
                {
                    m_OffOutNode.gameObject.SetActive(false);
                }
            }
            else
            {
                m_CurTime -= dt;
                if (m_SwitchElementUI != null)
                {
                    m_SwitchElementUI.icon.gameObject.SetActive(true);
                    m_SwitchElementUI.icon_in.gameObject.SetActive(false);
                    m_SwitchElementUI.progress_in.gameObject.SetActive(false);
                }

                if (m_OffInNode != null)
                {
                    m_OffInNode.gameObject.SetActive(false);
                }
                if (m_OffOutNode != null)
                {
                    m_OffOutNode.gameObject.SetActive(true);
                }
            }
            m_CurTime = Mathf.Max(m_CurTime, 0);
            float progress = m_CurTime / ProgressTime;
            int progressInt = EZMath.IDFloatToInt(progress * 100);
            if (m_SwitchElementUI != null)
            {
                m_SwitchElementUI.progress.image.fillAmount = progress;
                m_SwitchElementUI.progress_in.image.fillAmount = progress;
                m_SwitchElementUI.txt_progress.text.text = progressInt + "%";
                if (progressInt >= 100 || progressInt < 1)
                {
                    m_SwitchElementUI.gameObject.SetActive(false);
                }
                else
                {
                    m_SwitchElementUI.gameObject.SetActive(true);
                }
            }
            if(progressInt >= 100)
            {
                m_OnNode.gameObject.SetActive(true);
                m_OffNode.gameObject.SetActive(false);

                Global.gApp.CurFightScene.GetFirstHeroPlayer().HeroMgr.MainRole.TryEffective(Effect);
                LDVictoryBase victoryBase = Global.gApp.CurFightScene.gVictoryMgr.GetVictoryItem();
                (victoryBase as LDKillSpecifiedTypeAITimeLimt).AddLimitTime(AddTime);
            }
        }
        public override bool OnHittedN(LDAtkBullet bullet)
        {
            return false;
        }
    }
}
