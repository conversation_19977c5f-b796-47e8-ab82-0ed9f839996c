using UnityEngine;

namespace LD
{
    public class LDFangXianElement : LDLockElement
    {
        private FangXianHp m_NormalAIHP;
        private Transform m_ModeNode;
        private Transform m_Step1Go;
        private Transform m_Step2Go;
        private Transform m_Step3Go;
        public double Damage = 0;
        private float m_Step1Percent = 0;
        private float m_Step2Percent = 0;
        public override void Init(LDElementMgr elementMgr, TrapItem citiaoTrapItem)
        {
            base.Init(elementMgr, citiaoTrapItem);
            DYObject = LDGameObj.GoldCar;
            elementMgr.FangXianElement = this;
            GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(LDGlobalConfigId.PveTeamMissionDefenseHp);
            m_Step1Percent = globalCfgItem.valueIntarray[0] / 10000.0f;
            m_Step2Percent = globalCfgItem.valueIntarray[1] / 10000.0f;

            m_ModeNode = transform.Find(LDFightConstVal.ModelNodeName);
            Transform hpNode = transform.Find(LDFightConstVal.HpNode);
            if(hpNode != null)
            {
                GameObject hpUINode = Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.FangXianHp,
                                  ResSceneType.NormalRes, Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.HpNode.rectTransform);
                m_NormalAIHP = hpUINode.GetComponent<FangXianHp>();
                m_NormalAIHP.Init(-1, hpNode, Global.gApp.CurFightScene.gFightUI.ParentCanvas);
                m_NormalAIHP.gameObject.SetActive(true);
                FreshHpProgress();
            }
            m_Step1Go = transform.Find("ModelNode/fangxian_01_shadai01");
            m_Step2Go = transform.Find("ModelNode/fangxian_01_shadai02");
            m_Step3Go = transform.Find("ModelNode/fangxian_01_shadai03");
            OnFreshState();
            Debug.Log("CarHP " + m_HP);
        }
        public override bool OnHittedN(LDAtkBullet bullet)
        {
            base.OnHittedN(bullet);
            double damage = (bullet.AtkData.BulletAtk * bullet.AtkData.Param_SkillDamage + bullet.AtkData.Param_SkillDamageBase) * bullet.AtkData.DamageParam;

            Damage  += damage;
            return true;
        }
        public void AplayHeroMaxHp(long maxHp)
        {
            m_MaxHP = maxHp;
            m_HP = maxHp;
            FreshHpProgress();
        }
        private void OnFreshState()
        {
            float hpPercent = (float)(1.0d * m_HP / m_MaxHP);
            if (hpPercent > m_Step1Percent)
            {
                m_Step1Go.gameObject.SetActive(true);
                m_Step2Go.gameObject.SetActive(false);
                m_Step3Go.gameObject.SetActive(false);
            }
            else if(hpPercent > m_Step2Percent)
            {
                m_Step1Go.gameObject.SetActive(false);
                m_Step2Go.gameObject.SetActive(true);
                m_Step3Go.gameObject.SetActive(false);
            }
            else 
            {

                m_Step1Go.gameObject.SetActive(false);
                m_Step2Go.gameObject.SetActive(false);
                m_Step3Go.gameObject.SetActive(true);
            }
        }
        public void FreshDamage(long damage)
        {
            long newHp = m_MaxHP - damage;
            if (m_HP != newHp)
            {
                m_HP = newHp;
                OnFreshState();
                FreshHpProgress();
                if (m_HP <= 0)
                {
                    OnDeadth();
                }
            }
        }    
        protected override void OnDeadth()
        {
            base.OnDeadth();
            if(m_NormalAIHP != null)
            {
                Global.gApp.gResMgr.DestroyGameObj(m_NormalAIHP.gameObject);
            }
            gameObject.SetActive(true);
            Global.gApp.CurFightScene.gPassHandler.TryGameLose(LDGameEndReason.FangXian);
        }
        public void SePosition(Vector3 poisition)
        {
            transform.SetPoisition(poisition);
        }
        public void SetForward(Vector3 forward)
        {
            m_ModeNode.SetForward(forward);
        }
        private void FreshHpProgress()
        {
            if (m_NormalAIHP != null)
            {
                m_NormalAIHP.SetHpPercent((float)(1.0d * m_HP / m_MaxHP));
                m_NormalAIHP.HpVal.text.text = UiTools.FormateMoney(m_HP);
            }
        }
        public override void RemoveSelf()
        {
            base.RemoveSelf();

        }
    }
}
