using System;
using UnityEngine;

namespace LD
{
    public class LDRollBossElement : LDLockElement
    {
        [SerializeField] private float m_MoveSpeed = 2f;
        [SerializeField] private float m_SecKillDis;
        [SerializeField] public float HeroReviceDis = 20f;
        private const string AniName_Attack = "tui";
        
        private Transform m_HeroNode;
        private Animator m_Anim;
        private LDHeroPlayer m_HeroPlayer;
        private bool m_Inited;


        public override void Init(LDElementMgr elementMgr, TrapItem citiaoTrapItem)
        {
            base.Init(elementMgr, citiaoTrapItem);
            UpdatePyhsicCompent(gameObject, false);
            m_HeroNode = transform.Find(LDFightConstVal.AIAnimNode);
            m_Anim = m_HeroNode.GetComponent<Animator>();
            if (Collider != null)
            {
                Collider.enabled = true;
            }
            m_HeroPlayer = Global.gApp.CurFightScene.GetLocalHero();
        }

        public void PlayBegin()
        {
            m_Inited = true;
            m_HeroPlayer = Global.gApp.CurFightScene.GetLocalHero();
        }

        private void Update()
        {
            UpdateSpeed(Vector3.forward * m_MoveSpeed);

            if (m_Inited && m_HeroPlayer != null && m_HeroPlayer.GetHeroState() != LDHeroState.Death)
            {
                if (m_HeroPlayer.transform.position.z - transform.position.z < m_SecKillDis)
                {
                    m_HeroPlayer.OnDeadth();
                }
            }
        }

        public void PlayRunAni()
        {
            if (m_Anim != null)
            {
                m_Anim.speed = 1;
                m_Anim.Play(AniName_Attack);
            }    
        }

        public override void SetTimeScale(float timeScale)
        {
            base.SetTimeScale(timeScale);
            
            if (m_Anim != null)
            {
                m_Anim.speed = timeScale;
            }

            if (LDFightTools.GreaterOrEqualThenZero(timeScale,  0.01f))
            {
                UpdateSpeed(Vector3.zero);
            }
        }

        public override void OnDestroySelf()
        {
            base.OnDestroySelf();
        }
    }
}