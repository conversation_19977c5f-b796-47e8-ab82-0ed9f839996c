using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDSnakeJointChain : MonoBehaviour
    {
        public LDJoint HeadLDJoint; // 链条的头部关节
        private Transform m_PreParentNode = null;
        public void Init(ParticleSystem particleSystem)
        {
            if (m_PreParentNode == null)
            {
                if(particleSystem != null)
                {
                    m_PreParentNode = particleSystem.transform;
                }
                else
                {
                    m_PreParentNode = transform.parent;
                }
            }
        }

        // 初始化关节链条，创建指定数量的关节并依次连接
        public void SetNodeScale(float scale)
        {
            LDJoint currentLDJoint = HeadLDJoint;
            while (currentLDJoint.NextLDJoint != null)
            {
                currentLDJoint.transform.SetParent(m_PreParentNode, true);
                currentLDJoint.transform.localScale = Vector3.one * scale;
                currentLDJoint = currentLDJoint.NextLDJoint;
            }
            currentLDJoint.transform.SetParent(m_PreParentNode, true);
            currentLDJoint.transform.localScale = Vector3.one * scale;
        }
        public void UpdateSnakeScale(float newScale)
        {
            LDJoint currentLDJoint = HeadLDJoint;
            while (currentLDJoint.NextLDJoint != null)
            {
                currentLDJoint.transform.SetParent(m_PreParentNode,true);
                currentLDJoint.transform.localScale = Vector3.one;
                currentLDJoint = currentLDJoint.NextLDJoint;
                currentLDJoint.Length = currentLDJoint.Length * newScale;
            }
            currentLDJoint.transform.SetParent(m_PreParentNode, true);
            currentLDJoint.transform.localScale = Vector3.one;
            currentLDJoint.Length = currentLDJoint.Length * newScale;


            currentLDJoint = HeadLDJoint;
            while (currentLDJoint.NextLDJoint != null)
            {
                currentLDJoint.transform.SetParent(Global.gApp.gBulletNode.transform, true);
                currentLDJoint = currentLDJoint.NextLDJoint;
            }
            currentLDJoint.transform.SetParent(Global.gApp.gBulletNode.transform, true);
        }
        public void InitializeChain()
        {
            HeadLDJoint.UpPos(transform.position);
            HeadLDJoint.transform.forward = transform.forward;
            LDJoint currentLDJoint = HeadLDJoint;

            while (currentLDJoint.NextLDJoint != null)
            {
                //if (currentLDJoint.Length < 0)
                {
                    // 计算下一个关节相对当前关节的方向向量
                    currentLDJoint.UpPos(currentLDJoint.transform.position);
                    float dx = currentLDJoint.NextLDJoint.transform.position.x - currentLDJoint.transform.position.x;
                    float dy = 0;
                    float dz = currentLDJoint.NextLDJoint.transform.position.z - currentLDJoint.transform.position.z;
                    // 简单模拟受重力影响，让下一个关节在垂直方向有一定位移（这里简化了很多物理因素）
                    //currentLDJoint.NextLDJoint.transform.position = new Vector3(currentLDJoint.NextLDJoint.transform.position.x, currentLDJoint.NextLDJoint.transform.position.y + 1);
                    // 限制长度，保持关节连接长度相对固定（简单的长度修正，实际更复杂）
                    float length = (float)Mathf.Sqrt(dx * dx + dy * dy + dz * dz);
                    currentLDJoint.Length = length;
                    currentLDJoint.transform.SetParent(Global.gApp.gBulletNode.transform, true);
                }
                //else
                //{
                //    currentLDJoint.transform.SetParent(Global.gApp.gBulletNode.transform, true);
                //    Vector3 newPos = currentLDJoint.transform.position - currentLDJoint.NextLDJoint.Length * currentLDJoint.transform.forward;
                //    currentLDJoint.NextLDJoint.UpPos(newPos);
                //    currentLDJoint.NextLDJoint.transform.forward = currentLDJoint.transform.forward;
                //}
                currentLDJoint = currentLDJoint.NextLDJoint;
            }
            currentLDJoint.transform.SetParent(Global.gApp.gBulletNode.transform,true);

        }
        // 简单模拟更新关节位置，这里仅简单模拟在重力作用下的摆动（很简化的示意）
        public void UpdateChain(float dt)
        {
            HeadLDJoint.UpPos(transform.position);
            HeadLDJoint.transform.forward = transform.forward;
            LDJoint currentLDJoint = HeadLDJoint;
            while (currentLDJoint.NextLDJoint != null)
            {
                // 计算下一个关节相对当前关节的方向向量
                float dx = currentLDJoint.NextLDJoint.transform.position.x - currentLDJoint.transform.position.x;
                float dy = currentLDJoint.NextLDJoint.transform.position.y - currentLDJoint.transform.position.y;
                float dz = currentLDJoint.NextLDJoint.transform.position.z - currentLDJoint.transform.position.z;
                // 简单模拟受重力影响，让下一个关节在垂直方向有一定位移（这里简化了很多物理因素）
                //currentLDJoint.NextLDJoint.transform.position = new Vector3(currentLDJoint.NextLDJoint.transform.position.x, currentLDJoint.NextLDJoint.transform.position.y + 1);
                // 限制长度，保持关节连接长度相对固定（简单的长度修正，实际更复杂）
                float length = (float)Mathf.Sqrt(dx * dx + dy * dy + dz * dz);
                if (LDFightTools.GreaterThenZero(length - currentLDJoint.Length))
                {
                    float ratio = currentLDJoint.Length / length;
                    Vector3 newPos = new Vector3(
                       currentLDJoint.transform.position.x + dx * ratio,
                       currentLDJoint.transform.position.y + dy * ratio,
                       currentLDJoint.transform.position.z + dz * ratio);

                    Vector3 dtPos = newPos - currentLDJoint.NextLDJoint.transform.position;
                    currentLDJoint.NextLDJoint.UpPos(newPos);
                    currentLDJoint.NextLDJoint.transform.forward = dtPos;
                }
                currentLDJoint = currentLDJoint.NextLDJoint;
            }
        }
    }

}
