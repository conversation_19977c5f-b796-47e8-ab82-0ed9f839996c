using System.Collections.Generic;

namespace LD
{
    public class LDSurvivorCageState : LDSurvivorStateBase
    {
        private List<string> m_CageAnimList = new List<string>()
        {
            LDAnimName.SurvivorAnim[LDAnimName.Cage1],
            LDAnimName.SurvivorAnim[LDAnimName.Cage2],
        };
        
        protected override void OnEnterImp()
        {
            m_Survivor.SAnimCtrol.PlayAnim(LDAnimName.SurvivorAnim[LDAnimName.Idle02]); 
            m_Survivor.MoveCtrol.SetAgentEnable(false);
            m_Survivor.MoveCtrol.StopMove();
            m_Survivor.MoveCtrol.SetMoveState(false);
            
            RandomAnim();
        }

        private void RandomAnim()
        {
            float time = m_StateMachine.RandomUtil.NextFloat(3f, 5f);
            Global.gApp.CurScene.TimerMgrRender.AddTimer(time, 1, (_,_) =>
            {
                int idx = m_StateMachine.RandomUtil.NextInt(0, m_CageAnimList.Count);
                m_Survivor.SAnimCtrol.PlayAnim(m_CageAnimList[idx]);
                Global.gApp.CurScene.TimerMgrRender.AddTimer(6f, 1, (_,_) =>
                {
                    RandomAnim();
                }); 
            });
        }

        protected override void OnRUpdate(float dt)
        {
        }

        protected override void OnExitImp()
        {
            m_Survivor.MoveCtrol.SetAgentEnable(false);
            m_Survivor.MoveCtrol.StopMove();
            m_Survivor.MoveCtrol.SetMoveState(false);
        }
    }
}