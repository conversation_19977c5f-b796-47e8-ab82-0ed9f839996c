using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDWave
    { 
        private enum LDBornMode
        {
            SceneArea = 1,  //场景固定点区域
            InCamera = 2,   //主角周围相机内
            OutCamera = 3,  // 主角周围相机外
            OutCamera2 = 4,  // 主角周围相机外
            FeiChong = 5,  // 飞虫
        } 
        private LDBornMode m_BornMode;
        private List<Transform> m_AreaBornNodes;
        private void InitBornInfo()
        {
            //默认是 场景点出怪
            m_BornMode = (LDBornMode)(WaveData.bornMode);
        }
        private void FindBornNode()
        {
            if (m_BornMode == LDBornMode.SceneArea)
            {
                FindSceneBornNode();
            }
            else if(m_BornMode == LDBornMode.InCamera)
            {
                m_InRoundRoleBornNodes = Global.gApp.CurFightScene.gMapData.GetInCameraBornNodes();
            }
            else if(m_BornMode == LDBornMode.OutCamera)
            {
                m_OutRoundRoleBornNodes = Global.gApp.CurFightScene.gMapData.GetOutCameraBornNodes();
            }
            else if (m_BornMode == LDBornMode.OutCamera2)
            {
                m_OutRoundRoleBornNodes2 = Global.gApp.CurFightScene.gMapData.GetOutCameraBornNodes2();
            }
            else if (m_BornMode == LDBornMode.FeiChong)
            {
                m_OutRoundRoleBornNodes2 = Global.gApp.CurFightScene.gMapData.GetOutCameraBornNodes2();
            }
        }
        private void FindSceneBornNode()
        {
            m_AreaBornNodes = new List<Transform>();
            foreach (string pointName in WaveData.nodeID)
            {
                Transform bornNode = WaveMgr.GetScenePoint(pointName);
                if (bornNode != null)
                {
                    m_AreaBornNodes.Add(bornNode);
                }
            }
        }
        private Transform GetHeroOutRoundBornNode()
        {
            LDBornTrigger[] bornTriggers = Global.gApp.CurFightScene.gMapData.GetOutCameraBornNodes();
            int bornNodeLength = bornTriggers.Length;
            int bornIndex = m_RandomUtil.NextInt(0, bornNodeLength);
            for (int i = 0; i < bornNodeLength; i++)
            {
                LDBornTrigger bornNode = bornTriggers[(bornIndex + i) % bornNodeLength];
                if (bornNode.GetIsOutMap())
                {
                    return bornNode.transform;
                }
            }
            return null;
        }

        private Transform GetHeroOutRoundBornNode2()
        {
            LDBornTrigger[] bornTriggers = Global.gApp.CurFightScene.gMapData.GetOutCameraBornNodes2();
            int bornNodeLength = bornTriggers.Length;
            int bornIndex = m_RandomUtil.NextInt(0, bornNodeLength);
            for (int i = 0; i < bornNodeLength; i++)
            {
                LDBornTrigger bornNode = bornTriggers[(bornIndex + i) % bornNodeLength];
                if (bornNode.GetIsOutMap())
                {
                    return bornNode.transform;
                }
            }
            return null;
        }
        private Transform GetHeroInRoundBornNode()
        {
            int bornNodeLength = m_InRoundRoleBornNodes.Length;
            int bornIndex = m_RandomUtil.NextInt(0, bornNodeLength);
            for (int i = 0; i < bornNodeLength; i++)
            {
                LDBornTrigger bornNode = m_InRoundRoleBornNodes[(bornIndex + i) % bornNodeLength];
                if (bornNode.GetIsOutMap())
                {
                    return bornNode.transform;
                }
            }
            return null;
        }

        private Transform GetAreaBornNode()
        {
            int bornNodeLength = m_AreaBornNodes.Count;
            int bornIndex = m_RandomUtil.NextInt(0, bornNodeLength);
            Transform bornNode = m_AreaBornNodes[bornIndex];
            return bornNode;
        }
        public Vector3 GetSceneAreaRandomPoint(Transform bornNode)
        {
            Vector3 localScale = bornNode.transform.localScale;
            float x = m_RandomUtil.NextFloat(-localScale.x / 2,localScale.x / 2);       
            float z = m_RandomUtil.NextFloat(-localScale.z / 2,localScale.z / 2);       
            Vector3 position = bornNode.transform.GetPoisition() + new Vector3(x,0,z);
            return LDFightTools.GetV3IntF(position);
        }
    }
}