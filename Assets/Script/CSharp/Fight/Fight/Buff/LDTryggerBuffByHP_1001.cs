
using System.Collections.Generic;

namespace LD
{

    public class LDTryggerBuffByHP_1001 : LDEventBuff
    {
        private float m_HpThreshold;
        private int m_TriggerTimes = 0;
        public override void Init(LDSceneObj srcObj, LDBuffMgr buffMgr, float buffTime, double val)
        {
            base.Init(srcObj, buffMgr, buffTime, val);
            m_HpThreshold = (float)GetParam()[1];
            m_TriggerTimes = 0;
            if (m_SrcMonster != null)
            {
                m_SrcMonster.AIData.AddHpChangeAct(OnHpChanged);
            }
        }

        private  void OnHpChanged(float hpPercent)
        {
            if (m_TriggerTimes == 0)
            {
                if (LDFightTools.GreaterOrEqualThenZero(m_HpThreshold - hpPercent))
                {
                    m_TriggerTimes++;
                    double[] buffVal = GetParam();
                    for (int i = 2; i < buffVal.Length; i++)
                    {
                        m_BuffMgr.AddBuff(EZMath.IDDoubleToInt(buffVal[i]),m_SrcObj);
                    }
                }
            }
        }
        public override bool BuffDestroy()
        {
            if (m_SrcMonster != null)
            {
                m_SrcMonster.AIData.RemoveHpChangeAct(OnHpChanged);
            }
            return base.BuffDestroy();
        }
    }
}
