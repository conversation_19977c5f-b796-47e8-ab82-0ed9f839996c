namespace LD
{
    public class LDMechaBuffMgr : LDBuffMgr
    {
        public LDHeroPlayer HeroPlayer;
        public LDMechaBuffMgr(LDHeroPlayer heroPlayer) : base(heroPlayer)
        {
            HeroPlayer = heroPlayer;
        }

        public override LDBaseBuff AddBuff(int buffId, LDSceneObj buffSrc)
        {
            BuffItem luaConfigItem = GetBuffItem(buffId);
            if (luaConfigItem != null)
            {
                //int targetType = luaConfigItem.GetValInt(BuffKey.targetType);
                //if (targetType == LDBuffTargetType.Weapon)
                {
                    return AddBuffImp(luaConfigItem, buffSrc);
                }
            }
            return null;
        }

        protected override bool InitBuff(LDSceneObj sceneObj, LDBaseBuff lHBaseBuff, float buffTime, double val1)
        {
            lHBaseBuff.Init(sceneObj, this, buffTime, val1);
            return true;
        }
    }
}
