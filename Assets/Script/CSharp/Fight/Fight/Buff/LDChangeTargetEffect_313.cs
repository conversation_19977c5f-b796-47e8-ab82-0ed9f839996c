using System.Collections;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public class LDChangeTargetEffect_313 : LDRoleBaseBuff
    {
        public override void Init(LDSceneObj srcObj, LDBuffMgr buffMgr, float buffTime, double val)
        {
            base.Init(srcObj, buffMgr, buffTime, val);
            TryAddChangeEffectInfo();
        }
        private void TryAddChangeEffectInfo()
        {
            //if (m_AIBuffMgr != null)
            //{
            //    m_AIBuffMgr.AIMonster.AddReplaceEffectInfo(EZMath.IDDoubleToInt(GetParam()[0]), EZMath.IDDoubleToInt(GetParam()[1]));
            //}
            //else if (m_RoleBuffMgr != null)
            //{
            //    m_RoleBuffMgr.MainRole.AddReplaceEffectInfo(EZMath.IDDoubleToInt(GetParam()[0]), EZMath.IDDoubleToInt(GetParam()[1]));
            //}
        }
    }
}
