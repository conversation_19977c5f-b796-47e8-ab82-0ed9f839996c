namespace LD
{
    public class LDOnceDamageBuff_401 : LDDamageBuffBase
    {
        private float m_DamageTimes = 0;
        public override void Init(LDSceneObj srcObj, LDBuffMgr buffMgr, float keepTime, double val)
        {
            base.Init(srcObj, buffMgr, keepTime, val);
            m_DamageType = (LDBuffDamageType)(EZMath.IDDoubleToInt(GetParam()[1]));
            m_DamageTimes = 0;
            SetDamageParam((float)GetParam()[0]);
        }
        public override void MulBuffVal(int times)
        {
            base.MulBuffVal(times);
            SetDamageParam(CurVal);
        }
        public override void OnDUpdate(float dt)
        {
            if (!LDFightTools.GreaterOrEqualThenZero(m_DamageTimes))
            {
                m_DamageTimes++;
                OnHitMonster();
            }
            base.OnDUpdate(dt);
        }

    }
}
