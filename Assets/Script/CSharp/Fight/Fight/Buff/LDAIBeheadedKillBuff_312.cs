
using System.Collections.Generic;

namespace LD
{

    public class LDAIBeheadedKillBuff_312 : LDEventBuff
    {
        private float m_TriggerVal;
        public override void Init(LDSceneObj srcObj, LDBuffMgr buffMgr, float buffTime, double val)
        {
            base.Init(srcObj, buffMgr, buffTime, val);
            m_TriggerVal = (float)GetParam()[1];
        }
        public override void HandleAIPreHitted(LDBuffMgr buffMgr,LDAtkBullet atkBullet, LDAIMonster monster)
        {
            if (monster.AIType != LDAIType.Normal)
            {
                return;
            }
            if (monster.Live && atkBullet.AtkData.HeroPlayer == buffMgr.GetSceneObj())
            {

                if (!LDFightTools.GreaterOrEqualThenZero(monster.AIData.GetHpPercent(),GetTriggerParam(monster)))
                {
                    if (buffMgr.RandomMatch(CurVal))
                    {
                        monster.MonsterBeheadedKill(atkBullet);
                    }
                }
            }
        }
        private float GetTriggerParam(LDAIMonster monster)
        {
            if (monster.AIType == LDAIType.Normal)
            {
                return m_TriggerVal;
            }
            else
            {
                return m_TriggerVal / 2;
            }
        }
    }
}
