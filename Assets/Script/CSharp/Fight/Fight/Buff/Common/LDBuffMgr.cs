using System;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public class LDBuffSource
    {
        public const int None = 0;
        public const int Role = 1;    //主角与怪物
    }
    /// <summary>
    /// Buff关系
    /// 在加入机器人的buff管理之前，所有的Buff来源视为来源于主角-怪物
    /// </summary>

    public class LDBuffVal
    {
        public double BuffVal = 0;
    }
    public class LDBuffTargetType
    {
        public static int AllAI = 1;
        public static int Captain = 2;
        public static int AllHero = 31;
        public static int NormalAI = 3;
        public static int Weapon = 4;
    }


    public abstract class LDBuffMgr : IUpdate
    {
        public LDSafeMap<int, LDSafeList<LDBaseBuff>> BuffMap { protected set; get; }
        public Dictionary<int, LDBuffVal> m_BuffVal { protected set; get; }
        private LDSceneObj m_LockObj;
        private LDRandomUtil m_RandomUtil;
        private Action<LDBaseBuff> m_DestroyBuffAct;
        private Action<LDBaseBuff> CalcBuffAct;
        private Dictionary<int, LDEffectNodeContent> m_BuffEffect = new Dictionary<int, LDEffectNodeContent>();
        private Dictionary<int, int> m_EffectRef = new Dictionary<int, int>();

        public LDBuffMgr(LDSceneObj lockObj)
        {
            m_DestroyBuffAct = ForeachRemoveBuff;
            m_LockObj = lockObj;
            m_BuffVal = new Dictionary<int, LDBuffVal>();
            BuffMap = new LDSafeMap<int, LDSafeList<LDBaseBuff>>();
            m_RandomUtil = Global.gApp.CurFightScene.GeRandomUtil();

            CalcBuffAct = CalcBuffVal;
        }
        public void ReBorn()
        {
            BuffMap.Clear();
            ClearBuffVal();
        }
        public LDSceneObj GetSceneObj()
        {
            if(m_LockObj is LDMainRole)
            {
                return (m_LockObj as LDMainRole).GetCaptainPlayer();
            }
            else 
            {
                return m_LockObj;
            }

        }
        public bool RandomMatch(double val)
        {
            return m_RandomUtil.RandomMatchDouble(val);
        }
        public virtual void OnDUpdate(float dt)
        {
            BuffMap.OnDUpdate(dt);
        }

        public abstract LDBaseBuff AddBuff(int buffId, LDSceneObj buffSrc);
        protected BuffItem GetBuffItem(int buffId)
        {
            if (buffId > 0)
            {
                BuffItem buffItem = Buff.Data.Get(buffId);
                if (buffItem != null)
                {
                    return buffItem;
                }
                else
                {
                    Debug.LogError("BuffID BuffItem is not Exit" + buffId);
                    return null;
                }
            }
            else
            {
                Debug.LogError("BuffID is not Exit" + buffId);
                return null;
            }
        }
        protected LDBaseBuff AddBuffImp(BuffItem buffItem, LDSceneObj buffSrc)
        {
            TryAddBuff(buffItem, buffSrc);
            if (m_LockObj.Live)
            {
                return AddBuff(buffSrc, buffItem, (float)buffItem.time,buffItem.buffParam[0]);
            }
            else
            {
                return null;
            }
        }
        // Buff add 
        private LDBaseBuff AddBuff(LDSceneObj buffSrc, BuffItem buffItem, float keepTime, double val1)
        {
            // 加buff 之前
            PreAddBuff(buffItem);
            // 可能加不上 所以 addBuff 的返回值不靠谱
            if (!CheckBuffIsAdd(buffItem))
            {
                return null;
            }
            //创建buff
            LDBaseBuff buff = LDBuffFactory.CreateBuff(buffItem);
            buff.BuffItem = buffItem;
            //buff 初始化
            if (InitBuff(buffSrc, buff, keepTime, val1))
            {
                // 添加buff
                GetBuffs(buffItem.buffType).Add(buff);
                // 重新计算buff 的值
                ReCalcBuffVal(buff, buffItem);
                // buff 加结束了 做一下广播
                AddBuffEnd(buff);
                return buff;
            }
            else
            {
                Debug.LogError("InitError" + buffItem.id);
                return null;
            }
        }
        /// <summary>
        /// 不一定加的上 或者加的时候 死翘翘了
        /// </summary>
        protected virtual void TryAddBuff(BuffItem BuffItem, LDSceneObj buffSrc)
        {

        }
        public bool HasBuffByType(int buffType,LDBuffMgr buffMgr = null)
        {
            return Math.Abs(GetBuffVal(buffType, buffMgr)) > 0.0001f;
        }

        protected virtual void AddBuffEnd(LDBaseBuff LDBaseBuff)
        {

        }
        protected virtual void RemoveBuffEnd(LDBaseBuff LDBaseBuff)
        {
            Global.gApp.gMsgDispatcher.Broadcast< LDBaseBuff>(MsgIds.RemoveBuff, LDBaseBuff);
        }
        protected abstract bool InitBuff(LDSceneObj buffSrc, LDBaseBuff lHBaseBuff, float keepTime, double val1);
        public void RemoveBuff(LDBaseBuff LHBaseBuff, BuffItem buffItem)
        {
            if (LHBaseBuff.BuffDestroy())
            {
                GetBuffs(buffItem.buffType).Remove(LHBaseBuff);
                ReCalcBuffVal(LHBaseBuff, buffItem);
                RemoveBuffEnd(LHBaseBuff);
            }
        }
        LDBuffVal m_CurBuffVal;
        public void ReCalcBuffVal(LDBaseBuff LDBaseBuff, BuffItem buffItem)
        {
             double buffVal = ReclacBuffValImp(buffItem.buffType);
             LDBaseBuff.CalcBuffValEnd();
             CalcBuffValEnd(LDBaseBuff, buffVal);
        }
        protected virtual void CalcBuffValEnd(LDBaseBuff baseBuff,double newVal)
        {

        }
        private double ReclacBuffValImp(int buffType)
        {
            LDSafeList<LDBaseBuff> lHSafeMap = GetBuffs(buffType, true);
            if (lHSafeMap != null)
            {
                m_CurBuffVal = GetBuffValClass(buffType);
                // 先把数据清理掉
                m_CurBuffVal.BuffVal = 0;
                lHSafeMap.Foreach(CalcBuffAct);
                return m_CurBuffVal.BuffVal;
            }
            else
            {
                m_CurBuffVal = GetBuffValClass(buffType,true);
                if (m_CurBuffVal != null)
                {
                    // 先把数据清理掉
                    m_CurBuffVal.BuffVal = 0;
                    return m_CurBuffVal.BuffVal;
                }
            }
            return 0;
        }
        private void CalcBuffVal(LDBaseBuff buff)
        {
            m_CurBuffVal.BuffVal += buff.GetBuffVal();
        }
        public LDSafeList<LDBaseBuff> GetBuffs(int buffType,bool canNull = false)
        {
            LDSafeList<LDBaseBuff> dYSafeList = null;
            if (!BuffMap.TryGetValue(buffType, out dYSafeList))
            {
                if (!canNull)
                {
                    dYSafeList = new LDSafeList<LDBaseBuff>();
                    BuffMap.Add(buffType, dYSafeList);
                }
            }
            return dYSafeList;

        }
        public int GetBuffCount(int buffType)
        {
            LDSafeList<LDBaseBuff> dYSafeList = null;
            if (!BuffMap.TryGetValue(buffType, out dYSafeList))
            {
                return 0;
            }
            else
            {
                return dYSafeList.Count;
            }
        }
        private double GetBuffValSimple(int buffType)
        {
            LDBuffVal buffVal = GetBuffValClass(buffType,true);
            if(buffVal != null)
            {
                return buffVal.BuffVal;
            }
            else
            {
                return 0;
            }

        }
        private LDBuffVal GetBuffValClass(int buffType,bool canNull = false)
        {
            LDBuffVal buffVal;
            if (!m_BuffVal.TryGetValue(buffType,out buffVal))
            {
                if (!canNull)
                {
                    buffVal = new LDBuffVal();
                    m_BuffVal.Add(buffType, buffVal);
                }
            }
            return buffVal;
        }

        public double GetBuffValDefaultOne(int buffType,LDBuffMgr buffMgr = null)
        {
            double buffVal = GetBuffVal(buffType,buffMgr);
            if(buffVal < 0.001f)
            {
                return 1;
            }
            else
            {
                return buffVal;
            }
        }
        public double GetBuffValDefaultOne(int buffType, LDBaseWeapon weapon)
        {
            double buffVal = GetBuffVal(buffType, weapon);
            if (buffVal < 0.001d)
            {
                return 1;
            }
            else
            {
                return buffVal;
            }
        }
        public double GetBuffVal(int buffType, LDBuffMgr buffMgr = null)
        {
            if (buffMgr == null)
            {
                return GetBuffValSimple(buffType);
            }
            else
            {
                return GetBuffValSimple(buffType) + buffMgr.GetBuffValSimple(buffType);
            }
        }
        public double GetBuffVal(int buffType,LDBaseWeapon weapon)
        {
            if (weapon == null)
            {
                return GetBuffValSimple(buffType);
            }
            else
            {
                if (weapon.MechaBuffMgr == null)
                {
                    return GetBuffValSimple(buffType) + weapon.BuffMgr.GetBuffValSimple(buffType);
                }
                else
                {
                    return GetBuffValSimple(buffType) + weapon.BuffMgr.GetBuffValSimple(buffType) +
                      weapon.MechaBuffMgr.GetBuffValSimple(buffType);
                }
            }
        }
        public LDBaseBuff GetBuff(int buffType,LDBuffMgr buffMgr = null)
        {
            LDSafeList<LDBaseBuff> safeMap = GetBuffs(buffType,true);
            if (safeMap != null)
            {
                foreach (LDBaseBuff buff in safeMap.GetAll())
                {
                    if (!buff.IsEnded)
                    {
                        return buff;
                    }
                }
            }
            if(buffMgr != null)
            {
                return buffMgr.GetBuff(buffType);
            }
            return null;
        }

        public int GetBuffCountById(int buffId)
        {
            BuffItem dstItem = Buff.Data.Get(buffId);
            LDSafeList<LDBaseBuff> safeMap = GetBuffs(dstItem.buffType,true);
            int buffCount = 0;
            if (safeMap != null)
            {
                foreach (LDBaseBuff buff in safeMap.GetAll())
                {
                    if (buff.BuffItem == dstItem && !buff.IsEnded)
                    {
                        buffCount++;
                    }
                }
            }
            return buffCount; 
        }
        public LDBaseBuff GetBuffById(int buffId, LDBuffMgr buffMgr = null)
        {
            BuffItem dstItem = Buff.Data.Get(buffId);
            LDBaseBuff baseBuff = GetBuffByItem(dstItem);
            if(baseBuff == null)
            {
                if(buffMgr != null)
                {
                    return buffMgr.GetBuffById(buffId);
                }
            }
            return baseBuff;
        }
        private LDBaseBuff GetBuffByItem(BuffItem dstItem)
        {
            LDSafeList<LDBaseBuff> safeMap = GetBuffs(dstItem.buffType,true);
            if (safeMap != null)
            {
                foreach (LDBaseBuff buff in safeMap.GetAll())
                {
                    if (buff.BuffItem == dstItem && !buff.IsEnded)
                    {
                        return buff;
                    }
                }
            }
            return null;
        }

        private List<LDBaseBuff> GetBuffsById(int buffId)
        {
            BuffItem dstItem = Buff.Data.Get(buffId);
            return GetBuffsByItem(dstItem);
        }

        private List<LDBaseBuff> GetBuffsByItem(BuffItem dstItem)
        {
            List<LDBaseBuff> buffList = new List<LDBaseBuff>();
            LDSafeList<LDBaseBuff> safeMap = GetBuffs(dstItem.buffType,true);
            if (safeMap != null)
            {
                foreach (LDBaseBuff buff in safeMap.GetAll())
                {
                    if (buff.BuffItem == dstItem && !buff.IsEnded)
                    {
                        buffList.Add(buff);
                    }
                }
            }
            return buffList;
        }
        public int GetBuffValInt(int buffType, LDBaseWeapon weapon)
        {
            double val = GetBuffVal(buffType, weapon);
            return EZMath.IDDoubleToInt(val);
        }

        public int GetBuffValInt(int buffType, LDBuffMgr buffMgr = null)
        {
            double val = GetBuffVal(buffType, buffMgr);
            return EZMath.IDDoubleToInt(val);
        }

        private Action<LDSafeList<LDBaseBuff>> m_ForeachDestroyBuffs;

        // 暂时ai 用，主角不用
        public virtual void BuffMgrDestroy()
        {
            if (m_ForeachDestroyBuffs == null)
            {
                m_ForeachDestroyBuffs = ForeachDestroyBuffs;
            }
            BuffMap.Foreach(m_ForeachDestroyBuffs);
            BuffMap.Clear();
            ClearBuffVal();
        }
        protected void ClearBuffVal()
        {
            foreach (KeyValuePair<int, LDBuffVal> item in m_BuffVal)
            {
                item.Value.BuffVal = 0;
            }
        }
        public void RemoveTypeBuffWithWpn(int buffType,LDBaseWeapon baseWeapon = null)
        {
            if (baseWeapon == null)
            {
                RemoveTypeBuff(buffType);
            }
            else
            {
                RemoveTypeBuff(buffType, baseWeapon.BuffMgr);
                if(baseWeapon.MechaBuffMgr != null)
                {
                    baseWeapon.MechaBuffMgr.RemoveTypeBuff(buffType);
                }
            }
        }
        public void RemoveTypeBuff(int buffType,LDBuffMgr buffMgr = null)
        {
            LDSafeList<LDBaseBuff> baseBuffs = GetBuffs(buffType,true);
            if(baseBuffs != null)
            {
                baseBuffs.Foreach(m_DestroyBuffAct);
                //ReclacBuffValImp(buffType);
            }
            if(buffMgr != null)
            {
                buffMgr.RemoveTypeBuff(buffType);
            }

        }
        private void ForeachRemoveBuff(LDBaseBuff buff)
        {
            buff.RemoveBuff();
        }

        private void ForeachDestroyBuff(LDBaseBuff buff)
        {
            buff.BuffDestroy();
        }
        private Action<LDBaseBuff> m_ForeachDestroyBuff;

        private void ForeachDestroyBuffs(LDSafeList<LDBaseBuff> dYSafeMap)
        {
            if (m_ForeachDestroyBuff == null)
            {
                m_ForeachDestroyBuff = ForeachDestroyBuff;
            }
            dYSafeMap.Foreach(m_ForeachDestroyBuff);
        }

        //检查buff是否能添加
        private BuffItem m_CalcBuffCountItem = null;
        private int m_CurBuffCount = 0;
        private Action<LDBaseBuff> m_CalcBuffAction = null;
        protected virtual void PreAddBuff(BuffItem buffItem)
        {
            if(m_CalcBuffAction == null)
            {
                m_CalcBuffAction = CalcBuff;
            }
            m_CalcBuffCountItem = buffItem;
            m_CurBuffCount = 0;
            LDSafeList<LDBaseBuff> buff = GetBuffs(buffItem.buffType,true);
            if (buff != null)
            {
                buff.Foreach(m_CalcBuffAction);
                if (buffItem.addType == 2)
                {
                    if (buff.Count > 0)
                    {
                        buff[0].AddBuffVal(buffItem.buffParam[0]);
                    }
                }
            }
            m_CalcBuffCountItem = null;
        }

        public virtual bool CheckBuffIsAdd(BuffItem buffItem)
        {
            if(!m_LockObj.Live)
            {
                return false;
            }
            if (buffItem.immunity > 0)
            {
                if (HasBuffByType(buffItem.immunity))
                {
                    Global.gApp.gMsgDispatcher.Broadcast<LDSceneObj>(MsgIds.BuffMiss, m_LockObj);
                    return false;
                }
            }
            if(m_CurBuffCount >= buffItem.limit)
            {
                return false;
            }
            else
            {
                if(m_CurBuffCount > 0)
                {
                    if(buffItem.addType == 2)
                    {
                        return false;
                    }
                }
            }

            return true;
        }
        /// <summary>
        //同一个id 的buff 要处理方式 可以简化 一下。增加一个新的数据结构就行了 ,暂时不优化
        /// </summary>
        private void CalcBuff(LDBaseBuff LDBaseBuff)
        {
            if (LDBaseBuff.BuffItem == m_CalcBuffCountItem)
            {
                m_CurBuffCount++;
                if (LDBaseBuff.BuffItem.addType == 1)
                {
                    LDBaseBuff.ResetBuffTime(0);
                }
            }
        }
        public LDSceneObj GetPlayerTsf()
        {
            return m_LockObj;
        }
        protected void TryAddBullet(LDRoleBaseBuff baseBuff,int bulletId)
        {
            if (baseBuff != null)
            {
                baseBuff.TryAddMonsterTsfBullet(bulletId);
            }
        }
        public LDEffectNodeContent AddEffect(int effectId, int posType)
        {
            if (m_EffectRef.ContainsKey(effectId))
            {
                m_EffectRef[effectId] += 1;
            }
            else
            {
                m_EffectRef[effectId] = 1;
                LDEffectNodeContent buffEffect = Global.gApp.CurFightScene.gEffectMgr.GetFreeEffectNode(effectId);
                LDSceneObj sceneObj = GetSceneObj();
                Transform bodyNode = LDFightNodeTools.GetHeroNodeByBase(posType, sceneObj);
                buffEffect.Tsf.SetParent(bodyNode, false);
                buffEffect.Tsf.localPosition = Vector3.zero; ;
                buffEffect.Tsf.localRotation = Quaternion.identity;
                buffEffect.PlayParticle();
                m_BuffEffect[effectId] = buffEffect;
            }
            return m_BuffEffect[effectId];
        }
        public void RemEffect(List<int> effectIds)
        {
            foreach (int effectId in effectIds)
            {
                RemEffect(effectId);
            }
        }
        public void RemEffect(int effectId)
        {
            if (m_EffectRef.ContainsKey(effectId))
            {
                m_EffectRef[effectId] -= 1;
                if(m_EffectRef[effectId] == 0)
                {
                    m_BuffEffect[effectId].StopAndRecycleEffect();
                    m_BuffEffect.Remove(effectId);
                    m_EffectRef.Remove(effectId);
                }
            }
        }
        public Dictionary<int, LDBuffVal> GetBuffVals()
        {
            return m_BuffVal;
        }
        public bool TryConsumeShield(LDAtkBullet bullet, LDBuffMgr buffMgr = null)
        {
            if (bullet.AtkData.DamageParam > 0.0001f)
            {
                LDBaseBuff baseBuff = GetBuff(LDBuffType.AtkShield_15);
                if (baseBuff != null)
                {
                    int curVal = EZMath.IDDoubleToInt(baseBuff.GetBuffVal());
                    if (curVal > 1)
                    {
                        baseBuff.SetBuffVal(curVal - 1);
                        ReCalcBuffVal(baseBuff, baseBuff.BuffItem);
                    }
                    else
                    {
                        baseBuff.RemoveBuff();
                    }
                    return true;
                }
            }
            if (buffMgr != null)
            {
                return buffMgr.TryConsumeShield(bullet);
            }
            return false;
        }
    }
}
