using LD.Data;
using System.Collections.Generic;
using UnityEngine;
namespace LD
{
    public abstract class LDBaseBuff : IUpdate
    {
        public BuffItem BuffItem { set; get; }
        public float CurTime { set; get; }
        public float BuffTime { set; get; }
        public double CurVal { set; get; }
        public int BuffTimes { set; get; }
        public bool IsEnded { protected set; get; }
        protected LDBuffMgr m_BuffMgr = null;
        protected List<int> m_EffectIds = new List<int>();
        protected LDSceneObj m_SrcObj;

        protected LDAIMonster m_SrcMonster;
        protected LDBaseElement m_SrcElement;
        protected LDBaseWeapon m_SrcWeapon;
        protected LDHeroPlayer m_SrcHeroPlayer;
        protected LDMainRole m_SrcMainRole;
        protected LDAtkBullet m_SrcBullet;
        protected LDUAVFightBase m_UAVFight;
        protected LDAircraft m_SrcAircraft;


        public virtual void Init(LDSceneObj srcObj,LDBuffMgr buffMgr, float buffTime, double val)
        {
            m_SrcObj = srcObj;
            IsEnded = false;
            m_BuffMgr = buffMgr;
            BuffTime = buffTime;
            CurTime = 0;
            m_EffectIds.Clear();
            SetBuffVal(val);
            BuffTimesInc();
            TryAddEffect();
        }
        public LDAtkBullet GetSrcBullet()
        {
            return m_SrcBullet;
        }
        public LDBaseWeapon GetSrcWpn()
        {
            return m_SrcWeapon;
        }
        public LDAircraft GetSrcAircraft()
        {
            return m_SrcAircraft;
        }
        public LDHeroPlayer GetHeroPlayer()
        {
            if (m_SrcHeroPlayer != null)
            {
                return m_SrcHeroPlayer;
            }
            else if (m_SrcWeapon)
            {
                return m_SrcWeapon.HeroPlayer;
            }
            else if (m_UAVFight != null)
            {
                return m_UAVFight.HeroPlayer;
            }
            else if (m_SrcAircraft != null)
            {
                return m_SrcAircraft.AircraftMgr.MainRole.GetCaptainPlayer();
            }
            else if (m_SrcMainRole)
            {
                return m_SrcMainRole.GetCaptainPlayer();
            }
            else
            {
                return null;
            }
        }
        public LDMainRole GetSrcMainRole()
        {
            if (m_SrcMainRole != null)
            {
                return m_SrcMainRole;
            }
            else if (m_SrcHeroPlayer != null)
            {
                return m_SrcHeroPlayer.HeroMgr.MainRole;
            }
            else if (m_SrcWeapon)
            {
                return m_SrcWeapon.HeroPlayer.HeroMgr.MainRole;
            }
            else if (m_UAVFight != null)
            {
                return m_UAVFight.MainRole;
            }
            else
            {
                return null;
            }
        }
        public LDBuffMgr GetWpnBuffMgr()
        {
            if(m_SrcWeapon != null)
            {
                return m_SrcWeapon.BuffMgr;
            }
            else if(m_UAVFight != null)
            {
                return m_UAVFight.BuffMgr;
            }
            else
            {
                return null;
            }

        }
        public LDUAVFightBase GetUAV()
        {
            return m_UAVFight;
        }
        public void TryAddEffect()
        {
            if (m_EffectIds.Count == 0)
            {
                int[] effectInfo = BuffItem.effect;
                if (effectInfo.Length == 2)
                {
                    int posType = effectInfo[0];
                    int effectId = GetEffectId(effectInfo[1]);
                    // 挂在人身上的buff 
                    if(posType >= 0)
                    {
                        m_EffectIds.Add(effectId);
                        m_BuffMgr.AddEffect(effectId, posType);
 
                    }
                    else
                    {
                        LDEffectNodeContent effectNod = Global.gApp.CurFightScene.gEffectMgr.GetEffectNode(effectId);
                        effectNod.Tsf.position = m_BuffMgr.GetSceneObj().transform.GetPoisition();
                        effectNod.Tsf.localRotation = Quaternion.identity;
                    }
                }
            }
        }
        protected virtual int GetEffectId(int effectId)
        {
            return effectId;
        }
        public void TryRmEffect()
        {
            m_BuffMgr.RemEffect(m_EffectIds);
            m_EffectIds.Clear();
        }

        protected virtual void BuffTimesInc()
        {
            BuffTimes++;
        }
        public void ResetBuffTime(float curTime)
        {
            CurTime = curTime;
        }
        public virtual double GetBuffVal()
        {
            return CurVal;
        }
        public virtual void SetBuffVal(double val)
        {
            CurVal = val;
        }
        public void FreshBuffVal(double val)
        {
            CurVal = val;
            m_BuffMgr.ReCalcBuffVal(this, BuffItem);
        }

        public void AddBuffVal(double val)
        {
            SetBuffVal(CurVal  + val);
            m_BuffMgr.ReCalcBuffVal(this, BuffItem);
        }
        public virtual void MulBuffVal(int times)
        {
            SetBuffVal(CurVal * times);
            m_BuffMgr.ReCalcBuffVal(this,BuffItem);
        }
        public virtual void OnDUpdate(float dt)
        {
            CurTime += dt;
            if (LDFightTools.GreaterOrEqualThenZero(CurTime,BuffTime))
            {
                RemoveBuff();
            }
        }
        public void SetBuffKeepTime(float keepTime)
        {
            BuffTime = keepTime;
        }
        public void RemoveBuff()
        {
            m_BuffMgr.RemoveBuff(this, BuffItem);
        }
        public double[] GetParam()
        {
            return BuffItem.buffParam;
        }

        private int GetBuffId()
        {
            return BuffItem.id;
        }

        public virtual bool BuffDestroy()
        {
            if (!IsEnded)
            {
                TryRmEffect();
                IsEnded = true;
                return true;
            }
            else
            {
                return false;
            }
        }
        public virtual void CalcBuffValEnd()
        {

        }
    }
}
