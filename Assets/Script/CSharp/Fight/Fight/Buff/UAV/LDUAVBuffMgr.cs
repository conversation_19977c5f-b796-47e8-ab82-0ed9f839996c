namespace LD
{
    public class LDUAVBuffMgr : LDBuffMgr
    {
        public LDUAVFightBase m_UAV;
        public LDUAVBuffMgr(LDUAVFightBase uav) : base(uav)
        {
            m_UAV = uav;
        }

        public override LDBaseBuff AddBuff(int buffId, LDSceneObj buffSrc)
        {
            BuffItem luaConfigItem = GetBuffItem(buffId);
            if (luaConfigItem != null)
            {
                //int targetType = luaConfigItem.GetValInt(BuffKey.targetType);
                //if (targetType == LDBuffTargetType.Weapon)
                {
                    return AddBuffImp(luaConfigItem, buffSrc);
                }
            }
            return null;
        }

        protected override bool InitBuff(LDSceneObj sceneObj, LDBaseBuff lHBaseBuff, float buffTime, double val1)
        {
            lHBaseBuff.Init(sceneObj, this, buffTime, val1);
            return true;
        }
    }
}
