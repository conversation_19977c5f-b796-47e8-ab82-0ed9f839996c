using UnityEngine;

namespace LD
{
    public class LDEnableGoTrigger : LDNormalTrigger
    {
        [SerializeField] public GameObject EnableGo;
        [SerializeField] public string EnableAniName;

        private Animator m_Animator;
        
        public override void Init(LDTriggerMgr triggerMgr)
        {
            base.Init(triggerMgr);
            EnableGo.SetActive(false);
            m_Animator = EnableGo.GetComponentInChildren<Animator>();
        }

        protected override void OnTriEnterImp(LDSceneObj other)
        {
            EnableGo.SetActive(true);
            if (!string.IsNullOrEmpty(EnableAniName))
            {
                m_Animator?.Play(EnableAniName);
            }
        }

        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            TryRayCast();
        }
    }
}