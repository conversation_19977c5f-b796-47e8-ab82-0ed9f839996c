using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDInsertWaveTrigger : LDNormalTrigger
    {
        [SerializeField] private int[] Waves;
        private Action<LDWave> m_OnWaveInsert;
        
        public override void Init(LDTriggerMgr triggerMgr)
        {
            base.Init(triggerMgr);
        }
        
        public void SetOnWaveInsertCallback(Action<LDWave> onWaveInsert)
        {
            m_OnWaveInsert = onWaveInsert;
        }

        protected override void OnTriEnterImp(LDSceneObj other)
        {
            if (Waves != null && Waves.Length > 0)
            {
                foreach (int waveId in Waves)
                {
                    LDWave wave = Global.gApp.CurFightScene.gWaveMgr.InsertWave(waveId);
                    m_OnWaveInsert?.Invoke(wave);
                }
            }
        }

        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            TryRayCast();
        }
    }
}