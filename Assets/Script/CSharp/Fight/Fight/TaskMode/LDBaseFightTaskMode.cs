using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public enum LDTaskState
    {
        None = 0,
        Init = 1,
        Start = 2,
        Ended = 3,
    }

    public abstract class LDBaseFightTaskMode : MonoBehaviour, IUpdate
    {
        public int TaskIndex { private set; get; }
        public bool HideGo = true;
        public bool CompliteHideGo = false;

        private LDFightTaskMgr m_FightTaskMgr;
        private LDTaskState m_Started = LDTaskState.None;
        /// <summary>
        /// 任务模块初始化
        /// </summary>
        protected abstract void InitImp();
        /// <summary>
        /// 任务模块开始
        /// </summary>
        protected abstract void StartTaskImp();
        /// <summary>
        /// 任务模块开始
        /// </summary>
        protected abstract void OnUpdateImp(float dt);
        /// <summary>
        /// 任务模块结束
        /// </summary>
        protected abstract void EndTaskImp();
        /// <summary>
        /// 任务模块完成
        /// </summary>
        protected abstract void TaskCompletImp();
        /// <summary>
        /// 任务模块 Destroy
        /// </summary>
        protected abstract void DestroyImp();
        protected UIFightView m_FightView;
        public void Init(LDFightTaskMgr fightTaskMgr, int index)
        {
            m_FightView = Global.gApp.CurFightScene.gFightUI.FightUI_Nodes;
            m_FightTaskMgr = fightTaskMgr;
            TaskIndex = index;
            m_Started = LDTaskState.Init;
            gameObject.SetActive(!HideGo);

            InitImp();
        }
        protected void SetArrowState(bool hideArrow)
        {
            if (m_FightView != null)
            {
                m_FightView.ArrowNode.SetActive(hideArrow);
            }
        }
        protected void UpdateArrow(Vector3 disVec)
        {
            if (m_FightView != null)
            {
                m_FightView.UpdateArrow(disVec);
            }
        }
        
        public void OnDUpdate(float dt)
        {
            if (m_Started == LDTaskState.Start)
            {
                OnUpdateImp(dt);
            }
        }

        public void TaskCompleted()
        {
            if (m_Started == LDTaskState.Start)
            {
                TaskCompletImp();
                m_FightTaskMgr.EndTask(TaskIndex);
            }
        }

        public void StartTask()
        {
            if (m_Started == LDTaskState.Init)
            {
                m_Started = LDTaskState.Start;
                gameObject.SetActive(true);
                RegEvent(true);
                StartTaskImp();
            }
        }
        public void EndTask()
        {
            if (m_Started == LDTaskState.Start)
            {
                gameObject.SetActive(!CompliteHideGo);
                m_Started = LDTaskState.Ended;
                RegEvent(false);
                EndTaskImp();
            }
        }
        public void DestroyTask()
        {
            EndTask();
            DestroyImp();
        }
        protected virtual void RegEvent(bool addListener)
        {

        }

    }
}
