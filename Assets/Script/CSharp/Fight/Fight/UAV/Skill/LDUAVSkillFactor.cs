using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDUAVSkillFactor
    {
        private static string shoot = "shoot";
        private static string suicidal = "suicidal";
        private static string bomber = "bomber";
        private static string getBuff = "getBuff";
        private static string revive = "revive";
        private static string shootHide = "shootHide";
        public static LDUAVSkillBase CreateUAVSkill(UAVSkillItem uavSkillItem)
        {
            string skillType = uavSkillItem.skillType;
            LDUAVSkillBase UAVSkillBase = null;
            if (skillType == shoot)
            {
                UAVSkillBase = new LDUAVShootSkill();
            }
            else if(skillType == suicidal)
            {
                UAVSkillBase = new LDUAVSuicidalSkill();
            }
            else if(skillType == bomber)
            {
                UAVSkillBase = new LDUAVBomberSkill();
            }
            else if(skillType == getBuff)
            {
                UAVSkillBase = new LDUAVGetBuffSkill();
            }
            else if(skillType == revive)
            {
                UAVSkillBase = new LDUAVReviveSkill();
            }
            else if (skillType == shootHide)
            {
                UAVSkillBase = new LDUAVShootHideSkill();
            }
            return UAVSkillBase;

        }
    }
}
