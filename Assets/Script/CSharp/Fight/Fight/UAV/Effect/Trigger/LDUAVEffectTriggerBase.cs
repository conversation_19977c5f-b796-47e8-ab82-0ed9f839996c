using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public abstract class LDUAVEffectTriggerBase: INormalMode
    {
        public abstract void InitImp();
        public abstract void UpdateImp(float dt);
        public abstract void DestroyImp();
        protected LDUAVEffectBase m_UAVEffect;
        protected UAVSkillItem m_UAVSkillItem;
        protected LDUAVFightBase m_UAV;

        public void Init(LDUAVEffectBase UAVEffectBase,LDUAVFightBase UAVFightBase, UAVSkillItem uavSkillItem)
        {
            m_UAV = UAVFightBase;
            m_UAVEffect = UAVEffectBase;
            m_UAVSkillItem = uavSkillItem;
            InitImp();
            RegLitener(true);
        }
        public void OnDUpdate(float dt)
        {
            UpdateImp(dt);
        }
        protected virtual void RegLitener(bool reg)
        {

        }
        public void DestroyUAV()
        {
            RegLitener(false);
            DestroyImp();
        }
    }
}
