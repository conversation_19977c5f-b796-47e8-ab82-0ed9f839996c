using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LDNineGridUtil : MonoBehaviour
{
    List<Transform> m_GridTsf = new List<Transform>(9);
    public int CeilSize = 30;
    public int CeilHalfSize = 15;
    public int XCoord = 0;
    public int YCoord = 0;
    [SerializeField]
    private Transform TargetNode;
    private void Awake()
    {
        CeilHalfSize = CeilSize / 2;
        for (int i = 0; i < transform.childCount; i++)
        {
            m_GridTsf.Add(transform.GetChild(i));
        }
    }
    public void SetTargetNode(Transform targetNode)
    {
        TargetNode = targetNode;
    }

    private void LateUpdate()
    {
        if (TargetNode != null)
        {
            Vector3 poisition = TargetNode.position;
            int coordX = Mathf.FloorToInt((poisition.x + CeilHalfSize) / CeilSize);
            int coordY = Mathf.FloorToInt((poisition.z + CeilHalfSize) / CeilSize);
            CalcGridPos(coordX, coordY);
        }
    }
    void CalcGridPos(int coordX, int coordY)
    {
        if(coordX == XCoord && coordY == YCoord)
        {
            return;
        }
        int offsetX = coordX - XCoord;
        for (int i = 0; i < Mathf.Abs(offsetX); i++)
        {
            ChangeGridX(offsetX);
        }
        XCoord = coordX;

        int offsetZ = coordY - YCoord;
        for (int i = 0; i < Mathf.Abs(offsetZ); i++)
        {
            ChangeGridZ(offsetZ);
        }
        YCoord = coordY;
    }
    private void ChangeGridX(int symble)
    {
        if(symble > 0)
        {
            Vector3 offset = new Vector3(CeilSize * 3,0,0);
            for (int i = 0; i < 3; i++)
            {
                int index = i * 3;
                m_GridTsf[index].position = m_GridTsf[index].position + offset;
                Swap(index,index + 1);
                Swap(index + 1,index + 2);
            }
        }
        else
        {

            Vector3 offset = new Vector3(CeilSize * 3, 0, 0);
            for (int i = 3; i > 0; i--)
            {
                int index = i * 3 - 1;
                m_GridTsf[index].position = m_GridTsf[index].position - offset;
                Swap(index, index - 1);
                Swap(index - 1, index - 2);
            }
        }
    }

    private void ChangeGridZ(int symble)
    {
        if (symble > 0)
        {
            Vector3 offset = new Vector3(0, 0, CeilSize * 3);
            for (int i = 0; i < 3; i++)
            {
                int index = 6 + i;
                m_GridTsf[index].position = m_GridTsf[index].position + offset;
                Swap(index, index - 3);
                Swap(index - 3, index - 6);
            }
        }
        else
        {

            Vector3 offset = new Vector3(0, 0, CeilSize * 3);
            for (int i = 0; i < 3; i++)
            {
                int index = i;
                m_GridTsf[index].position = m_GridTsf[index].position - offset;
                Swap(index, index + 3);
                Swap(index + 3, index + 6);
            }
        }
    }
    private void Swap(int indexA,int indexB)
    {
        Transform ATsf = m_GridTsf[indexA];
        m_GridTsf[indexA] = m_GridTsf[indexB];
        m_GridTsf[indexB] = ATsf;
    }
}
