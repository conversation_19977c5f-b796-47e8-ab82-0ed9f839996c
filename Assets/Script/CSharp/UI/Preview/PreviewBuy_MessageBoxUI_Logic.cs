using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PreviewBuy_MessageBoxUI
    {
        private int m_Id;
        private int m_Type;
        private bool m_IsVip;

        protected override void OnInitImp()
        {
            m_Orange_btn_Confirm.AddListener(OnConfirmBtnClick);
            m_Green_btn_Cancel.AddListener(OnCancelBtnClick);
        }

        protected override void OnCloseImp()
        {

        }

        public void InitData(int id, int type, bool isVip, StorageGiftItem giftCfg)
        {
            m_Id = id;
            m_Type = type;
            m_IsVip = isVip;

            string earlyItemNum = isVip ? giftCfg.earlyItemNum02 : giftCfg.earlyItemNum01;
            LDCommonItem item = new LDCommonItem(earlyItemNum);
            string info = isVip ? giftCfg.returnItemNum02 : giftCfg.returnItemNum01;
            string[] returnItemInfo = info.Split(",");
            m_Buy_Txt01.text.SetTips(37206, item.Num, UiTools.Localize(item.Name), returnItemInfo[2], returnItemInfo[3],
                UiTools.Localize(item.Name));
        }

        public override void OnFreshUI()
        {

        }

        private void OnConfirmBtnClick()
        {
            TouchClose();
            Global.gApp.gSystemMgr.gActivityPreviewMgr.SendStorageGift(m_Id, m_Type, m_IsVip);
        }

        private void OnCancelBtnClick()
        {
            TouchClose();
        }
    }
}
