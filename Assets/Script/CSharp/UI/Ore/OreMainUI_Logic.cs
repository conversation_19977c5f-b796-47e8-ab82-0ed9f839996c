using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class OreMainUI
    {
        private float m_MapWidth = 10000f;
        private float m_MapHeight = 10000f;
        private float m_ItemWidth = 500f;
        private float m_ItemHeight = 500f;

        private float m_MinScale = 0.3f;
        private float m_MaxScale = 1f;

        private float m_CurScale = 1f;
        private float m_ZoomSpeed = 0.1f;

        private List<float> m_LodIndex = new List<float>()
        {
            1f, 0.75f, 0.5f
        };

        private int m_CurLodIndex = 1;

        private ScrollRect m_ScrollRect;
        private float m_CenterX;
        private float m_CenterY;

        private float m_InitPercentX = 0.4f;
        private float m_InitPercentY = 0.5f;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_ScaleContent.rectTransform.sizeDelta = new Vector2(m_MapWidth, m_MapHeight);
            m_ScaleContent.gameObject.transform.localScale = Vector3.one * m_CurScale;
            m_ScrollRect = m_MapScrollView.gameObject.GetComponent<ScrollRect>();
            m_ScrollRect.onValueChanged.AddListener(OnScrollRectValueChanged);
            m_ScaleContent.rectTransform.localPosition =
                new Vector3(-m_MapWidth * m_CurScale * m_InitPercentX + m_ScrollRect.rectTransform().sizeDelta.x / 2f,
                    m_MapHeight * m_CurScale * m_InitPercentY - m_ScrollRect.rectTransform().sizeDelta.y / 2f, 0);
            InitMap();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }


        private void InitMap()
        {
            m_Image.CacheInstanceList();
            for (int i = 0; i < 20; i++)
            {
                for (int j = 0; j < 20; j++)
                {
                    OreMainUI_Image itemUI = m_Image.GetInstance(true);
                    itemUI.gameObject.transform.localPosition = new Vector3(i * 500, -j * 500, 0);
                    itemUI.gameObject.name = $"{i}_{j}";
                    itemUI.Refresh(m_CurLodIndex);
                }
            }
        }

        private void Update()
        {
            HandleMouseInput();
            HandleTouchInput();

            var curScale = m_ScaleContent.gameObject.transform.localScale.x;
            if (!Mathf.Approximately(curScale, m_CurScale))
            {
                float offsetScaleX = 0;
                float offsetScaleY = 0;
                offsetScaleX = (curScale - m_CurScale) * m_MapWidth;
                offsetScaleY = (curScale - m_CurScale) * m_MapHeight;


                float offsetPercentX = (m_ScaleContent.gameObject.transform.localPosition.x - m_ScrollRect.rectTransform().sizeDelta.x / 2f) / (-m_MapWidth * curScale); //0.4f;
                float offsetPercentY = (m_ScaleContent.gameObject.transform.localPosition.y + m_ScrollRect.rectTransform().sizeDelta.y / 2f) / (m_MapHeight * curScale); //0.5f;

                m_ScaleContent.gameObject.transform.localScale = Vector3.one * m_CurScale;

                if (m_ScaleContent.gameObject.transform.localPosition.x + offsetScaleX * offsetPercentX > 0)
                {
                    offsetScaleX = 0;
                }

                if (m_ScaleContent.gameObject.transform.localPosition.y - offsetScaleY * offsetPercentY < 0)
                {
                    offsetScaleY = 0;
                }

                m_ScaleContent.gameObject.transform.localPosition += new Vector3(offsetScaleX * offsetPercentX, -offsetScaleY * offsetPercentY, 0);
            }

            int lodIndex = GetLodIndex();
            if (!m_CurLodIndex.Equals(lodIndex))
            {
                m_CurLodIndex = lodIndex;
                Global.gApp.gMsgDispatcher.Broadcast(MsgIds.OreLodChange, m_CurLodIndex);
            }
        }

        #region 输入控制

        // 鼠标输入控制
        private void HandleMouseInput()
        {
            // 鼠标滚轮控制 Y 轴的缩放
            float scrollInput = Input.GetAxis("Mouse ScrollWheel");
            if (scrollInput != 0)
            {
                m_CurScale -= scrollInput * m_ZoomSpeed;
                m_CurScale = Mathf.Clamp(m_CurScale, m_MinScale, m_MaxScale);
            }
        }

        private Vector2 touch0PreviousPosition;

        private Vector2 touch1PreviousPosition;

        // 触摸输入控制
        private void HandleTouchInput()
        {
            if (Input.touchCount == 2) // 双指控制缩放
            {
                m_ScrollRect.enabled = false;
                Touch touch0 = Input.GetTouch(0);
                Touch touch1 = Input.GetTouch(1);

                // 记录每帧的触摸点位置
                if (touch0.phase == TouchPhase.Began || touch1.phase == TouchPhase.Began)
                {
                    touch0PreviousPosition = touch0.position;
                    touch1PreviousPosition = touch1.position;
                }

                // 计算两个触摸点的当前距离
                float currentDistance = Vector2.Distance(touch0.position, touch1.position);

                // 计算两个触摸点的上一帧距离
                float previousDistance = Vector2.Distance(touch0PreviousPosition, touch1PreviousPosition);

                // 计算距离的变化
                float difference = currentDistance - previousDistance;

                // 根据距离变化来调整缩放
                m_CurScale += difference * m_ZoomSpeed / 50f;

                // 限制缩放比例
                m_CurScale = Mathf.Clamp(m_CurScale, m_MinScale, m_MaxScale);

                // 更新上一帧的触摸位置
                touch0PreviousPosition = touch0.position;
                touch1PreviousPosition = touch1.position;
            }
            else
            {
                m_ScrollRect.enabled = true;
            }
        }

        #endregion


        private int GetLodIndex()
        {
            for (int i = 0; i < m_LodIndex.Count; i++)
            {
                if (m_CurScale > m_LodIndex[i])
                {
                    return i;
                }
            }

            return m_LodIndex.Count;
        }

        private void OnScrollRectValueChanged(Vector2 value)
        {
            // 处理滚动视图的滚动事件
            // 这里可以添加一些逻辑来处理滚动位置变化

            m_CenterX = m_ScrollRect.rectTransform().sizeDelta.x / 2f - m_ScaleContent.rectTransform.localPosition.x;
            m_CenterY = -(m_ScrollRect.rectTransform().sizeDelta.y / 2f + m_ScaleContent.rectTransform.localPosition.y);
            // Debug.LogError($"Value : {m_CenterX}     {m_CenterY}");
            m_CenterImage.rectTransform.localPosition = new Vector3(m_CenterX, m_CenterY, 0) / m_CurScale;
        }
    }
}