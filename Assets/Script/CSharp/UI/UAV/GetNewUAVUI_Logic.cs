using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{

    public partial class GetNew<PERSON>AVUI
    {
        private LDRewardUIData m_RewardUIData;
        private LDCommonItem m_CommonItem;
        protected override void OnInitImp()
        {
        }
        public void InitData(LDRewardUIData rewardUIData, LDCommonItem commonItem)
        {
            m_CommonItem = commonItem;
            m_RewardUIData = rewardUIData;
            OnFreshUI();
        }
        public override void OnFreshUI()
        {
            UAVItem uAVItem = UAV.Data.Get(m_CommonItem.Id);
            LoadSprite(m_Image.image, uAVItem.UVABG);
            m_info.text.SetTips(uAVItem.desc);
            LDUIPrefabTools.InitNameNodeForUAV(m_NameNode.gameObject, m_CommonItem.Id,m_CommonItem.Quality);
        }

        protected override void OnCloseImp()
        {
            Global.gApp.gUiMgr.CheckExternRewardDataUI(m_RewardUIData);
        }

    }
}
