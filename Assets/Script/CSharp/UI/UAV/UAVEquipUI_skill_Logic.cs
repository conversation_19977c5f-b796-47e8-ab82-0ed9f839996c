namespace LD
{
    public partial class UAVEquipUI_skill
    {
        public void RefreshUI(UAVQualityItem skillQualityItem, UAVQualityItem curQualityItem)
        {
            bool isLock = curQualityItem.quality < skillQualityItem.quality;
            m_Itemlock.gameObject.SetActive(isLock);
            m_img_rankLock.gameObject.SetActive(isLock);
            m_img_rankLogo.gameObject.SetActive(!isLock);
            LDUIPrefabTools.InitItemRank(m_ItemRank.gameObject,  skillQualityItem.quality);
            UAVSkillItem skillCfg = UAVSkill.Data.Get(skillQualityItem.uavSkill);
            if (skillCfg != null)
            {
                m_SkillDesc.text.SetTips(skillCfg.SkillInfo); 
            }
        }
    }
}