using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UAVrollback
    {
        LDUAVDTOItem m_UAVDTOItem;
        protected override void OnInitImp()
        {
            m_btn_reset.AddListener(OnResetWeapon);
            m_btn_close.AddListener(TouchClose);
        }
        public void InitData(LDUAVDTOItem uavDTOItem)
        {
            m_UAVDTOItem = uavDTOItem;
            LDCommonItem lDCommonItem = new LDCommonItem(LDCommonType.UAV, m_UAVDTOItem.UAVID,1);
            lDCommonItem.SetNewQuality(m_UAVDTOItem.UAVQUA);
            KnapsackItem knapsackItem = LDUIPrefabTools.GetKnapsackItemUI(lDCommonItem, m_uav_icon_Node.rectTransform);
            knapsackItem.Num_bg.gameObject.SetActive(false);

            ItemQualityItem quality = ItemQuality.Data.Get(m_UAVDTOItem.UAVQUA);
            m_txt_nowQuality.text.SetTips(quality.name);
            m_txt_nowQuality.text.SetQuaColor(m_UAVDTOItem.UAVQUA);

            m_txt_name_upgrade.text.SetTips(m_UAVDTOItem.UAVItem.name);
            m_txt_name_upgrade.text.SetQuaColor(m_UAVDTOItem.UAVQUA);
            OnFreshUI();
        }
        private void OnResetWeapon()
        {
            Global.gApp.gSystemMgr.gUAVMgr.SendResetUAV(m_UAVDTOItem);
        }
        public override void OnFreshUI()
        {
            //LDUIPrefabTools.InitNameNodeForPart(m_NameNode.gameObject, m_PartCfgItem.id);

            //m_txt_nowLv.text.text = "Lv." + m_NetMechaPartItemDTO.Lv;
            //m_img_arrow.gameObject.SetActive(true);

            costItem.CacheInstanceList();

            UAVQualityItem UAVQualityItem = Global.gApp.gSystemMgr.gUAVMgr.GetQualityItem(m_UAVDTOItem);
            foreach (string rewardStr in UAVQualityItem.rollbackReward)
            {
                LDCommonItem commonItem = new LDCommonItem(rewardStr);
                UAVrollback_costItem partUpgradeUI_CostItem = costItem.GetInstance();
                LDUIPrefabTools.GetKnapsackItemUIQuaIcon(commonItem, partUpgradeUI_CostItem.itemScale.rectTransform);
                partUpgradeUI_CostItem.txt_costNum.text.text = UiTools.FormateMoney(commonItem.Num);
            }
        }
        protected override void OnCloseImp()
        {
        }
    }
}
