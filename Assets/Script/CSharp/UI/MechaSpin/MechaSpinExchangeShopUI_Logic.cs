using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class MechaSpinExchangeShopUI
    {
        private LDNetMechaSpinMgr m_SpinMgr;

        protected override void OnInitImp()
        {
            m_SpinMgr = Global.gApp.gSystemMgr.gMechaSpinMgr;

            RefreshUI();
        }

        public override void OnFreshUI() 
        {
            RefreshUI();
        }

        private void RefreshUI()
        {
            RefreshTime();
            RefreshList();
        }

        // 活动时间
        private void RefreshTime()
        {
            if (m_SpinMgr.IsTimeOver())
            {
                m_time.text.SetTips(67666);
            }
            else
            {
                m_time.text.SetTips(67652, Global.gApp.gGameData.GetExpireTimeTips2(m_SpinMgr.Data.EndTime));
            }
        }

        // 商品列表
        private void RefreshList()
        {
            m_item.CacheInstanceList();
            List<MechaSpinExchangeShopUI_item> uiList = new List<MechaSpinExchangeShopUI_item>();
            foreach (MechaSpinExchangeShopItem item in MechaSpinExchangeShop.Data.items)
            {
                MechaSpinExchangeShopUI_item itemUI = m_item.GetInstance(true);
                itemUI.RefreshUI(item);
                uiList.Add(itemUI);
            }

            uiList.Sort(SortList);
            foreach (MechaSpinExchangeShopUI_item item in uiList)
            {
                item.transform.SetAsLastSibling();
            }
        }

        private int SortList(MechaSpinExchangeShopUI_item a, MechaSpinExchangeShopUI_item b)
        {
            int sortNumA = a.IsSellOut ? 1 : 0;
            int sortNumB = b.IsSellOut ? 1 : 0;

            return sortNumA - sortNumB;
        }

        protected override void OnCloseImp()
        {

        }
    }

    public partial class MechaSpinExchangeShopUI_item
    {
        private LDNetMechaSpinMgr m_SpinMgr;
        private MechaSpinExchangeShopItem m_Cfg;
        public bool IsSellOut = false; // 售罄

        public void RefreshUI(MechaSpinExchangeShopItem cfg)
        {
            m_SpinMgr = Global.gApp.gSystemMgr.gMechaSpinMgr;
            m_Cfg = cfg;

            m_buyBtn.button.AddListener(OnBuy);

            RefreshBaseInfo();
            RefreshReward();
        }

        private void RefreshBaseInfo()
        {
            IsSellOut = !m_SpinMgr.GetShopSurplusNumInfo(m_Cfg.id, out int surplusNum);
            m_buyBtn.gameObject.SetActive(!IsSellOut);
            m_selloutBtn.gameObject.SetActive(IsSellOut);

            // 消耗
            LDCommonItem commonItem = new LDCommonItem(m_Cfg.cost);
            m_Left_Cost.text.SetText(commonItem.Num);

            // 库存
            if (m_Cfg.packTotal > 0)
            {
                m_quota_text.gameObject.SetActive(true);
                m_quota_text.text.SetTips(67688, surplusNum);

            }
            else
            {
                m_quota_text.gameObject.SetActive(false);
            }
        }

        private void RefreshReward()
        {
            m_item.CacheInstanceList();
            LDCommonItem rewardItem = new LDCommonItem(m_Cfg.item);
            MechaSpinExchangeShopUI_item_item itemUI = m_item.GetInstance(true);
            RectTransform rt = itemUI.scaleNode.rectTransform;
            LDCommonTools.DestoryChildren(rt);
            LDUIPrefabTools.GetKnapsackItemUI(rewardItem, rt);
        }

        private void OnBuy()
        {
            if (!m_SpinMgr.GetShopCostInfo(m_Cfg.id, out _, out _))
            {
                Global.gApp.gToastMgr.ShowGameTips(95001); // 道具不足
                return;
            }

            Global.gApp.gUiMgr.OpenUIAsync<MechaSpinPopUI>(LDUICfg.MechaSpinPopUI).SetLoadedCall(ui =>
            {
                ui?.RefreshUI(m_Cfg, (count) =>
                {
                    m_SpinMgr.SendExchangeShopReq(m_Cfg.id, count);
                });
            });
        }
    }
}