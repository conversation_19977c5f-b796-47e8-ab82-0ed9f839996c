using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class GetNewSkinUI
    {
        private CommonModelUI m_CommonModel;
        
        public void Init()
        {
            m_CommonModel = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            
            SetShowImage(string.Empty);
            SetShowModel(string.Empty);
        }

        public void SetInfo(int skinId)
        {
            SkinItem cfg = Skin.Data.Get(skinId);

            m_info.text.SetTips(cfg.skinInfo);
            if (cfg.skinType == 1)
            {
                LDUIPrefabTools.InitNameNodeForMechaSkin(m_NameNode.gameObject, skinId);
                SetShowModel(cfg.skinPrefab);
            }
            else if (cfg.skinType == 2)
            {
                LDUIPrefabTools.InitNameNodeForPartSkine(m_NameNode.gameObject, skinId);
                SetShowImage(cfg.bigIcon);
            }
        }

        private void SetShowModel(string path)
        {
            if (!string.IsNullOrEmpty(path))
            {
                m_ModelImage.gameObject.SetActive(true);
                m_CommonModelUI.gameObject.SetActive(true);

                m_CommonModel.InitModel(path, m_ModelImage.rawImage);
                m_CommonModel.SetRootPosX(9000);
            }
            else
            {
                m_ModelImage.gameObject.SetActive(false);
                m_CommonModelUI.gameObject.SetActive(false);
            }
        }

        private void SetShowImage(string path)
        {
            if (!string.IsNullOrEmpty(path))
            {
                m_partImage.gameObject.SetActive(true);
                LoadSprite(m_partImage.image, path);
            }
            else
            {
                m_partImage.gameObject.SetActive(false);
            }
        }
    }
}