using System.Collections;
using System.Collections.Generic;
using LD.Protocol;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

namespace LD
{
    public partial class GuildLeagueFormatUI
    {
        private LDNetGuildLeagueMgr m_LeagueMgr;
        private LDGuildLeagueInfo m_LeagueInfo;
        private Dictionary<long, LDGuildGVGBasicInfo> m_GuildInfos = new Dictionary<long, LDGuildGVGBasicInfo>();

        protected override void OnInitImp()
        {
            m_LeagueMgr = Global.gApp.gSystemMgr.gGuildLeagueMgr;

            m_btn_close.button.AddListener(TouchClose);
            m_btn_01Tab.button.AddListener(OnTab1);
            m_btn_02Tab.button.AddListener(OnTab2);
            m_btn_03Tab.button.AddListener(OnTab3);
            m_btn_04Tab.button.AddListener(OnTab4);
        }

        public override void OnFreshUI()
        {
        }

        public void InitUI(LDGuildLeagueInfo leagueInfo, Dictionary<long, LDGuildGVGBasicInfo> allGuildInfo)
        {
            m_LeagueInfo = leagueInfo;
            m_GuildInfos = allGuildInfo;
            
            if (m_LeagueMgr.IsLeagueOpened())
            {
                RefreshUI(m_LeagueMgr.GetCurStageType());
            }
            else
            {
                RefreshUI();
            }
        }

        private void RefreshUI(GuildStageType type = GuildStageType.GuildStageEightToFour)
        {
            RefreshTab(type);

            m_Guild.CacheInstanceList();

            Refresh8to4();
            Refresh4to2();
            RefreshFinalWin();
            RefreshFinalLose();
            RefreshFriendShip();
        }

        private void Refresh8to4()
        {
            LDGuildLeagueStageInfo stageInfo = m_LeagueInfo.GetStageInfo(GuildStageType.GuildStageEightToFour);
            for (int i = 0; i < 4; i++)
            {
                LDGuildLeagueTeamInfo teamInfo = null;
                if (stageInfo != null && stageInfo.TeamInfo.Count > i)
                {
                    teamInfo = stageInfo.TeamInfo[i];
                }
                
                RefreshGroup(
                    GuildStageType.GuildStageEightToFour,
                    teamInfo,
                    m_Node8to4.gameObject.transform.GetChild(i * 2),
                    m_Node8to4.gameObject.transform.GetChild(i * 2 + 1),
                    m_WinNode8to4.gameObject.transform.GetChild(i),
                    m_Line8to4.gameObject.transform.GetChild(i * 2),
                    m_Line8to4.gameObject.transform.GetChild(i * 2 + 1)
                );
            }
        }

        private void Refresh4to2()
        {
            LDGuildLeagueStageInfo stageInfo = m_LeagueInfo.GetStageInfo(GuildStageType.GuildStageFourToTwo);
            for (int i = 0; i < 2; i++)
            {
                LDGuildLeagueTeamInfo teamInfo = null;
                if (stageInfo != null && stageInfo.TeamInfo.Count > i)
                {
                    teamInfo = stageInfo.TeamInfo[i];
                }
                
                RefreshGroup(
                    GuildStageType.GuildStageFourToTwo,
                    teamInfo,
                    m_Node4to2.gameObject.transform.GetChild(i * 2),
                    m_Node4to2.gameObject.transform.GetChild(i * 2 + 1),
                    m_WinNode4to2.gameObject.transform.GetChild(i),
                    m_Line4to2.gameObject.transform.GetChild(i * 2),
                    m_Line4to2.gameObject.transform.GetChild(i * 2 + 1)
                );
            }
        }

        private void RefreshFinalWin()
        {
            LDGuildLeagueStageInfo stageInfo = m_LeagueInfo.GetStageInfo(GuildStageType.GuildStageFinalBattleWin);
            LDGuildLeagueTeamInfo teamInfo = null;
            if (stageInfo != null && stageInfo.TeamInfo.Count > 0)
            {
                teamInfo = stageInfo.TeamInfo[0];
            }
            
            RefreshGroup(
                GuildStageType.GuildStageFinalBattleWin,
                teamInfo,
                m_NodeFinal.gameObject.transform.GetChild(0),
                m_NodeFinal.gameObject.transform.GetChild(1),
                m_WinNodeFinal.gameObject.transform.GetChild(0),
                m_LineFinal.gameObject.transform.GetChild(0),
                m_LineFinal.gameObject.transform.GetChild(1)
            );
        }

        private void RefreshFinalLose()
        {
            LDGuildLeagueStageInfo stageInfo = m_LeagueInfo.GetStageInfo(GuildStageType.GuildStageFinalBattleLose);
            LDGuildLeagueTeamInfo teamInfo = null;
            if (stageInfo != null && stageInfo.TeamInfo.Count > 0)
            {
                teamInfo = stageInfo.TeamInfo[0];
            }

            RefreshGroup(
                GuildStageType.GuildStageFinalBattleLose,
                teamInfo,
                m_NodeFinal.gameObject.transform.GetChild(2),
                m_NodeFinal.gameObject.transform.GetChild(3),
                m_WinNodeFinal.gameObject.transform.GetChild(1),
                m_LineFinal.gameObject.transform.GetChild(2),
                m_LineFinal.gameObject.transform.GetChild(3)
            );
        }

        private void RefreshFriendShip()
        {
            LDGuildLeagueStageInfo stageInfo = m_LeagueInfo.GetStageInfo(GuildStageType.GuildStageFriendShip);
            for (int i = 0; i < 2; i++)
            {
                LDGuildLeagueTeamInfo teamInfo = null;
                if (stageInfo != null && stageInfo.TeamInfo.Count > i)
                {
                    teamInfo = stageInfo.TeamInfo[i];
                }
                
                RefreshGroup(
                    GuildStageType.GuildStageFriendShip,
                    teamInfo,
                    m_NodeFriendly.gameObject.transform.GetChild(i * 2),
                    m_NodeFriendly.gameObject.transform.GetChild(i * 2 + 1),
                    null,
                    null,
                    null
                );
            }
        }

        private void RefreshGroup(GuildStageType stageType, LDGuildLeagueTeamInfo teamInfo, Transform node1, Transform node2, Transform winNode, Transform line1, Transform line2)
        {
            GuildLeagueFormatUI_Guild ui1 = m_Guild.GetInstance(true);
            GuildLeagueFormatUI_Guild ui2 = m_Guild.GetInstance(true);
            GuildLeagueFormatUI_Guild uiWin = m_Guild.GetInstance(true);
            ui1.gameObject.SetActive(false);
            ui2.gameObject.SetActive(false);
            uiWin.gameObject.SetActive(false);
            long winnerId = 0;

            if (stageType == GuildStageType.GuildStageFinalBattleWin)
            {
                uiWin.SetScale(0.5f);
            }

            if (teamInfo != null)
            {
                if (teamInfo.IsSomeoneBye(out long guildId))
                {
                    // 有轮空
                    LDGuildGVGBasicInfo guildInfo = GetGuildInfo(guildId);
                    ui1.gameObject.SetActive(true);
                    ui1.RefreshUI(guildInfo, false);
                    ui1.gameObject.transform.SetParent(node1, false);
                    line1?.gameObject.SetActive(guildInfo != null);

                    ui2.gameObject.SetActive(true);
                    ui2.RefreshUI(null, false);
                    ui2.gameObject.transform.SetParent(node2, false);
                    line2?.gameObject.SetActive(false);
                }
                else
                {
                    // 无轮空
                    LDGuildGVGBasicInfo guildInfo1 = GetGuildInfo(teamInfo.GuildId1);
                    LDGuildGVGBasicInfo guildInfo2 = GetGuildInfo(teamInfo.GuildId2);
                    winnerId = m_LeagueMgr.GetWinner(guildInfo1, guildInfo2, teamInfo);
                    
                    ui1.gameObject.SetActive(true);
                    ui1.RefreshUI(guildInfo1, teamInfo.IsLoser(teamInfo.GuildId1));
                    ui1.gameObject.transform.SetParent(node1, false);
                    line1?.gameObject.SetActive(winnerId == teamInfo.GuildId1);

                    ui2.gameObject.SetActive(true);
                    ui2.RefreshUI(guildInfo2, teamInfo.IsLoser(teamInfo.GuildId2));
                    ui2.gameObject.transform.SetParent(node2, false);
                    line2?.gameObject.SetActive(winnerId == teamInfo.GuildId2);
                }

                if (winNode != null)
                {
                    // 胜者节点
                    if (winnerId > 0)
                    {
                        LDGuildGVGBasicInfo guildInfo = GetGuildInfo(winnerId);
                        uiWin.gameObject.SetActive(true);
                        uiWin.RefreshUI(guildInfo, false);
                        uiWin.gameObject.transform.SetParent(winNode, false);
                    }
                    else
                    {
                        uiWin.gameObject.SetActive(true);
                        uiWin.RefreshUI(null, false);
                        uiWin.gameObject.transform.SetParent(winNode, false);
                    }
                }
            }
            else
            {
                // 没有数据
                ui1.gameObject.SetActive(true);
                ui1.RefreshUI(null, false);
                ui1.gameObject.transform.SetParent(node1, false);
                line1?.gameObject.SetActive(false);

                ui2.gameObject.SetActive(true);
                ui2.RefreshUI(null, false);
                ui2.gameObject.transform.SetParent(node2, false);
                line2?.gameObject.SetActive(false);

                if (winNode != null)
                {
                    uiWin.gameObject.SetActive(true);
                    uiWin.RefreshUI(null, false);
                    uiWin.gameObject.transform.SetParent(winNode, false);
                }
            }
        }

        private void RefreshTab(GuildStageType type)
        {
            m_Stage8to4.gameObject.SetActive(type == GuildStageType.GuildStageEightToFour);
            m_Stage4to2.gameObject.SetActive(type == GuildStageType.GuildStageFourToTwo);
            m_StageFinals.gameObject.SetActive(type == GuildStageType.GuildStageFinalBattleWin || type == GuildStageType.GuildStageFinalBattleLose);
            m_StageFriendly.gameObject.SetActive(type == GuildStageType.GuildStageFriendShip);

            m_Select01.gameObject.SetActive(type == GuildStageType.GuildStageEightToFour);
            m_UnSelect01.gameObject.SetActive(type != GuildStageType.GuildStageEightToFour);
            m_Select02.gameObject.SetActive(type == GuildStageType.GuildStageFourToTwo);
            m_UnSelect02.gameObject.SetActive(type != GuildStageType.GuildStageFourToTwo);
            m_Select03.gameObject.SetActive(type == GuildStageType.GuildStageFinalBattleWin || type == GuildStageType.GuildStageFinalBattleLose);
            m_UnSelect03.gameObject.SetActive(type != GuildStageType.GuildStageFinalBattleWin && type != GuildStageType.GuildStageFinalBattleLose);
            m_Select04.gameObject.SetActive(type == GuildStageType.GuildStageFriendShip);
            m_UnSelect04.gameObject.SetActive(type != GuildStageType.GuildStageFriendShip);
        }

        private LDGuildGVGBasicInfo GetGuildInfo(long guildId)
        {
            return m_GuildInfos.GetValueOrDefault(guildId);
        }
        
        private void OnTab1()
        {
            RefreshUI(GuildStageType.GuildStageEightToFour);
        }

        private void OnTab2()
        {
            RefreshUI(GuildStageType.GuildStageFourToTwo);
        }

        private void OnTab3()
        {
            RefreshUI(GuildStageType.GuildStageFinalBattleWin);
        }

        private void OnTab4()
        {
            RefreshUI(GuildStageType.GuildStageFriendShip);
        }

        protected override void OnCloseImp()
        {
        }
    }

    public partial class GuildLeagueFormatUI_Guild
    {
        public void RefreshUI(LDGuildGVGBasicInfo guildInfo, bool isLoser)
        {
            m_Info.gameObject.SetActive(false);
            m_Waiting.gameObject.SetActive(false);

            if (guildInfo != null)
            {
                m_Info.gameObject.SetActive(true);
                m_GuildLogo.gameObject.GetComponent<GuildLogoUI>().RefreshUI(guildInfo.GuildLevel, guildInfo.GuildLogoId);
                m_GuildName.text.SetText(guildInfo.GuildName);
                m_Kill.gameObject.SetActive(isLoser);
                m_MyGuild.gameObject.SetActive(guildInfo.IsMyGuild());
            }
            else
            {
                m_Waiting.gameObject.SetActive(true);
            }
        }

        public void SetScale(float scale)
        {
            m_SizeNode.gameObject.transform.localScale = new Vector3(scale, scale, 1);
            m_WaitingNode.gameObject.transform.localScale = new Vector3(scale, scale, 1);
        }
    }
}