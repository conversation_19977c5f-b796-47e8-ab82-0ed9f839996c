using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDGuildLeaguePrisonMechaData
    {
        public int MechaId;
        public int MechaSkinId;
        // TODO: dqx 助战机甲隐藏皮肤问题
        public int HideSkin;
    }
    
    public class LDGuildLeaguePrison : LDBaseResUI
    {
        private List<LDGuildLeaguePrisonMecha> m_Mechas = new List<LDGuildLeaguePrisonMecha>();

        public bool Stop = true;
        
        
        public void InitMechas(List<LDGuildLeaguePrisonMechaData> mechas)
        {
            m_Mechas.Clear();
            LDCommonTools.DestoryChildren(transform);
            foreach (LDGuildLeaguePrisonMechaData mechaData in mechas)
            {
                LDGuildLeaguePrisonMecha mecha = CreateMecha(mechaData);
                m_Mechas.Add(mecha);
            }

            Stop = false;

            for (int index = m_Mechas.Count - 1; index >= 0; index--)
            {
                LDGuildLeaguePrisonMecha mecha = m_Mechas[index];
                mecha.TryNextAction();
            }
        }
        
        private LDGuildLeaguePrisonMecha CreateMecha(LDGuildLeaguePrisonMechaData data)
        {
            string path = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaPrefabPath(data.MechaId, data.MechaSkinId, data.HideSkin);
            GameObject mechaGo = InstantiateObj(path, ResSceneType.NormalRes, transform);
            LDGuildLeaguePrisonMecha mecha = mechaGo.TryAddComponent<LDGuildLeaguePrisonMecha>();
            mecha.Init();
            return mecha;
        }
        
        private void Update()
        {
            if (Stop)
            {
                return;
            }

            float dt = Time.deltaTime;

            for (int i = m_Mechas.Count - 1; i >= 0; i--)
            {
                LDGuildLeaguePrisonMecha mecha = m_Mechas[i];
                mecha.UpdateMecha(dt);
            }
        }

        public void ClearPrison()
        {
            Stop = true;
            StartCoroutine(ClearPrisonImp());
        }
        
        private IEnumerator ClearPrisonImp()
        {
            yield return 1;
            LDCommonTools.DestoryChildren(transform);
            m_Mechas.Clear();
        }
    }
}
