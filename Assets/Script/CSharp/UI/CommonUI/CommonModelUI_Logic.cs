using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class CommonModelUI
    {
        public Camera RenderCamera { set; get; }
        private RawImage m_ModelUIShow;
        private RenderTexture m_RenderTex;
        private string m_ModelPath;
        Animator m_Animator;
        private readonly Queue<Action> m_PendingAnimations = new Queue<Action>();

        private void Awake()
        {
            RenderCamera = m_RenderCameraNode.gameObject.GetComponentInChildren<Camera>(true);
            RenderCamera.enabled = false;
            // Global.gApp.gGameAdapter.AdaptCamera(RenderCamera);
            SetBg(false);
        }

        public void SetCameraFOV(float fov)
        {
            RenderCamera.fieldOfView = fov;
        }

        public void SetCameraPos(Vector3 pos)
        {
            RenderCamera.transform.localPosition = pos;
        }

        public void SetCameraRot(Vector3 rot)
        {
            RenderCamera.transform.localRotation = Quaternion.Euler(rot);
        }

        public GameObject InitModel(string modelPath, RawImage show, int renderTextureScale = 1)
        {
            m_ModelPath = modelPath;
            m_ModelUIShow = show;
            m_ModelUIShow.enabled = true;
            GameObject go = new GameObject("modeGo");

            LDCommonTools.DestoryChildren(m_ModelNode.transform);
            go.transform.SetParent(m_ModelNode.transform, false);
            InstantiateObjAsync(m_ModelPath, ResSceneType.NormalRes, m_ModelNode.transform, (obj) =>
            {
                if (go != null)
                {
                    obj.transform.SetParent(go.transform, false);
                }

                m_Animator = obj.GetComponentInChildren<Animator>();
                if (m_Animator != null)
                {
                    m_Animator.Play(LDAnimName.DiyNormalAnim[LDAnimName.Idle01], 0);
                    m_Animator.Play(LDAnimName.DiyNormalAnim[LDAnimName.Idle01], 1);

                    while (m_PendingAnimations.Count > 0)
                    {
                        m_PendingAnimations.Dequeue().Invoke();
                    }
                }

                if (m_RenderTex == null)
                {
                    RenderCamera = m_RenderCameraNode.gameObject.GetComponentInChildren<Camera>(true);
                    m_RenderTex = UiTools.CreatureTexture(renderTextureScale);
                    RenderCamera.targetTexture = m_RenderTex;
                    m_ModelUIShow.texture = m_RenderTex;
                    RenderCamera.enabled = true;
                    // Global.gApp.gGameAdapter.AdaptCamera(RenderCamera);
                }


                SetBg(false);
            });

            return go;
        }

        public void PlayAni(string stateName, int layer = 0)
        {
            if (m_Animator == null)
            {
                m_PendingAnimations.Enqueue(() => PlayAni(stateName, layer));
                return;
            }

            m_Animator.Play(stateName, layer);
        }

        public GameObject InitCreature(RawImage show, int mechaId = 0, int scale = 2)
        {
            return InitCreatureImp(show, mechaId, scale, false);
        }
        public GameObject InitCreatureDIY(RawImage show, int mechaId, int scale, bool fromDIY)
        {
            return InitCreatureImp(show, mechaId, scale,fromDIY );
        }

        private GameObject InitCreatureImp(RawImage show, int mechaId, int scale, bool fromDIY)
        {
            m_ModelUIShow = show;
            m_ModelUIShow.enabled = true;

            LDCommonTools.DestoryChildren(m_ModelNode.transform);
            LDCreature creature = new LDCreature();
            if (mechaId > 0)
            {
                creature.Init(m_ModelNode.transform, Global.gApp.gSystemMgr.gDIYMgr.GetDIYData(mechaId));
            }
            else
            {
                creature.Init(m_ModelNode.transform);
            }

            creature.PlayUIIdle();

            if (m_RenderTex == null)
            {
                RenderCamera = m_RenderCameraNode.gameObject.GetComponentInChildren<Camera>(true);
                m_RenderTex = UiTools.CreatureTexture(scale,fromDIY);
                RenderCamera.targetTexture = m_RenderTex;
                m_ModelUIShow.texture = m_RenderTex;
                RenderCamera.enabled = true;
                // Global.gApp.gGameAdapter.AdaptCamera(RenderCamera);
                SetBg(false);
            }

            return creature.MechaNode;
        }

        public GameObject InitDiyData(RawImage show, LDDIYData diyData, int scale = 2, bool isMirror = false)
        {
            m_ModelUIShow = show;
            m_ModelUIShow.enabled = true;

            LDCommonTools.DestoryChildren(m_ModelNode.transform);
            LDCreature creature = new LDCreature();
            creature.Init(m_ModelNode.transform, diyData);
            creature.PlayUIIdle();

            if (m_RenderTex == null)
            {
                RenderCamera = m_RenderCameraNode.gameObject.GetComponentInChildren<Camera>(true);
                m_RenderTex = UiTools.CreatureTexture(scale);
                RenderCamera.targetTexture = m_RenderTex;
                m_ModelUIShow.texture = m_RenderTex;
                RenderCamera.enabled = true;
                // Global.gApp.gGameAdapter.AdaptCamera(RenderCamera);
                SetBg(false);
            }

            MechaItem mechaCfg = Mecha.Data.Get(diyData.BodyId);
            int mirror = isMirror ? -1 : 1;
            m_ModelNode.transform.localEulerAngles = new Vector3(mechaCfg.mechaXYZ[0], mechaCfg.mechaXYZ[1] * mirror, mechaCfg.mechaXYZ[2]);
            m_ModelNode.transform.localScale = Vector3.one * mechaCfg.mechaScale;
            return creature.MechaNode;
        }

        public void SetRootPosX(float posX)
        {
            Vector3 pos = new Vector3(posX, 0, 0);
            m_ModelNodeRoot.transform.localPosition = pos;
        }

        public void SetBg(bool isShow)
        {
            m_diybeijing.gameObject.SetActive(isShow);
        }

        public void SetModelPosZ(float posZ)
        {
            Vector3 pos = m_ModelNode.transform.localPosition;
            pos = new Vector3(pos.x, pos.y, posZ);
            m_ModelNode.transform.localPosition = pos;
        }

        public void Dispose()
        {
            if (RenderCamera != null)
                RenderCamera.targetTexture = null;
            if (m_ModelUIShow != null)
                m_ModelUIShow.texture = null;
            if (m_RenderTex != null)
                GameObject.Destroy(m_RenderTex);
        }
    }
}