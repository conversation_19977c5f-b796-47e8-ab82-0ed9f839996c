using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    /// <summary>
    /// 通用道具Prefab
    /// </summary>
    public partial class KnapsackItem
    {
        private LDCommonItem m_Item;
        private bool m_ShowDetail = true;
        private bool m_ShowDetailFromBag = true;
        private bool m_ShowSimpleDetail = false;
        private Action m_ClickCallback;
        private Action<LDCommonItem> m_ClickCallbackItem;
        private Action m_HoldCallback;

        private void Awake()
        {
            base.ChangeLanguage();
        }

        /// <summary>
        /// 通过LDCommonItem初始化
        /// </summary>
        public void Init(LDCommonItem commonItem)
        {
            if (commonItem == null)
            {
                Global.LogError($"UIBagItem : commonItem = null");
                return;
            }

            m_Item = commonItem;

            InitUI();
        }

        /// <summary>
        /// 通过通用配置结构 道具类型_id_数量  初始化
        /// </summary>
        public void Init(string strConfig)
        {
            if (string.IsNullOrEmpty(strConfig))
            {
                Global.LogError($"UIBagItem : strConfig is null or empty");
                return;
            }

            m_Item = new LDCommonItem(strConfig);
            InitUI();
        }

        private void InitUI()
        {
            // 初始把特殊显示的隐藏了  需要哪个显示哪个
            for (int i = 0; i < m_Offset.rectTransform.childCount; i++)
            {
                m_Offset.rectTransform.GetChild(i).gameObject.SetActive(false);
            }

            ShowButton();
            Update();
        }

        public void ShowButton()
        {
            m_ClickBtn.gameObject.SetActive(true);
            m_ClickBtn.gameObject.GetComponent<LDCustomTouchClick>().OnPointClick = OnClick;
            m_ClickBtn.gameObject.GetComponent<LDCustomTouchClick>().OnPointHold = OnHold;
        }

        // Icon
        public void ShowIcon()
        {
            m_Icon01.gameObject.SetActive(true);
            LoadSprite(m_Icon01.image, m_Item.Icon);
            ShowJiaoBiao();
            ShowUAVJiaoBiao();
        }

        public void ShowEquipIcon(bool isShow = true)
        {
            m_equip.gameObject.SetActive(isShow);
        }

        private void ShowJiaoBiao()
        {
            Icon02_JiaoBiao.gameObject.SetActive(true);
            LoadSprite(Icon02_JiaoBiao.image, m_Item.Icon_JiaoBiao);
        }

        private void ShowUAVJiaoBiao()
        {
            if (m_Item.UAVCfg != null)
            {
                Icon02_JiaoBiao.gameObject.SetActive(false);
                uavType.gameObject.SetActive(true);
                LoadSprite(uavType.image, m_Item.Icon_JiaoBiao);
            }
            else
            {
                uavType.gameObject.SetActive(false);
            }
        }

        // 数量 大于0显示
        public void HideNum()
        {
            m_Num_bg.gameObject.SetActive(false);
        }

        public void ShowNum()
        {
            if (m_Item.Num > 0)
            {
                m_Num_bg.gameObject.SetActive(true);
                m_Num.gameObject.SetActive(true);
                m_Num.text.SetText(UiTools.FormateMoney(m_Item.Num));
            }
        }

        public void ShowNumAndHave()
        {
            if (m_Item.Num > 0)
            {
                m_Num_bg.gameObject.SetActive(true);
                m_Num.gameObject.SetActive(true);
                var isHaveNum = m_Item.CurNum;
                m_Num.text.SetCostTextRich($"{UiTools.FormateMoney(m_Item.Num)}/{UiTools.FormateMoney(isHaveNum)}", isHaveNum, m_Item.Num);
            }
        }

        // 资质 大于普通显示
        public void ShowRarity()
        {
            if (m_Item.Rarity > 1)
            {
                m_Rarity.gameObject.SetActive(true);

                m_Rarity.gameObject.SetActive(m_Item.Rarity == LDItemRarityType.S);
            }
            else
            {
                m_Rarity.gameObject.SetActive(false);
            }
        }

        public void ShowDamageType()
        {
            int damageType = m_Item.DamageType;
            if (damageType > 0)
            {
                m_Xi.gameObject.SetActive(true);
                LoadSprite(m_Xi.image, LDUIResTools.GetElementIconPath(damageType));
            }
        }

        // 品质
        public void ShowQuality(bool isShow = true)
        {
            if (!isShow)
            {
                m_Quality.gameObject.SetActive(false);
                m_Num_Quality.gameObject.SetActive(false);
                return;
            }

            m_Quality.gameObject.SetActive(isShow);
            LoadSprite(m_Quality.image, LDUIResTools.GetItemQualityBg(m_Item.Quality));
            LDCommonTools.DestoryChildren(m_Quality.rectTransform);

            string path = LDUIResTools.GetQualityBgEffectPath(m_Item.Quality);
            if (!string.IsNullOrEmpty(path))
            {
                InstanceUIEffectNode(path, ResSceneType.NormalRes, m_Quality.rectTransform);
            }

            int qualityCount = LDUIResTools.GetItemQualityNum(m_Item.Quality);

            if (qualityCount > 0)
            {
                m_Num_Quality.gameObject.SetActive(true);
                LoadSprite(m_Quality1.image, LDUIResTools.GetItemQualityItemIcon(m_Item.Quality));
                LoadSprite(m_Quality2.image, LDUIResTools.GetItemQualityItemIcon(m_Item.Quality));
                LoadSprite(m_Quality3.image, LDUIResTools.GetItemQualityItemIcon(m_Item.Quality));
                LoadSprite(m_Quality4.image, LDUIResTools.GetItemQualityItemIcon(m_Item.Quality));
                LoadSprite(m_Quality5.image, LDUIResTools.GetItemQualityItemIcon(m_Item.Quality));
                m_Quality1.gameObject.SetActive(qualityCount >= 1);
                m_Quality2.gameObject.SetActive(qualityCount >= 2);
                m_Quality3.gameObject.SetActive(qualityCount >= 3);
                m_Quality4.gameObject.SetActive(qualityCount >= 4);
                m_Quality5.gameObject.SetActive(qualityCount >= 5);
            }
            else
            {
                m_Num_Quality.gameObject.SetActive(false);
            }
        }

        // 遮罩
        public void ShowMask(bool isShow)
        {
            m_Mask.gameObject.SetActive(isShow);
        }

        //只显示锁
        public void ShowRightLock(bool isShow)
        {
            m_RightLocked.gameObject.SetActive(isShow);
        }

        // 可领取
        public void ShowCanGet(bool isShow)
        {
            m_CanGet.gameObject.SetActive(isShow);
        }

        //右上角小锁
        public void ShowLocked(bool isLock)
        {
            m_locked.gameObject.SetActive(isLock);
        }

        private void Update()
        {
            // time倒计时  + 已过期 操作
            if (m_Item != null && m_Item.ItemNetData != null && m_Item.ItemNetData.ExpireAt > 0)
            {
                m_Time.gameObject.SetActive(true);

                m_Time_lbl.text.text = Global.gApp.gGameData.GetExpireTimeTips2(m_Item.ItemNetData.ExpireAt);
            }
            else
            {
                m_Time.gameObject.SetActive(false);
            }
        }

        public void ShowDetail()
        {
            if (m_Item != null)
            {
                m_Item.ShowDetails(m_ShowDetailFromBag);
            }
        }

        public void ShowSimpleDetail()
        {
            if (m_Item != null)
            {
                m_Item.ShowSimpleDetails();
            }
        }

        // 右上角额外奖励标记
        public void ShowBonusMark()
        {
            if (m_Item != null)
            {
                m_Bonus.SetActive(m_Item.IsBonusReward);
            }
        }

        // 右上角矿石奖励标记
        public void ShowOreMark()
        {
            if (m_Item != null)
            {
                m_Ore.SetActive(m_Item.IsOreReward);
            }
        }

        public void SetIsNew()
        {
            if (m_Item != null)
            {
                m_newNode.SetActive(m_Item.GetIsNew());
            }
        }

        /// <summary>
        /// 点击展示详情
        /// </summary>
        public void SetShowDetail(bool isShow)
        {
            m_ShowDetail = isShow;
        }

        public void SetShowSimpleDetail(bool isShow)
        {
            m_ShowSimpleDetail = true;
        }

        /// <summary>
        /// 自定义点击回调
        /// </summary>
        public void SetClickCallback(Action cb)
        {
            m_ClickCallback = cb;
            m_ClickBtn.gameObject.SetActive(true);
        }

        public void SetClickCallbackItem(Action<LDCommonItem> cb)
        {
            m_ClickCallbackItem = cb;
            m_ClickBtn.gameObject.SetActive(true);
        }

        /// 自定义点击回调
        /// </summary>
        public void SetHoldCallback(Action cb)
        {
            m_HoldCallback = cb;
            m_ClickBtn.gameObject.SetActive(true);
        }

        public void SetIsFromBagMark(bool isFromBag)
        {
            m_ShowDetailFromBag = isFromBag;
        }

        /// <summary>
        /// 设置first
        /// </summary>
        /// <param name="isShow"></param>
        public void ShowFirstReward(bool isShow)
        {
            m_firstReward.SetActive(isShow);
        }

        private void OnHold()
        {
            if (m_HoldCallback != null)
            {
                m_HoldCallback.Invoke();
            }
        }

        private void OnClick()
        {
#if UNITY_EDITOR
            Global.Log($"ItemId = {m_Item.Id}");
            if (Input.GetKey(KeyCode.LeftShift))
            {
                long addNum = m_Item.Num;
                if (m_Item.Num == 0)
                {
                    addNum = 100;
                }

                Global.gApp.gSystemMgr.gGmMgr.SendGMCommand($"itemadd {m_Item.Type}_{m_Item.Id}_{addNum}");
                return;
            }
#endif

            if (m_ClickCallback != null) // 优先自定义的callback
            {
                m_ClickCallback.Invoke();
            }
            else if(m_ClickCallbackItem != null) // 优先自定义的callback
            {
                m_ClickCallbackItem.Invoke(m_Item);
            }
            else if (m_ShowSimpleDetail)
            {
                ShowSimpleDetail();
            }
            else if (m_ShowDetail) // 默认
            {
                ShowDetail();
            }
        }
    }
}