using System;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class NetErrorConfirm : LDBaseUI
    {
        private Action m_OnConfirm;
        private Action m_OnCancle;

        protected override void OnInitImp()
        {
            m_ConfirmBtn.button.AddListener(OnConfirm);
            m_CancelBtn.button.AddListener(OnCancel);
            m_contenttxt.text.SetTips(69904);
        }

        public override void OnFreshUI()
        {
        }

        public void SetInfo(Action onConfirm, Action onCancel)
        {
            m_OnConfirm = onConfirm;
            m_OnCancle = onCancel;
        }

        private void OnConfirm()
        {
            TouchClose();
            m_OnConfirm?.Invoke();
        }

        private void OnCancel()
        {
            TouchClose();
            m_OnCancle?.Invoke();
        }

        protected override void OnCloseImp()
        {
        }
    }
}