using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class RoleHead
    {
        private Image m_Icon;
        private Image m_Frame;
        private Image m_AvatarFrame;
        private GameObject m_Empty;
        private GameObject m_Info;

        private LDRoleHeadInfo m_ShowHeadInfo;
        private bool m_IsInited;

        private Action<LDRoleHeadInfo> m_Click;
        public bool CanClick = true;

        private void Init()
        {
            m_Icon = transform.Find("info/icon").GetComponent<Image>();
            m_Frame = transform.Find("info/frame").GetComponent<Image>();
            m_AvatarFrame = transform.Find("info/AvatarFrame").GetComponent<Image>();
            m_AvatarFrame.gameObject.SetActive(true);

            m_Empty = transform.Find("empty").gameObject;
            m_Info = transform.Find("info").gameObject;

            Btn.AddListener(OnShowInfoClick);

            m_IsInited = true;
        }

        // 初始化自己的头像
        public void InitSelf()
        {
            //LDRoleHeadInfo info = Global.gApp.gSystemMgr.gRoleMgr.GetHeadInfo();
            // 暂用机甲id 逻辑 替代

            LDRoleHeadInfo info = Global.gApp.gSystemMgr.gRoleMgr.Data.RoleInfo.RoleHeadInfo;
            info.MechaId = Global.gApp.gSystemMgr.gMechaDataMgr.GetCurBattleMecha().MechaId;
            info.MechaSkinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(info.MechaId);
            info.HideSkin = Global.gApp.gSystemMgr.gMechaSkinDataMgr.ISMechaSkineHide(info.MechaSkinId);
            Init(info);
        }

        // 初始化其他人头像
        public void InitOther(LDRoleHeadInfo info)
        {
            Init(info);
        }

        private void Init(LDRoleHeadInfo info)
        {
            if (!m_IsInited)
            {
                Init();
            }

            m_ShowHeadInfo = info;
            bool isEmpty = info == null;
            m_Info.SetActive(!isEmpty);
            m_Empty.SetActive(isEmpty);
            if (!isEmpty)
            {
                // 头像  临时用机甲逻辑实现
                if (info.IsMySelf())
                {
                    // 自己的直接读本地
                    string path = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaAssistIcon(info.MechaId, info.HideSkin);
                    LoadSprite(m_Icon, path);
                }
                else if (Mecha.Data.TryGet(info.MechaId, out MechaItem mechaCfg, false))
                {
                    if (!info.HideSkin && Skin.Data.TryGet(info.MechaSkinId, out SkinItem skinCfg, false))
                    {
                        if (skinCfg.ComboId == info.MechaId)
                        {
                            LoadSprite(m_Icon, skinCfg.assistSkinIcon);
                        }
                        else
                        {
                            Global.LogError($"头像：机甲ID({info.MechaId}) 与 皮肤ID({info.MechaSkinId}) 不匹配");
                            LoadSprite(m_Icon, mechaCfg.circleIcon);
                        }
                    }
                    else
                    {
                        LoadSprite(m_Icon, mechaCfg.circleIcon);
                    }
                }

                //m_Frame.SetSprite(LoadSprite());
                AvatarBoxItem cfgHeadBox = AvatarBox.Data.Get(info.HeadBox);
                LoadSprite(m_AvatarFrame, cfgHeadBox.avatarDec);
            }
        }

        public void SetHeadBox(int id)
        {
            AvatarBoxItem cfgHeadBox = AvatarBox.Data.Get(id);
            LoadSprite(m_AvatarFrame, cfgHeadBox.avatarDec);
        }

        private void OnShowInfoClick()
        {
            if (!CanClick)
            {
                return;
            }
            if (m_Click != null)
            {
                m_Click?.Invoke(m_ShowHeadInfo);
                return;
            }

            if (m_ShowHeadInfo == null)
            {
                return;
            }

            if (m_ShowHeadInfo.PlayerId > 0)
            {
                if (m_ShowHeadInfo.PlayerId == Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId())
                {
                    return;
                }

                Global.gApp.gSystemMgr.gRoleMgr.OtherRoleData.ShowViewOtherUI(m_ShowHeadInfo.PlayerId);
            }
        }

        public void SetClickCallBack(Action<LDRoleHeadInfo> click)
        {
            m_Click = click;
        }
    }
}