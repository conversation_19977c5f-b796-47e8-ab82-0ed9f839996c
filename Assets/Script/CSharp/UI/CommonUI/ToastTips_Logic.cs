using UnityEngine;
using UnityEngine.UI;

namespace LD
{

	public partial class ToastTips 
	{
        public static string NO_TIPS_CONFIG_FOMAT = "no tips : {0}";
        public void ShowText(int id)
        {
            TipsItem tipsItem = Tips.Data.Get(id);
            if (tipsItem != null)
            {
                ShowText(Global.gApp.gGameData.GetTipsInCurLanguage(id));
            }
            else
            {
                ShowText(string.Format(NO_TIPS_CONFIG_FOMAT, id));
            }

        }

        public void ShowText(int id, string param)
        {
            TipsItem tipsItem = Tips.Data.Get(id);
            if (tipsItem != null)
            {
                ShowText(string.Format(Global.gApp.gGameData.GetTipsInCurLanguage(id), param));
            }
            else
            {
                ShowText(string.Format(NO_TIPS_CONFIG_FOMAT, id));
            }

        }
        public void ShowText(string text)
        {
            ShowTextImp(text);
        }
        private void ShowTextImp(string text)
        {
            TipsText.text.text = text;
            Destroy(gameObject, 1);
        }
    }
}
