using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UISkillItem
    {
        private CitiaoItem m_CitiaoCfg;
        private Action m_Action;

        public void Init(int citiaoId)
        {
            m_Btn.button.AddListener(OnClick);

            m_CitiaoCfg = Citiao.Data.Get(citiaoId);

            //SetBg(path);
            //SetIcon(m_CitiaoCfg.icon);
        }

        public void Init(string iconPath)
        {
            m_Btn.button.AddListener(OnClick);

            SetIcon(iconPath);
        }

        public void Init(string iconPath,string bgPath)
        {
            m_Btn.button.AddListener(OnClick);

            SetBg(bgPath);
            SetIcon(iconPath);
        }

        private void SetIcon(string path)
        {
            LoadSprite(m_SkillIcon.image, path);
        }

        private void SetBg(string path)
        {
            //m_SkillBg.image.SetSprite(LoadSprite(path));
        }

        public void SetOnClick(Action cb)
        {
            m_Action = cb;
        }

        private void OnClick()
        {
            m_Action?.Invoke();
        }
    }
}
