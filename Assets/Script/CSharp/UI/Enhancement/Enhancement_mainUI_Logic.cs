using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class Enhancement_mainUI
    {
        private LDNetEnhancementMgr m_EnhancementMgr;
        private int m_QuaId;
        private List<LDEnhancementLevelNode> m_CurNodes = new List<LDEnhancementLevelNode>();
        private List<Enhancement_mainUI_Slot> m_SlotUIs = new List<Enhancement_mainUI_Slot>();
        private Dictionary<int, LDAttrAddition> m_CurAttrInfo = new Dictionary<int, LDAttrAddition>();
        private bool m_PreventLvUp;
        private bool m_PreventProgress;
        private int m_TotalLv;
        private bool m_IsCurLvMax;
        private float m_CurProPercent;
        private float m_NextProPercent;
        private CommonModelUI m_CommonModel;
        private Enhancement_mainUI_ProItem m_ProItemUI;

        // 特效
        private struct EffectData
        {
            public Enhancement_mainUI_mecha_effect Effect;
            public long CreateTime;
        }

        private List<EffectData> m_Effects = new List<EffectData>();
        private float m_SlotShowEffectInterval = 0.1f;

        protected override void OnInitImp()
        {
            m_EnhancementMgr = Global.gApp.gSystemMgr.gEnhancementMgr;
            m_OverviewBtn.button.AddListener(OnOverview);
            m_btn_close.button.AddListener(TouchClose);
            m_TabMain_UncheckBtn.button.AddListener(OnTabMain);
            m_TabUnlock_UncheckBtn.button.AddListener(OnTabRankUnlock);
            m_RankLeftBtn.button.AddListener(OnRankLeft);
            m_RankRightBtn.button.AddListener(OnRankRight);
            m_RankUnlockBtn.button.AddListener(OnRankUnlock);
            m_CurLvMaxBtn.button.AddListener(OnCurLvMax);
            m_Obtain_Button.button.AddListener(OnCurLvMax);

            m_CommonModel = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();

            m_Exp.CacheInstanceList();
            m_mecha_effect.CacheInstanceList();
            m_AniMask.gameObject.SetActive(false);
            m_ProItem.CacheInstanceList();

            RefreshUI();
            OnTabMain();
            RefreshModelNode();

            m_EnhancementMgr.GetLevelNodesGroup();

            AddTimer(0.2f, 1, (_, _) => { TryGuide(); });
        }

        private void TryGuide()
        {
            if (m_Slot.mCachedList.Count > 0)
            {
                Enhancement_mainUI_Slot itemUI = m_Slot.mCachedList[0];
                if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Enhancement, itemUI.rectTransform(), 2)) { return; }
            }
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(int val)
        {
            if (val == 999)
            {
                OnUnlockSuccess();
                PlayUIAudioClip(AudioConfig.RankUp_Sucess);
                RefreshUI();
            }
            else
            {
                m_SlotUIs[val - 1].Fresh();
                PlayLvUpAni(val);
                PlayUIAudioClip(AudioConfig.UI_CommonLvUp);
                
                CommonUI commonUI = Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.CommonUI) as CommonUI;
                commonUI.FreshCurrrency(LDSpecialItemId.Gold);
            }
        }

        public void RefreshUI()
        {
            RefreshBaseInfo();
            RefreshProgressBar();
            RefreshBtns();
            RefreshCurLvMaxInfo();
        }

        private void RefreshBaseInfo()
        {
            m_QuaId = m_EnhancementMgr.GetCurQuaId();
            EnhancementQualityItem quaCfg = EnhancementQuality.Data.Get(m_QuaId);
            if (!string.IsNullOrEmpty(quaCfg.badgeBG))
            {
                m_BadgeIcon.gameObject.SetActive(true);
                //m_BadgeIcon.image.SetSprite(LoadSprite(quaCfg.badgeBG));
                m_BadgeIconTxt.text.SetText(m_EnhancementMgr.GetLevelName(quaCfg.id));
            }
            else
            {
                m_BadgeIcon.gameObject.SetActive(false);
            }

            // 属性加成
            m_upAttr.CacheInstanceList();
            m_CurAttrInfo = m_EnhancementMgr.GetAttrInfo();
            foreach (int slotId in LDEnhancementSlotId.Ids)
            {
                Enhancement_mainUI_upAttr ui = m_upAttr.GetInstance(true);
                if (m_CurAttrInfo.TryGetValue(slotId, out LDAttrAddition attr))
                {
                    ui.RefreshUI(slotId, attr);
                }
                else
                {
                    ui.SetDefault(slotId);
                }
            }
        }

        private void RefreshProgressBar()
        {
            m_Pro1.image.fillAmount = 0f;
            
            m_TotalLv = m_EnhancementMgr.GetTotalLevel();

            if (m_EnhancementMgr.IsTotalLevelMax())
            {
                m_progressNode.gameObject.SetActive(false);
                m_lvmax.gameObject.SetActive(true);
                m_PreventProgress = true;
                return;
            }
            m_PreventProgress = false;
            
            m_progressNode.gameObject.SetActive(true);
            m_lvmax.gameObject.SetActive(false);

            m_CurNodes = m_EnhancementMgr.GetCurNodes();
            if (m_CurNodes.Count <= 0)
            {
                Global.LogError($"Enhancement_mainUI_Logic RefreshProgressBar error : m_CurNodes.Count <= 0");
            }
            
            if (m_ProItemUI == null)
            {
                m_ProItemUI = m_ProItem.GetInstance(true);
            }
            foreach (LDEnhancementLevelNode levelNode in m_CurNodes)
            {
                if (levelNode.Level > m_TotalLv)
                {
                    m_ProItemUI.RefreshUI(levelNode, this);   
                    break;
                }
            }
            
            RefreshProgress();
            UpdateCurLvNodePos();
        }

        private void RefreshProgress()
        {
            int startLevel = m_CurNodes[0].Level;
            int endLevel = m_CurNodes[m_CurNodes.Count - 1].Level;
            int lvDiff = endLevel - startLevel;
            float percent = lvDiff > 0 ? (float)(m_TotalLv - startLevel) / lvDiff : 0;
            m_ProText.text.SetText($"Lv. {m_TotalLv} / {endLevel}");
            m_Pro1.image.fillAmount = percent;
            m_CurProPercent = percent;

            int nextLevel = endLevel;
            foreach (LDEnhancementLevelNode node in m_CurNodes)
            {
                if (node.Level > m_TotalLv)
                {
                    nextLevel = node.Level;
                    break;
                }
            }

            m_NextProPercent = lvDiff > 0 ? (float)(nextLevel - startLevel) / lvDiff : 0;
        }

        private void RefreshBtns()
        {
            m_Slot.CacheInstanceList();
            m_SlotUIs.Clear();
            float showEffectDelay = m_SlotShowEffectInterval;
            foreach (int slotId in LDEnhancementSlotId.Ids)
            {
                Enhancement_mainUI_Slot slotUI = m_Slot.GetInstance(true);
                slotUI.InitUI(slotId, m_QuaId, OnSlotLvUp);
                slotUI.transform.SetParent(m_Slots.rectTransform.GetChild(slotId - 1));
                slotUI.transform.localPosition = Vector3.zero;
                slotUI.PlayShowEffect(showEffectDelay);
                showEffectDelay += m_SlotShowEffectInterval;
                m_SlotUIs.Add(slotUI);
            }
        }

        private void RefreshCurLvMaxInfo()
        {
            if (m_EnhancementMgr.IsTotalLevelMax())
            {
                m_CurLvMaxNode.gameObject.SetActive(false);
                return;
            }
            
            m_IsCurLvMax = m_EnhancementMgr.IsCurLevelMax();
            m_CurLvMaxNode.gameObject.SetActive(m_IsCurLvMax);
        }

        private void RefreshModelNode()
        {
            int curMechaId = Global.gApp.gSystemMgr.gMechaDataMgr.GetCurBattleMecha().MechaId;
            MechaItem mechaCfg = Mecha.Data.Get(curMechaId);

            GameObject model = m_CommonModel.InitCreature(m_ModelImage.rawImage, curMechaId, 1);
            m_CommonModel.SetRootPosX(7000);
            model.transform.localEulerAngles = new Vector3(mechaCfg.mechaXYZ[0], mechaCfg.mechaXYZ[1], mechaCfg.mechaXYZ[2]);
            model.transform.localScale = Vector3.one * mechaCfg.mechaScale;
        }

        private void UpdateCurLvNodePos()
        {
            int endLevel = m_CurNodes[m_CurNodes.Count - 1].Level;
            m_lv.text.SetText(m_TotalLv);
            m_ProText.text.SetText($"Lv. {m_TotalLv} / {endLevel}");
            float proPosX = m_Pro1.image.rectTransform.localPosition.x + m_Pro1.image.rectTransform.rect.width * m_Pro1.image.fillAmount;
            LDCommonTools.SetLocalPosX(m_currentlv.rectTransform, proPosX);
        }

        #region 动画

        private void PlayLvUpAni(int slotId)
        {
            m_TotalLv = m_EnhancementMgr.GetTotalLevel();
            bool isReachNode = false;
            foreach (LDEnhancementLevelNode node in m_CurNodes)
            {
                if (m_TotalLv == node.Level)
                {
                    isReachNode = true;
                    if (node.IsUpgrade)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.ShieldFightPowerUI);
                    }
                    break;
                }    
            }
            

            int startLevel = m_CurNodes[0].Level;
            int endLevel = m_CurNodes[m_CurNodes.Count - 1].Level;
            int lvDiff = endLevel - startLevel;
            float percent = lvDiff > 0 ? (float)(m_TotalLv - startLevel) / lvDiff : 0;

            Enhancement_mainUI_Exp expUI = m_Exp.GetInstance();
            Vector3 startPos = m_SlotUIs[slotId - 1].ExpPoint.rectTransform.position;
            expUI.Init(m_currentlv.rectTransform, percent, startPos, OnExpAniOver);

            if (!isReachNode)
            {
                m_PreventLvUp = false;
                RefreshBaseInfo();
            }
        }

        private void OnExpAniOver(Enhancement_mainUI_Exp exp)
        {
            float percent = exp.ProPercent;
            if (m_CurProPercent < percent)
            {
                m_CurProPercent = percent;
            }

            m_Exp.CacheInstance(exp);
        }

        private void OnReachNode()
        {
            LDEnhancementLevelNode node = null;
            foreach (LDEnhancementLevelNode item in m_CurNodes)
            {
                if (m_TotalLv == item.Level)
                {
                    node = item;
                    break;
                }
            }
            
            if (node.IsUpgrade)
            {
                Global.gApp.gUiMgr.OpenUIAsync<Enhancement_LevelUpUI>(LDUICfg.Enhancement_LevelUpUI).SetLoadedCall(ui =>
                {
                    ui?.RefreshUI(node.QuaCfgId);
                });
                m_PreventProgress = true;
            }
            else if (!string.IsNullOrEmpty(node.Reward))
            {
                m_ProItemUI.PlayItemAni();
                m_PreventProgress = true;
            }
        }

        private void Update()
        {
            EffectUpdate();

            if (m_PreventProgress)
            {
                return;
            }

            if (m_Pro1.image.fillAmount >= m_CurProPercent)
            {
                return;
            }

            m_Pro1.image.fillAmount += 0.01f;
            UpdateCurLvNodePos();
            if (m_Pro1.image.fillAmount >= m_NextProPercent)
            {
                foreach (LDEnhancementLevelNode levelNode in m_CurNodes)
                {
                    if (m_TotalLv == levelNode.Level)
                    {
                        OnReachNode();
                        break;
                    }
                }
            }
        }

        #endregion 动画

        // 特效
        private void PlayLvUpEffect()
        {
            Enhancement_mainUI_mecha_effect effectUI = m_mecha_effect.GetInstance(true);
            EffectData effectData = new EffectData
            {
                Effect = effectUI,
                CreateTime = DateTimeUtil.GetServerTime(),
            };
            m_Effects.Add(effectData);
        }

        private void EffectUpdate()
        {
            for (int i = m_Effects.Count - 1; i >= 0; i--)
            {
                EffectData effect = m_Effects[i];
                if (DateTimeUtil.GetServerTime() - effect.CreateTime > 500)
                {
                    m_mecha_effect.CacheInstance(effect.Effect);
                    m_Effects.RemoveAt(i);
                }
            }
        }

        private void OnSlotLvUp(Enhancement_mainUI_Slot slot)
        {
            if (m_PreventLvUp)
            {
                return;
            }

            if (m_IsCurLvMax)
            {
                return;
            }

            slot.PlayClickEffect();
            PlayLvUpEffect();

            m_EnhancementMgr.SendLevelUpReq(slot.GetSlotId());
            m_PreventLvUp = true;

            if (!slot.ButtonOnPressed.GetIsDown())
            {
                m_EnhancementMgr.CheckLvUpLongPressTips();
            }
        }

        private void OnOverview()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.Enhancement_Overview);
        }

        protected override void OnOtherUICloseFresh(string uiName)
        {
            Global.gApp.gUiMgr.CloseUI(LDUICfg.ShieldFightPowerUI);
            if (uiName.Equals(LDUICfg.Enhancement_LevelUpUI) || uiName.Equals(LDUICfg.GetRewardUI))
            {
                OnReachNodeRefresh();
            }
        }

        private void OnReachNodeRefresh()
        {
            m_PreventLvUp = false;
            RefreshBaseInfo();
            RefreshProgressBar();
            RefreshCurLvMaxInfo();
            foreach (Enhancement_mainUI_Slot slotUI in m_SlotUIs)
            {
                slotUI.FreshEx(m_QuaId);
            }
        }

        private void OnTabMain()
        {
            m_LevelNode.gameObject.SetActive(true);
            m_RankNode.gameObject.SetActive(false);

            m_TabMain_CheckBtn.gameObject.SetActive(true);
            m_TabMain_UncheckBtn.gameObject.SetActive(false);
            m_TabUnlock_CheckBtn.gameObject.SetActive(false);
            m_TabUnlock_UncheckBtn.gameObject.SetActive(true);
        }

        private void OnTabRankUnlock()
        {
            if (!Global.gApp.gSystemMgr.gEnhancementUnlockMgr.IsUnlock(true))
            {
                return;
            }

            m_LevelNode.gameObject.SetActive(false);
            m_RankNode.gameObject.SetActive(true);
            RefreshRankNode(true);

            m_TabUnlock_CheckBtn.gameObject.SetActive(true);
            m_TabUnlock_UncheckBtn.gameObject.SetActive(false);
            m_TabMain_CheckBtn.gameObject.SetActive(false);
            m_TabMain_UncheckBtn.gameObject.SetActive(true);
        }
        
        private void OnCurLvMax()
        {
            OnTabRankUnlock();
        }

        protected override void OnCloseImp()
        {
            Global.gApp.gUiMgr.CloseUI(LDUICfg.ShieldFightPowerUI);
            Global.gApp.gUiMgr.CloseUI(LDUICfg.LongPressTipsUI);
        }
    }

    #region item class

    public partial class Enhancement_mainUI_upAttr
    {
        private int m_SlotId;

        public void RefreshUI(int slotId, LDAttrAddition attr)
        {
            m_SlotId = slotId;

            RefreshBase();
            m_attrval.text.SetText(attr.GetValueStr());
        }

        private void RefreshBase()
        {
            m_iconHp.gameObject.SetActive(m_SlotId == LDEnhancementSlotId.Hp);
            m_iconAtk.gameObject.SetActive(m_SlotId == LDEnhancementSlotId.Atk);
            m_iconDef.gameObject.SetActive(m_SlotId == LDEnhancementSlotId.Def);
        }

        internal void SetDefault(int slotId)
        {
            m_SlotId = slotId;

            RefreshBase();
            m_attrval.text.SetText(0);
        }
    }

    public partial class Enhancement_mainUI_ProItem
    {
        private LDEnhancementLevelNode m_LvNode;
        private KnapsackItem m_ItemUI;
        private Enhancement_mainUI m_ParentUI;

        public void RefreshUI(LDEnhancementLevelNode node, Enhancement_mainUI parentUI)
        {
            m_LvNode = node;
            m_ParentUI = parentUI;

            ButtonOnPressed buttonPressed = m_badge_btn.gameObject.GetComponent<ButtonOnPressed>();
            buttonPressed.OnPointDown = OnBadgeDown;
            buttonPressed.OnPointUp = OnBadgeUp;

            // m_lv_num.text.SetText(node.Level);
            // m_lv_num.gameObject.SetActive(true);
            m_badge.gameObject.SetActive(false);
            m_item1.gameObject.SetActive(false);
            m_tips.gameObject.SetActive(false);
            // if (node.IsUpgrade)
            // {
            //     EnhancementQualityItem quaCfg = EnhancementQuality.Data.Get(node.QuaCfgId);
            //     string quaName = m_EnhancementMgr.GetLevelName(node.QuaCfgId);
            //     m_badge.gameObject.SetActive(true);
            //     m_lv_num.gameObject.SetActive(false);
            //     m_badge_name.text.SetText(quaName);
            //     m_badge_big.gameObject.SetActive(false);
            //     m_badge_s.gameObject.SetActive(false);
            //     if (quaCfg.smallRank == 0)
            //     {
            //         m_badge_big.gameObject.SetActive(true);
            //         m_badge_big.image.SetSprite(LoadSprite(quaCfg.badgeBG));
            //     }
            //     else
            //     {
            //         m_badge_s.gameObject.SetActive(true);
            //         m_badge_s.image.SetSprite(LoadSprite(quaCfg.badgeBG));
            //         m_badge_s_txt.text.SetText(quaCfg.smallRank);
            //     }
            //
            //     m_tips_name.text.SetText(quaName);
            //     m_describe.text.SetTips(quaCfg.describe);
            // }

            if (!string.IsNullOrEmpty(node.Reward))
            {
                m_item1.gameObject.SetActive(true);
                LDCommonItem commonItem = new LDCommonItem(node.Reward);
                LDCommonTools.DestoryChildren(m_scaleNode.rectTransform);
                m_ItemUI = LDUIPrefabTools.GetKnapsackItemUI(commonItem, m_scaleNode.rectTransform);
                m_ItemUI.ShowQuality(false);
            }

            m_item_lv.text.SetText($"{UiTools.Localize(9502)}{node.Level}");
        }

        public LDEnhancementLevelNode GetLevelNode()
        {
            return m_LvNode;
        }

        public void PlayItemAni()
        {
            if (m_item1.gameObject.TryGetComponent(out Animator animator))
            {
                animator.enabled = true;
            }

            m_ParentUI?.AddTimer(0.3f, 1, (_, _) =>
            {
                Global.gApp.gUiMgr.TryShowGetRewardUI(new LDCommonItem(m_LvNode.Reward));
                animator.enabled = false;
            });
        }

        private void OnBadgeDown()
        {
            // m_tips.gameObject.SetActive(true);
        }

        private void OnBadgeUp()
        {
            // m_tips.gameObject.SetActive(false);
        }
    }

    public partial class Enhancement_mainUI_Slot
    {
        private LDNetEnhancementMgr m_EnhancementMgr;
        private int m_SlotId;
        private int m_QuaId;
        private bool m_IsMax;
        private Action<Enhancement_mainUI_Slot> m_OnLvUp;
        private LDCommonItem m_CostItem;
        public ButtonOnPressed ButtonOnPressed;

        private struct EffectData
        {
            public Enhancement_mainUI_Slot_click_effect Effect;
            public long CreateTime;
        }

        private List<EffectData> m_Effects = new List<EffectData>();
        private float m_ShowEffectDelay = -1;
        private float m_ShowEffectTime;

        public void InitUI(int slotId, int quaId, Action<Enhancement_mainUI_Slot> onLvUp)
        {
            m_EnhancementMgr = Global.gApp.gSystemMgr.gEnhancementMgr;
            m_SlotId = slotId;
            m_OnLvUp = onLvUp;
            ButtonOnPressed = m_PressBtn.gameObject.GetComponent<ButtonOnPressed>();
            ButtonOnPressed.PressAction = OnPressed;
            ButtonOnPressed.IgnorePointerExit = true;
            FreshEx(quaId);

            for (int i = 0; i < m_Icon.rectTransform.childCount; i++)
            {
                m_Icon.rectTransform.GetChild(i).gameObject.SetActive(i + 1 == m_SlotId);
            }

            for (int i = 0; i < m_SlotName.rectTransform.childCount; i++)
            {
                m_SlotName.rectTransform.GetChild(i).gameObject.SetActive(i + 1 == m_SlotId);
            }

            m_click_effect.CacheInstanceList();
        }

        public void FreshEx(int quaId)
        {
            m_QuaId = quaId;
            Fresh();
        }

        public void Fresh()
        {
            int lv = m_EnhancementMgr.Data.GetSlotLv(m_SlotId);
            m_IsMax = false;
            if (m_EnhancementMgr.IsCurLevelMax())
            {
                m_IsMax = true;
            }
            else
            {
                EnhancementQualityItem nextQuaCfg = EnhancementQuality.Data.Get(m_QuaId + 1);
                m_IsMax = lv >= nextQuaCfg.lvmax;
            }

            m_defaultBg.gameObject.SetActive(!m_IsMax);
            m_maxBg.gameObject.SetActive(m_IsMax);
            m_lv.gameObject.SetActive(!m_IsMax);
            m_maxLv.gameObject.SetActive(m_IsMax);
            m_arrt_num.gameObject.SetActive(!m_IsMax);
            m_arrt_MAX_num.gameObject.SetActive(m_IsMax);
            m_maxCost.gameObject.SetActive(m_IsMax);
            m_coin.gameObject.SetActive(!m_IsMax);

            EnhancementQualityItem quaCfg = EnhancementQuality.Data.Get(m_QuaId);
            EnhancementLvItem lvCfg = EnhancementLv.Data.Get(lv);
            int localLv = lv - quaCfg.lvmax;
            m_CostItem = new LDCommonItem(lvCfg.cost);

            m_lv.text.SetText(UiTools.Localize(77009, localLv));
            m_arrt_num.text.SetText($"+{m_EnhancementMgr.GetSlotUpValue(m_SlotId)}");
            m_arrt_MAX_num.text.SetText($"+{m_EnhancementMgr.GetSlotUpValue(m_SlotId)}");
            m_Cost.text.SetText(UiTools.FormateMoney(m_CostItem.Num));

            long curNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(m_CostItem.Id);
            if (curNum < m_CostItem.Num)
            {
                Global.gApp.gSystemMgr.gDIYPackMgr.SendGoldPack();
            }
        }

        public int GetSlotId()
        {
            return m_SlotId;
        }

        public void PlayClickEffect()
        {
            Enhancement_mainUI_Slot_click_effect effectUI = m_click_effect.GetInstance(true);
            EffectData effectData = new EffectData
            {
                Effect = effectUI,
                CreateTime = DateTimeUtil.GetServerTime(),
            };
            m_Effects.Add(effectData);
        }

        public void PlayShowEffect(float delay)
        {
            m_ShowEffectDelay = delay;
            if (TryGetComponent(out Animator ani))
            {
                m_PressBtn.gameObject.SetActive(false);
                ani.enabled = false;
            }
        }

        private void OnPressed()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Enhancement, 2);
            
            if (m_IsMax)
            {
                return;
            }

            if (m_CostItem != null)
            {
                long curNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(m_CostItem.Id);
                if (curNum < m_CostItem.Num)
                {
                    if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.QuickBuyDiamondUI))
                    {
                        return;
                    }

                    if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.MessageBox_KuaiSuHuoQu))
                    {
                        return;
                    }

                    Global.gApp.gUiMgr.TryShowQuickBuyGold(m_CostItem.Num);
                    return;
                }
            }

            m_OnLvUp?.Invoke(this);
        }

        private void Update()
        {
            for (int i = m_Effects.Count - 1; i >= 0; i--)
            {
                EffectData effect = m_Effects[i];
                if (DateTimeUtil.GetServerTime() - effect.CreateTime > 500)
                {
                    m_click_effect.CacheInstance(effect.Effect);
                    m_Effects.RemoveAt(i);
                }
            }

            if (m_ShowEffectDelay > 0)
            {
                m_ShowEffectTime += Time.deltaTime;
                if (m_ShowEffectTime > m_ShowEffectDelay)
                {
                    m_ShowEffectDelay = -1;
                    if (TryGetComponent(out Animator ani))
                    {
                        m_PressBtn.gameObject.SetActive(true);
                        ani.enabled = true;
                    }
                }
            }
        }
    }

    public partial class Enhancement_mainUI_Exp
    {
        private Transform m_Target;
        private Action<Enhancement_mainUI_Exp> m_OnOver;
        private float m_Time = 7f;
        private float m_CurTime;
        public float ProPercent;

        public void Init(Transform target, float proPercent, Vector3 startPos, Action<Enhancement_mainUI_Exp> onOver)
        {
            m_Target = target;
            ProPercent = proPercent;
            m_OnOver = onOver;
            transform.position = startPos;
            m_CurTime = 0f;
        }

        private void Update()
        {
            if (!m_Target)
            {
                return;
            }

            if ((m_Target.position - transform.position).sqrMagnitude < 0.05f)
            {
                m_Target = null;
                m_OnOver?.Invoke(this);
                return;
            }

            m_CurTime += Time.deltaTime;
            transform.position = Vector3.Lerp(transform.position, m_Target.position, m_CurTime / m_Time);
        }
    }

    #endregion item class
}