using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UIFightRankCitiaoView
    {
        private float m_CurTime = 3;
        protected override void OnInitImp()
        {
            m_AnimBtn.AddListener(TouchClose);
        }
        public void InitData(LDCiTiaoItemData data)
        {
            GameObject normalCiTiao =
                Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.NormalCitiao, ResSceneType.NormalRes, m_CitiaoPanel.rectTransform);

            NormalCitiao citiao_Normal = normalCiTiao.GetComponent<NormalCitiao>();
            citiao_Normal.gameObject.SetActive(true);
            citiao_Normal.Init(data, null, 0, 0);
            citiao_Normal.citiao_BG_Btn.image.enabled = false;
            citiao_Normal.root.animator.enabled = false;
            m_AnimatorNode.gameObject.SetActive(true);
            int dstQuality = LDCiTiaoItemData.IsRankCiTiao(data.CitiaoItem.id,true);
            if (dstQuality <= 0)
            {
                m_root.animator.Play("fxani_citiao_qianghua", -1, 0);
                dstQuality = LDCiTiaoItemData.IsPowerCiTiao(data.CitiaoItem.id,true);
                m_txt_title.text.SetTips(9551);
            }
            else
            {
                m_root.animator.Play("fxani_citiao_jiesuo", -1, 0);
                m_txt_title.text.SetTips(9550);
            }

            if (dstQuality > 0)
            {
                LoadSprite(m_img_rank_lock.image, LDUIResTools.GetQualityLockPath(dstQuality));
                LoadSprite(m_img_rank_unlock.image, LDUIResTools.GetQualityMedalPath(dstQuality));

                m_img_rank_lockBG1.image.color = LDUIResTools.GetQualityColor2(dstQuality);
                m_img_rank_lockBG2.image.color = LDUIResTools.GetQualityColor2(dstQuality);
            }
        }
        public override void OnFreshUI()
        {
        }
        private void Update()
        {
            m_CurTime -= Time.deltaTime;
            if (m_CurTime <= 0)
            {
                m_CurTime = 9999;
                if (Global.gApp.gSystemMgr.AutoSelectOpen())
                {
                    TouchClose();
                }
            }
        }
        protected override void OnCloseImp()
        {
            Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.UIFightCitiaoView, 1);
            Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.UIFightCitiao4View, 1);
        }

    }
}
