using LD.Protocol;

namespace LD
{
    public partial class UIFightTeamChat_dialogueItem
    {
        private int m_id;

        public void RefreshText(int id)
        {
            m_id = id;
            m_dialogueText.text.SetTips(id);
            m_btnBg.AddListener(SendTeamChat);
        }

        private void SendTeamChat()
        {
            Global.gApp.gSystemMgr.gChatMgr.SendChatText(ChatType.ChatTeam, UiTools.Localize(m_id));
            Global.gApp.gUiMgr.CloseUI(LDUICfg.UIFightTeamChat);
        }
    }
}