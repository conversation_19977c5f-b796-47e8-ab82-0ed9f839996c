using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class LDFightCitiaoViewData
    {
        public string Icon;
        public LDCiTiaoBaseGroup Group;
        public List<LDCiTiaoItemData> Citiao;

        // 用于战斗外UI显示
        public LDFightCitiaoUIData GetUIData()
        {
            LDFightCitiaoUIData uiData = new LDFightCitiaoUIData();
            uiData.GroupId = Group.CitiaoGroupItem.id;
            foreach (LDCiTiaoItemData ciTiaoItemData in Citiao)
            {
                uiData.CitiaoIds.Add(ciTiaoItemData.CitiaoItem.id);
            }
            uiData.MechaId = Group.GroupInfo.MechaId;
            uiData.CurLv = Group.CitiaoLv;
            uiData.MaxLv = Group.GroupData.MaxLv;
            
            return uiData;
        }
    }
    
    public partial class UIFightPauseView
    {
        private const int Tab_Main = 1;
        private const int Tab_Citiao = 2;
        private const int Tab_Config = 3;

        private int m_Tab = Tab_Main;
        
        protected override void OnInitImp()
        {
            m_exit_btn_m.AddListener(OnExit);
            m_resume_btn_m.AddListener(OnResume);
            m_img_music_on.AddListener(OnMusicStateChange);
            m_img_music_off.AddListener(OnMusicStateChange);

            m_img_sound_on.AddListener(OnSoundStateChange);
            m_img_sound_off.AddListener(OnSoundStateChange);
            
            m_img_shock_on.AddListener(OnShockStateChange);
            m_img_shock_off.AddListener(OnShockStateChange);
            
            m_btn_low.AddListener(OnQuaLow);
            m_btn_middle.AddListener(OnQuaMiddle);
            m_btn_hight.AddListener(OnQuaHight);
            
            m_mainTab_off.AddListener(OnMainTab);
            m_citiaoTab_off.AddListener(OnCitiaoTab);
            m_configTab_off.AddListener(OnConfigTab);

            if(!RuntimeSettings.Release)
            {
                m_DebugNode.gameObject.SetActive(true);
                m_debugLose.AddListener(OnDebugLose);
                m_debugWin.AddListener(OnDebugWin);
            }
            Global.gApp.CurFightScene.Pause();
            OnFreshUI();
        }

        public override void OnFreshUI()
        {
            dropItem.CacheInstanceList();

            m_Nothing.gameObject.SetActive(true);

            List<LDCommonItem> commonItems = LDCommonItem.MergeItems(new List<string>(Global.gApp.CurFightScene.gPassData.enterReward),
                Global.gApp.CurFightScene.gPassHandler.GetGameEndGainItems());
            foreach (LDCommonItem commonItem in commonItems)
            {
                if (commonItem.Type != LDCommonType.Drop)
                {
                    m_Nothing.gameObject.SetActive(false);
                    UIFightPauseView_dropItem rewardItem = dropItem.GetInstance();
                    LDUIPrefabTools.GetKnapsackItemUI(commonItem, rewardItem.scaleNode.rectTransform);
                }
            }

            m_txt_stageName.text.text = Global.gApp.CurFightScene.gPassData.NameStr;
            damageItem.CacheInstanceList();
            LDMainRole mainRole = Global.gApp.CurFightScene.GetLocalRole();
            List<LDDamageContent> damageContent = mainRole.DamageRecord.GetDamageContent();
            int index = 0;
            foreach (LDDamageContent item in damageContent)
            {
                UIFightPauseView_damageItem fightPauseView_DamageItem = damageItem.GetInstance(true);
                index ++;
                fightPauseView_DamageItem.itemBanmaBG.gameObject.SetActive(index % 2 == 1);
                if (item.PartCfgItem != null)
                {
                    string iconPath = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartLocalBigIcon(item.PartCfgItem.id);
                    LoadSprite(fightPauseView_DamageItem.skillIcon.image, iconPath);
                }
                else if (item.MechaItem != null)
                {
                    string bigIconPath = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaBigIcon(item.MechaItem.id);
                    LoadSprite(fightPauseView_DamageItem.skillIcon.image, bigIconPath);
                }
                else if (item.UAVItem != null)
                {
                    LoadSprite(fightPauseView_DamageItem.skillIcon.image, item.UAVItem.Icon);
                }
                else if (item.DriverItem != null)
                {
                    LoadSprite(fightPauseView_DamageItem.skillIcon.image, item.DriverItem.icon);
                }
                else if (item.AircraftCfgItem != null)
                {
                    LoadSprite(fightPauseView_DamageItem.skillIcon.image, item.AircraftCfgItem.icon);
                }
                fightPauseView_DamageItem.txt_damage_TD.text.text = UiTools.FormateMoney(item.Damage); 
                fightPauseView_DamageItem.txt_damage_DPS.text.text = UiTools.FormateMoney(item.DPS); 
            }
            OnFreshTaggleBtnState();
            FreshCitiaoContent();
            SetTab(Tab_Main);
        }
        private void OnFreshTaggleBtnState()
        {
            int musicState = Global.gApp.gAudioSource.BGMVolum;
            int soundState = Global.gApp.gAudioSource.AudioVolum;
            bool shakeState = Global.gApp.CurFightScene.Vibrate;
            int qua = LDPlatformCfg.FightQuality;
            m_img_music_on.gameObject.SetActive(musicState == 1);
            m_img_music_off.gameObject.SetActive(musicState == 0);
            m_img_sound_on.gameObject.SetActive(soundState == 1);
            m_img_sound_off.gameObject.SetActive(soundState == 0);
            m_img_shock_on.gameObject.SetActive(shakeState);
            m_img_shock_off.gameObject.SetActive(!shakeState);
            m_btn_low.gameObject.SetActive(qua != 0);
            m_img_low_on.gameObject.SetActive(qua == 0);
            m_btn_middle.gameObject.SetActive(qua != 1);
            m_img_middle_on.gameObject.SetActive(qua == 1);
            m_btn_hight.gameObject.SetActive(qua != 2);
            m_img_hight_on.gameObject.SetActive(qua == 2);
        }
        private void OnResume()
        {
            TouchClose();
        }
        private void OnExit()
        {
            if (Global.gApp.CurFightScene is LDArenaFightScene)
            {
                 Global.gApp.gUiMgr.OpenUIAsync<ConfirmUI>(LDUICfg.NormalConfirmUI).SetLoadedCall(
                     confirmUI =>
                     {
                         confirmUI.SetInfo(OnExitImp, null, 56057, -1);
                     }
                    ) ;
          
            }
            else
            {
                OnExitImp();
            }
        }

        private void OnExitImp()
        {
            TouchClose();
            Global.gApp.CurFightScene.gPassHandler.TryGameLose(LDGameEndReason.GiveUp);
        }
        
        private void OnMusicStateChange()
        {
            int musicState = Global.gApp.gAudioSource.BGMVolum;
            if(musicState == 1)
            {
                Global.gApp.gAudioSource.BGMVolum = 0;
            }
            else
            {
                Global.gApp.gAudioSource.BGMVolum = 1;
            }
            OnFreshTaggleBtnState();
        }
        private void OnSoundStateChange()
        {
            int soundState = Global.gApp.gAudioSource.AudioVolum;
            if (soundState == 1)
            {
                Global.gApp.gAudioSource.AudioVolum = 0;
            }
            else
            {
                Global.gApp.gAudioSource.AudioVolum = 1;
            }
            OnFreshTaggleBtnState();
        }
        
        private void OnShockStateChange()
        {
            int val = Global.gApp.CurFightScene.Vibrate ? 0 : 1;
            Global.gApp.CurFightScene.Vibrate = val > 0;
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.Fight_Vibrate, val);
            OnFreshTaggleBtnState();
        }
       
        private void OnQuaLow()
        {
            Global.gApp.CurFightScene.FreshRenderSetting(0);
            OnFreshTaggleBtnState(); 
        } 
        private void OnQuaMiddle()
        {
            Global.gApp.CurFightScene.FreshRenderSetting(1);
            OnFreshTaggleBtnState();
        }
        private void OnQuaHight()
        {
            Global.gApp.CurFightScene.FreshRenderSetting(2);
            OnFreshTaggleBtnState();
        } 





        #region Citiao

        private void ShowEmptyCitiaoNode(bool empty)
        {
            m_citiaoIconView.gameObject.SetActive(!empty);
            m_citiaoInfoView.gameObject.SetActive(!empty);
            m_citiaoEmptyNode.gameObject.SetActive(empty); 
        }

        private void FreshCitiaoContent()
        {
            int passType = Global.gApp.CurFightScene.PassType;
            if (passType == LDPassType.Arena || passType == LDPassType.CSArena || passType == LDPassType.GuildLeague)
            {
                ShowEmptyCitiaoNode(true);
            }
            else
            {
                ShowEmptyCitiaoNode(false);
            }
            
            m_citiaoItem.CacheInstanceList();
            m_citiaoInfoItem_good.CacheInstanceList();
            m_citiaoInfoItem_elite.CacheInstanceList();
            m_citiaoInfoItem_normal.CacheInstanceList();
            
            List<LDFightCitiaoViewData> data = Global.gApp.CurFightScene.GetShowCitiaoData();
            foreach (LDFightCitiaoViewData viewData in data)
            {
                if (viewData.Citiao.Count > 0)
                {
                    UIFightPauseView_citiaoItem citiaoItem = m_citiaoItem.GetInstance(true);
                    citiaoItem.RefreshUI(viewData, OnCitiaoGroupSelect);    
                }
            }

            int itemCount = m_citiaoItem.mCachedList.Count;
            if (itemCount > 0)
            {
                OnCitiaoGroupSelect(m_citiaoItem.mCachedList[0]);
            }
            else
            {
                ShowEmptyCitiaoNode(true);
            }
        }

        private void OnCitiaoGroupSelect(UIFightPauseView_citiaoItem obj)
        {
            m_citiaoInfoItem_good.CacheInstanceList();
            m_citiaoInfoItem_elite.CacheInstanceList();
            m_citiaoInfoItem_normal.CacheInstanceList();
            
            foreach (UIFightPauseView_citiaoItem citiaoItem in m_citiaoItem.mCachedList)
            {
                citiaoItem.SetSelect(obj.ViewData.Group.CitiaoGroupItem.id);
            }

            List<LDCiTiaoItemData> selectCiTiao = obj.ViewData.Citiao;
            for (int index = selectCiTiao.Count - 1; index >= 0; index--)
            {
                LDCiTiaoItemData ciTiaoItemData = selectCiTiao[index];
                CitiaoItem citiaoItem = ciTiaoItemData.CitiaoItem;

                if (citiaoItem.citiaoName == 0 || citiaoItem.citiaoDesc == 0)
                {
                    Global.LogError($"战斗暂停： {citiaoItem.id} 技能没有名称或描述");
                }
                
                if (citiaoItem.citiaoType == LDCiTiaoType.Normal)
                {
                    UIFightPauseView_citiaoInfoItem_normal item = m_citiaoInfoItem_normal.GetInstance(true);
                    item.txt_citiaoName_normal.text.SetTips(citiaoItem.citiaoName);
                    item.txt_citiaoDes_normal.text.SetTips(citiaoItem.citiaoDesc);
                }
                else if (citiaoItem.citiaoType == LDCiTiaoType.Power)
                {
                    UIFightPauseView_citiaoInfoItem_good item = m_citiaoInfoItem_good.GetInstance(true);
                    item.txt_citiaoName_good.text.SetTips(citiaoItem.citiaoName);
                    item.txt_citiaoDes_good.text.SetTips(citiaoItem.citiaoDesc);
                }
                else if (citiaoItem.citiaoType == LDCiTiaoType.EliteBook)
                {
                    UIFightPauseView_citiaoInfoItem_elite item = m_citiaoInfoItem_elite.GetInstance(true);
                    item.txt_citiaoName_elite.text.SetTips(citiaoItem.citiaoName);
                    item.txt_citiaoDes_elite.text.SetTips(citiaoItem.citiaoDesc);
                }
            }
            
            LayoutGroup[] layouts = m_citiaoInfoViewContent.rectTransform.GetComponentsInChildren<LayoutGroup>();
            foreach (LayoutGroup layout in layouts)
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(layout.rectTransform());
            }
        }
        
        #endregion

        #region Tab

        private void SetTab(int tab)
        {
            m_Tab = tab;
            FreshTab();
        }

        private void FreshTab()
        {
            m_mainTab_on.gameObject.SetActive(m_Tab == Tab_Main);
            m_mainTab_off.gameObject.SetActive(m_Tab != Tab_Main);
            m_citiaoTab_on.gameObject.SetActive(m_Tab == Tab_Citiao);
            m_citiaoTab_off.gameObject.SetActive(m_Tab != Tab_Citiao);
            m_configTab_on.gameObject.SetActive(m_Tab == Tab_Config);
            m_configTab_off.gameObject.SetActive(m_Tab != Tab_Config);
            
            m_mainContent.gameObject.SetActive(m_Tab == Tab_Main);
            m_citiaoContent.gameObject.SetActive(m_Tab == Tab_Citiao);
            m_configContent.gameObject.SetActive(m_Tab == Tab_Config);
        }

        private void OnMainTab()
        {
            SetTab(Tab_Main);
        }
        
        private void OnCitiaoTab()
        {
            SetTab(Tab_Citiao);
        }
        
        private void OnConfigTab()
        {
            SetTab(Tab_Config);
        }
        
        #endregion
        
        private void OnDebugLose()
        {
            Global.gApp.CurFightScene.gPassHandler.TryGameLose(LDGameEndReason.Debug);
            TouchClose();
        }

        private void OnDebugWin()
        {
            Global.gApp.CurFightScene.gPassHandler.TryGameWin(LDGameEndReason.Debug);
            TouchClose();
        }

        protected override void OnCloseImp()
        {
            Global.gApp.CurFightScene.Resume();
        }
    }
}

