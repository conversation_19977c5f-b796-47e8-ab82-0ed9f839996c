using UnityEngine;

namespace LD
{
    public partial class UIFightView_AIDamage
    {
        public enum ActStep
        {
            Scale = 0,
            Wait = 1,
            FadeOut = 2,
        }

        [HideInInspector]
        public UIFightView_AIDamage PreNode = null;
        [HideInInspector]
        public UIFightView_AIDamage NextNode = null;
        private RectTransform CanvasTransf;
        private LDFightUI m_BattleUI;
        private float m_ScaleTime = 0.2f;
        private float m_WaitTime = 0.4f;
        private float m_FadeTime = 0.4f;
        private float m_EffectTime = 0;
        private float m_LiveTime = 0.3f;
        private float m_CurTime = 0;
        private float m_OffsetX = 120;
        private float m_OffsetY = 60;
        private DamageType m_DamageType = DamageType.Normal;

        private string KillStr => "Kill";
        private string ExpStr => "Exp+{0}";

        private ActStep m_ActStep = ActStep.Scale;

        public void InitDamageType1(LDFightUI battleUI, LDAIMonster monster, double damage)
        {
            InitInfo(battleUI, monster, damage);
            m_DamageType = DamageType.Normal;
            m_LiveTime = m_FadeTime + m_ScaleTime + m_WaitTime;
            Text.text.color = LDCommonColorTools.DamageNormal;
            Text_kill.gameObject.SetActive(false);
            Text_Miss.gameObject.SetActive(false);
            Text.gameObject.SetActive(true);
        }

        public void InitDamageType2(LDFightUI battleUI, LDAIMonster monster, double damage)
        {
            InitInfo(battleUI, monster, damage);
            m_DamageType = DamageType.DamageInc;
            m_LiveTime = m_FadeTime + m_ScaleTime + m_WaitTime;
            Text.text.color = LDCommonColorTools.DamageIncical;
            Text_kill.gameObject.SetActive(false);
            Text_Miss.gameObject.SetActive(false);
            Text.gameObject.SetActive(true);
        }
        public void InitDamageType3(LDFightUI battleUI, LDAIMonster monster, double damage)
        {
            InitInfo(battleUI, monster, damage);
            m_DamageType = DamageType.Crit;
            m_ScaleTime = 0.2f;
            m_WaitTime = 0.3f;
            m_FadeTime = 0.3f;
            m_LiveTime = m_FadeTime + m_ScaleTime + m_WaitTime;
            Text.text.color = LDCommonColorTools.DamageCritical;
            Text_kill.gameObject.SetActive(false);
            Text_Miss.gameObject.SetActive(false);
            Text.gameObject.SetActive(true);
        }        
        public void InitDamageType5(LDFightUI battleUI, LDAIMonster monster, double damage)
        {
            InitInfo(battleUI, monster, damage);
            m_DamageType = DamageType.CritHead;
            m_ScaleTime = 0.2f;
            m_WaitTime = 0.6f;
            m_FadeTime = 0.3f;
            m_LiveTime = m_FadeTime + m_ScaleTime + m_WaitTime;
            Text.text.color = LDCommonColorTools.DamageCritHeadical;
            Text_kill.gameObject.SetActive(false);
            Text_Miss.gameObject.SetActive(false);
            Text.gameObject.SetActive(true);
        }


        public void InitBeheadedKill(LDFightUI battleUI, LDAIMonster monster)
        {
            InitInfo(battleUI, monster, KillStr);
            m_DamageType = DamageType.Beheaded;
            m_LiveTime = m_FadeTime + m_ScaleTime + m_WaitTime;
            Text.text.color = LDCommonColorTools.DamageBeheaded;
            Text_kill.text.color = LDCommonColorTools.DamageBeheaded;
            Text_Miss.text.color = LDCommonColorTools.DamageBeheaded;

            Text.gameObject.SetActive(false);
            Text_Miss.gameObject.SetActive(false);
            Text_kill.gameObject.SetActive(true);
            transform.SetAsLastSibling();
        }       
        public void InitBuffMiss(LDFightUI battleUI, LDSceneObj buffMissObj)
        {
            InitInfo(battleUI, buffMissObj, KillStr);
            m_DamageType = DamageType.BuffMiss;
            m_LiveTime = m_FadeTime + m_ScaleTime + m_WaitTime;
            Text.text.color = LDCommonColorTools.BuffMiss;
            Text_kill.text.color = LDCommonColorTools.BuffMiss;
            Text_Miss.text.color = LDCommonColorTools.BuffMiss;

            Text.gameObject.SetActive(false);
            Text_kill.gameObject.SetActive(false);
            Text_Miss.gameObject.SetActive(true);
            transform.SetAsLastSibling();
        }       
        public void InitExpAdd(LDFightUI battleUI,float expInc, LDHeroPlayer heroPlayer)
        {
            InitInfo(battleUI, heroPlayer, string.Format(ExpStr,UiTools.FormateMoney(expInc)));
            m_DamageType = DamageType.Exp;
            m_LiveTime = m_FadeTime + m_ScaleTime + m_WaitTime;
            Text.text.color = LDCommonColorTools.ExpAdd;
            Text_kill.gameObject.SetActive(false);
            Text_Miss.gameObject.SetActive(false);
            Text.gameObject.SetActive(true);
            m_TextNode.rectTransform.anchoredPosition = UiTools.WorldToRectPos(gameObject, heroPlayer.HeadNode.position,
                CanvasTransf) + new Vector2(Random.Range(-m_OffsetX, m_OffsetX), Random.Range(-m_OffsetY, m_OffsetY));
        }
        private void InitInfo(LDFightUI battleUI, LDSceneObj monster, double damage)
        {
            m_BattleUI = battleUI;
            gameObject.SetActive(true);
            transform.localPosition = Vector3.zero;
            m_TextNode.rectTransform.localScale = Vector3.one;
            Text.text.text = LDNumberString.GetDamageString(damage);
            m_TextNode.rectTransform.anchoredPosition = UiTools.WorldToRectPos(gameObject, LDFightNodeTools.GetBodyNode(monster).position,
               CanvasTransf) + new Vector2(Random.Range(-m_OffsetX, m_OffsetX), Random.Range(-m_OffsetY, m_OffsetY));
            m_CurTime = 0;
            m_EffectTime = 0;
            m_ActStep = ActStep.Scale;
        }

        private void InitInfo(LDFightUI battleUI, LDSceneObj monster, string txt)
        {
            m_BattleUI = battleUI;
            gameObject.SetActive(true);
            //Text.rectTransform.localScale = Vector3.one;
            Text.text.text = txt;
            m_TextNode.rectTransform.anchoredPosition = UiTools.WorldToRectPos(gameObject, LDFightNodeTools.GetBodyNode(monster).position,
               CanvasTransf) + new Vector2(Random.Range(-m_OffsetX, m_OffsetX), Random.Range(-m_OffsetY, m_OffsetY));
            m_CurTime = 0;
            m_EffectTime = 0;
            m_ActStep = ActStep.Scale;
        }


        private void Update()
        {
            if (m_BattleUI == null) return;
            m_CurTime += Time.deltaTime;
            m_EffectTime += Time.deltaTime;
            if (m_CurTime > m_LiveTime)
            {
                m_CurTime = -9999999;
                m_BattleUI.CacheTipsInstance(this);
            }
            else
            {
                if (m_DamageType == DamageType.Normal)
                {
                    Type1Mode(LDCommonColorTools.DamageNormal);
                }
                else if (m_DamageType == DamageType.DamageInc)
                {
                    Type2Mode(LDCommonColorTools.DamageIncical);
                }
                else if (m_DamageType == DamageType.Beheaded)
                {
                    Type2Mode(LDCommonColorTools.DamageBeheaded);
                }
                else if (m_DamageType == DamageType.BuffMiss)
                {
                    Type2Mode(LDCommonColorTools.BuffMiss);
                }
                else if (m_DamageType == DamageType.Crit)
                {
                    Type2Mode(LDCommonColorTools.DamageCritical);
                }                
                else if (m_DamageType == DamageType.CritHead)
                {
                    Type2Mode(LDCommonColorTools.DamageCritHeadical);
                }           
                else if (m_DamageType == DamageType.Exp)
                {
                    Type2Mode(LDCommonColorTools.ExpAdd);
                }
            }
        }

        private void Type1Mode(Color oriColor)
        {
            if (m_ActStep == ActStep.Scale)
            {
                Scale();
                m_TextNode.rectTransform.localScale = m_TextNode.rectTransform.localScale * 0.7f;
            }
            else if (m_ActStep == ActStep.Wait)
            {
                if (m_EffectTime >= m_WaitTime)
                {
                    m_EffectTime = 0;
                    m_ActStep = ActStep.FadeOut;
                }
            }
            else
            {
                FadeOut(oriColor);
            }
        }

        private void Type2Mode(Color oriColor)
        {
            if (m_ActStep == ActStep.Scale)
            {
                Scale();
                if (m_DamageType == DamageType.Beheaded)
                {
                    m_TextNode.rectTransform.localScale = m_TextNode.rectTransform.localScale * 0.6f;
                }
                else if (m_DamageType == DamageType.BuffMiss)
                {
                    m_TextNode.rectTransform.localScale = m_TextNode.rectTransform.localScale * 0.6f;
                }
                else if (m_DamageType == DamageType.Crit)
                {
                    m_TextNode.rectTransform.localScale = m_TextNode.rectTransform.localScale * 1.2f;
                }
                else if (m_DamageType == DamageType.CritHead)
                {
                    m_TextNode.rectTransform.localScale = m_TextNode.rectTransform.localScale * 1.8f;
                }
                else if (m_DamageType == DamageType.Exp)
                {
                    m_TextNode.rectTransform.localScale = m_TextNode.rectTransform.localScale * 0.6f;
                }
            }
            else if (m_ActStep == ActStep.Wait)
            {
                if (m_EffectTime >= m_WaitTime)
                {
                    m_EffectTime = 0;
                    m_ActStep = ActStep.FadeOut;
                }
            }
            else
            {
                FadeOut(oriColor);
            }
        }

        private void Scale()
        {
            float ratio = 0.8f * m_EffectTime / m_ScaleTime;
            float scale = 1.2f + 1 * LDMath.Sin(5 * ratio - 0.5f) + 1f * ratio;
            m_TextNode.rectTransform.localScale = Vector3.one * scale;
            if (m_EffectTime >= m_ScaleTime)
            {
                m_ActStep = ActStep.Wait;
                m_EffectTime = 0;
            }
        }

        private void FadeOut(Color oriColor)
        {
            Vector2 newPos = m_TextNode.rectTransform.anchoredPosition + Vector2.up * 200 * Time.deltaTime;
            m_TextNode.rectTransform.anchoredPosition = newPos;
            Color color = oriColor;
            float ratio = m_EffectTime / m_FadeTime;
            color.a = Mathf.Lerp(1, 0, ratio);
            Text.text.color = color;
            Text_kill.text.color = color;
            Text_Miss.text.color = color;
        }
    }
}