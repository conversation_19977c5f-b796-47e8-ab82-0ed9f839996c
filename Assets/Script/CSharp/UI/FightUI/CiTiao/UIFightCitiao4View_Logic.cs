using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UIFightCitiao4View
    {
        private LDCitiaoUIData m_CitiaoUIData;
        private LDCiTiaoInfo m_CiTiaoInfo;
        private LDMainRole m_MainRole;
        private LDExpUtils m_ExpUtils;
        private List<NormalCitiao> m_NormalCitiaos = new List<NormalCitiao>();
        private List<LDCiTiaoItemData> m_ShowCiTiaoData = new List<LDCiTiaoItemData>();
        private List<LDCiTiaoItemData> m_SelectCiTiao = new List<LDCiTiaoItemData>();
        private float m_CountTime = 10;
        private bool m_CanCountDown = false;
        private Animator m_Animator;

        protected override void OnInitImp()
        {
            Global.gApp.CurFightScene.TryPause();
            m_btn_ok.AddListener(OnConfirm);
            m_btn_ok_grey.AddListener(OnConfirm);
            m_btn_auto.AddListener(OnAutoSelect);
            FreshAutoSelectState();
            m_Animator = gameObject.GetComponent<Animator>();
        }

        public override void OnFreshUI()
        {
        }


        public void InitCitiao(LDExpUtils expUtils, LDMainRole mainRole, LDCitiaoUIData citiaoUIData, LDCiTiaoInfo ciTiaoInfo)
        {
            if (citiaoUIData.ExternEffect <= 0)
            {
                m_Animator.enabled = true;
                m_SkillTipsNode.gameObject.SetActive(false);
                m_root.gameObject.SetActive(true);
                InitCitiaoImp(expUtils, mainRole, citiaoUIData, ciTiaoInfo);
            }
            else
            {
                if (ciTiaoInfo.MechaId > 0)
                {
                    string bigIconPath = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaBigIcon(ciTiaoInfo.MechaId);
                    LoadSprite(m_mechaIcon.image, bigIconPath);
                }

                m_SkillTipsNode.gameObject.SetActive(true);
                m_root.gameObject.SetActive(false);
                m_Animator.enabled = false;

                AddTimer(2, 1, (float a, bool b) =>
                {
                    m_SkillTipsNode.gameObject.SetActive(false);
                    m_Animator.enabled = true;
                    m_root.gameObject.SetActive(true);
                    InitCitiaoImp(expUtils, mainRole, citiaoUIData, ciTiaoInfo);
                });
            }
        }

        private void InitCitiaoImp(LDExpUtils expUtils, LDMainRole mainRole, LDCitiaoUIData citiaoUIData, LDCiTiaoInfo ciTiaoInfo)
        {
            m_ExpUtils = expUtils;
            foreach (NormalCitiao item in m_NormalCitiaos)
            {
                Global.gApp.gResMgr.DestroyGameObj(item.gameObject);
            }

            m_NormalCitiaos.Clear();
            m_ShowCiTiaoData.Clear();
            m_MainRole = mainRole;
            // UI 随机无所谓。反正得点
            m_CitiaoUIData = citiaoUIData;
            m_CiTiaoInfo = ciTiaoInfo;

            FreshMaxCitiaoInfo();
            List<LDCiTiaoItemData> CiTiaoItemDatas = new List<LDCiTiaoItemData>(citiaoUIData.CiTiaoItemDatas);
            int maxCount = Mathf.Min(CiTiaoItemDatas.Count, 4);
            for (int i = 0; i < maxCount; i++)
            {
                ClipExclusiveCiTiao(CiTiaoItemDatas);
                if (CiTiaoItemDatas.Count > 0)
                {
                    ShowNormalCiTiao(i, maxCount, CiTiaoItemDatas);
                }
            }
            OnFreshUI(1);
            FreshSelectText();
        }

        private void ClipExclusiveCiTiao(List<LDCiTiaoItemData> CiTiaoItemDatas)
        {
            if(m_ShowCiTiaoData.Count == 0)
            {
                return;
            }
            List<LDCiTiaoItemData> rmCiTiaoData = new List<LDCiTiaoItemData>();
            for (int i = 0; i < CiTiaoItemDatas.Count; i++)
            {
                int[] exclusiveCiTiao = CiTiaoItemDatas[i].CitiaoItem.exclusiveCitiao;
                if (exclusiveCiTiao.Length > 0)
                {
                    // 互斥词条存在
                    foreach (int exclusiveId in exclusiveCiTiao)
                    {
                        foreach (LDCiTiaoItemData item in m_ShowCiTiaoData)
                        {
                            if(exclusiveId == item.CitiaoItem.id)
                            {
                                rmCiTiaoData.Add(CiTiaoItemDatas[i]);
                                break;
                            }
                        }
                    }
                }
            }
            foreach (LDCiTiaoItemData item in rmCiTiaoData)
            {
                CiTiaoItemDatas.Remove(item);
            }
        }
        private void ShowNormalCiTiao(int citiaoindex,int maxCount, List<LDCiTiaoItemData> CiTiaoItemDatas)
        {

            int maxWeight = 0;
            foreach (LDCiTiaoItemData item in CiTiaoItemDatas)
            {
                maxWeight += item.GetWeight();
            }

            int dstWeight = Random.Range(1, maxWeight);
            int curWeight = 0;
            int index = 0;
            foreach (LDCiTiaoItemData item in CiTiaoItemDatas)
            {
                curWeight += item.GetWeight();
                if (curWeight > dstWeight)
                {
                    break;
                }
                else
                {
                    index++;
                }
            }

            int realIndex = index % CiTiaoItemDatas.Count;
            LDCiTiaoItemData data = CiTiaoItemDatas[realIndex];

            CiTiaoItemDatas.RemoveAt(realIndex);



            GameObject normalCiTiao =
                Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.NormalCitiao, ResSceneType.NormalRes, m_CitiaoPanel.rectTransform);

            NormalCitiao citiao_Normal = normalCiTiao.GetComponent<NormalCitiao>();
            citiao_Normal.gameObject.SetActive(true);
            citiao_Normal.Init4(data, this, citiaoindex, maxCount,false);
            m_NormalCitiaos.Add(citiao_Normal);
            m_ShowCiTiaoData.Add(data);

        }

        public override void OnFreshUI(int val)
        {
            if (val == 1)
            {
                TryOpeRelateCiTiaoView();
            }
        }


        private void TryOpeRelateCiTiaoView()
        {
            if (m_ShowCiTiaoData.Count > 0)
            {
                LDCiTiaoItemData data = null;
                for (int i = 0; i < m_ShowCiTiaoData.Count; i++)
                {
                    if (!m_ExpUtils.FreshCitiaoInfo.Contains(m_ShowCiTiaoData[i]))
                    {
                        m_ExpUtils.FreshCitiaoInfo.Add(m_ShowCiTiaoData[i]);
                        if (m_ShowCiTiaoData[i].CitiaoItem.optionalCondition.Length > 0)
                        {
                            data = m_ShowCiTiaoData[i];
                            break;
                        }
                    }
                }

                if (data != null)
                {
                    gameObject.SetActive(false);
                    Global.gApp.gUiMgr.OpenUIAsync<UIFightRelateCitiaoView>(LDUICfg.UIFightRelateCitiaoView).SetLoadedCall(
                        fightRelateCitiaoView =>
                        {
                            fightRelateCitiaoView?.InitData(data);
                        }
                        );
           
                    return;
                }
                else
                {
                    gameObject.SetActive(true);
                }


                for (int i = 0; i < m_ShowCiTiaoData.Count; i++)
                {
                    if (!m_ExpUtils.RankCitiaoInfo.Contains(m_ShowCiTiaoData[i]))
                    {
                        m_ExpUtils.RankCitiaoInfo.Add(m_ShowCiTiaoData[i]);
                        if (LDCiTiaoItemData.IsRankCiTiao(m_ShowCiTiaoData[i].CitiaoItem.id) > 0 || LDCiTiaoItemData.IsPowerCiTiao(m_ShowCiTiaoData[i].CitiaoItem.id) > 0)
                        {
                            data = m_ShowCiTiaoData[i];
                            break;
                        }
                    }
                }

                if (data != null)
                {
                    gameObject.SetActive(false);
                    Global.gApp.gUiMgr.OpenUIAsync<UIFightRankCitiaoView>(LDUICfg.UIFightRankCitiaoView).SetLoadedCall(
                        fightRelateCitiaoView =>
                        {
                            fightRelateCitiaoView?.InitData(data);
                        }
                       );
                    return;
                }
                else
                {
                    gameObject.SetActive(true);
                }
            }
            else
            {
                gameObject.SetActive(true);
            }
            if (Global.gApp.gSystemMgr.AutoSelectOpen())
            {
                m_CanCountDown = true;
            }
        }

        public void OnSelect(LDCiTiaoItemData ciTiaoItemData)
        {
            if(m_SelectCiTiao.Contains(ciTiaoItemData))
            {
                m_SelectCiTiao.Remove(ciTiaoItemData);
                foreach (NormalCitiao item in m_NormalCitiaos)
                {
                    if(item.Data == ciTiaoItemData)
                    {
                        item.selected.gameObject.SetActive(false);
                        break;
                    }
                }
            }
            else
            {
                if (m_SelectCiTiao.Count < 2)
                {
                    m_SelectCiTiao.Add(ciTiaoItemData);
                    foreach (NormalCitiao item in m_NormalCitiaos)
                    {
                        if (item.Data == ciTiaoItemData)
                        {
                            item.selected.gameObject.SetActive(true);
                            break;
                        }
                    }
                }
                else
                {
                    Global.gApp.gToastMgr.ShowGameTips(91183);
                }
            }
            FreshSelectText();
        }

        private void OnConfirm()
        {
            if(m_ShowCiTiaoData.Count >= 2)
            {
                if(m_SelectCiTiao.Count < 2)
                {
                    Global.gApp.gToastMgr.ShowGameTips(91182);
                    return;
                }
            }
            else
            {
                if(m_SelectCiTiao.Count == 0)
                {
                    return;
                }
            }
            int index = 0;
            foreach (LDCiTiaoItemData ciTiaoItemData in m_SelectCiTiao)
            {
                ciTiaoItemData.CiTiaoGroup.SetCitiaoUsed(ciTiaoItemData.CitiaoItem.id);
                //int sequence = m_CiTiaoInfo.Sequence + index;
                //Global.gApp.CurFightScene.Orders.SendCiTiaoSelect(Global.gApp.CurFightScene.LocalPlayerGuid,
                // m_CiTiaoInfo.PlayerLv, sequence, ciTiaoItemData.CiTiaoGroup.CitiaoGroupItem.id, ciTiaoItemData.CitiaoItem.id);
                //index++;
            }
            TouchClose();
        }

        private void FreshAutoSelectState()
        {
            if (Global.gApp.gSystemMgr.AutoSelectOpen())
            {
                m_autoNode.gameObject.SetActive(true);
                string showMark = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.AutoSelectCitiao);
                m_img_choose.gameObject.SetActive(!string.IsNullOrEmpty(showMark));
                m_timeShowNode.gameObject.SetActive(m_img_choose.gameObject.activeSelf);
            }
            else
            {
                m_autoNode.gameObject.SetActive(false);
            }
        }

        private void Update()
        {
            if(!m_CanCountDown)
            { return; }
            string showMark = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.AutoSelectCitiao);
            if (!string.IsNullOrEmpty(showMark))
            {
                if (m_CountTime < 100)
                {
                    m_CountTime -= Time.deltaTime;
                    if (m_CountTime <= 0)
                    {
                        TryAutoSelectCiTiao();
                        m_CountTime = 1000;
                    }
                    else
                    {
                        string sTips = Global.gApp.gGameData.GetTipsInCurLanguage(96005);
                        m_txt_timeShow.text.text = string.Format(sTips, Mathf.CeilToInt(m_CountTime).ToString());
                    }
                }
            }
        }
        private void OnAutoSelect()
        {
            m_CountTime = 10;
            string showMark = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.AutoSelectCitiao);
            if (string.IsNullOrEmpty(showMark))
            {
                Global.gApp.gSystemMgr.gNetClientDataMgr.ChangeGenerateClientData(GeneralClientDataKey.AutoSelectCitiao, "1");
            }
            else
            {
                Global.gApp.gSystemMgr.gNetClientDataMgr.ChangeGenerateClientData(GeneralClientDataKey.AutoSelectCitiao, string.Empty);
            }
            FreshAutoSelectState();
            Update();
        }
        private void TryAutoSelectCiTiao()
        {
            AddTouchMask();
            int maxCount = 2 - m_SelectCiTiao.Count;
            for (int i = 0; i < maxCount; i++)
            {
                foreach (LDCiTiaoItemData item in m_SelectCiTiao)
                {
                    m_ShowCiTiaoData.Remove(item);
                }
                foreach (LDCiTiaoItemData item in m_ShowCiTiaoData)
                {
                    if(item.CitiaoItem.citiaoType == LDCiTiaoBaseGroup.PowerType)
                    {
                        m_SelectCiTiao.Add(item);
                        break;
                    }
                }
                foreach (LDCiTiaoItemData item in m_SelectCiTiao)
                {
                    m_ShowCiTiaoData.Remove(item);
                }
            }

            maxCount = 2 - m_SelectCiTiao.Count;
            for (int i = 0; i < maxCount; i++)
            {
                foreach (LDCiTiaoItemData item in m_SelectCiTiao)
                {
                    m_ShowCiTiaoData.Remove(item);
                }
                if (m_ShowCiTiaoData.Count > 0)
                {
                    int index = Random.Range(0, m_ShowCiTiaoData.Count);
                    m_SelectCiTiao.Add(m_ShowCiTiaoData[index]);
                }
                foreach (LDCiTiaoItemData item in m_SelectCiTiao)
                {
                    m_ShowCiTiaoData.Remove(item);
                }
            }

            foreach (NormalCitiao item in m_NormalCitiaos)
            {
                if (m_SelectCiTiao.Contains(item.Data))
                {
                    item.selected.gameObject.SetActive(true);
                    item.ShowAutoSelectAnim();
                }
            }
            FreshSelectText();
            AddTimer(1, 1, (float a, bool b) =>
            {
                OnConfirm();
            });
        }


        private void FreshSelectText()
        {
            int curCount = m_SelectCiTiao.Count;
            m_txt_count.text.SetTips(9543,curCount);

            if (m_SelectCiTiao.Count >= 2 || m_SelectCiTiao.Count >= m_ShowCiTiaoData.Count)
            {
                m_btn_ok.gameObject.SetActive(true);
                m_btn_ok_grey.gameObject.SetActive(false);
                foreach (NormalCitiao item in m_NormalCitiaos)
                {
                    if(m_SelectCiTiao.Contains(item.Data))
                    {
                        item.NormalBg_Grey.gameObject.SetActive(false);
                    }
                    else
                    {
                        item.NormalBg_Grey.gameObject.SetActive(true);
                    }
                }
            }
            else
            {
                m_btn_ok.gameObject.SetActive(false);
                m_btn_ok_grey.gameObject.SetActive(true);
                foreach (NormalCitiao item in m_NormalCitiaos)
                {
                    item.NormalBg_Grey.gameObject.SetActive(false);
                }
            }
        }

        private void FreshMaxCitiaoInfo()
        {
            max_citiao_show.CacheInstanceList();
            List<LDCiTiaoBaseGroup> CiTiaoItemDatas = m_MainRole.GetCaptainPlayer().CiTiaoMgr.GetMaxCiTiaoGroup();
            if (CiTiaoItemDatas.Count > 0)
            {
                m_maxCitiao.gameObject.SetActive(true);
                foreach (LDCiTiaoBaseGroup item in CiTiaoItemDatas)
                {
                    UIFightCitiao4View_max_citiao_show fightCitiaoView_Max_Citiao_Show = max_citiao_show.GetInstance();
                    string icon = LDCiTiaoBaseGroup.GetCitiaoGroupIcon(item.CitiaoGroupItem);
                    LoadSprite(fightCitiaoView_Max_Citiao_Show.img_diypart_icon.image, icon);
                }
            }
            else
            {
                m_maxCitiao.gameObject.SetActive(false);
            }
        }

        protected override void OnCloseImp()
        {
            Global.gApp.CurFightScene.TryResume();
        }
    }
}