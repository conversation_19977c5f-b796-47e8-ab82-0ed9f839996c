using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UImagnet
    {
        private bool m_FreeAd = false;

        protected override void OnInitImp()
        {
            m_cancel.AddListener(TouchClose);
            m_btn_close.AddListener(TouchClose);
            m_use.AddListener(OnAd);
            m_buy.AddListener(onBuy);
            Global.gApp.CurFightScene.Pause();

            m_buyNode.gameObject.SetActive(Global.gApp.gSystemMgr.gMonthlyCardMgr.IsUnlock(false));

            int passId = GlobalCfg.Data.Get(LDGlobalConfigId.MagnetTrialHide).valueIntarray[0];
            if (Global.gApp.CurFightScene.gPassData.PassItem.id == passId)
            {
                m_FreeAd = true;
            }

            m_adImage.gameObject.SetActive(!m_FreeAd);
            m_adFree.gameObject.SetActive(m_FreeAd);

            m_num.text.SetTips(99430, Global.gApp.gSystemMgr.gAdvertiseMgr.Data.GetAdvertiseLeftCount(10014));
        }

        private void OnAd()
        {
            if (!m_FreeAd)
            {
                Global.gApp.gSdkMgr.gADMgr.Show(10014, LDUICfg.UImagnet, 1);
            }
            else
            {
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.FightUI, 1);
            }
        }

        private void onBuy()
        {
            Global.gApp.gUiMgr.OpenUIAsync<MonthlyCardUI>(LDUICfg.MonthlyCardUI).SetLoadedCall(monthlyCardUI =>
                {
                    if (monthlyCardUI != null)
                    {
                        monthlyCardUI.MonthlyCard02.gameObject.SetActive(false);
                    }
                }
            );
        }

        protected override void OnOtherUICloseFresh(string uiName)
        {
            if (Global.gApp.gSystemMgr.gMonthlyCardMgr.GetMonthlyNormal())
            {
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.FightUI, 1);
                // fightUI 关闭了
            }
        }

        public override void OnFreshUI()
        {
        }

        protected override void OnCloseImp()
        {
            Global.gApp.CurFightScene.Resume();
        }
    }
}