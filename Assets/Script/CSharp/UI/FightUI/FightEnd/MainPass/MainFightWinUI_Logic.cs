using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDMainFightEndUIData : LDUIDataBase
    {
        public int MaxProgress { get; set; }
        public List<LDCommonItem> Rewards { get; set; } = new List<LDCommonItem>();
    }

    public partial class MainFightWinUI
    {
        private LDNetMainPassMgr m_MainPassMgr;
        private LDFightNormalScene m_FightScene;
        private LDMainHandler m_Handler;
        private LDPassInfo m_PassInfo;
        private MainMissionItem m_MissionCfg;
        private LDMainFightEndUIData m_UIData;
        private LDMainRole m_MainRole;
        private bool m_IsTimeout;

        private MainFightResultUI m_ResultUI;

        protected override void OnInitImp()
        {
            Global.gApp.CurFightScene.Pause();
            m_SendBtn.button.AddListener(OnSend);

            m_MainPassMgr = Global.gApp.gSystemMgr.gMainPassMgr;
            m_FightScene = Global.gApp.CurFightScene as LDFightNormalScene;
            m_Handler = m_FightScene.gPassHandler as LDMainHandler;
            m_PassInfo = m_FightScene.gPassInfo;
            m_MissionCfg = MainMission.Data.Get(m_PassInfo.PassData.MisssionId);
            m_MainRole = Global.gApp.CurFightScene.GetLocalRole();
            m_IsTimeout = m_PassInfo.GetGameEndReason() == LDGameEndReason.TimeLimit;

            m_SendBtn.gameObject.SetActive(true);
            m_root.gameObject.SetActive(false);
            int adRewardThree = Global.gApp.gSystemMgr.gVipMgr.GetPrivilegeNum(PrivilegeType.DoubleChangeTriple);
            if (adRewardThree == 0)
            {
                m_fightresult_lable_ad_a.text.SetTips(9517);
            }
            else
            {
                m_fightresult_lable_ad_a.text.SetTips(9547);
            }

            SendFightWin();
        }

        public override void OnFreshUI() { }
        
        public override void OnFreshUI(int val)
        {
            if (val == 1) // 广告双倍奖励
            {
                int adRewardThree = Global.gApp.gSystemMgr.gVipMgr.GetPrivilegeNum(PrivilegeType.DoubleChangeTriple);
                if (adRewardThree == 0)
                {
                    m_ResultUI?.DoubleDropReward(2);
                }
                else
                {
                    m_ResultUI?.DoubleDropReward(3);
                }
            }
        }

        public override void OnFreshUI(LDUIDataBase uiData)
        {
            m_UIData = uiData as LDMainFightEndUIData;

            m_SendBtn.gameObject.SetActive(false);
            m_root.gameObject.SetActive(true);


            RefreshResultInfo();
        }

        private void RefreshResultInfo()
        {
            MainFightResultUI resultUI = m_MainFightResultUI.gameObject.GetComponent<MainFightResultUI>();
            m_ResultUI = resultUI;

            resultUI.InitUI();
            resultUI.SetWin(true);
            resultUI.SetPassName(m_MainPassMgr.GetMainPassFullNameStr(m_MissionCfg.id));
            //float progress = m_IsTimeout ? -1 : Global.gApp.CurFightScene.gVictoryMgr.GetProgress();
            resultUI.SetPassProgress(-1); // 胜利不显示进度
            resultUI.SetTimeout(m_IsTimeout);
            // bool isNewRecord = 100 > m_UIData.MaxProgress;
            // resultUI.SetNewRecord(isNewRecord);
            resultUI.SetDropInfo(m_UIData.Rewards);
            resultUI.SetDamageInfo(m_MainRole.DamageRecord.GetDamageContent());
            int adId = m_MainPassMgr.GetBattleEndDoubleRewardADId();
            m_ResultUI.SetDoubleReward(adId, OnAd);
        }

        private void SendFightWin()
        {
            float time = m_PassInfo.GetGameEndReason() == LDGameEndReason.Debug ? 999 : m_PassInfo.GetTime();
            List<LDCommonItem> commonItems = new List<LDCommonItem>();
            foreach (string item in m_Handler.GetGameEndGainItems())
            {
                LDCommonItem data = new LDCommonItem(item);
                commonItems.Add(data);
            }
            m_MainPassMgr.SendPassEnd(m_PassInfo.PassData.MisssionId, time, LDNetPassResult.Success, m_FightScene.gVictoryMgr.GetProgress(), commonItems, m_PassInfo.FightData.UniqueId, Global.gApp.CurFightScene.gWaveMgr.KillAICount);
        }

        private void OnSend()
        {
            SendFightWin();
        }

        private void OnAd()
        {
            int adId = m_MainPassMgr.GetBattleEndDoubleRewardADId();
            Global.gApp.gSdkMgr.gADMgr.Show(adId, LDUICfg.MainFightWinUI, 1);            
        }

        protected override void OnCloseImp()
        {

        }
    }
}
