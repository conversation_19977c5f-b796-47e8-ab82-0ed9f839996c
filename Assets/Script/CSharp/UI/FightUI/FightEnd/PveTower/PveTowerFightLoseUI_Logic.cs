using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PveTowerFightLoseUI
    {
        private LDNetPveTowerDataMgr m_PveTowerMgr;
        private LDPveTowerFightScene m_FightScene;
        private LDPveTowerHandler m_Handler;
        private LDPassInfo m_PassInfo;
        private TowerMissionItem m_MissionCfg;
        private LDPveTowerFightEndUIData m_UIData;
        private LDMainRole m_MainRole;
        private bool m_IsTimeout;

        protected override void OnInitImp()
        {
            Global.gApp.CurFightScene.Pause();
            m_SendBtn.button.AddListener(OnSend);

            m_PveTowerMgr = Global.gApp.gSystemMgr.gPveTowerDataMgr;
            m_FightScene = Global.gApp.CurFightScene as LDPveTowerFightScene;
            m_Handler = m_FightScene.gPassHandler as LDPveTowerHandler;
            m_PassInfo = m_FightScene.gPassInfo;
            m_MissionCfg = TowerMission.Data.Get(m_PassInfo.PassData.MisssionId);
            m_MainRole = Global.gApp.CurFightScene.GetLocalRole();
            m_IsTimeout = m_PassInfo.GetGameEndReason() == LDGameEndReason.TimeLimit;

            m_SendBtn.gameObject.SetActive(true);
            m_root.gameObject.SetActive(false);
            SendFightLose();
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(LDUIDataBase uiData)
        {
            m_UIData = uiData as LDPveTowerFightEndUIData;

            m_SendBtn.gameObject.SetActive(false);
            m_root.gameObject.SetActive(true);


            RefreshResultInfo();
        }

        private void RefreshResultInfo()
        {
            MainFightResultUI resultUI = m_MainFightResultUI.gameObject.GetComponent<MainFightResultUI>();

            resultUI.InitUI();
            resultUI.SetWin(false);
            resultUI.SetPassName(UiTools.Localize(m_MissionCfg.name));
            float progress = m_IsTimeout ? -1 : Global.gApp.CurFightScene.gVictoryMgr.GetProgress();
            resultUI.SetPassProgress(progress);
            resultUI.SetTimeout(m_IsTimeout);
            resultUI.SetNewRecord(false);
            resultUI.SetDropInfo(m_UIData.Rewards);
            resultUI.SetDamageInfo(m_MainRole.DamageRecord.GetDamageContent());
            resultUI.SetDoubleReward(-1, null);
        }

        private void SendFightLose()
        {
            List<LDCommonItem> commonItems = new List<LDCommonItem>();
            foreach (string item in m_Handler.GetGameEndGainItems())
            {
                LDCommonItem data = new LDCommonItem(item);
                commonItems.Add(data);
            }

            int missionEndStatus = m_PassInfo.GetGameEndReason() == LDGameEndReason.GiveUp ? LDNetPassResult.GiveUp: LDNetPassResult.Failure;
            m_PveTowerMgr.SendBattleEnd(m_PassInfo.PassData.MisssionId, missionEndStatus, commonItems, (int)m_PassInfo.GetTime(), m_PassInfo.FightData.UniqueId);
        }

        private void OnSend()
        {
            SendFightLose();
        }

        protected override void OnCloseImp()
        {
        }
    }
}