using System;
using System.Collections.Generic;
using SuperScrollView;
using UnityEngine;

namespace LD
{
    public partial class SeasonRankUI
    {
        public const int PerPageCount = 30; // 一页
        public const int PreCount = 10; // 提前

        private LoopListView2 m_LoopListView;

        private List<SeasonMissionItem> m_SortItem;
        private List<LDRankSeasonMissionUIItemData> m_Data = new List<LDRankSeasonMissionUIItemData>();

        private int m_CurDataMaxIndex;

        private RedTips m_RankListRedTip;

        public void InitReward()
        {
            m_SortItem = m_SeasonMgr.GetMissionRewardSortCfgs(m_SeasonId);
            m_CurDataMaxIndex = Mathf.Min(PerPageCount - 1, m_SortItem.Count - 1);

            m_LoopListView = m_reward.gameObject.GetComponent<LoopListView2>();
            m_LoopListView.InitListView(-1, OnGetItemByIndex);

            m_RankListRedTip = m_rewardRedTips.gameObject.GetComponent<RedTips>();

            RefreshRewardList();
        }

        public override void OnFreshUI(int val)
        {
            if (val == 1)
            {
                RefreshRewardList();
            }
            else if (val == 2)
            {
                m_LoopListView.RefreshAllShownItem();
                RefreshRedTips();
            }
        }

        public void RefreshRewardList()
        {
            // 组织后面要加载的数据 
            List<int> checkIds = new List<int>();
            int rankMaxId = m_SeasonMgr.GetMissionRewradTopOneId(m_SeasonId);
            for (int i = Mathf.Max(0, m_Data.Count - 1); i < m_SortItem.Count; i++)
            {
                if (i > m_CurDataMaxIndex)
                {
                    break;
                }

                SeasonMissionItem item = m_SortItem[i];

                if (item.id <= rankMaxId)
                {
                    checkIds.Add(item.id);
                }
            }

            // 检测本地有没有数据  没有就去请求
            if (m_SeasonMgr.CheckMissionFirstData(m_SeasonId, checkIds))
            {
                m_Data.Clear();
                for (int i = 0; i <= m_CurDataMaxIndex; i++)
                {
                    SeasonMissionItem sortItem = m_SortItem[i];
                    LDRankSeasonMissionUIItemData data = new LDRankSeasonMissionUIItemData();
                    data.MissionCfg = sortItem;
                    data.RankInfo = m_SeasonMgr.GetMissionFirstRecord(m_SeasonId, sortItem.id);
                    m_Data.Add(data);
                }

                m_LoopListView.RefreshAllShownItem();
            }

            RefreshRedTips();
        }

        private LoopListViewItem2 OnGetItemByIndex(LoopListView2 listView, int index)
        {
            if (m_Data == null || m_Data.Count <= index || index < 0)
            {
                return null;
            }

            LoopListViewItem2 item = listView.NewListViewDefaultItem();
            SeasonRankUI_RankTop_Item itemScript = item.GetComponent<SeasonRankUI_RankTop_Item>();
            itemScript.RefreshUI(m_SeasonId, m_Data[index], OnTop5);

            CheckData(index);
            return item;
        }

        private void CheckData(int index)
        {
            if (index >= m_SortItem.Count)
            {
                return;
            }

            if (m_CurDataMaxIndex >= m_SortItem.Count - 1)
            {
                return;
            }

            // 提前x个加载
            if (index + PerPageCount - PreCount > m_CurDataMaxIndex)
            {
                m_CurDataMaxIndex = Mathf.Min(m_CurDataMaxIndex + PerPageCount, m_SortItem.Count - 1);
                RefreshRewardList();
            }
        }

        private void RefreshRedTips()
        {
            m_RankListRedTip.FreshState(m_SeasonMgr.GetFirstRewradRed());
        }

        private void OnTop5(int missionId)
        {
            Global.gApp.gUiMgr.OpenUIAsync<SeasonMissionTop5UI>(LDUICfg.SeasonMissionTop5UI).SetLoadedCall(ui => { ui?.RefreshUI(m_SeasonId, missionId); });
        }
    }

    public class LDRankSeasonMissionUIItemData
    {
        public SeasonMissionItem MissionCfg;
        public LDRankRecordDTOItem RankInfo;
    }

    public partial class SeasonRankUI_RankTop_Item
    {
        private LDNetSeasonMgr m_SeasonMgr;
        private LDRankSeasonMissionUIItemData m_Data;
        private Action<int> m_Callback;
        private SeasonMissionItem m_MissionCfg;
        private int m_SeasonID;
        private bool m_HasData;
        private bool m_IsCanReceive;
        private bool m_IsReceived;
        private bool m_NoReceive;

        public void RefreshUI(int seasonId, LDRankSeasonMissionUIItemData data, Action<int> cb)
        {
            m_SeasonMgr = Global.gApp.gSystemMgr.gSeasonMgr;
            m_Data = data;
            m_SeasonID = seasonId;
            m_Callback = cb;
            m_MissionCfg = SeasonMission.Data.Get(m_Data.MissionCfg.id);
            m_HasData = m_Data.RankInfo != null;
            m_IsReceived = data.MissionCfg.id <= m_SeasonMgr.GetMaxMisssionRewardedId(m_SeasonID);
            m_IsCanReceive = !m_IsReceived && data.MissionCfg.id <= m_SeasonMgr.GetMissionRewradTopOneId(m_SeasonID);
            m_NoReceive = data.MissionCfg.id > m_SeasonMgr.GetMissionRewradTopOneId(m_SeasonID);

            m_Rank_Item_Top5_Btn.button.AddListener(OnTop5);

            RefreshBaseInfo();
            RefreshHead();
            RefreshName();
            RefreshReward();
        }

        private void RefreshBaseInfo()
        {
            bool isPeakMatch = Global.gApp.gSystemMgr.gSeasonMgr.IsSeasonIsPeakMatch(m_SeasonID);
            if (isPeakMatch)
            {
                int wellen = Global.gApp.gSystemMgr.gSeasonMgr.GetPeakUnlockWellen();
                if (m_MissionCfg.Wellen > wellen)
                {
                    m_Rank_Item_Titel.text.SetText(UiTools.Localize(67615, m_MissionCfg.Wellen - wellen));
                }
                else
                {
                    m_Rank_Item_Titel.text.SetText(UiTools.Localize(67549, m_MissionCfg.Wellen));
                }
            }
            else
            {
                m_Rank_Item_Titel.text.SetText(UiTools.Localize(67549, m_MissionCfg.Wellen));
            }

            m_First_Dec.gameObject.SetActive(m_HasData);

            m_Rank_Item_Top5_Btn.gameObject.SetActive(!m_NoReceive);
        }

        private void RefreshHead()
        {
            m_default_head_img.gameObject.SetActive(!m_HasData);
            m_RoleHead.gameObject.SetActive(m_HasData);
            LDRoleHeadInfo headInfo = null;
            if (m_HasData)
            {
                headInfo = m_Data.RankInfo.HeadInfo;
            }

            LDUIPrefabTools.InitOtherRoleHead(m_RoleHead.gameObject, headInfo);
        }

        private void RefreshName()
        {
            m_Rank_Item_Nobody.gameObject.SetActive(!m_HasData);
            m_Rank_Item_PlayerName.gameObject.SetActive(m_HasData);
            if (m_HasData)
            {
                m_Rank_Item_PlayerName.text.SetText(m_Data.RankInfo.Name);
            }
        }

        private void RefreshReward()
        {
            LDCommonTools.DestoryChildren(m_Rank_icon.rectTransform);
            LDCommonItem item = new LDCommonItem(m_Data.MissionCfg.FirstPassReward[0]);
            KnapsackItem kItem = LDUIPrefabTools.GetKnapsackItemUI(item, m_Rank_icon.rectTransform);

            if (m_IsCanReceive)
            {
                kItem.ShowCanGet(true);
                kItem.SetClickCallback(OnReceive);
            }
            else if (m_IsReceived)
            {
                kItem.ShowMask(true);
            }
        }

        private void OnReceive()
        {
            m_SeasonMgr.SendRankSeasonMissionRewardReceiveReq(m_SeasonID);
        }

        private void OnTop5()
        {
            m_Callback?.Invoke(m_Data.MissionCfg.id);
        }
    }
}