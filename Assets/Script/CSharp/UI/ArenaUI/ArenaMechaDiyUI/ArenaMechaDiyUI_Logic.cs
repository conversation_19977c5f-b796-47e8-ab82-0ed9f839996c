using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class ArenaMechaDiyUI
    {
        private LDCreature m_Creature = new LDCreature();
        private LDNetDIYDataMgr m_DIYDataMgr;
        private LDNetMechaPartDataMgr m_MechaPartDataMgr;
        private bool m_ShowDIY = false;
        private float m_Canvas3Distance = 0;
        private int m_Canvas3OrderInLayer = 0;
        private Canvas m_Canvas3;
        private RedTips m_LeftRedCompt;
        private RedTips m_RightRedCompt;

        private int m_CurDiyMechId;
        private Action<LDDIYData> m_SaveAction;
        private List<DIYPartCfgItem> m_UnLockEquipPart;
        private List<DIYPartCfgItem> m_UnLockWeaponPart;

        protected override void OnInitImp()
        {
            m_LeftRedCompt = m_LeftRedTips.gameObject.GetComponent<RedTips>();
            m_RightRedCompt = m_RightRedTips.gameObject.GetComponent<RedTips>();
            m_DIYDataMgr = Global.gApp.gSystemMgr.gDIYMgr;
            m_MechaPartDataMgr = Global.gApp.gSystemMgr.gMechaPartDataMgr;

            m_Canvas3 = transform.Find("Canvas3").GetComponent<Canvas>();
            m_Canvas3Distance = m_Canvas3.planeDistance;
            m_Canvas3OrderInLayer = m_Canvas3.sortingOrder;

            m_btn_close.AddListener(OnSaveDIY);
            m_DragNode.gameObject.GetComponent<LDArenaDragDiyNode>().InitDIYUI(this);
            Physics.autoSimulation = true;

            m_btn_diy.AddListener(OnDIY);
            m_btn_mechaSkin.AddListener(OnMechaSkin);
            m_btn_equip.AddListener(OnEquipment);
            m_btn_weapon.AddListener(OnWeapon);

            m_btn_right.AddListener(OnRight);
            m_btn_left.AddListener(OnLeft);
            m_btn_reset.AddListener(OnReset);
            m_GuideBtn.AddListener(OnGuide);
            m_btn_reset.gameObject.SetActive(false);
        }

        public void FreshArrowRedTips()
        {
            // m_LeftRedCompt.FreshState((LDRedTipsState)Mathf.Max((int)GetLeftEquipState(), (int)(GetLeftWeaponState())));
            // m_RightRedCompt.FreshState((LDRedTipsState)Mathf.Max((int)GetRightEquipState(), (int)(GetRightWeaponState())));
        }

        public override void OnFreshUI()
        {
        }

        /// <summary>
        /// 0 点击返回 安装
        /// 1 是换皮肤
        /// </summary>
        /// <param name="eventType"></param>
        public override void OnFreshUI(int eventType)
        {
            if (eventType == 0)
            {
                TouchClose();
            }
            else if (eventType == 1)
            {
                ChangeSkin();
            }
        }

        public void InitMechaId(int mechaId, LDDIYData diyData, Action<LDDIYData> diyCallBack, List<DIYPartCfgItem> unLockEquipPart, List<DIYPartCfgItem> unLockWeaponPart)
        {
            m_CurDiyMechId = mechaId;
            m_SaveAction = diyCallBack;
            m_UnLockEquipPart = unLockEquipPart;
            m_UnLockWeaponPart = unLockWeaponPart;
            OnDIY();
            InitDIYNode();
            InitAdjust();
            InitCreature(diyData);
            TryGuide();
            FreshArrowRedTips();
        }

        private void ChangeSkin()
        {
            int selectBlockId = -1;
            if (m_SelectBodyBlock != null)
            {
                selectBlockId = m_SelectBodyBlock.Pid;
            }

            ChangeSelectBlock(null);
            if (m_Creature != null)
            {
                LDDIYData DIYData = m_Creature.GetCurDiyData();
                m_Creature?.DestroyMechaNode();
                m_Creature = new LDCreature();
                m_Creature.Init(m_DIYNode.transform, DIYData);
                m_Creature.PlayUIIdle();
            }

            ChangeSelectBlock(selectBlockId);
        }

        public void InitCreature(LDDIYData diyData)
        {
            if (diyData == null)
            {
                diyData = new LDDIYData();
                diyData.BodyId = m_CurDiyMechId;
            }

            m_Creature.Init(m_DIYNode.transform, diyData);
            m_Creature.PlayUIIdle();
            FreshSlot();
            OnWeapon();
            ResetDIYNodeRotate();
        }

        // 
        private void OnDIY()
        {
            if (!m_ShowDIY)
            {
                m_ShowDIY = true;
                m_img_diy_On.gameObject.SetActive(true);
                m_img_diy_Off.gameObject.SetActive(false);
                m_img_mechaSkin_On.gameObject.SetActive(false);
                m_img_mechaSkin_Off.gameObject.SetActive(true);
                Global.gApp.gUiMgr.CloseUI(LDUICfg.MechaSkinMainUI);
                m_Canvas3.planeDistance = m_Canvas3Distance;
                m_Canvas3.sortingOrder = m_Canvas3OrderInLayer;
            }
        }

        private void OnMechaSkin()
        {
            if (m_Creature != null)
            {
                if (m_ShowDIY)
                {
                    m_ShowDIY = false;
                    m_img_diy_On.gameObject.SetActive(false);
                    m_img_diy_Off.gameObject.SetActive(true);
                    m_img_mechaSkin_On.gameObject.SetActive(true);
                    m_img_mechaSkin_Off.gameObject.SetActive(false);
                    Global.gApp.gUiMgr.OpenUIAsync<MechaSkinMainUI>(LDUICfg.MechaSkinMainUI).SetLoadedCall(ui =>
                    {
                        ui.RefreshUI(m_Creature.DiyData.BodyId);
                        int planeDis;
                        int layer;
                        ui.GetMaxOrder(out planeDis, out layer);
                        m_Canvas3.planeDistance = planeDis + 5;
                        m_Canvas3.sortingOrder = layer + 5;
                    });
                }
            }
        }

        //
        private void OnEquipment()
        {
            OnCloseWeapon();
            OnOpenEquipment();
            FreshSlot();
            FreshArrowRedTips();
        }

        private void OnWeapon()
        {
            OnCloseEquipment();
            OnOpenWeapon();
            FreshSlot();
            FreshArrowRedTips();
        }

        private void OnRight()
        {
            OnRightWeapon();
            OnRightEquip();
            FreshArrowRedTips();
        }

        private void OnLeft()
        {
            OnLeftWeapon();
            OnLeftEquip();
            FreshArrowRedTips();
        }

        private void OnReset()
        {
            Global.gApp.gUiMgr.OpenUIAsync<ConfirmUI>(LDUICfg.NormalConfirmUI).SetLoadedCall(ui =>
            {
                ui?.SetInfo(OnConfirmReset, null, 12820, -1);
            });
        }

        private void OnConfirmReset()
        {
            ChangeSelectBlock(null);

            m_CamerRootNode.transform.localEulerAngles = Vector3.zero;
            m_Creature?.DestroyMechaNode();
            m_Creature = new LDCreature();
            m_Creature.Init(m_DIYNode.transform);
            m_Creature.PlayUIIdle();
            FreshEquipItem();
            FreshWpnItem();
            FreshSlot();
            ResetDIYNodeRotate();
        }

        private void ResetDIYNodeRotate()
        {
            int bodyId = m_Creature.DiyData.BodyId;
            MechaItem mechaItem = Mecha.Data.Get(bodyId);
            m_DIYNode.transform.localEulerAngles = new Vector3(mechaItem.mechaXYZ[0], mechaItem.mechaXYZ[1], mechaItem.mechaXYZ[2]);
            //m_DIYNode.transform.localScale = Vector3.one * mechaItem.mechaScale;
        }


        private void Update()
        {
            OnUpdateHandle();
            OnUpdateGuide();
        }

        public LDCreature GetCreature()
        {
            return m_Creature;
        }

        public void OnDIYHandleEnd()
        {
            FreshSlot();
        }

        private void OnSaveDIY()
        {
            LDDIYData pbMsg = m_Creature.SaveLocalData();

            if (pbMsg != null)
            {
                m_SaveAction?.Invoke(pbMsg);
            }

            TouchClose();
        }

        protected override void OnCloseImp()
        {
            if (Global.gApp.CurFightScene == null)
            {
                Global.gApp.gUICameraCmpt.cullingMask = LDFightConstVal.UICameraMask;
            }
            else
            {
                Global.gApp.gUICameraCmpt.cullingMask = LDFightConstVal.UICameraFightMask;
            }

            CloseDIYHandle();
            Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MechaMainUI, 1);
            Global.gApp.gUiMgr.CloseUI(LDUICfg.MechaSkinMainUI);

            Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_DIY_Last);
        }
    }
}