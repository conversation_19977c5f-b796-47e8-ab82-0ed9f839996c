using System.Collections.Generic;
using SuperScrollView;

namespace LD
{
    public class ArenaRankPlayerRank : LDUIDataBase
    {
        public List<LDArenaFighter> m_ArenaFighters = new();
    }

    public partial class ArenaRankUI
    {
        public long m_LastRequestTime = 0;

        private List<LDArenaFighter> m_ShowArenaFighters = new();

        private LoopListView2 m_LoopListView;
        private bool m_loopListInit = false;

        private void CheckRequestRank()
        {
            var curTime = DateTimeUtil.GetServerTime();
            if (m_ShowArenaFighters.Count <= 0 || curTime - m_LastRequestTime > 10 * DateTimeUtil.Sec_Mills)
            {
                if (m_LoopListView == null)
                {
                    m_LoopListView = m_rankScorl.gameObject.GetComponent<LoopListView2>();
                }

                if (m_loopListInit == false)
                {
                    m_LoopListView.InitListView(0, OnGetItemByIndex);
                    m_loopListInit = true;
                }
                else
                {
                    m_LoopListView.SetListItemCount(0);
                }

                Global.gApp.gSystemMgr.gArenaMgr.SendTopFightersGetReq();
            }
            else
            {
                RefreshPlayerRank(m_ShowArenaFighters);
            }
        }

        public void RefreshPlayerRank(List<LDArenaFighter> list)
        {
            m_No_RankNode.SetActive(true);
            m_myrank.SetActive(false);

            m_ShowArenaFighters = list;
            m_ShowArenaFighters.Sort((a, b) => { return a.Rank - b.Rank; });

            if (m_LoopListView == null)
            {
                m_LoopListView = m_rankScorl.gameObject.GetComponent<LoopListView2>();
            }

            if (m_loopListInit == false)
            {
                m_LoopListView.InitListView(m_ShowArenaFighters.Count, OnGetItemByIndex);
                m_loopListInit = true;
            }
            else
            {
                m_LoopListView.SetListItemCount(m_ShowArenaFighters.Count);
            }


            LDArenaFighter self = null;
            for (int i = 0; i < m_ShowArenaFighters.Count; i++)
            {
                var fighter = m_ShowArenaFighters[i];
                if (fighter.IsMySelf())
                {
                    self = fighter;
                    break;
                }
            }

            RefreshSelf(self);

            m_LoopListView.MovePanelToItemIndex(0, 0);
        }

        private LoopListViewItem2 OnGetItemByIndex(LoopListView2 listView, int index)
        {
            if (m_ShowArenaFighters == null || m_ShowArenaFighters.Count <= index || index < 0)
            {
                return null;
            }

            LoopListViewItem2 item = listView.NewListViewDefaultItem();
            var itemScript = item.GetComponent<ArenaRankUI_PlayerRankItem>();
            itemScript.RefreshFighter(m_ShowArenaFighters[index]);
            return item;
        }

        private void RefreshSelf(LDArenaFighter fighter)
        {
            m_No_RankNode.SetActive(fighter == null);
            m_myrank.SetActive(fighter != null);
            if (fighter != null)
            {
                m_ranknum.text.SetText(fighter.Rank);

                m_PlayerName.text.SetText(fighter.Name);
                m_LevelNode.gameObject.SetActive(fighter.Level > 0);
                m_Level_Txt.text.SetText(fighter.Level);
                m_Power.text.SetText(fighter.GetPowerStr());
                m_RoleHead.gameObject.GetComponent<RoleHead>()?.InitOther(fighter.HeadInfo);
            }
        }
    }
}