using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class OtherLineupUI
    {
        public LDArenaFighter m_Fighter;

        private CommonModelUI m_SlotModel1;
        private CommonModelUI m_SlotModel2;
        private CommonModelUI m_SlotModel3;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_SlotModel1 = CommonModelUI1.gameObject.GetComponent<CommonModelUI>();
            m_SlotModel1.SetRootPosX(-20000);
            m_SlotModel2 = CommonModelUI2.gameObject.GetComponent<CommonModelUI>();
            m_SlotModel2.SetRootPosX(-21000);
            m_SlotModel3 = CommonModelUI3.gameObject.GetComponent<CommonModelUI>();
            m_SlotModel3.SetRootPosX(-22000);
        }

        protected override void OnCloseImp()
        {
            m_SlotModel1?.Dispose();
            m_SlotModel2?.Dispose();
            m_SlotModel3?.Dispose();
        }

        public override void OnFreshUI()
        {
        }


        public void InitLineup(LDArenaFighter fighter)
        {
            m_Fighter = fighter;

            RefreshHead();
            RefreshFormation();
        }

        public void RefreshHead()
        {
            RoleHead ui = m_RoleHead.gameObject.GetComponent<RoleHead>();
            ui.InitOther(m_Fighter.HeadInfo);

            m_PlayerName.text.SetText(m_Fighter.Name);
            m_Power.text.SetText(m_Fighter.GetPowerStr());
        }

        private void RefreshFormation()
        {
            m_ModuleItem.CacheInstanceList();


            var slotInfo1 = m_Fighter.Formation.Slots.GetValueOrDefault(1);
            if (slotInfo1 != null && slotInfo1.Model != null && slotInfo1.Model.BodyId > 0)
            {
                int mechaId1 = slotInfo1.Model.BodyId;
                var mechaCfg1 = Mecha.Data.Get(mechaId1);
                m_Mecha_Model1.gameObject.SetActive(true);
                CreateModel(slotInfo1, m_SlotModel1, m_Mecha_Model1.rawImage);
                m_Mecha_Name1.text.SetTips(mechaCfg1.name);
                LoadSprite(m_Mecha_DamageSystem1.image, LDUIResTools.GetElementIconPath(mechaCfg1.DamageSystem));
                m_common1.gameObject.SetActive(true);
                m_Empty1.gameObject.SetActive(false);
                foreach (var data in slotInfo1.Model.Data)
                {
                    int id = data.PId;
                    DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(data.PId);
                    if (partCfgItem.pos == LDDIYPartItemType.PartWeapon)
                    {
                        var partInfo1 = GetPart(partCfgItem.id);
                        var commonitem = new LDCommonItem(LDCommonType.DIYPart, id, 0);
                        commonitem.SetSkinId(data.SId);
                        commonitem.IgnoreSkin = data.HSId > 0;
                        commonitem.SetNewQuality(partInfo1?.Quality ?? 1);
                        var itemUI = m_ModuleItem.GetInstance(true);
                        LDUIPrefabTools.InitKnapsackItem(itemUI.rectTransform(), commonitem);
                        itemUI.gameObject.transform.SetParent(m_ModuleList1.gameObject.transform, false);
                    }
                }
            }
            else
            {
                m_common1.gameObject.SetActive(false);
                m_Empty1.gameObject.SetActive(true);
                m_Mecha_Model1.gameObject.SetActive(false);
            }


            var slotInfo2 = m_Fighter.Formation.Slots.GetValueOrDefault(2);
            if (slotInfo2 != null && slotInfo2.Model != null && slotInfo2.Model.BodyId > 0)
            {
                int mechaId2 = slotInfo2.Model.BodyId;
                var mechaCfg2 = Mecha.Data.Get(mechaId2);
                m_Mecha_Model2.gameObject.SetActive(true);
                CreateModel(slotInfo2, m_SlotModel2, m_Mecha_Model2.rawImage);
                m_Mecha_Name2.text.SetTips(mechaCfg2.name);
                LoadSprite(m_Mecha_DamageSystem2.image, LDUIResTools.GetElementIconPath(mechaCfg2.DamageSystem));
                m_common2.gameObject.SetActive(true);
                m_Empty2.gameObject.SetActive(false);
                foreach (var data in slotInfo2.Model.Data)
                {
                    int id = data.PId;
                    DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(data.PId);
                    if (partCfgItem.pos == LDDIYPartItemType.PartWeapon)
                    {
                        var partInfo2 = GetPart(partCfgItem.id);
                        var commonitem = new LDCommonItem(LDCommonType.DIYPart, id, 0);
                        commonitem.SetSkinId(data.SId);
                        commonitem.IgnoreSkin = data.HSId > 0;
                        commonitem.SetNewQuality(partInfo2?.Quality ?? 1);
                        var itemUI = m_ModuleItem.GetInstance(true);
                        LDUIPrefabTools.InitKnapsackItem(itemUI.rectTransform(), commonitem);
                        itemUI.gameObject.transform.SetParent(m_ModuleList2.gameObject.transform, false);
                    }
                }
            }
            else
            {
                m_common2.gameObject.SetActive(false);
                m_Empty2.gameObject.SetActive(true);
                m_Mecha_Model2.gameObject.SetActive(false);
            }


            var slotInfo3 = m_Fighter.Formation.Slots.GetValueOrDefault(3);
            if (slotInfo3 != null && slotInfo3.Model != null && slotInfo3.Model.BodyId > 0)
            {
                int mechaId3 = slotInfo3.Model.BodyId;
                var mechaCfg3 = Mecha.Data.Get(mechaId3);
                m_Mecha_Model3.gameObject.SetActive(true);
                CreateModel(slotInfo3, m_SlotModel3, m_Mecha_Model3.rawImage);
                m_Mecha_Name3.text.SetTips(mechaCfg3.name);
                LoadSprite(m_Mecha_DamageSystem3.image, LDUIResTools.GetElementIconPath(mechaCfg3.DamageSystem));
                m_common3.gameObject.SetActive(true);
                m_Empty3.gameObject.SetActive(false);
                foreach (var data in slotInfo3.Model.Data)
                {
                    int id = data.PId;
                    DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(data.PId);
                    if (partCfgItem.pos == LDDIYPartItemType.PartWeapon)
                    {
                        var partInfo3 = GetPart(partCfgItem.id);
                        var commonitem = new LDCommonItem(LDCommonType.DIYPart, id, 0);
                        commonitem.SetNewQuality(partInfo3?.Quality ?? 1);
                        commonitem.SetSkinId(data.SId);
                        commonitem.IgnoreSkin = data.HSId > 0;
                        var itemUI = m_ModuleItem.GetInstance(true);
                        LDUIPrefabTools.InitKnapsackItem(itemUI.rectTransform(), commonitem);
                        itemUI.gameObject.transform.SetParent(m_ModuleList3.gameObject.transform, false);
                    }
                }
            }
            else
            {
                m_common3.gameObject.SetActive(false);
                m_Empty3.gameObject.SetActive(true);
                m_Mecha_Model3.gameObject.SetActive(false);
            }
        }

        public LDNetRoleMechaInfo GetMecha(int mechaId)
        {
            foreach (LDNetRoleMechaInfo mechaInfo in m_Fighter.MechaInfos)
            {
                if (mechaId == mechaInfo.CfgId)
                {
                    return mechaInfo;
                }
            }

            return null;
        }

        public LDNetRoleDiyPartInfo GetPart(int partId)
        {
            foreach (LDNetRoleDiyPartInfo partInfo in m_Fighter.PartInfos)
            {
                if (partId == partInfo.CfgId)
                {
                    return partInfo;
                }
            }

            return null;
        }

        private GameObject CreateModel(LDArenaSlotInfo slotInfo, CommonModelUI modelUI, RawImage showRawImage)
        {
            GameObject modelObj = null;

            if (slotInfo != null && slotInfo.Model != null)
            {
                MechaItem mechaCfg = Mecha.Data.Get(slotInfo.Model.BodyId);
                modelObj = modelUI.InitDiyData(showRawImage, slotInfo.Model, 4);
                modelObj.transform.localEulerAngles = new Vector3(mechaCfg.mechaXYZ[0], mechaCfg.mechaXYZ[1], mechaCfg.mechaXYZ[2]);
                modelObj.transform.localScale = Vector3.one * mechaCfg.mechaScale;
            }

            return modelObj;
        }
    }
}