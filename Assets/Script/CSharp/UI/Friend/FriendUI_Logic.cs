using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class FriendUI
    {
        public const int Tab_List = 1;
        public const int Tab_Add = 2;
        public const int Tab_Black = 3;
        public const int Tab_Del = 4;

        private const string FriendListPath = "PrefabsN/UI/Friend/FriendUI_FriendList";
        private const string AddFriendPath = "PrefabsN/UI/Friend/FriendUI_AddFriend";
        private const string BlackListPath = "PrefabsN/UI/Friend/FriendUI_BlackList";

        private LDNetFriendMgr m_FriendMgr;
        private FriendUI_FriendList m_FriendListUI;
        private FriendUI_AddFriend m_AddFriendUI;
        private FriendUI_BlackList m_BlackListUI;
        private int m_CurTab;

        protected override void OnInitImp()
        {
            m_FriendMgr = Global.gApp.gSystemMgr.gFriendMgr;

            m_btn_close.button.AddListener(TouchClose);
            m_ListUnSelect.button.AddListener(OnList);
            m_AddUnSelect.button.AddListener(OnAdd);
            m_BlackListUnSelect.button.AddListener(OnBlack);

            CreateChildNode();
            ChangeTab(Tab_List);
        }

        public override void OnFreshUI() { }
        public override void OnFreshUI(int val) 
        {
            // 好友列表刷新
            if (val == Tab_List && m_CurTab == Tab_List)
            {
                m_FriendListUI.RefreshUI();
            }
            
            // 删除好友列表刷新
            if (val == Tab_Del && m_CurTab == Tab_List)
            {
                m_FriendListUI.RefreshUI(false);
            }
            
            // 添加好友刷新
            if (val == Tab_Add && m_CurTab == Tab_Add)
            {
                m_AddFriendUI.RefreshUI();
            }

            // 黑名单刷新
            if (val == Tab_Black && m_CurTab == Tab_Black)
            {
                m_BlackListUI.RefreshUI();
            }
            
            // 播放领取体力动画
            if (val == 999 && m_CurTab == Tab_List)
            {
                m_FriendListUI.PlayReceiveEnergyAni();
            }
        }

        private void CreateChildNode()
        {
            GameObject friendListNode = Global.gApp.gResMgr.InstantiateLoadObj(FriendListPath, ResSceneType.NormalRes, m_ChildUI.gameObject.transform);
            GameObject addFriendNode = Global.gApp.gResMgr.InstantiateLoadObj(AddFriendPath, ResSceneType.NormalRes, m_ChildUI.gameObject.transform);
            GameObject blackListNode = Global.gApp.gResMgr.InstantiateLoadObj(BlackListPath, ResSceneType.NormalRes, m_ChildUI.gameObject.transform);

            m_FriendListUI = friendListNode.GetComponent<FriendUI_FriendList>();
            m_AddFriendUI = addFriendNode.GetComponent<FriendUI_AddFriend>();
            m_BlackListUI = blackListNode.GetComponent<FriendUI_BlackList>();

            m_FriendListUI.InitUI(this);
            m_AddFriendUI.InitUI(this);
            m_BlackListUI.InitUI(this);
        }


        #region Tab
        public void ChangeTab(int tab)
        {
            m_CurTab = tab;
            RefreshTab();

            if (m_CurTab == Tab_List)
            {
                m_FriendListUI.RefreshUI();
            }
            else if (m_CurTab == Tab_Add)
            {
                m_AddFriendUI.RefreshUI();
            }
            else if (m_CurTab == Tab_Black)
            {
                m_BlackListUI.RefreshUI();
            }
        }

        private void RefreshTab()
        {
            m_ListSelect.gameObject.SetActive(m_CurTab == Tab_List);
            m_ListUnSelect.gameObject.SetActive(m_CurTab != Tab_List);
            m_AddSelect.gameObject.SetActive(m_CurTab == Tab_Add);
            m_AddUnSelect.gameObject.SetActive(m_CurTab != Tab_Add);
            m_BlackListtSelect.gameObject.SetActive(m_CurTab == Tab_Black);
            m_BlackListUnSelect.gameObject.SetActive(m_CurTab != Tab_Black);

            m_FriendListUI.gameObject.SetActive(m_CurTab == Tab_List);
            m_AddFriendUI.gameObject.SetActive(m_CurTab == Tab_Add);
            m_BlackListUI.gameObject.SetActive(m_CurTab == Tab_Black);
        }

        private void OnList()
        {
            ChangeTab(Tab_List);
        }

        private void OnAdd()
        {
            ChangeTab(Tab_Add);
        }

        private void OnBlack()
        {
            ChangeTab(Tab_Black);
        }
        #endregion Tab

        protected override void OnCloseImp()
        {
            m_FriendMgr.Data.ClearRecord();
            m_FriendMgr.RecommondTime = 0;
        }
    }
}