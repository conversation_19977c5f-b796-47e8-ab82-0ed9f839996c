using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class SeasonRewardsPreviewUI_RankRewardItem
    {
        private Animator m_Anim;

        public void RefreshItem(SeasonRankRewardItem cfg)
        {
            if (m_Anim == null)
            {
                m_Anim = this.gameObject.GetComponent<Animator>();
            }

            m_item.CacheInstanceList();

            for (int i = 0; i < cfg.RankItem.Length; i++)
            {
                string str = cfg.RankItem[i];
                var itemUI = m_item.GetInstance(true);
                LDUIPrefabTools.InitKnapsackItem(itemUI.rectTransform(),str);
            }

            m_Top1.gameObject.SetActive(cfg.MinRank == 1);
            m_Top2.gameObject.SetActive(cfg.MinRank == 2);
            m_Top3.gameObject.SetActive(cfg.MinRank == 3);
            m_Top4.gameObject.SetActive(cfg.MinRank > 3);
            if (cfg.MinRank > 3)
            {
                m_Top_Num.text.SetText($"{cfg.MaxRank}-{cfg.MinRank}");
            }
        }

        public void SetAnimEnable(bool isEnabled)
        {
            if (m_Anim != null)
            {
                m_Anim.enabled = isEnabled;
            }
        }
    }
}