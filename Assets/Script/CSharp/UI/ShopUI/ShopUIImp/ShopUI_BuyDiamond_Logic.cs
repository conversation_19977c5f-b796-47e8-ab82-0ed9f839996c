using System;

namespace LD
{
    public partial class ShopUI_BuyDiamond
    {
        private LDNetShopDiamondMgr m_ShopMgr;

        public void RefreshUI()
        {
            m_ShopMgr = Global.gApp.gSystemMgr.gShopDiamondMgr;
            gameObject.SetActive(m_ShopMgr.IsUnlock());

            RefreshList();
        }

        private void RefreshList()
        {
            m_DiamondItem.CacheInstanceList();
            if (Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency)
            {
                ThirdPayItem[] cfgs = ThirdPay.Data.items;
                for (int i = 0; i < cfgs.Length; i++)
                {
                    if (cfgs[i].gameShow == 1)
                    {
                        ShopUI_BuyDiamond_DiamondItem itemUI = m_DiamondItem.GetInstance(true);
                        itemUI.RefreshUI(cfgs[i], i);
                    }
                }
            }
            else
            {
                PayItem[] cfgs = Pay.Data.items;
                for (int i = 0; i < cfgs.Length; i++)
                {
                    ShopUI_BuyDiamond_DiamondItem itemUI = m_DiamondItem.GetInstance(true);
                    itemUI.RefreshUI(cfgs[i], i);
                }
            }
        }
    }

    public partial class ShopUI_BuyDiamond_DiamondItem
    {
        private LDNetShopDiamondMgr m_ShopMgr;
        private LDNetPaymentMgr m_PaymentMgr;
        private PayItem m_Cfg;
        private ThirdPayItem m_ThirdCfg;
        private MallGoodsItem m_MallGoodCfg;

        public void RefreshUI(PayItem cfg, int index)
        {
            m_ShopMgr = Global.gApp.gSystemMgr.gShopDiamondMgr;
            m_PaymentMgr = Global.gApp.gSystemMgr.gPaymentMgr;
            m_Cfg = cfg;
            m_MallGoodCfg = MallGoods.Data.Get(m_Cfg.goods_id);
            m_buy_Btn.button.AddListener(OnBuy);

            m_fx_xinggaung01.gameObject.SetActive(index + 1 == 1);
            m_fx_xinggaung02.gameObject.SetActive(index + 1 == 2);
            m_fx_xinggaung03.gameObject.SetActive(index + 1 == 3);
            m_fx_xinggaung04.gameObject.SetActive(index + 1 == 4);
            m_fx_xinggaung05.gameObject.SetActive(index + 1 == 5);
            m_fx_xinggaung06.gameObject.SetActive(index + 1 == 6);

            LDCommonItem itemData = new LDCommonItem(cfg.item);

            m_Num.text.SetText("X " + UiTools.FormateMoney(itemData.Num));
            LoadSprite(m_icon.image, m_Cfg.icon);

            bool isFirst = m_PaymentMgr.IsFirst_RechargeMall(m_Cfg.id);
            m_first_tips.gameObject.SetActive(isFirst);
            if (isFirst)
            {
                LDCommonItem firstItemData = new LDCommonItem(cfg.first_rate);
                m_first_tips_Text.text.SetText(UiTools.Localize(67879, firstItemData.Num));
            }

            string price = Global.gApp.gSdkMgr.gPaymentMgr.GetDisplayPrice(m_MallGoodCfg.id);
            m_Price.text.SetText(price);
        }

        public void RefreshUI(ThirdPayItem cfg, int index)
        {
            m_ShopMgr = Global.gApp.gSystemMgr.gShopDiamondMgr;
            m_PaymentMgr = Global.gApp.gSystemMgr.gPaymentMgr;
            m_ThirdCfg = cfg;
            m_MallGoodCfg = MallGoods.Data.Get(m_ThirdCfg.goods_id);
            m_buy_Btn.button.AddListener(OnBuy);

            m_fx_xinggaung01.gameObject.SetActive(index + 1 == 1);
            m_fx_xinggaung02.gameObject.SetActive(index + 1 == 2);
            m_fx_xinggaung03.gameObject.SetActive(index + 1 == 3);
            m_fx_xinggaung04.gameObject.SetActive(index + 1 == 4);
            m_fx_xinggaung05.gameObject.SetActive(index + 1 == 5);
            m_fx_xinggaung06.gameObject.SetActive(index + 1 == 6);

            LDCommonItem itemData = new LDCommonItem(cfg.money);

            m_Num.text.SetText("X " + UiTools.FormateMoney(itemData.Num));
            LoadSprite(m_icon.image, m_ThirdCfg.icon);

            // bool isFirst = m_PaymentMgr.IsFirst_RechargeMall(m_Cfg.id);
            m_first_tips.gameObject.SetActive(false);
            // if (isFirst)
            // {
            //     LDCommonItem firstItemData = new LDCommonItem(cfg.first_rate);
            //     m_first_tips_Text.text.SetText(UiTools.Localize(67879, firstItemData.Num));
            // }

            string price = Global.gApp.gSdkMgr.gPaymentMgr.GetDisplayPrice(m_MallGoodCfg.id);
            m_Price.text.SetText(price);
        }

        private void OnBuy()
        {
            m_PaymentMgr.SendMallBuyItemRequest(m_MallGoodCfg.id);
        }
    }
}