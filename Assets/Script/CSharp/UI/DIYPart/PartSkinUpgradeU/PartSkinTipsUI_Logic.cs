using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PartSkinTipsUI
    {
        private LDNetMechaPartSkinDataMgr m_SkinMgr;
        private SkinTipsUI m_TipsUI;
        protected override void OnInitImp()
        {
            m_SkinMgr = Global.gApp.gSystemMgr.gMechaPartSkinePart;
            m_TipsUI = m_SkinTipsUI.gameObject.GetComponent<SkinTipsUI>();
            m_TipsUI.Init(this);
        }

        public override void OnFreshUI()
        {
        }

        public void RefreshUI(LDCommonItem commonItem)
        {
            m_TipsUI.SetInfo(commonItem);

            if (m_SkinMgr.IsSkinUnlocked(commonItem.Id))
            {
                int star = m_SkinMgr.GetSkinStarLv(commonItem.Id);
                List<LDAttrAddition> attrs = m_SkinMgr.GetSkinStarLvAttrAdditions(commonItem.Id, star);
                m_TipsUI.SetAttrList(attrs);
            }
            else
            {
                m_TipsUI.SetAttrList(new List<LDAttrAddition>());
            }

            List<SkinStarItem> cfgs = m_SkinMgr.GetPartSkinStarLvCfg(commonItem.Id);
            m_TipsUI.SetEffectList(cfgs);
        }

        protected override void OnCloseImp()
        {
        }
    }
}