using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class MechaDiyUI_adjustItem
    {
        // 1 角度 2 缩放
        private LDBodyBlock m_ControlBlock;
        private int m_AjustType = 1;
        public void Init(int ajustType, LDBodyBlock controlBlock)
        {
            m_ControlBlock = controlBlock;
            m_AjustType = ajustType;
            if (ajustType == 1)
            {
                m_txt_adjustType.text.SetTips(12808);
                m_adjustSlider.slider.value = controlBlock.RotationFactor;
            }
            else
            {
                m_txt_adjustType.text.SetTips(12807);
                m_adjustSlider.slider.value = controlBlock.ScaleFactor;
            }
            FreshSize();
            m_adjustSlider.slider.onValueChanged.RemoveAllListeners();
            m_adjustSlider.slider.onValueChanged.AddListener(OnValChange);
        }
        private void OnValChange(float val)
        {
            if (m_ControlBlock != null)
            {
                if (m_AjustType == 1)
                {
                    m_ControlBlock.FreshRotationFactor(val);
                }
                else
                {
                    m_ControlBlock.FreshScaleFactor(val);
                }
                FreshSize();
            }
        }
        private void FreshSize()
        {
            int size = Mathf.CeilToInt(m_adjustSlider.slider.value * 100);
            if (m_AjustType == 2)
            {
                size = Mathf.Max(1, size);
            }
            m_txt_adjustNum.text.text = size.ToString();
        }
    }
}
