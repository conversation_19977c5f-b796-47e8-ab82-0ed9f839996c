using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PartUpgradeUI_AttrItem_wpn
    {
        public void RefreshUI(LDAttrAddition oldAdd, LDAttrAddition newAdd,int index,bool lvMax)
        {
            m_AttrName.text.SetTips(oldAdd.GetNameTips());
            m_AttrVal1.text.SetText(oldAdd.GetValueStr());
            if (!lvMax)
            {
                m_AttrVal2.text.SetText(newAdd.GetValueStr());
                m_UpInfo.gameObject.SetActive(true);
            }
            else
            {
                m_UpInfo.gameObject.SetActive(false);
            }
        }
        public void FreshUILv(int oldAdd, int newAdd, int index,bool lvMax, bool showLimitLv)
        {
            if (!showLimitLv)
            {
                m_AttrName.text.SetTips(12812);
                m_UpInfo.gameObject.SetActive(false);
                m_AttrVal1.text.SetText(oldAdd.ToString() + "/" + newAdd.ToString());
            }
            else
            {
                m_AttrName.text.SetTips(5105);
                m_UpInfo.gameObject.SetActive(false);
                m_AttrVal1.text.SetText(oldAdd.ToString());
                if (!lvMax)
                {
                    m_AttrVal2.text.SetText(newAdd.ToString());
                    m_UpInfo.gameObject.SetActive(true);
                }
                else
                {
                    m_UpInfo.gameObject.SetActive(false);
                }
            }
        }
    }
}
