using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PartUpgradeUI
    {
        private LDNetMechaPartSkinDataMgr m_SkinMgr;
        private SkinUpgradeUI m_SkinUI;
        
        private int m_CurSelectSkinId;
        private List<int> m_SkinIds = new List<int>();

        private void OnInitImpSkin()
        {
            m_SkinMgr = Global.gApp.gSystemMgr.gMechaPartSkinePart;
            m_SkinUI = SkinUpgradeUI.gameObject.GetComponent<SkinUpgradeUI>();
            m_SkinUI.Init(this);
            m_SkinUI.AddListeners(OnUseSkin, OnGrayUseSkin, OnUnUseSkin, OnSelectSkin, OnStarUpSkin);
            m_SkinUI.AddHideSkinListeners(OnHideSkin);
        }

        private void FreshSkinView()
        {
            ShowSkinView();
            FreshHideSkinState();
        }

        private void ShowSkinView()
        {
            int partId = m_PartCfgItem.id;
            int curUseSkinId = m_NetMechaPartDataMgr.GetPartSkinId(partId);
            m_SkinIds = m_SkinMgr.GetPartSkinIdsByConfig(partId);
            
            Global.Log($"部件={partId}  当前使用皮肤={curUseSkinId}");
            
            m_SkinUI.SetSkinList(m_SkinIds);
            if (curUseSkinId > 0)
            {
                m_SkinUI.OnSelectSkin(curUseSkinId);
            }
            else if (m_SkinIds.Count > 0)
            {
                m_SkinUI.OnSelectSkin(m_SkinIds[0]);
            }
            else
            {
                Global.LogError($"该部件没有皮肤数据");
                TouchClose();
            }
        }

        private void OnSelectSkin(int skinId)
        {
            m_CurSelectSkinId = skinId;
            SkinItem cfg = Skin.Data.Get(skinId);
            bool isUnlock = m_SkinMgr.IsSkinUnlocked(skinId);
            bool isEquip = m_SkinMgr.IsSkinEquiped(skinId);
            int starLv = m_SkinMgr.GetSkinStarLv(skinId);
            bool isMax = m_SkinMgr.IsSkinMaxLv(skinId);

            m_SkinUI.SetName(UiTools.Localize(cfg.skinName));
            m_SkinUI.SetStarLv(starLv);
            m_SkinUI.SetAttrList(m_SkinMgr.GetSkinStarLvAttrAdditions(skinId, starLv));
            m_SkinUI.btn_use.gameObject.SetActive(isUnlock && !isEquip);
            m_SkinUI.btn_unuse.gameObject.SetActive(isUnlock && isEquip);
            m_SkinUI.Node__cost.gameObject.SetActive(isUnlock && !isMax);
            m_SkinUI.max_Node.gameObject.SetActive(isUnlock && isMax);
            m_SkinUI.star_node.gameObject.SetActive(isUnlock);

            List<SkinStarItem> cfgList = m_SkinMgr.GetPartSkinStarLvCfg(skinId);
            m_SkinUI.SetStartDetialList(cfgList);
            
            if (!isMax)
            {
                m_SkinUI.SetCostInfo(m_SkinMgr.GetUpgradeCost(skinId));
            }
            
            LDRedTipsState newRedState = m_SkinMgr.IsNewPartSkin(m_PartCfgItem.id, m_CurSelectSkinId) ? LDRedTipsState.RedPoint : LDRedTipsState.None;
            m_SkinUI.SetNewRed(newRedState);
            m_SkinUI.RefreshListItemRedState();
            FreshSkinBtnRed();
            // 设置查看新皮肤
            m_SkinMgr.SetViewNewPartSkin(m_PartCfgItem.id, m_CurSelectSkinId);
            
            m_SkinUI.SetShowImage(cfg.bigIcon);
            FreshHideSkinState();
        }

        private void OnUseSkin()
        {
            m_NetMechaPartDataMgr.SendSkinUse(m_PartCfgItem.id, m_CurSelectSkinId);
        }

        private void OnGrayUseSkin()
        {
            Global.gApp.gToastMgr.ShowGameTips(95001);
        }

        private void OnUnUseSkin()
        {
            m_NetMechaPartDataMgr.SendSkinUse(m_PartCfgItem.id, 0);
        }

        private void OnStarUpSkin()
        {
            m_SkinMgr.SendPartSkinStarUp(m_CurSelectSkinId);
        }
        private void OnHideSkin()
        {
            m_SkinMgr.SendHideSkin(m_CurSelectSkinId);
        }
        public void FreshHideSkinState()
        {
            if (m_CurSelectSkinId > 0)
            {
                bool skinHide = m_SkinMgr.ISPartSkineHide(m_CurSelectSkinId);
                m_SkinUI.img_show_on.gameObject.SetActive(!skinHide);
                if (m_SkinMgr.IsSkinEquiped(m_CurSelectSkinId))
                {
                    m_SkinUI.showNode.gameObject.SetActive(true);
                }
                else
                {
                    m_SkinUI.showNode.gameObject.SetActive(false);
                }
            }
        }


    }
}
