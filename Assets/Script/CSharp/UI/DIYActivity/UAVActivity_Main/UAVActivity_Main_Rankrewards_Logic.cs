using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UAVActivity_Main_Rankrewards
    {
        private List<UAVActivity_Main_Rankrewards_RankRewardItem> m_RankItems = new();

        private LDNetRankDTOItem m_RankData;

        public void RefreshView(int activityId, LDNetRankDTOItem rankData)
        {
            m_RankData = rankData;
            m_RankItems.Clear();
            m_RankRewardItem.CacheInstanceList();
            DiyActivityRankItem[] items = DiyActivityRank.Data.items;
            for (int i = 0; i < items.Length; i++)
            {
                DiyActivityRankItem cfg = items[i];
                if (cfg.activityID != activityId)
                    continue;
                var itemUI = m_RankRewardItem.GetInstance(true);
                itemUI.RefreshItem(cfg);
                m_RankItems.Add(itemUI);
            }

            Vector2 currentPosition = m_RankRewardContent.rectTransform.anchoredPosition;
            currentPosition.y = 0;
            m_RankRewardContent.rectTransform.anchoredPosition = currentPosition;

            RefreshSelf();
        }

        private void RefreshSelf()
        {
            if (m_RankData != null)
            {
                LDRankRecordDTOItem myRankData = m_RankData.SelfRank;
                bool hasMyRank = myRankData != null;

                // 排名
                string rankStr = hasMyRank ? myRankData.Rank.ToString() : UiTools.Localize(79738);
                m_self_rank_nul.text.SetText(rankStr);

                // 头像
                LDUIPrefabTools.InitSelfRoleHead(m_Self_RoleHead.gameObject);

                // 名称
                m_self_role_name.text.SetText(Global.gApp.gSystemMgr.gRoleMgr.GetRoleName());

                // 进度
                string rankInfo = hasMyRank ? myRankData.GetScoreStr() : string.Empty;
                m_self_rank_info.text.SetText(rankInfo);
            }
        }
    }
}