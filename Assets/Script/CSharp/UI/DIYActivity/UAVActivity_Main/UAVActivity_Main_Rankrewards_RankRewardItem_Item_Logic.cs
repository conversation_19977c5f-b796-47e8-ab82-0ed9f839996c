namespace LD
{
    public partial class UAVActivity_Main_Rankrewards_RankRewardItem_item
    {
        private KnapsackItem m_Knapsack;

        public void RefreshItem(string str)
        {
            LDCommonItem item = new LDCommonItem(str);
            if (m_Knapsack == null)
            {
                m_Knapsack = LDUIPrefabTools.GetKnapsackItemUI(this.rectTransform());
            }

            LDUIPrefabTools.InitKnapsackItem(m_Knapsack, item);
        }
    }
}