using System;
using UnityEngine;

namespace LD
{
    public partial class CSarenaLineupUI_Position
    {
        private int m_Index;
        private int m_MechaId;
        private CSarenaLineupUI m_ParentUI;
        private CommonModelUI m_ModelUI;
        private LDDragUIButton m_DragUIButton;
        
        public void InitUI(int index, CSarenaLineupUI parentUI)
        {
            m_ParentUI = parentUI;
            m_ModelUI = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            m_Index = index;
            LDDropHandle dropHandle = m_Drop.gameObject.GetComponent<LDDropHandle>();
            dropHandle.InitDrop(new CSArenaDropData() { SlotIndex = index }, m_ParentUI.OnDrop);

            m_DragUIButton = m_Drop.gameObject.gameObject.GetComponent<LDDragUIButton>();


            Canvas canvas = m_CommonModelUI.gameObject.GetComponentInParent<Canvas>();
            if (canvas != null)
            {
                m_CommonModelUI.rectTransform.SetParent(canvas.transform.parent, false);
            }
            m_ModelUI.SetRootPosX(10000f + index * 1000);
        }

        public int GetIndex()
        {
            return m_Index;
        }

        public void RefreshUI(int mechaId)
        {
            m_MechaId = mechaId;
            m_Add.gameObject.SetActive(m_MechaId <= 0);
            m_ModeImg.gameObject.SetActive(m_MechaId > 0);
            m_Lock.gameObject.SetActive(false);//暂无逻辑
            m_Num.text.SetText(m_Index);
            if (m_MechaId > 0)
            {
                m_ParentUI.CreateModel(m_MechaId, m_ModelUI, m_ModeImg.rawImage);
            }
            m_RefitLockBtn1.gameObject.SetActive(m_MechaId > 0);
            m_DragUIButton.InitDragData(new CSArenaDragData() { MechaId = m_MechaId }, m_ParentUI.OnDragMechaItemBegin, m_ParentUI.OnDragMechaItem, m_ParentUI.OnDragMechaItemEnd);
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            m_ModelUI?.Dispose();
        }
    }
}