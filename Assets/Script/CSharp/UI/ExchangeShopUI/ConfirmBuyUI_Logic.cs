using System;

namespace LD
{
    public partial class ConfirmBuyUI
    {
        private Action m_Callback;
        private Action<int> m_CallbackInt;

        private int m_Count;

        protected override void OnInitImp()
        {
            m_Green_btn_Cancel.button.AddListener(TouchClose);
            m_Orange_btn_Confirm.button.AddListener(OnConfirm);
        }

        public override void OnFreshUI()
        {
        }

        public void InitInfo(LDCommonItem item, Action cb, int tipsId = 91310)
        {
            m_Callback = cb;
            m_Desc.text.SetTips(tipsId, item.ColorName(), item.Num);
        }

        public void InitInfo(LDCommonItem item, int count, Action<int> cb, int tipsId = 91310)
        {
            m_Count = count;
            m_CallbackInt = cb;
            m_Desc.text.SetTips(tipsId, item.ColorName(), UiTools.FormateMoney(item.Num * m_Count));
        }

        private void OnConfirm()
        {
            TouchClose();
            m_Callback?.Invoke();
            m_CallbackInt?.Invoke(m_Count);
        }

        protected override void OnCloseImp()
        {
        }
    }
}