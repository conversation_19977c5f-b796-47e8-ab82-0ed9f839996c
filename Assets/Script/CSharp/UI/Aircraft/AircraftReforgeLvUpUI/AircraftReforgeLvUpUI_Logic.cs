using System.Collections.Generic;

namespace LD
{
    public partial class AircraftReforgeLvUpUI
    {
        protected override void OnInitImp()
        {
            RefreshView();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }

        private void RefreshView()
        {
            int curLv = Global.gApp.gSystemMgr.gAircraftMgr.Data.RefinementLevel;
            int lastLv = curLv - 1;
            AircraftReforgeBaseItem curCfg = AircraftReforgeBase.Data.Get(curLv);
            AircraftReforgeBaseItem lastCfg = AircraftReforgeBase.Data.Get(lastLv);

            m_lvNUM.text.SetTips(65541, lastLv);
            m_lv_newNUM.text.SetTips(65541, curLv);


            Dictionary<int, int> lastPro = new();
            foreach (string s in lastCfg.manufactureProbability)
            {
                var lines = LDCommonTools.Split(s);
                var quality = LDParseTools.IntParse(lines[0]);
                var pro = LDParseTools.IntParse(lines[1]);
                lastPro.Add(quality, pro);
            }

            m_Quality.CacheInstanceList();
            foreach (string s in curCfg.manufactureProbability)
            {
                var lines = LDCommonTools.Split(s);
                var quality = LDParseTools.IntParse(lines[0]);
                var pro = LDParseTools.IntParse(lines[1]);

                var lastvalue = lastPro.ContainsKey(quality) ? lastPro[quality] : 0;
                if (pro > lastvalue)
                {
                    var item = m_Quality.GetInstance(true);

                    LoadSprite(item.quality_icon.image, LDCommonTipsTools.GetAircraftQualityBorder(quality));
                    item.QualityName.text.SetText(LDCommonTipsTools.GetQualityTips(quality));
                    item.gailvVal1.text.SetText($"{lastvalue / 100f}%");
                    item.gailvVal2.text.SetText($"{pro / 100f}%");
                }
            }
        }
    }
}