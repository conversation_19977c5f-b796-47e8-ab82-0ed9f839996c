using UnityEngine;

namespace LD
{
    public partial class AircraftGainedUI
    {
        private AircraftCfgItem m_AircraftCfg;
        private LDRewardUIData m_RewardUIData;
        private CommonModelUI m_CommonModel;
        private LDCommonItem m_CommonItem;

        protected override void OnInitImp()
        {
            m_CommonModel = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            m_CommonModel.SetCameraPos(new UnityEngine.Vector3(0, 17.45f, -27.8f));
            m_CommonModel.SetCameraFOV(60);
            m_CommonModel.SetCameraRot(new UnityEngine.Vector3(30, 0, 0));
        }

        public override void OnFreshUI()
        {
        }

        public void RefreshUI(LDRewardUIData rewardUIData, LDCommonItem itemData)
        {
            m_RewardUIData = rewardUIData;
            m_CommonItem = itemData;
            m_AircraftCfg = AircraftCfg.Data.Get(itemData.Id);

            LDUIPrefabTools.InitNameNodeForAircraft(m_NameNode.gameObject, itemData.Id, m_AircraftCfg.quality);

            RefreshModel();
            RefreshEffect();
        }

        // 模型
        private void RefreshModel()
        {
            AircraftCfgItem cfgItem = AircraftCfg.Data.Get(m_AircraftCfg.id);
            GameObject model = m_CommonModel.InitModel(cfgItem.prefab, m_ModelImage.rawImage);
            m_CommonModel.SetRootPosX(6000);
        }

        // 特效
        private void RefreshEffect()
        {
            LDCommonTools.DestoryChildren(m_fx_texiao.rectTransform);
            InstanceUIEffectNode(LDUIResTools.GetQualityObtainEffectPath(m_CommonItem.Quality), ResSceneType.NormalRes, m_fx_texiao.rectTransform);
        }

        protected override void OnCloseImp()
        {
            if (m_RewardUIData != null)
            {
                Global.gApp.gUiMgr.CheckExternRewardDataUI(m_RewardUIData);
            }
        }
    }
}