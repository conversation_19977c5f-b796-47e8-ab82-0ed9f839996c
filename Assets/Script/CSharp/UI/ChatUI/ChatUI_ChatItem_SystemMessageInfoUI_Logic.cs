namespace LD
{
    public partial class ChatUI_ChatItem_SystemMessageInfoUI
    {
        protected override float m_MaxItemHeight => 80f;
        protected override float m_MaxChatTextWidth => 630f;
        protected override float m_TextBgWidthOffset => 40f;
        protected override float m_TextBgHeightOffset => 50f;

        protected override float m_ItemTopOffset => 0f;
        protected override float m_ItemDownOffset => 40f;

        protected override void RefreshChatItemImp(LDNetChatInfo chatInfo)
        {
            m_System_Txt.text.raycastTarget = true;
            
            m_System_Txt.text.SetText(chatInfo.GetShowContent());
            RefreshItemSize(m_System_Txt, m_System_BG);
        }
    }
}