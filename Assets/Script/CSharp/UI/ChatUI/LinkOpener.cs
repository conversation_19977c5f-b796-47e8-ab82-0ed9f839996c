using System;
using System.Collections.Generic;
using LitJson;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;

namespace LD
{
    [RequireComponent(typeof(TMP_Text))]
    public partial class LinkOpener : MonoBehaviour, IPointerClickHandler
    {
        private void Start()
        {
            TMP_Text tmpText = GetComponent<TMP_Text>();
            if (tmpText != null)
            {
                tmpText.raycastTarget = true;
            }
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            TMP_Text tmpText = GetComponent<TMP_Text>();
            int linkIndex = TMP_TextUtilities.FindIntersectingLink(tmpText, eventData.position, Global.gApp.gUICameraCmpt);

            if (linkIndex != -1)
            {
                TMP_LinkInfo linkInfo = tmpText.textInfo.linkInfo[linkIndex];
                TryShowChatLink(linkInfo.GetLinkID());
            }
        }

        private void TryShowChatLink(string linkInfo)
        {
            string[] linkInfos = LDCommonTools.Split(linkInfo, "=");
            if (linkInfos.Length > 1)
            {
                switch (linkInfos[0])
                {
                    case LdNetChatConst.ChatLinkKeyPlayer:
                        if (!string.IsNullOrEmpty(linkInfos[1]))
                        {
                            long playerId = LDParseTools.longParse(linkInfos[1]);
                            if (playerId > 0)
                            {
                                Global.gApp.gSystemMgr.gRoleMgr.OtherRoleData.ShowViewOtherUI(playerId);
                            }
                        }

                        break;
                    case LdNetChatConst.ChatLinkKeyItem:
                    {
                        int itemId = LDParseTools.IntParse(linkInfos[1]);
                        LDCommonItem item = new LDCommonItem(LDCommonType.Item, itemId, 1);
                        item.ShowDetails();
                    }
                        break;
                    case LdNetChatConst.ChatLinkKeyMecha:
                    {
                        int itemId = LDParseTools.IntParse(linkInfos[1]);
                        LDCommonItem item = new LDCommonItem(LDCommonType.Mecha, itemId, 1);
                        item.ShowDetails();
                    }
                        break;
                    case LdNetChatConst.ChatLinkKeyDiypart:
                    {
                        int itemId = LDParseTools.IntParse(linkInfos[1]);
                        LDCommonItem item = new LDCommonItem(LDCommonType.DIYPart, itemId, 1);
                        item.ShowDetails();
                    }
                        break;
                    case LdNetChatConst.ChatLinkKeyUAV:
                    {
                        var lines = LDCommonTools.Split(linkInfos[1], "_");
                        int itemId = LDParseTools.IntParse(lines[0]);
                        int quality = LDParseTools.IntParse(lines[1]);
                        LDCommonItem item = new LDCommonItem(LDCommonType.UAV, itemId, 1);
                        item.SetNewQuality(quality);
                        item.ShowDetails();
                    }
                        break;
                    case LdNetChatConst.ChatLinkUrl:
                    {
                        Global.gApp.gSdkMgr.gOneMTMgr.ShowSDKH5Page(linkInfos[1]);
                        Debug.Log($"假装打开了网页 = {linkInfos[1]}");
                    }
                        break;
                    case LdNetChatConst.ChatLinkKeySurvivor:
                    {
                        int itemId = LDParseTools.IntParse(linkInfos[1]);
                        LDCommonItem item = new LDCommonItem(LDCommonType.Survivor, itemId, 1);
                        item.ShowDetails();
                    }
                        break;
                    case LdNetChatConst.ChatLinkKeyDriver:
                    {
                        int itemId = LDParseTools.IntParse(linkInfos[1]);
                        LDCommonItem item = new LDCommonItem(LDCommonType.Driver, itemId, 1);
                        item.ShowDetails();
                    }
                        break;
                    case LdNetChatConst.ChatLinkInviteGuild:
                    {
                        var guildId = LDParseTools.longParse(linkInfos[1]);
                        var expirationTime = LDParseTools.longParse(linkInfos[2]);
                        if (expirationTime < DateTimeUtil.GetServerTime())
                        {
                            Global.gApp.gToastMgr.ShowGameTips(77413);
                            return;
                        }

                        Global.gApp.gSystemMgr.gGuildMgr.SendJoinReq(guildId);
                    }
                        break;
                    case LdNetChatConst.ChatLinkGuildRescue:
                    {
                        var id = LDParseTools.longParse(linkInfos[1]);
                        double expireTime = LDParseTools.DoubleParse(linkInfos[2]);
                        double curTime = DateTimeUtil.GetServerTime();
                        if (curTime > expireTime)
                        {
                            Global.gApp.gToastMgr.ShowGameTips(54046);
                            return;
                        }

                        Global.gApp.gSystemMgr.gGuildLeagueMgr.SendGuildLeaguePrisonMissionCheckReq(id, 2);
                    }
                        break;
                    case LdNetChatConst.ChatLinkCollectAsk:
                    {
                        double expireTime = LDParseTools.DoubleParse(linkInfos[1]);
                        double curTime = DateTimeUtil.GetServerTime();
                        if (curTime > expireTime)
                        {
                            Global.gApp.gToastMgr.ShowGameTips(55047);
                            return;
                        }

                        var roleInfoParam = linkInfos[2];
                        var lines = LDCommonTools.Split(roleInfoParam, "_");
                        long receiverId = LDParseTools.longParse(lines[0]);

                        if (receiverId == Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId())
                        {
                            Global.gApp.gToastMgr.ShowGameTips(55052);
                            return;
                        }

                        Global.gApp.gSystemMgr.gCollectActivityMgr.SendFriendGiftCheck(
                            receiverId, LDParseTools.IntParse(lines[7]), LDParseTools.IntParse(lines[8]), LDParseTools.IntParse(lines[9]), roleInfoParam);
                    }
                        break;
                }
            }
            else
            {
                Global.LogError($"link chat is error:{linkInfo}");
            }
        }
    }
}