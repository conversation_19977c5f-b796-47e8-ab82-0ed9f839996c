using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class Season_skipPassUI
    {
        private LDNetSeasonMgr m_SeasonMgr;
        private Action m_BattleSingleCallback;
        private int m_StartSweepId;
        private int m_MaxSweepId;
        private int m_CanSweepCount;
        private List<SeasonMissionItem> m_MissionCfgs = new List<SeasonMissionItem>();
        private int m_TargetIndex;
        private int m_TotalCost;
        private int m_CostId;

        protected override void OnInitImp()
        {
            m_SeasonMgr = Global.gApp.gSystemMgr.gSeasonMgr;
            m_BattleStartSingle.button.AddListener(OnBattleStartSingle);
            m_PassStartBtn.button.AddListener(OnSweep);
            m_Slider.slider.AddListener(OnSliderChange);
        }

        public override void OnFreshUI()
        {
        }

        public void RefreshUI(Action OnBattleSingle, int startSweepId, int maxSweepId, int canSweepCount)
        {
            m_BattleSingleCallback = OnBattleSingle;
            m_MaxSweepId = maxSweepId;
            m_CanSweepCount = canSweepCount;
            m_StartSweepId = startSweepId;

            m_MissionCfgs.Clear();
            int costCount = 0;
            int canBattleCount = 0;
            for (int i = m_StartSweepId; i <= m_MaxSweepId; i++)
            {
                if (SeasonMission.Data.TryGet(i, out SeasonMissionItem cfg))
                {
                    m_MissionCfgs.Add(cfg);
                    LDCommonItem cost = new LDCommonItem(cfg.stamina_cost);
                    costCount += (int)cost.Num;
                    if (cost.CurNum >= costCount)
                    {
                        canBattleCount++;
                    }
                }
            }

            if (m_MissionCfgs.Count <= 0)
            {
                Global.LogError($"Season_skipPassUI 数据错误");
                return;
            }

            m_explain.text.SetTips(67593, m_CanSweepCount);

            LDCommonItem commonItem = new LDCommonItem(m_MissionCfgs[0].stamina_cost);
            m_CostSimple.text.SetText(commonItem.Num);
            m_CostId = commonItem.Id;

            if (canBattleCount == 0)
            {
                canBattleCount = 1; // 至少显示一条
            }

            m_Slider.slider.value = canBattleCount / ((float)m_MissionCfgs.Count);
            m_Mission.text.SetTips(67549, m_MissionCfgs[canBattleCount - 1].Wellen);
            m_Cost.text.SetText(canBattleCount * commonItem.Num);
            m_TotalCost = canBattleCount * (int)commonItem.Num;
            m_TargetIndex = canBattleCount - 1;
        }

        private void OnSliderChange(float percent)
        {
            m_TargetIndex = Mathf.Min(Mathf.CeilToInt(percent * (m_MissionCfgs.Count - 1)));
            m_TotalCost = 0;
            for (int i = 0; i <= m_TargetIndex; i++)
            {
                SeasonMissionItem cfg = m_MissionCfgs[i];
                LDCommonItem commonItem = new LDCommonItem(cfg.stamina_cost);
                m_TotalCost += (int)commonItem.Num;
            }

            m_Mission.text.SetTips(67549, m_MissionCfgs[m_TargetIndex].Wellen);
            m_Cost.text.SetText(m_TotalCost);
        }

        private void OnBattleStartSingle()
        {
            m_BattleSingleCallback?.Invoke();
            TouchClose();
        }

        private void OnSweep()
        {
            if (m_TotalCost > Global.gApp.gSystemMgr.gBagMgr.GetItemCount(m_CostId))
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.BuyEnergyUI);
                return;
            }

            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.PreventPerformanceUI);
            m_SeasonMgr.SendPlayerSeasonMissionSweepReq(m_StartSweepId, m_MissionCfgs[m_TargetIndex].id, m_SeasonMgr.GetCurSeasonId());
            TouchClose();
        }

        protected override void OnCloseImp()
        {
        }
    }
}