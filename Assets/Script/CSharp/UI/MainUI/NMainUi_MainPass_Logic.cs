using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class MainSeasonUIData : LDUIDataBase
    {
        public List<LDCommonItem> Rewards;
    }

    public class MainSeasonRankTopOneData : LDUIDataBase
    {
        public LDRankRecordDTOItem TopOne;
    }


    public partial class NMainUi
    {
        private const string SeasonMainUIPath = "PrefabsN/UI/Main/SeasonMainUi";

        private LDNetMainPassMgr m_MainPassMgr;
        private int m_PassId;
        private int m_RePlayId;
        private MainMissionItem m_MainMissionCfg;
        private Vector3 m_OrignPassBossPos;
        private int m_ShowNextMechaId;
        private SeasonMainUi m_SeasonUI;

        private void InitMainPassPartial()
        {
            m_PassStartNormalBtn.button.AddListener(OnPassStart);
            m_PassStartBossBtn.button.AddListener(OnPassStart);
            m_NextPassBtn.button.AddListener(OnNextPass);
            m_PassMaxBtn.button.AddListener(OnPassMax);
            m_nextMechaShow.button.AddListener(OnNextMecha);
            m_dailyStage.AddListener(OnDailyStageClick);
            m_MainPassMgr = Global.gApp.gSystemMgr.gMainPassMgr;
            m_OrignPassBossPos = m_PassModel.rectTransform.localPosition;

            m_TeamBtn.gameObject.SetActive(false);
            // m_TeamBtn.AddListener(OnSeasonTestClick);

            RefreshPassUI();
            RefreshDailyStage();
        }

        private void OnFreshUIMainPassPartial()
        {
            RefreshPassUI();
        }

        private void OnEndOfFrameCallMainPassPartial()
        {
        }

        private void UpdateMainPassPartial()
        {
        }

        private void OnCloseImpMainPassPartial()
        {
        }

        private void RefreshPassUI()
        {
            if (TryChangeToSeasonPanel())
            {
                m_PassNode.gameObject.SetActive(false);
                m_PassModel.gameObject.SetActive(false);
                return;
            }

            m_PassId = m_MainPassMgr.GetCurShowPassId();
            m_RePlayId = m_MainPassMgr.GetLastFinishedPassId();
            m_MainMissionCfg = MainMission.Data.Get(m_PassId);

            RefreshPassInfo();
            RefreshRewardList();
            RefreshBattleBtns();
            RefreshMapPoint();
            RefreshMainPassModel();
            RefreshNextMechaInfo();
        }

        // 关卡信息
        private void RefreshPassInfo()
        {
            // m_PassTitle.text.SetText(m_MainPassMgr.GetMainPassFullNameStr(m_MainMissionCfg.id));
            LoadSprite(m_PassBackBg.image, m_MainMissionCfg.boss_bg);
            LoadSprite(m_PassFrontBg.image, m_MainMissionCfg.BattleMap);
        }

        // 奖励列表
        private void RefreshRewardList()
        {
            m_PassRewardItem.CacheInstanceList();
            List<LDMainPassReward> passRewards = m_MainPassMgr.GetPassRewards(m_PassId);
            if (passRewards.Count <= 0)
            {
                m_PassRewardNode.gameObject.SetActive(false);
                return;
            }

            m_PassRewardBar.image.fillAmount = 0;
            for (int i = 0; i < passRewards.Count; i++)
            {
                LDMainPassReward _reward = passRewards[i];
                NMainUi_PassRewardItem item = m_PassRewardItem.GetInstance(true);
                item.RefreshUI(m_PassId, _reward, i);

                // 进度条
                bool isNotReach = m_MainPassMgr.IsPassRewardNotReach(m_PassId, _reward.RewardId);
                if (!isNotReach)
                {
                    if (i == 1)
                    {
                        m_PassRewardBar.image.fillAmount = 0.5f;
                    }

                    if (i == 2)
                    {
                        m_PassRewardBar.image.fillAmount = 1f;
                    }
                }
            }
        }

        // 出战按钮
        private void RefreshBattleBtns()
        {
            m_NextPassBtn.gameObject.SetActive(false);
            m_PassMaxBtn.gameObject.SetActive(false);
            m_PassStartBossBtn.gameObject.SetActive(false);
            m_PassStartNormalBtn.gameObject.SetActive(false);
            m_BattleInfo.gameObject.SetActive(false);

            if (!m_MainPassMgr.IsPassedAll()) // 全部通关  按钮都不显示
            {
                if (m_MainPassMgr.IsPassFinished(m_PassId))
                {
                    m_NextPassBtn.gameObject.SetActive(true);
                }
                else
                {
                    m_BattleInfo.gameObject.SetActive(true);
                    bool isBossPass = m_MainMissionCfg.type == LDMainPassType.Boss;
                    m_PassStartBossBtn.gameObject.SetActive(isBossPass);
                    m_PassStartNormalBtn.gameObject.SetActive(!isBossPass);
                    LDCommonItem staminaItem = new LDCommonItem(m_MainMissionCfg.stamina_cost);
                    m_PassBattleInfoCost.text.SetText(staminaItem.Num);
                    m_PassOrder.text.SetText($"{m_MainMissionCfg.order}-{m_MainMissionCfg.StageNumber}");
                }
            }
            else
            {
                m_PassMaxBtn.gameObject.SetActive(true);
            }
        }

        // 地图点
        private void RefreshMapPoint()
        {
            m_PassMapPoint.CacheInstanceList();
            m_PassMapPointReward.CacheInstanceList();
            LDCommonTools.DestoryChildren(m_PassMapParent.rectTransform);
            List<MainMissionItem> passList = m_MainPassMgr.GetMainPassCfgListByChapterOrder(m_MainMissionCfg.order);
            if (passList.Count <= 0)
            {
                return;
            }

            string prefabPath = passList[0].mapPrefab;
            if (string.IsNullOrEmpty(prefabPath))
            {
                return;
            }

            GameObject mapNode = InstantiateObj(prefabPath, ResSceneType.NormalRes, m_PassMapParent.rectTransform);
            MainUIMap mainUIMap = mapNode.TryAddComponent<MainUIMap>();
            mainUIMap.Init();
            for (int i = 0; i < passList.Count; i++)
            {
                MainMissionItem passCfg = passList[i];
                bool isCurPass = passCfg.id == m_MainPassMgr.GetCurShowPassId();
                bool isFinished = m_MainPassMgr.IsPassFinished(passCfg.id);
                bool isUnlock = isCurPass || isFinished;
                mainUIMap.SetLineLock(passCfg.StageNumber, !isUnlock);

                NMainUi_PassMapPoint pointUI = m_PassMapPoint.GetInstance(true);
                pointUI.RefreshUI(passCfg);
                mainUIMap.SetPointInfo(passCfg.StageNumber, pointUI.transform);

                if (!string.IsNullOrEmpty(passCfg.ItemDisplay))
                {
                    NMainUi_PassMapPointReward rewardUI = m_PassMapPointReward.GetInstance(true);
                    rewardUI.RefreshUI(passCfg);
                    mainUIMap.SetRewardInfo(passCfg.StageNumber, rewardUI.transform);
                }
            }
        }

        // 模型
        private void RefreshMainPassModel()
        {
            m_PassModel.gameObject.SetActive(true);
            LDCommonTools.DestoryChildren(m_PassModel.rectTransform);
            GameObject boss = InstantiateObj(m_MainMissionCfg.BossEntity, ResSceneType.NormalRes, m_PassModel.rectTransform);
            boss.GetComponentInChildren<Animator>().Play(LDAnimName.Idle);
            float[] bossInfo = m_MainMissionCfg.bossInfo;
            if (bossInfo.Length >= 4)
            {
                m_PassModel.rectTransform.localPosition = m_OrignPassBossPos + new Vector3(bossInfo[0], bossInfo[1], bossInfo[2]);
                m_PassModel.rectTransform.localScale = new Vector3(bossInfo[3], bossInfo[3], bossInfo[3]);
            }
        }

        // 机甲获得预览
        private void RefreshNextMechaInfo()
        {
            m_nextMechaShow.gameObject.SetActive(false);
            return; // 暂时屏蔽

            m_ShowNextMechaId = 0;
            UnlockMechaTipsItem[] cfgs = UnlockMechaTips.Data.items;
            List<UnlockMechaTipsItem> cfgList = new List<UnlockMechaTipsItem>(cfgs);
            cfgList.Sort((a, b) => { return a.sort - b.sort; });
            bool isShow = false;
            foreach (UnlockMechaTipsItem item in cfgList)
            {
                LDCommonItem commonItem = new LDCommonItem(item.UnlockMechaID);
                if (!Global.gApp.gSystemMgr.gMechaDataMgr.IsMechaUnlocked(commonItem.Id))
                {
                    isShow = true;
                    m_ShowNextMechaId = commonItem.Id;
                    m_nextMechaShow.gameObject.SetActive(true);
                    MechaItem mechaCfg = Mecha.Data.Get(commonItem.Id);
                    string bigIconPath = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaBigIcon(mechaCfg.id);
                    LoadSprite(m_img_nextMecha.image, bigIconPath);
                    m_txt_conditionNext.text.SetTips(mechaCfg.mechaUnlockTips);
                    LoadSprite(m_nextMechaShow2.image, LDUIResTools.GetNextMechaBgPath(mechaCfg.quality));
                    LoadSprite(m_nextMechaShow.image, LDUIResTools.GetNextMechaFrontBgPath(mechaCfg.quality));
                    break;
                }
            }

            if (!isShow)
            {
                m_nextMechaShow.gameObject.SetActive(false);
            }
        }

        // 日常副本
        private void RefreshDailyStage()
        {
            m_dailyStage.gameObject.SetActive(false);
            List<LDNetDataMgr> sysMgrs = new List<LDNetDataMgr>()
            {
                Global.gApp.gSystemMgr.gCoinPassMgr,
                Global.gApp.gSystemMgr.gPveTowerDataMgr,
                Global.gApp.gSystemMgr.gExpeditionMgr
            };

            foreach (LDNetDataMgr dataMgr in sysMgrs)
            {
                if (dataMgr.IsUnlock())
                {
                    m_dailyStage.gameObject.SetActive(true);
                    return;
                }
            }
        }

        public SeasonMainUi GetSeasonUI()
        {
            return m_SeasonUI;
        }

        // 切换到赛季界面
        private bool TryChangeToSeasonPanel()
        {
            if (m_MainPassMgr.IsPassedAll())
            {
                if (m_SeasonUI == null)
                {
                    LDCommonTools.DestoryChildren(m_SeasonNode.rectTransform);
                    GameObject seasonMainUIGO = Global.gApp.gResMgr.InstantiateLoadObj(SeasonMainUIPath, ResSceneType.NormalUI, m_SeasonNode.rectTransform);
                    m_SeasonUI = seasonMainUIGO.GetComponent<SeasonMainUi>();
                    m_SeasonUI.InitUI(this);
                }

                m_SeasonUI.RefreshUI();

                return true;
            }

            return false;
        }

        private void OnPassStart()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.FirstMain_Battle, 0);
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.SecondPass, 1);
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.ThirdPass, 0);

            LDCommonItem staminaItem = new LDCommonItem(m_MainMissionCfg.stamina_cost);
            long needCount = staminaItem.Num;
            long curCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(staminaItem.Id);
            if (needCount > curCount)
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.BuyEnergyUI);
                return;
            }

            bool noTips = Global.gApp.gSystemMgr.gRoleMgr.GetNotTipsForCurLogin(LDSystemEnum.MainPass);
            if (!noTips && m_MainMissionCfg.recommend_power > Global.gApp.gSystemMgr.gRoleMgr.GetPowerNum())
            {
                Global.gApp.gUiMgr.OpenUIAsync<FightTowerTipsUI>(LDUICfg.FightTowerTipsUI).SetLoadedCall(ui =>
                {
                   
                    ui?.RefreshUI(m_MainMissionCfg.recommend_power, LDSystemEnum.MainPass, MainPassStart,MainPassReplay);
                });
            }
            else
            {
                MainPassStart();
            }
        }

        private void MainPassReplay()
        {
            m_MainPassMgr.SendLastPassStart(m_RePlayId);
        }
        
        private void MainPassStart()
        {
            m_MainPassMgr.SendPassStart(m_PassId);
        }

        private void OnNextPass()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.SecondMain, 0);
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.ThirdMain, 0);
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.FifthMain, 0);

            m_MainPassMgr.ReceiveAllPassRewards(m_PassId);

            RefreshPassUI();
        }

        private void OnPassMax()
        {
            Global.gApp.gToastMgr.ShowGameTips(91144);
        }

        private void OnNextMecha()
        {
            if (m_ShowNextMechaId > 0)
            {
                Global.gApp.gUiMgr.OpenUIAsync<MechaStoryUI>(LDUICfg.MechaStoryUI).SetLoadedCall(ui => { ui?.RefreshUI(m_ShowNextMechaId); });
            }
        }

        private void OnSeasonTestClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.SeasonUI);
        }

        private void OnDailyStageClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.GamePlayRootUI);
        }
    }

    public partial class NMainUi_PassRewardItem
    {
        private LDNetMainPassMgr m_MainPassMgr;
        private int m_PassId;
        private LDMainPassReward m_RewardData;
        private MainMissionItem m_MissionCfg;

        public void RefreshUI(int passId, LDMainPassReward reward, int index)
        {
            m_ReceiveBtn.button.AddListener(OnReceive);
            m_MainPassMgr = Global.gApp.gSystemMgr.gMainPassMgr;
            m_PassId = passId;
            m_RewardData = reward;

            m_MissionCfg = MainMission.Data.Get(passId);

            LDCommonItem data = reward.ItemData;
            bool isCanReceive = m_MainPassMgr.IsPassRewardCanReceive(passId, reward.RewardId);
            bool isReceived = m_MainPassMgr.IsPassRewardReceived(passId, reward.RewardId);

            LDCommonTools.DestoryChildren(m_Reward.rectTransform);
            KnapsackItem knapsackItem = LDUIPrefabTools.GetKnapsackItemUI(data, m_Reward.rectTransform);
            knapsackItem.ShowCanGet(isCanReceive);
            if (isCanReceive)
            {
                knapsackItem.SetClickCallback(OnReceive);
            }

            m_RewardTxt.text.SetTips(m_MissionCfg.RewardText[index]);

            m_RewardTxt.gameObject.SetActive(!isCanReceive && !isReceived);
            m_CanReceive.gameObject.SetActive(isCanReceive);
            m_Received.gameObject.SetActive(isReceived);
        }

        private void OnReceive()
        {
            m_MainPassMgr.ReceiveAllPassRewards(m_PassId);
            // m_MainPassMgr.SendReceivePassReward(m_PassId, new List<int>() { m_RewardData.RewardId });
        }
    }

    public partial class NMainUi_PassMapPoint
    {
        private LDNetMainPassMgr m_MainPassMgr;
        private MainMissionItem m_Cfg;

        private bool m_IsCurPass;
        private bool m_IsFinished;
        private bool m_IsPassedAll;

        public void RefreshUI(MainMissionItem cfg)
        {
            m_MainPassMgr = Global.gApp.gSystemMgr.gMainPassMgr;
            m_Cfg = cfg;

            m_IsCurPass = cfg.id == m_MainPassMgr.GetCurShowPassId();
            m_IsFinished = m_MainPassMgr.IsPassFinished(cfg.id);
            m_IsPassedAll = m_MainPassMgr.IsPassedAll();

            RefreshBaseInfo();
            RefreshIconNode();
        }

        private void RefreshBaseInfo()
        {
            m_PassOrderNode.gameObject.SetActive(false);
            if (m_IsCurPass && m_Cfg.type != LDMainPassType.Boss)
            {
                // m_PassOrder.text.SetText($"{m_Cfg.order}-{m_Cfg.StageNumber}");
                m_txt_stagePower.text.SetText(UiTools.FormateMoney(m_Cfg.recommend_power));
                m_PassOrderNode.gameObject.SetActive(true);
            }
        }

        private void RefreshIconNode()
        {
            m_normal.gameObject.SetActive(false);
            m_elite.gameObject.SetActive(false);
            m_boss.gameObject.SetActive(false);

            if (m_Cfg.type == LDMainPassType.Normal)
            {
                m_normal.gameObject.SetActive(true);
                m_normalState.gameObject.SetActive(false);
                m_normalBattleState.gameObject.SetActive(false);
                m_normalLockState.gameObject.SetActive(false);

                if (m_IsCurPass)
                {
                    m_normalBattleState.gameObject.SetActive(true);
                    LDUIPrefabTools.InitSelfRoleHead(m_NormalRoleHead.gameObject);
                }
                else if (m_IsFinished)
                {
                    m_normalState.gameObject.SetActive(true);
                }
                else
                {
                    m_normalLockState.gameObject.SetActive(true);
                }
            }

            if (m_Cfg.type == LDMainPassType.Elite)
            {
                m_elite.gameObject.SetActive(true);
                m_eliteState.gameObject.SetActive(true);
                m_eliteBattleState.gameObject.SetActive(false);
                m_eliteLockState.gameObject.SetActive(false);

                if (m_IsCurPass)
                {
                    m_eliteBattleState.gameObject.SetActive(true);
                    LDUIPrefabTools.InitSelfRoleHead(m_EliteRoleHead.gameObject);
                }
                else if (m_IsFinished)
                {
                    m_eliteState.gameObject.SetActive(true);
                }
                else
                {
                    m_eliteLockState.gameObject.SetActive(true);
                }
            }

            if (m_Cfg.type == LDMainPassType.Boss)
            {
                m_boss.gameObject.SetActive(true);
                m_BossState.gameObject.SetActive(false);
                m_BossBattleState.gameObject.SetActive(false);

                if (m_IsCurPass)
                {
                    m_BossBattleState.gameObject.SetActive(true);
                    LDUIPrefabTools.InitSelfRoleHead(m_BossRoleHead.gameObject);
                }
                else
                {
                    m_BossState.gameObject.SetActive(true);
                }
            }
        }
    }

    public partial class NMainUi_PassMapPointReward
    {
        private MainMissionItem m_Cfg;

        public void RefreshUI(MainMissionItem cfg)
        {
            m_Cfg = cfg;

            RefreshReward();
        }

        private void RefreshReward()
        {
            LDCommonTools.DestoryChildren(m_scaleNode.rectTransform);
            LDUIPrefabTools.SetKnapsackItemToParentNode(m_Cfg.ItemDisplay, m_scaleNode.rectTransform);
        }
    }
}