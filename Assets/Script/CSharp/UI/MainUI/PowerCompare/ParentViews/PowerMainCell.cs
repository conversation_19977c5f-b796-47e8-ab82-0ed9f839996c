using System;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class PowerMainCell: LDBaseResUI, ITreeChildView
    {
        #region GameObj Reference

        public Text m_Module_Name;
        public Text m_Other_PowerNum;
        public Text m_Self_PowerNum;

        public GameObject m_Arrow;
        public Button m_BtnExpand; //展开
        public Button m_BtnFold; //折叠

        public Image m_Up;
        public Image m_Down;

        #endregion

        private int m_TreeItemIndex = -1;
        private int m_IdxInTotalCell = -1;

        private Action<int> m_ClickHandler;

        public int TreeItemIndex => m_TreeItemIndex;
        public void Init()
        {
            m_BtnExpand.onClick.AddListener(OnClickExpand);
        }

        public void ShowArrow(bool isShow)
        {
            m_Arrow.SetActive(isShow);
        }

        public void SetClickCallBack(Action<int> clickHandler)
        {
            m_ClickHandler = clickHandler;
        }

        void OnClickExpand()
        {
            if (m_ClickHandler != null)
            {
                m_ClickHandler(m_TreeItemIndex);
            }
        }

        public void SetExpand(bool expand)
        {
            m_Arrow.transform.localEulerAngles = expand ? new Vector3(0, 0, 0) : new Vector3(0, 0, 180);
        }

        public void SetItemData(TreeViewItemDataBase data, int treeItemIndex, int idxInTotalCell)
        {
            if (data is not PowerMainData powerMainData)
            {
                return;
            }

            m_TreeItemIndex = treeItemIndex;
            m_IdxInTotalCell = idxInTotalCell;
            m_Module_Name.SetTips(powerMainData.m_NameId);
            m_Other_PowerNum.SetText(UiTools.FormateMoney(powerMainData.m_OtherPower, true));
            m_Self_PowerNum.SetText(UiTools.FormateMoney(powerMainData.m_SelfPower, true));

            m_Up.gameObject.SetActive(powerMainData.m_SelfPower > powerMainData.m_OtherPower);
            m_Down.gameObject.SetActive(powerMainData.m_SelfPower < powerMainData.m_OtherPower);
        }
    }
}