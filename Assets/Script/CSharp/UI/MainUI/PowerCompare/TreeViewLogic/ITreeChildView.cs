using UnityEngine;

namespace LD
{
    public interface ITreeChildView
    {
        /// <summary>
        /// 让子节点 View 更新自身显示
        /// </summary>
        /// <param name="data">当前子节点的数据（派生自 TreeViewItemDataBase）</param>
        /// <param name="parentIndex">父节点的索引</param>
        /// <param name="childIndex">子节点在父节点下的索引</param>
        void SetItemData(TreeViewItemDataBase data, int parentIndex, int childIndex);
    }
}