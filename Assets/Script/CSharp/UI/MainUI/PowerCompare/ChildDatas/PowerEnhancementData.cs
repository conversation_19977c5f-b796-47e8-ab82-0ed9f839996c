namespace LD
{
    public class PowerEnhancementData : PowerCompareItemDataBase
    {
        public PowerEnhancementData(long otherId, long selfId) : base(otherId, selfId)
        {

        }

        public override void Init(int index, int parentIndex)
        {
            base.Init(index, parentIndex);
        }

        public LDNetEnhancement GetOtherData()
        {
            LDNetPlayerCombatPower otherInfo = Global.gApp.gSystemMgr.gPowerCompareMgr.GetPowerInfo(PlayIdOther);
            return otherInfo.Enhancement;
        }

        public LDNetEnhancement GetSelfData()
        {
            LDNetPlayerCombatPower selfInfo = Global.gApp.gSystemMgr.gPowerCompareMgr.GetPowerInfo(PlayIdSelf);
            return selfInfo.Enhancement;
        }
    }
}