using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class GamePlayRootUI_CoinPassItem
    {
        private LDNetCoinPassMgr m_CoinPassMgr;
        private ActivityInfoItem m_PlayCfg;
        private int m_CurMissionId;
        private CoinMissionItem m_MissionCfg;
        private bool m_IsUnlock;

        public void RefreshUI()
        {
            m_CoinPassMgr = Global.gApp.gSystemMgr.gCoinPassMgr;
            m_PlayCfg = ActivityInfo.Data.Get(2);
            m_Btn.button.AddListener(OnClick);

            m_CurMissionId = m_CoinPassMgr.GetCurMissionId();
            m_MissionCfg = CoinMission.Data.Get(m_CurMissionId);
            m_IsUnlock = m_CoinPassMgr.IsUnlock();

            RefreshBaseInfo();
            RefreshRewardInfo();
            RefreshProgressInfo();
            RefreshUnlockInfo();
            TryGuide();
        }

        private void TryGuide()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.CoinPass, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.CoinPass, m_root.rectTransform, 1))
            {
                return;
            }
        }

        private void RefreshBaseInfo()
        {
            LoadSprite(m_BG.image, m_PlayCfg.ActivityDec);
            m_PlayName.text.SetTips(m_PlayCfg.ActivityName);
            m_Introduction.text.SetTips(m_PlayCfg.ActivityTips);
            m_txt_Progress.text.SetText($"{m_MissionCfg.Order}-{m_MissionCfg.PassNum}");
            m_Introduction.gameObject.SetActive(!m_IsUnlock);
            m_txt_Progress_Node.gameObject.SetActive(m_IsUnlock);
        }

        private void RefreshRewardInfo()
        {
            m_Item.CacheInstanceList();
            if (m_PlayCfg.ActivityReward.Length > 0)
            {
                foreach (string rwd in m_PlayCfg.ActivityReward)
                {
                    GamePlayRootUI_CoinPassItem_Item itemUI = m_Item.GetInstance(true);
                    RectTransform rt = itemUI.gameObject.GetComponent<RectTransform>();
                    LDCommonTools.DestoryChildren(rt);
                    LDUIPrefabTools.SetKnapsackItemToParentNode(rwd, rt);
                }
            }
        }

        private void RefreshProgressInfo()
        {
            m_Progress.text.SetTips(m_MissionCfg.Name);

            m_CoinPassMgr.GetCountInfo(out int curCount, out int maxCount);
            m_BattleNum.text.SetText(UiTools.Localize(69175, $"{curCount}/{maxCount}"));
        }

        private void RefreshUnlockInfo()
        {
            m_BtnNode.gameObject.SetActive(m_IsUnlock);
            m_Lock.gameObject.SetActive(!m_IsUnlock);
            if (!m_IsUnlock)
            {
                string tips = Global.gApp.gSystemMgr.GetSystemUnlockTips(LDSystemEnum.CoinPass);
                m_Lock_Condition_lable.text.SetText(tips);
            }
        }

        private void OnClick()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.CoinPass, 1);
            if (Global.gApp.gSystemMgr.gCoinPassMgr.IsUnlock(true))
            {
                Global.gApp.gSystemMgr.gCoinPassMgr.SendPassInfoReq();
            }
        }
    }
}