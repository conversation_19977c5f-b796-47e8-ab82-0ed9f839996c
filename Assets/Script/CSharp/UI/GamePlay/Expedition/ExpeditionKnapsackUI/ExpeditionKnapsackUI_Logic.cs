using System.Collections.Generic;

namespace LD
{
    public partial class ExpeditionKnapsackUI
    {
        private List<ExpeditionKnapsackUI_item> m_Items = new();

        public override void OnFreshUI()
        {
        }

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_Illustrated_BG.AddListener(OpenCollectWindow);
            RefreshView();
        }

        protected override void OnCloseImp()
        {
        }

        public void RefreshView()
        {
            m_item.CacheInstanceList();
            m_Items.Clear();
            List<LDNetExpeditionBox> allBox = Global.gApp.gSystemMgr.gExpeditionMgr.Data.Boxes;
            allBox.Sort((a, b) =>
            {
                var cfgA = ExpeditionBox.Data.Get(a.CfgId);
                var cfgb = ExpeditionBox.Data.Get(b.CfgId);
                return cfgA.order - cfgb.order;
            });
            for (int i = 0; i < allBox.Count; i++)
            {
                ExpeditionKnapsackUI_item itemUI = m_item.GetInstance(true);
                itemUI.RefreshItem(allBox[i]);
                m_Items.Add(itemUI);
            }

            m_Empty.gameObject.SetActive(allBox.Count <= 0);
        }

        private void OpenCollectWindow()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.ExpeditionCollectUI);
        }
    }
}