namespace LD
{
    public partial class ExpeditionCollectUI_list_item01
    {
        private KnapsackItem m_KnapsackItem;
        private LDCommonItem m_CommonItem;

        public void RefreshView(LDCommonItem item)
        {
            m_CommonItem = item;
            m_Unknown.AddListener(ItemClick);
            if (m_KnapsackItem == null)
            {
                m_KnapsackItem = m_SignleItem.gameObject.transform.Find("KnapsackItem")?.GetComponent<KnapsackItem>();
                m_KnapsackItem?.SetClickCallback(ItemClick);
            }

            int boxId = m_CommonItem.Id;
            bool historyHave = Global.gApp.gSystemMgr.gExpeditionMgr.Data.CheckBoxHistory(boxId);
            if (historyHave)
            {
                m_KnapsackItem?.gameObject.SetActive(true);
                m_Unknown.gameObject.SetActive(false);
                LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem, item);
            }
            else
            {
                m_KnapsackItem?.gameObject.SetActive(false);
                m_Unknown.gameObject.SetActive(true);
            }
        }

        private void ItemClick()
        {
            m_CommonItem.ShowExpeditionBoxDetail(null);
        }
    }
}