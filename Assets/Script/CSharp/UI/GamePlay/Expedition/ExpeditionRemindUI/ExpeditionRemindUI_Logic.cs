using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class ExpeditionRemindUI
    {
        private LDCreature m_Creature = new LDCreature();
        private LDNetExpeditionNode m_Node;
        private List<RectTransform_Container> m_PartParent = new();
        private List<RectTransform_Container> m_PartNodes = new();
        private List<KnapsackItem> m_PartItem = new();
        private List<GameObject> m_NodeEmpty = new();

        private CommonModelUI m_CommonModel;

        public override void OnFreshUI()
        {
        }

        protected override void OnInitImp()
        {
            Global.gApp.gLightCompt.gameObject.SetActive(true);
            m_CommonModel = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            m_Cancel_Button.AddListener(AbandonClick);
            m_Battle_Button.AddListener(ChallengeClick);
            m_btn_close.AddListener(TouchClose);
            m_PartNodes.Add(m_partIcon01);
            m_PartNodes.Add(m_partIcon02);
            m_PartNodes.Add(m_partIcon03);
            m_PartNodes.Add(m_partIcon04);
            m_PartParent.Add(m_part01);
            m_PartParent.Add(m_part02);
            m_PartParent.Add(m_part03);
            m_PartParent.Add(m_part04);
            m_NodeEmpty.Add(m_empty01.gameObject);
            m_NodeEmpty.Add(m_empty02.gameObject);
            m_NodeEmpty.Add(m_empty03.gameObject);
            m_NodeEmpty.Add(m_empty04.gameObject);

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Week_Reminder_Button.rectTransform.parent.parent.rectTransform());
        }

        protected override void OnCloseImp()
        {
            m_CommonModel?.Dispose();
        }

        public void InitView(LDNetExpeditionNode node)
        {
            m_Node = node;
            RefreshModelNode();
            RefreshDIYPart();
        }

        private void RefreshModelNode()
        {
            // LDCommonTools.DestoryChildren(m_Model.rectTransform);
            // if (m_Creature == null)
            // {
            //     m_Creature = new LDCreature();
            // }
            //
            // if (m_Creature.MechaNode == null)
            // {
            //     m_Creature.Init(m_Model.rectTransform);
            //     m_Creature.PlayUIIdle();
            // }
            GameObject obj = m_CommonModel.InitCreature(m_ModelRawImage.rawImage);
        }

        private void RefreshDIYPart()
        {
            LDDIYData lddiyData = Global.gApp.gSystemMgr.gDIYMgr.GetDIYData();
            MechaItem mechaItem = Mecha.Data.Get(lddiyData.BodyId);
            int maxWpn = mechaItem.weapon;
            for (int i = 0; i < m_PartParent.Count; i++)
            {
                m_PartParent[i].SetActive(i < maxWpn);
            }


            int nodeIndex = 0;
            for (int i = 0; i < lddiyData.Data.Count; i++)
            {
                DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(lddiyData.Data[i].PId);
                if (partCfgItem.pos == LDDIYPartItemType.PartWeapon)
                {
                    LDCommonItem commonItem = new LDCommonItem(LDCommonType.DIYPart, lddiyData.Data[i].PId, 0);
                    commonItem.SetSkinId(lddiyData.Data[i].SId);
                    commonItem.IgnoreSkin = lddiyData.Data[i].HSId > 0;
                    var itemUI = LDUIPrefabTools.GetKnapsackItemUI(commonItem, m_PartNodes[nodeIndex].rectTransform);
                    m_PartItem.Add(itemUI);
                    m_NodeEmpty[nodeIndex].SetActive(false);
                    nodeIndex++;
                }
            }

            for (int i = nodeIndex; i < 4; i++)
            {
                m_NodeEmpty[i].SetActive(true);
            }
        }

        private void ChallengeClick()
        {
            if (m_Week_Reminder_Button.toggle.isOn)
            {
                Global.gApp.gSystemMgr.gExpeditionMgr.SetDailyTipsWeedMark();
            }

            Global.gApp.gSystemMgr.gExpeditionMgr.SendExpeditionGetModelRequest(m_Node.NodeId);
            TouchClose();
        }

        private void AbandonClick()
        {
            TouchClose();
        }
    }
}