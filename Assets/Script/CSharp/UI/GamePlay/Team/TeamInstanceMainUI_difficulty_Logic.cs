using System;

namespace LD
{
    public partial class TeamInstanceMainUI_difficulty
    {
        private int m_CfgId;
        private Action<int, bool> m_Callback;

        public void RefreshUI(int cfgId, Action<int, bool> onClickTab)
        {
            m_CfgId = cfgId;
            // m_Cfg = cfg;

            var missionCfg = TeamInstanceMission.Data.Get(cfgId);

            bool isUnlock = Global.gApp.gSystemMgr.gTeamMgr.Data.CheckMissionIsUnlock(m_CfgId, Global.gApp.gSystemMgr.gRoleMgr.GetHistoryMaxPowerNum());
            m_Callback = onClickTab;
            m_defaultBg.button.AddListener(OnClick);

            m_difficultylock.gameObject.SetActive(!isUnlock);
            m_difficultylock.AddListener(OnLockClick);

            m_mission_name.text.SetText(missionCfg.difficultyNumber);
            m_default_name.text.SetText(missionCfg.difficultyNumber);
            m_lock_name.text.SetText(missionCfg.difficultyNumber);
        }

        public void SetSelect(int missionId)
        {
            m_defaultBg.gameObject.SetActive(missionId != m_CfgId);
            m_selectedBg.gameObject.SetActive(missionId == m_CfgId);
        }

        private void OnClick()
        {
            if (!Global.gApp.gSystemMgr.gTeamMgr.Data.RoomInfo.IsLeader())
            {
                Global.gApp.gToastMgr.ShowGameTips(66126);
                return;
            }

            bool isUnlock = Global.gApp.gSystemMgr.gTeamMgr.Data.CheckMissionIsUnlock(m_CfgId, Global.gApp.gSystemMgr.gRoleMgr.GetHistoryMaxPowerNum());
            if (!isUnlock)
            {
                Global.gApp.gToastMgr.ShowGameTips(66200);
                return;
            }

            var parter = Global.gApp.gSystemMgr.gTeamMgr.Data.RoomInfo.GetRoomRole(false);
            if (parter != null)
            {
                if (!Global.gApp.gSystemMgr.gTeamMgr.Data.CheckMissionIsUnlock(m_CfgId, parter.Role.BriefRole.Power))
                {
                    Global.gApp.gToastMgr.ShowGameTips(66201);
                    return;
                }
            }

            m_Callback?.Invoke(m_CfgId, true);
        }

        private void OnLockClick()
        {
            if (!Global.gApp.gSystemMgr.gTeamMgr.Data.RoomInfo.IsLeader())
            {
                return;
            }

            var cfg = TeamInstanceMission.Data.Get(m_CfgId);
            if (cfg != null)
            {
                Global.gApp.gToastMgr.ShowGameTips(66172, UiTools.FormateMoney(cfg.unlockPowerNum));
            }
        }
    }
}