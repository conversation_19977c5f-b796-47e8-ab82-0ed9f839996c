using UnityEngine;

namespace LD
{
    public partial class CSarenaMainUI_Top3
    {
        private LDRankRecordDTOItem m_Data;
        private CommonModelUI m_CommonModel;
        
        public void RefreshUI(LDRankRecordDTOItem data)
        {
            m_Info_Btn.AddListener(OnInfoBtn);
            m_Data = data;
            m_CommonModel = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            Canvas canvas = m_CommonModelUI.gameObject.GetComponentInParent<Canvas>();
            if (canvas != null)
            {
                m_CommonModelUI.rectTransform.SetParent(canvas.transform.parent, false);
            }

            RefreshBaseInfo();
        }

        private void RefreshBaseInfo()
        {
            m_top1.gameObject.SetActive(false);
            m_top2.gameObject.SetActive(false);
            m_top3.gameObject.SetActive(false);
            
            if (m_Data.Rank == 1)
            {
                m_top1.gameObject.SetActive(true);
                m_name1.text.SetText(m_Data.Name);
                m_rank_info1.text.SetText(m_Data.GetScoreStr());
                m_CommonModel.SetRootPosX(1000f);
            }
            else if (m_Data.Rank == 2)
            {
                m_top2.gameObject.SetActive(true);
                m_name2.text.SetText(m_Data.Name);
                
                m_rank_info2.text.SetText(m_Data.GetScoreStr());
                m_CommonModel.SetRootPosX(2000f);
            }
            else if (m_Data.Rank == 3)
            {
                m_top3.gameObject.SetActive(true);
                m_name3.text.SetText(m_Data.Name);
                m_rank_info3.text.SetText(m_Data.GetScoreStr());
                m_CommonModel.SetRootPosX(3000f);
            }

            if ( m_Data.Model != null)
            {
                m_CommonModel.InitDiyData(m_ModelImage.rawImage, m_Data.Model, 4);
            }
            m_CommonModelUI.gameObject.SetActive(m_Data.Model != null);
            m_Info_Btn.gameObject.SetActive(m_Data.Model != null);
        }
        
        public void OnInfoBtn()
        {
            Global.gApp.gSystemMgr.gRoleMgr.OtherRoleData.ShowViewOtherUI(m_Data.HeadInfo.PlayerId);
        }
    }
}