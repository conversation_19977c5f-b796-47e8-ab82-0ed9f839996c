using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PVETowerRankTipsUI
    {
        private LDNetPveTowerDataMgr m_TowerMgr;
        private int m_Rank;
        private int m_ChapterId;

        private float m_AniTime = 2f;

        protected override void OnInitImp()
        {
            m_TowerMgr = Global.gApp.gSystemMgr.gPveTowerDataMgr;
            m_Unlock_Button.button.AddListener(OnUnlock);
            m_LastUnlock_Button.button.AddListener(OnLastUnlock);
            m_Go_button.AddListener(OnGo);

            OnFreshUI();
        }

        public override void OnFreshUI()
        {
            m_Award.gameObject.SetActive(false);
            m_Attend.gameObject.SetActive(false);
            m_AniMask.gameObject.SetActive(false);

            m_Rank = m_TowerMgr.GetCurRank();
            m_ChapterId = m_TowerMgr.GetCurChapterId();

            int storageMaxPassId = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.PveTower_MaxPass, 0);
            if(m_Rank > 0 && m_TowerMgr.Data.LastFinishedPassId != storageMaxPassId) // 关卡扩展 最后完成关=存储的最大关  不再展示奖励
            {
                RefreshRewardNode();
            }
            else
            {
                RefreshAttendNode();
            }
            RefreshUnlockBtn();
        }

        public override void OnFreshUI(int val)
        {
            if(val == 1)
            {
                PlayUnlockAni();
            }
        }

        // 进排名  有奖励
        private void RefreshRewardNode()
        {
            m_Award.gameObject.SetActive(true);

            m_lv02.text.SetText(m_Rank);
            m_Tips.text.SetText(UiTools.Localize(69522, m_Rank));

            RefreshRankRewardList();
        }

        // 排名奖励
        private void RefreshRankRewardList()
        {
            m_Item.CacheInstanceList();
            TowerRankItem[] rankCfgs = TowerRank.Data.items;
            foreach (TowerRankItem cfg in rankCfgs)
            {
                if (cfg.orderID == m_ChapterId && m_Rank >= cfg.MaxRank && m_Rank <= cfg.MinRank)
                {
                    foreach (string rwd in cfg.RankItem)
                    {
                        PVETowerRankTipsUI_Item itemUI = m_Item.GetInstance(true);
                        LDCommonTools.DestoryChildren(itemUI.gameObject);
                        LDUIPrefabTools.GetKnapsackItemUI(new LDCommonItem(rwd), itemUI.rectTransform());
                    }
                    return;
                }
            }
        }

        // 仅通关
        private void RefreshAttendNode()
        {
            m_Attend.gameObject.SetActive(true);
        }

        // 解锁按钮
        private void RefreshUnlockBtn()
        {
            m_ChapterUnlockBtn.gameObject.SetActive(!m_TowerMgr.IsPassMax());
            m_LastUnlockBtn.gameObject.SetActive(m_TowerMgr.IsPassMax());
        }

        // 播放动画
        private void PlayUnlockAni()
        {
            m_AniMask.gameObject.SetActive(true);
            for (int i = 0; i < m_LoadingAniNode.rectTransform.childCount; i++)
            {
                m_LoadingAniNode.rectTransform.GetChild(i).gameObject.SetActive(false);
            }
            m_LoadingAniNode.rectTransform.GetChild((m_ChapterId - 1) % 3).gameObject.SetActive(true);
            
            m_Go_button.gameObject.SetActive(false);
            AddTimer(m_AniTime, 1, OnAniOver);
        }

        // 动画结束
        private void OnAniOver(float arg1, bool arg2)
        {
            //TouchClose();
            m_Go_button.gameObject.SetActive(true);
        }

        // 章节解锁
        private void OnUnlock()
        {
            m_TowerMgr.SendNextChapter();
        }

        // 通关
        private void OnLastUnlock()
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.PveTower_MaxPass, m_TowerMgr.GetMaxPassId());
            TouchClose();
        }

        // 跳过动画
        private void OnGo()
        {
            TouchClose();
        }

        protected override void OnCloseImp()
        {

        }
    }
}