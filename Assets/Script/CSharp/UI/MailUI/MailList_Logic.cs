using SuperScrollView;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class MailList
    {
        private LDNetMailDataMgr m_MailMgr;
        private LoopListView2 m_LoopListView;
        private List<LDNetMailDataDTOItem> m_MailDatas;

        protected override void OnInitImp()
        {
            m_MailMgr = Global.gApp.gSystemMgr.gMailMgr;
            m_del_btn.button.AddListener(OnDelAllRead);
            m_get_btn.button.AddListener(OnGetAll);
            m_btn_close.button.AddListener(TouchClose);

            m_LoopListView = m_List.gameObject.GetComponent<LoopListView2>();
            m_LoopListView.InitListView(-1, OnGetItemByIndex);
            OnFreshUI();
        }

        public override void OnFreshUI()
        {
            RefreshUI();
            m_LoopListView.SetListItemCount(m_MailDatas.Count);
            m_LoopListView.RefreshAllShownItem();
        }

        private void RefreshUI()
        {
            m_MailDatas = m_MailMgr.GetAllMail();

            bool hasMail = m_MailDatas.Count > 0;
            m_Empty.gameObject.SetActive(!hasMail);
            m_del_btn.gameObject.SetActive(hasMail);
            m_get_btn.gameObject.SetActive(hasMail);
        }

        private LoopListViewItem2 OnGetItemByIndex(LoopListView2 listView, int index)
        {
            if(m_MailDatas == null || m_MailDatas.Count <= index || index < 0)
            {
                return null; 
            }

            LoopListViewItem2 item = listView.NewListViewDefaultItem();
            MailList_Detail itemScript = item.GetComponent<MailList_Detail>();
            itemScript.RefreshUI(m_MailDatas[index], OnClickMailItem);
            return item;
        }

        private void OnClickMailItem(LDNetMailDataDTOItem mail)
        {
            if (mail.IsExpired()) // 过期
            {
                Global.gApp.gToastMgr.ShowGameTips(95043);
                OnFreshUI();
                return;
            }

            Global.gApp.gUiMgr.OpenUIAsync<MailContent>(LDUICfg.MailContent).SetLoadedCall(ui =>
            {
                ui?.RefreshUI(mail);
            });
        }

        private void OnDelAllRead()
        {
            m_MailMgr.SendDelAllReadMail();
        }

        private void OnGetAll()
        {
            m_MailMgr.SendGetMailRewardAll();
        }

        protected override void OnCloseImp()
        {

        }
    }

    public partial class MailList_Detail
    {
        private LDNetMailDataMgr m_MailMgr;
        private LDNetMailDataDTOItem m_Mail;
        //private MailItem m_MailCfg;
        private Action<LDNetMailDataDTOItem> m_OnClick;
        private LoopListView2 m_LoopListView;
        public void RefreshUI(LDNetMailDataDTOItem mail, Action<LDNetMailDataDTOItem> cb)
        {
            m_MailMgr = Global.gApp.gSystemMgr.gMailMgr;
            m_Mail = mail;
            //m_MailCfg = Mail.Data.Get(mail.MailCfgId);
            m_OnClick = cb;
            m_Btn.button.AddListener(OnClick);

            RefreshBaseInfo();
            RefreshIconNode();
            RefreshRedNode();
            RefreshItemList();
        }

        // 信息
        private void RefreshBaseInfo()
        {
            // 标题
            m_MailTitle.text.SetText(m_MailMgr.GetTitle(m_Mail.MailId));
            // 过期时间
            m_MailExpire.text.SetText(Global.gApp.gGameData.GetExpireTimeTips2(m_Mail.ExpireTime));
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_MailExpire.rectTransform.parent as RectTransform);
            // 发送时间
            long leftTime = DateTimeUtil.GetServerTime() - m_Mail.CreateTime;
            m_MailTime.text.SetText(Global.gApp.gGameData.GetSimpleTimelongInCurLanguageOut2(leftTime));
        }

        // 图标
        private void RefreshIconNode()
        {
            bool hasItem = m_Mail.Attachments.Count > 0;
            bool isRead = m_Mail.State != LDMailState.UnRead;
            bool isReceived = m_Mail.State != LDMailState.Received;

            m_icon_read.gameObject.SetActive(!hasItem && isRead);
            m_icon_item_read.gameObject.SetActive(hasItem && isRead);
            m_icon_unread.gameObject.SetActive(!hasItem && !isRead);
            m_icon_item_unread.gameObject.SetActive(hasItem && !isRead);
            m_read_gift.gameObject.SetActive(hasItem && isReceived);
        }

        // 红点
        private void RefreshRedNode()
        {
            m_Red.gameObject.GetComponent<RedTips>().FreshState(m_MailMgr.GetRedTipsState(m_Mail.MailId));
        }

        private void RefreshItemList()
        {
            if(m_LoopListView == null)
            {
                m_LoopListView = m_List.gameObject.GetComponent<LoopListView2>();
                m_LoopListView.InitListView(m_Mail.Attachments.Count, OnGetItemByIndex);
            }
            m_LoopListView.SetListItemCount(m_Mail.Attachments.Count);
            m_LoopListView.RefreshAllShownItem();
            m_System_Title.gameObject.SetActive(m_Mail.Attachments.Count <= 0);
        }

        private LoopListViewItem2 OnGetItemByIndex(LoopListView2 listView, int index)
        {
            if (m_Mail.Attachments == null || m_Mail.Attachments.Count <= index || index < 0)
            {
                return null;
            }

            LoopListViewItem2 item = listView.NewListViewDefaultItem();
            MailList_Detail_Item itemScript = item.GetComponent<MailList_Detail_Item>();
            RectTransform rt = itemScript.scaleNode.rectTransform;
            LDCommonTools.DestoryChildren(rt);
            KnapsackItem knaItem = LDUIPrefabTools.GetKnapsackItemUI(m_Mail.Attachments[index], rt);
            knaItem.SetShowDetail(false);
            knaItem.ShowMask(m_Mail.State == LDMailState.Received);
            return item;
        }

        private void OnClick()
        {
            m_OnClick?.Invoke(m_Mail);
        }
    }
}