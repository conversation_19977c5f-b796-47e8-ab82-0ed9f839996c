using System;
using System.Collections.Generic;
using LD.Protocol;
using UnityEngine;
using UnityEngine.UI;
using Vector3 = UnityEngine.Vector3;

namespace LD
{
    public class CombatTargetMainFun
    {
        public const int Task = 1;
        public const int Shop = 2;

        public const int Rank = 3;
        // public const int RankReward = 4;
    }

    public partial class CombatTarget_Main
    {
        private int m_CurSelectViewId = -1;

        // private LDBaseActivityChildUI m_RankTopComp;
        private LDBaseActivityChildUI m_TaskComp;
        private LDBaseActivityChildUI m_ShopComp;
        private LDBaseActivityChildUI m_RankComp;
        private LDBaseActivityChildUI m_RankRewardComp;

        private int m_ActivityId;
        private LDNetActivityInfo m_ActivityInfo;
        private LDNetRankDTOItem m_RankData;

        private bool m_InitTop = false;
        private bool m_InitShop = false;

        private List<RectTransform_Container> m_TopRewardParent = new();
        private List<RectTransform_Text_Container> m_TopScores = new();
        private List<DiyActivityTasksItem> m_TopScoreItems = new();

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_defaultTask_btn.AddListener(OnTaskClick);
            m_defaultShop_btn.AddListener(OnShopClick);
            m_defaultRank_btn.AddListener(OnRankClick);
            // m_defaultRankReward_btn.AddListener(OnRankRewardClick);

            m_TopRewardParent.Add(m_Last_Reward_Item1);
            m_TopRewardParent.Add(m_Last_Reward_Item2);
            m_TopRewardParent.Add(m_Last_Reward_Item3);
            m_TopRewardParent.Add(m_Last_Reward_Item4);

            m_TopScores.Add(m_scoreLast1);
            m_TopScores.Add(m_scoreLast2);
            m_TopScores.Add(m_scoreLast3);
            m_TopScores.Add(m_scoreLast4);
            // InitPanel();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(int val)
        {
            RefreshCenterView(false);
            RefreshTaskTop();
            RefreshTabRed();
        }

        public override void OnFreshUI(LDUIDataBase data)
        {
            m_RankData = (data as LDRankUIData).RankData;

            if (m_CurSelectViewId == CombatTargetMainFun.Rank)
            {
                ShowRank();
            }

            RefreshTabRed();
        }

        public void RefreshView(int activityId)
        {
            m_ActivityId = activityId;
            m_ActivityInfo = Global.gApp.gSystemMgr.gActivityMgr.Data.GetActivityInfo(m_ActivityId);
            InitPanel();
            Global.gApp.gSystemMgr.gRankMgr.SendCombatTargetActivityRankReq(LDRankEnum.CombatTargetActivityRank);

            RefreshTime();
            OnTaskClick();
            RefreshTabRed();
        }

        private void RefreshTime()
        {
            if (m_ActivityInfo == null)
                return;
            //倒计时
            long endTime = m_ActivityInfo.EndTime;
            long curTime = DateTimeUtil.GetServerTime();
            if (curTime < endTime)
            {
                m_Time_Txt.gameObject.SetActive(true);
                int offset = (int)(endTime - curTime);
                RefreshTimeText(endTime - curTime);
                AddTimer(1, offset, TimerCall);
            }
            else
            {
                m_Time_Txt.gameObject.SetActive(false);
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Time_Txt.gameObject.transform.parent.rectTransform());
        }

        private void TimerCall(float a, bool b)
        {
            long endTime = m_ActivityInfo.EndTime;
            long curTime = DateTimeUtil.GetServerTime();
            long mills = endTime - curTime;
            if (mills <= 0)
            {
                TouchClose();
            }

            RefreshTimeText(mills);
        }

        private void RefreshTimeText(long mills)
        {
            TimeSpan timeSpan = TimeSpan.FromMilliseconds((long)mills);
            string timeStr = "";
            if (timeSpan.Days > 0)
            {
                timeStr = UiTools.Localize(91162, timeSpan.Days, timeSpan.ToString(@"hh\:mm\:ss"));
            }
            else
            {
                timeStr = timeSpan.ToString(@"hh\:mm\:ss");
            }

            m_Time_Txt.text.SetText(timeStr);
        }


        private void RefreshTaskTop()
        {
            if (m_TopScoreItems.Count == 0)
            {
                GetScoreItems();
            }

            int dis = 0;
            for (int i = 0; i < m_TopScoreItems.Count; i++)
            {
                var cfg = m_TopScoreItems[i];

                var lines = LDCommonTools.Split(cfg.type);
                int score = LDParseTools.IntParse(lines[1]);
                dis += score;
                m_TopScores[i].text.SetText(UiTools.FormateMoney(score));
                var rewardParent = m_TopRewardParent[i];
                LDCommonTools.DestoryChildren(rewardParent.rectTransform);
                LDCommonItem reward = new LDCommonItem(cfg.Reward[0]);
                var knapsack = LDUIPrefabTools.GetKnapsackItemUI(rewardParent.rectTransform);
                LDUIPrefabTools.InitKnapsackItem(knapsack, reward);


                var taskInfo = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetDiyActivityTaskInfo(cfg.id);
                if (taskInfo == null) continue;

                if (taskInfo.Status == DiyActivityTaskStatus.Finish.GetHashCode())
                {
                    knapsack.ShowCanGet(true);
                    knapsack.SetClickCallback(() => { Global.gApp.gSystemMgr.gCombatTargetMgr.SendReceiveFightPowerActivityReward(cfg.id); });
                    knapsack.ShowMask(false);
                }
                else if (taskInfo.Status == DiyActivityTaskStatus.Reward.GetHashCode())
                {
                    knapsack.ShowCanGet(false);
                    knapsack.SetClickCallback(null);
                    knapsack.ShowMask(true);
                }
                else
                {
                    knapsack.ShowMask(false);
                    knapsack.SetClickCallback(null);
                    knapsack.ShowCanGet(false);
                }
            }

            var lastCfg = m_TopScoreItems[^1];
            var taskInfoLast = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetDiyActivityTaskInfo(lastCfg.id);
            long pro = taskInfoLast?.Progress ?? 0;
            long curPro = Global.gApp.gSystemMgr.gTaskMgr.GetTaskProgress(lastCfg.type, pro);

            m_Progress_score.text.SetText(UiTools.FormateMoney(curPro, true));


            RefreshProgress(curPro);
        }

        private void RefreshProgress(long curPoint)
        {
            int beginIndex = 0;
            int beginDate = 0;
            int endIndex = 0;
            int endDate = 0;

            for (int i = 0; i < m_TopScoreItems.Count; i++)
            {
                var itemData = m_TopScoreItems[i];
                var lines = LDCommonTools.Split(itemData.type);
                int score = LDParseTools.IntParse(lines[1]);
                if (curPoint >= score)
                {
                    beginIndex = i + 1;
                    beginDate = score;
                }

                if (endIndex == 0)
                {
                    if (curPoint < score)
                    {
                        endIndex = i + 1;
                        endDate = score;
                    }
                }
            }

            if (beginIndex > 0 && endIndex == 0)
            {
                endIndex = m_TopScoreItems.Count + 1;
                var itemData = m_TopScoreItems[^1];
                var lines = LDCommonTools.Split(itemData.type);
                int score = LDParseTools.IntParse(lines[1]);
                endDate = score;
            }

            float amount = 0f;
            if (curPoint >= endDate)
            {
                amount = 1;
            }
            else
            {
                float beginValue = TotalProgress(beginIndex);
                float endValue = TotalProgress(endIndex);
                float progressDate = ((curPoint - beginDate) / (float)(endDate - beginDate)) * (endValue - beginValue);

                amount = beginValue + progressDate;
            }

            m_img_scorePro.image.fillAmount = amount;

            float fxX = m_img_scorePro.rectTransform.sizeDelta.x * amount;
            m_fxjdt.gameObject.transform.localPosition = new Vector3(fxX, 0, 0);
        }

        private float TotalProgress(int index)
        {
            switch (index)
            {
                case 0:
                    return 0f;
                case 1:
                    return 0.25f;
                case 2:
                    return 0.5f;
                case 3:
                    return 0.75f;
                default:
                    return 1f;
            }
        }


        private void GetScoreItems()
        {
            DiyActivityTasksItem[] items = DiyActivityTasks.Data.items;
            List<DiyActivityTasksItem> temp = new List<DiyActivityTasksItem>();
            for (int i = 0; i < items.Length; i++)
            {
                DiyActivityTasksItem cfg = items[i];
                if (cfg.activityID != m_ActivityId)
                    continue;

                var lines = LDCommonTools.Split(cfg.type);
                if (lines.Length > 0 && lines[0] == "40")
                {
                    temp.Add(cfg);
                }
            }

            m_TopScoreItems.AddRange(temp);
        }

        private void InitPanel()
        {
            // InitRankTop();
            InitTaskPanel();
            InitShopPanel();
            InitRankPanel();
            // InitRankReward();
        }

        private void InitRankTop()
        {
            // if (m_InitTop)
            //     return;
            // if (m_RankTopComp == null)
            // {
            //     var panel = RankTopThreeUI.gameObject.GetComponent<RankTopThreeUI>();
            //     panel.RegEvent(true); // 注册事件
            //     m_RankTopComp = panel;
            // }
            //
            // m_RankTopComp.Init(new RankUIData() { m_RankData = m_RankData }, this);
            // m_InitTop = true;
        }

        private void InitTaskPanel()
        {
            DiyActivityTasksItem[] items = DiyActivityTasks.Data.items;
            List<DiyActivityTasksItem> temp = new List<DiyActivityTasksItem>();
            for (int i = 0; i < items.Length; i++)
            {
                DiyActivityTasksItem cfg = items[i];
                if (cfg.activityID != m_ActivityId)
                    continue;

                var lines = LDCommonTools.Split(cfg.type);
                if (lines.Length > 0 && lines[0] == "41")
                {
                    temp.Add(cfg);
                }
            }

            if (m_TaskComp == null)
            {
                var panel = ActivityTaskUI.gameObject.GetComponent<ActivityTaskUI>();
                panel.RegEvent(true); // 注册事件
                m_TaskComp = panel;
            }

            m_TaskComp.Init(new ActivityTaskUIData() { m_ActivityId = m_ActivityId, m_ShowItemCfg = temp.ToArray() }, this);
        }

        private void InitShopPanel()
        {
            if (m_InitShop)
                return;
            if (m_ShopComp == null)
            {
                var panel = ActivityShopUI.gameObject.GetComponent<ActivityShopUI>();
                panel.RegEvent(true); // 注册事件
                m_ShopComp = panel;
            }

            m_ShopComp.Init(new ActivityShopUIData() { m_ActivityId = this.m_ActivityId }, this);
            m_InitShop = true;
        }

        private void InitRankPanel()
        {
            if (m_RankComp == null)
            {
                var panel = RankDownInfoUI.gameObject.GetComponent<RankDownInfoUI>();
                panel.RegEvent(true); // 注册事件
                m_RankComp = panel;
            }

            m_RankComp.Init(new RankUIData() { m_RankData = this.m_RankData }, this);
            (m_RankComp as RankDownInfoUI)?.SetActivityId(m_ActivityId);
        }

        private void InitRankReward()
        {
            if (m_RankRewardComp == null)
            {
                var panel = ActivityCommonUI.CreatePanel(RankRewardsPreviewUI.RankRewardsPreviewUIPath, m_Interface.rectTransform);
                panel.RegEvent(true); // 注册事件
                m_RankRewardComp = panel;
            }

            m_RankRewardComp.Init(new RankRewardsPreviewData() { m_RankData = this.m_RankData, m_ActivityId = this.m_ActivityId }, this);
        }

        private void RefreshBtns()
        {
            m_defaultTask_btn.gameObject.SetActive(m_CurSelectViewId != CombatTargetMainFun.Task);
            m_selectedTask_btn.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.Task);

            m_defaultShop_btn.gameObject.SetActive(m_CurSelectViewId != CombatTargetMainFun.Shop);
            m_selectedShop_btn.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.Shop);

            m_defaultRank_btn.gameObject.SetActive(m_CurSelectViewId != CombatTargetMainFun.Rank);
            m_selectedRank_btn.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.Rank);

            // m_defaultRankReward_btn.gameObject.SetActive(m_CurSelectViewId != CombatTargetMainFun.RankReward);
            // m_selectedRankReward_btn.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.RankReward);
        }


        private void RefreshCenterView(bool needAnim)
        {
            m_TaskComp.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.Task);
            m_ActivityShopUI.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.Shop);
            m_RankComp.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.Rank);
            // m_RankRewardComp.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.RankReward);
            // m_RankTopThreeUI.gameObject.SetActive(m_CurSelectViewId != CombatTargetMainFun.Task && m_CurSelectViewId != CombatTargetMainFun.Shop);

            m_BG.gameObject.SetActive(m_CurSelectViewId == CombatTargetMainFun.Task);
            switch (m_CurSelectViewId)
            {
                case CombatTargetMainFun.Task:
                    ShowTask(needAnim);
                    break;
                case CombatTargetMainFun.Shop:
                    ShowShop(needAnim);
                    break;
                case CombatTargetMainFun.Rank:
                    ShowRank();
                    break;
                // case CombatTargetMainFun.RankReward:
                //     ShowRankRewards();
                //     break;
            }
        }

        private void OnTaskClick()
        {
            m_CurSelectViewId = CombatTargetMainFun.Task;
            RefreshBtns();
            RefreshCenterView(true);
        }

        private void OnShopClick()
        {
            m_CurSelectViewId = CombatTargetMainFun.Shop;
            RefreshBtns();
            RefreshCenterView(true);
        }

        private void OnRankClick()
        {
            m_CurSelectViewId = CombatTargetMainFun.Rank;
            RefreshBtns();
            RefreshCenterView(true);
        }

        private void OnRankRewardClick()
        {
            // m_CurSelectViewId = CombatTargetMainFun.RankReward;
            RefreshBtns();
            RefreshCenterView(true);
        }

        private void ShowTask(bool needanim)
        {
            m_TaskComp.RefreshUI(needanim);
            RefreshTaskTop();
        }

        private void ShowShop(bool needanim)
        {
            InitShopPanel();
            m_ShopComp.RefreshUI(needanim);
            // m_RankTopComp.RefreshUI();
        }

        private void ShowRank()
        {
            InitRankTop();
            m_RankComp.RefreshUI(new RankUIData() { m_RankData = this.m_RankData });
            // m_RankTopComp.RefreshUI(new RankUIData() { m_RankData = m_RankData });
        }

        private void ShowRankRewards()
        {
            m_RankRewardComp.RefreshUI(new RankRewardsPreviewData() { m_RankData = this.m_RankData, m_ActivityId = this.m_ActivityId });
            // m_RankTopComp.RefreshUI();
        }

        private void RefreshTabRed()
        {
            if (m_ActivityId <= 0)
                return;
            LDRedTipsState taskRed = Global.gApp.gSystemMgr.gDiyActivityMgr.CheckTaskRed(m_ActivityId);
            m_TaskRedTips.gameObject.SetActive(taskRed != LDRedTipsState.None);
            LDRedTipsState shopRed = Global.gApp.gSystemMgr.gDiyActivityMgr.CheckShopRed(m_ActivityId);
            m_ShopRedTips.gameObject.SetActive(shopRed != LDRedTipsState.None);
        }
    }
}