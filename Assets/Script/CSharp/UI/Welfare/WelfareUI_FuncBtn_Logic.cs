namespace LD
{
    public partial class WelfareUI_WelfareFuncBtn
    {
        public LDSystemEnum FunctionEnum;

        public void RefreshUI(int moduleId)
        {
            FunctionEnum = (LDSystemEnum)moduleId;
            m_Uncheck_Btn.AddListener(OnClick);

            ModuleOpenItem item = ModuleOpen.Data.Get(moduleId);

            m_Check_Btn_txt.text.SetTips(item.name);

            GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(GetGlobalCfgId());
            if (globalCfgItem != null)
            {
                LoadSprite(m_Check_Dec.image, globalCfgItem.valueStringarray[0]);
                LoadSprite(m_Uncheck_Dec.image, globalCfgItem.valueStringarray[1]);
            }
        }

        public void SetSelectState(bool isSelect)
        {
            m_Uncheck_Btn.gameObject.SetActive(!isSelect);
            m_Check_Btn.gameObject.SetActive(isSelect);
        }

        private void OnClick()
        {
        }

        private int GetGlobalCfgId()
        {
            switch (FunctionEnum)
            {
                case LDSystemEnum.DailyAward:
                    return 80025;
                default:
                    return 80025;
            }
        }
    }
}