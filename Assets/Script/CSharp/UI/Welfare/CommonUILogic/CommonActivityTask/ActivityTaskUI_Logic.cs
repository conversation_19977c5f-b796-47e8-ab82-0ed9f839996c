using System.Collections.Generic;
using LD.Protocol;
using SuperScrollView;
using UnityEngine;

namespace LD
{
    public class ActivityTaskUIData : LDUIDataBase
    {
        public int m_ActivityId;
        public DiyActivityTasksItem[] m_ShowItemCfg = DiyActivityTasks.Data.items;
    }

    public partial class ActivityTaskUI
    {
        public static readonly string ActivityTaskUIPath = "PrefabsN/UI/Welfare/CombatTarget/ActivityTaskUI";

        private ActivityTaskUIData m_ShowUIData;
        private List<ActivityTaskUI_TaskList> m_TaskItems = new();

        private float m_ShowItemTimeOffset = 0.1f;
        private List<int> m_WaitShowItemTimer = new();
        private LoopListView2 m_LoopListView; 
        private List<DiyActivityTasksItem> m_ListData = new();
        private bool m_Anim = true;
        private int m_ShowAnimCount = 5;

        public override void Init(LDUIDataBase baseData, LDBaseUI parentUI)
        {
            base.Init(baseData, parentUI);
            m_ShowUIData = baseData as ActivityTaskUIData;
        }

        public override void RefreshUI(bool needAnim)
        {
            RefreshView(needAnim);
        }


        public void RefreshView(bool needAnim)
        {
            m_TaskList.CacheInstanceList();
            m_TaskItems.Clear();

            foreach (int i in m_WaitShowItemTimer)
            {
                m_ParentUI.RemoveTimer(i);
            }

            m_WaitShowItemTimer.Clear();

            DiyActivityTasksItem[] items = m_ShowUIData.m_ShowItemCfg;
            List<DiyActivityTasksItem> temp = new List<DiyActivityTasksItem>();
            List<DiyActivityTasksItem> tempSelected = new List<DiyActivityTasksItem>();
            List<DiyActivityTasksItem> tempWeiwancheng = new List<DiyActivityTasksItem>();
            for (int i = 0; i < items.Length; i++)
            {
                DiyActivityTasksItem cfg = items[i];

                var info = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetDiyActivityTaskInfo(cfg.id);
                if (info == null)
                {
                    tempWeiwancheng.Add(cfg);
                    continue;
                }

                if (info.Status == DiyActivityTaskStatus.Finish.GetHashCode())
                {
                    temp.Add(cfg);
                }
                else if (info.Status == DiyActivityTaskStatus.Reward.GetHashCode())
                {
                    tempSelected.Add(cfg);
                }
                else
                {
                    tempWeiwancheng.Add(cfg);
                }
            }

            temp.AddRange(tempWeiwancheng);
            temp.AddRange(tempSelected);

            m_ListData = temp;
            m_Anim = true;
            if (m_LoopListView == null)
            {
                m_LoopListView = m_List.gameObject.GetComponent<LoopListView2>();
                m_LoopListView.InitListView(m_ListData.Count, OnGetItemByIndex);
            }
            else
            {
                Vector2 currentPosition = m_TaskContent.rectTransform.anchoredPosition;
                currentPosition.y = 0;
                m_TaskContent.rectTransform.anchoredPosition = currentPosition;
                m_LoopListView.RefreshAllShownItem();
            }

            // for (int i = 0; i < temp.Count; i++)
            // {
            //     DiyActivityTasksItem cfg = temp[i];
            //     var itemUI = m_TaskList.GetInstance(true);
            //     itemUI.RefreshItem(m_ShowUIData.m_ActivityId, cfg);
            //     m_TaskItems.Add(itemUI);
            //     itemUI.SetAnimEnable(needAnim);
            //     if (needAnim)
            //     {
            //         itemUI.gameObject.SetActive(false);
            //         int timerId = m_ParentUI.AddTimer(i * m_ShowItemTimeOffset, 1, (a, b) => { itemUI.gameObject.SetActive(true); });
            //         m_WaitShowItemTimer.Add(timerId);
            //     }
            // }
        }

        private LoopListViewItem2 OnGetItemByIndex(LoopListView2 listView, int index)
        {
            if (m_ListData.Count <= index || index < 0)
            {
                return null;
            }

            LoopListViewItem2 item = listView.NewListViewDefaultItem();
            var itemScript = item.GetComponent<ActivityTaskUI_TaskList>();
            var data = m_ListData[index];
            itemScript.RefreshItem(m_ShowUIData.m_ActivityId, data);

            itemScript.SetAnimEnable(m_Anim && index < m_ShowAnimCount);
            if (m_Anim && index < m_ShowAnimCount)
            {
                itemScript.gameObject.SetActive(false);
                int timerId = m_ParentUI.AddTimer(index * m_ShowItemTimeOffset, 1, (a, b) => { itemScript.gameObject.SetActive(true); });
                m_WaitShowItemTimer.Add(timerId);
            }

            if (index >= m_ShowAnimCount)
            {
                m_Anim = false;
            }

            return item;
        }
    }
}