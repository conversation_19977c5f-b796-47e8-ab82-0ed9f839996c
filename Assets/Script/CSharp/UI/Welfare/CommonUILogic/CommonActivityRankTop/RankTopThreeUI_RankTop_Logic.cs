using System.Collections;
using UnityEngine;

namespace LD
{
    public partial class RankTopThreeUI_RankTop
    {
        private bool m_IsNull = true;
        private LDRoleHeadInfo m_HeadInfo;

        private LDRankRecordDTOItem m_RankData;

        public void RefreshUI(LDRankRecordDTOItem rankData, int index)
        {
            m_RankData = rankData;
            m_info_btn.AddListener(OnViewInfo);
            StartCoroutine(RefreshUI(index));
        }

        IEnumerator RefreshUI(int index)
        {
            // 背景
            int order = index + 1;
            for (int i = 0; i < index; i++)
            {
                yield return 1;
            }

            m_RankTop1.gameObject.SetActive(order == 1);
            m_RankTop2.gameObject.SetActive(order == 2);
            m_RankTop3.gameObject.SetActive(order == 3);
            var rankData = m_RankData;
            m_IsNull = m_RankData == null;
            CommonModelUI commonModelUI = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            Canvas canvas = m_CommonModelUI.gameObject.GetComponentInParent<Canvas>();
            if (canvas != null)
            {
                m_CommonModelUI.rectTransform.SetParent(canvas.transform.parent, false);
            }

            if (order == 1)
            {
                if (!m_IsNull)
                {
                    m_role_name_Empty1.gameObject.SetActive(false);

                    // 头像
                    m_HeadInfo = rankData.HeadInfo;

                    // 名称
                    m_user_name1.gameObject.SetActive(true);
                    m_user_name1.text.SetText(rankData.Name);

                    // 进度
                    m_tips1.gameObject.SetActive(true);
                    m_rank_usernum_Top1.gameObject.SetActive(true);
                    m_rank_usernum_Top1.text.SetText(rankData.GetScoreStr());
                    m_role_name_Empty_top1.gameObject.SetActive(false);

                    // 模型
                    m_CommonModelUI.gameObject.SetActive(true);
                    m_ModelImage1.gameObject.SetActive(true);

                    commonModelUI.SetRootPosX(34000f);
                    commonModelUI.InitDiyData(m_ModelImage1.rawImage, rankData.Model, 4);
                }
                else
                {
                    m_CommonModelUI.gameObject.SetActive(false);
                    m_ModelImage1.gameObject.SetActive(false);

                    m_role_name_Empty1.gameObject.SetActive(true);
                    user_name1.gameObject.SetActive(false);
                    m_tips1.gameObject.SetActive(false);
                    m_rank_usernum_Top1.gameObject.SetActive(false);
                    m_role_name_Empty_top1.gameObject.SetActive(true);
                }

                LDUIPrefabTools.InitOtherRoleHead(m_RoleHead1.gameObject, m_HeadInfo);
            }

            if (order == 2)
            {
                if (!m_IsNull)
                {
                    m_role_name_Empty2.gameObject.SetActive(false);

                    // 头像
                    m_HeadInfo = rankData.HeadInfo;

                    // 名称
                    m_user_name2.gameObject.SetActive(true);
                    m_user_name2.text.SetText(rankData.Name);

                    // 进度
                    m_tips2.gameObject.SetActive(true);
                    m_rank_usernum_Top2.gameObject.SetActive(true);
                    m_rank_usernum_Top2.text.SetText(rankData.GetScoreStr());
                    m_role_name_Empty_top2.gameObject.SetActive(false);

                    // 模型
                    m_CommonModelUI.gameObject.SetActive(true);
                    m_ModelImage2.gameObject.SetActive(true);

                    commonModelUI.SetRootPosX(35000f);
                    commonModelUI.InitDiyData(m_ModelImage2.rawImage, rankData.Model, 4);
                }
                else
                {
                    m_CommonModelUI.gameObject.SetActive(false);
                    m_ModelImage2.gameObject.SetActive(false);

                    m_role_name_Empty2.gameObject.SetActive(true);
                    user_name2.gameObject.SetActive(false);
                    m_tips2.gameObject.SetActive(false);
                    m_rank_usernum_Top2.gameObject.SetActive(false);
                    m_role_name_Empty_top2.gameObject.SetActive(true);
                }

                LDUIPrefabTools.InitOtherRoleHead(m_RoleHead2.gameObject, m_HeadInfo);
            }

            if (order == 3)
            {
                if (!m_IsNull)
                {
                    m_role_name_Empty3.gameObject.SetActive(false);

                    // 头像
                    m_HeadInfo = rankData.HeadInfo;

                    // 名称
                    m_user_name3.gameObject.SetActive(true);
                    m_user_name3.text.SetText(rankData.Name);

                    // 进度
                    m_tips3.gameObject.SetActive(true);
                    m_rank_usernum_Top3.gameObject.SetActive(true);
                    m_rank_usernum_Top3.text.SetText(rankData.GetScoreStr());
                    m_role_name_Empty_top3.gameObject.SetActive(false);


                    // 模型
                    m_CommonModelUI.gameObject.SetActive(true);
                    m_ModelImage3.gameObject.SetActive(true);

                    commonModelUI.SetRootPosX(36000f);
                    commonModelUI.InitDiyData(m_ModelImage3.rawImage, rankData.Model, 4);
                }
                else
                {
                    m_CommonModelUI.gameObject.SetActive(false);
                    m_ModelImage3.gameObject.SetActive(false);

                    m_role_name_Empty3.gameObject.SetActive(true);
                    user_name3.gameObject.SetActive(false);
                    m_tips3.gameObject.SetActive(false);
                    m_rank_usernum_Top3.gameObject.SetActive(false);
                    m_role_name_Empty_top3.gameObject.SetActive(true);
                }

                LDUIPrefabTools.InitOtherRoleHead(RoleHead3.gameObject, m_HeadInfo);
            }
        }

        private void OnViewInfo()
        {
            if (!m_IsNull)
            {
                if (m_RankData.Id == Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId())
                {
                    return;
                }

                Global.gApp.gSystemMgr.gRoleMgr.OtherRoleData.ShowViewOtherUI(m_RankData.Id);
            }
        }
    }
}