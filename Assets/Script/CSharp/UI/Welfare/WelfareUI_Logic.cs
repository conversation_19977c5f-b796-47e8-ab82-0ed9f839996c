using System.Collections.Generic;

namespace LD
{
    public partial class WelfareUI
    {
        private List<WelfareUI_WelfareFuncBtn> m_WelfareFuncBtns = new List<WelfareUI_WelfareFuncBtn>();
        private LDSystemEnum m_CurSystem;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            InitToggleBtns();
        }

        protected override void OnCloseImp()
        {
            Global.gApp.gUiMgr.TryShowPerformanceUI();
        }

        public override void OnFreshUI()
        {
            
        }

        public override void OnFreshUI(int moduleId)
        {
            if (moduleId == 0)
            {
                moduleId = LDSystemEnum.DailyAward.GetHashCode();
            }

            ChangeTab(moduleId);
        }

        // 页签
        private void InitToggleBtns()
        {
            m_WelfareFuncBtn.CacheInstanceList();
            m_WelfareFuncBtns.Clear();

            bool isOpen = Global.gApp.gSystemMgr.IsSystemUnlock(LDSystemEnum.DailyAward);
            // if (isOpen)
            {
                WelfareUI_WelfareFuncBtn dailyAwardBtn = m_WelfareFuncBtn.GetInstance(true);
                dailyAwardBtn.RefreshUI(LDSystemEnum.DailyAward.GetHashCode());
                m_WelfareFuncBtns.Add(dailyAwardBtn);
            }
        }

        //页签
        private void RefreshToggleBtns()
        {
            foreach (WelfareUI_WelfareFuncBtn funcBtn in m_WelfareFuncBtns)
            {
                funcBtn.SetSelectState(funcBtn.FunctionEnum == m_CurSystem);
            }
        }

        //切换页签
        private void ChangeTab(int moduleId)
        {
            m_CurSystem = (LDSystemEnum)moduleId;
            RefreshToggleBtns();
            ChangeUIView();
            switch (m_CurSystem)
            {
                case LDSystemEnum.DailyAward:
                    RefreshDailyAwardUI();
                    break;
            }
        }
        //切换UI组件显示
        private void ChangeUIView()
        {
            m_node_dailyAward.SetActive(m_CurSystem == LDSystemEnum.DailyAward);
        }
    }
}