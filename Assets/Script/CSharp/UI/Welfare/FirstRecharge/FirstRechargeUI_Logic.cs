using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class FirstRechargeUI
    {
        private int m_ShowId;
        private List<FirstRechargeUI_item> m_Items = new();
        private CommonModelUI m_CommonModel;

        private LDPaymentBtnUI m_PaymentBtn;

        protected override void OnInitImp()
        {
            m_CommonModel = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            m_Mecha_Name_View_ClickBtn.AddListener(OnDetailClick);

            if (m_PaymentBtn == null)
                m_PaymentBtn = m_ReCharge_btn.gameObject.AddComponent<LDPaymentBtnUI>();
            m_PaymentBtn.InitBtn(m_ReCharge_btn.button, this, OnBuyClick);
            RefreshView();
        }

        protected override void OnCloseImp()
        {
            Global.gApp.gUiMgr.TryShowPerformanceUI();
            m_CommonModel?.Dispose();
        }

        public override void OnFreshUI()
        {
            RefreshView();
        }

        public override void OnFreshUI(int val)
        {
            TouchClose();
        }

        private void RefreshView()
        {
            m_ShowId = Global.gApp.gSystemMgr.gFirstRechargeMgr.GetShowId();
            if (m_ShowId == -1)
            {
                TouchClose();
                return;
            }

            m_fx_light_jiantou.gameObject.SetActive(m_ShowId > 1);
            if (m_ShowId == 1)
            {
                PlayUIAudioClip(AudioConfig.UI_FallToGround);
            }

            FirstRechargeItem cfg = FirstRecharge.Data.Get(m_ShowId);
            RefreshView(cfg);
        }

        private void RefreshView(FirstRechargeItem item)
        {
            LoadSprite(m_BG_LightEffect.image, item.EffectBackGround);
            LoadSprite(m_BG_Item.image, item.itemBackGround);

            m_Value_Num.text.SetTips(item.saleTxt);

            // string price = Global.gApp.gSdkMgr.gPaymentMgr.GetDisplayPrice(item.MallGoodsId);
            m_PaymentBtn.ShowPrice(item.MallGoodsId);
            // m_ReCharge_lable.text.SetText(price);

            m_item.CacheInstanceList();
            m_Items.Clear();
            for (int i = 0; i < item.reward.Length; i++)
            {
                FirstRechargeUI_item itemUI = m_item.GetInstance(true);
                itemUI.RefreshView(item.reward[i]);
                m_Items.Add(itemUI);
            }

            LDCommonTools.DestoryChildren(m_Model.rectTransform);
            // GameObject obj = Global.gApp.gResMgr.InstantiateObj("PrefabsN/DIYMecha/part_mecha_006_shouchong", ResSceneType.NormalRes, m_Model.rectTransform);
            m_Title01.text.SetTips(item.firstRechargeTxt);
            if (m_ShowId == 1)
            {
                GameObject obj = m_CommonModel.InitModel("PrefabsN/DIYMecha/part_mecha_006_shouchong", m_ModelImage.rawImage);
                GameObject pinzhi1 = obj.transform.Find("fx_ziquan")?.gameObject;
                GameObject pinzhi2 = obj.transform.Find("fx_chengquan")?.gameObject;
                GameObject pinzhi3 = obj.transform.Find("fx_hongquan")?.gameObject;
                pinzhi1?.SetActive(m_ShowId - 1 == 0);
                pinzhi2?.SetActive(m_ShowId - 1 == 1);
                pinzhi3?.SetActive(m_ShowId - 1 == 2);
                m_Mecha_Day01.gameObject.SetActive(false);
                m_Mecha_Day02.gameObject.SetActive(false);
                m_Item.gameObject.SetActive(false);

                m_ModelImage.gameObject.SetActive(true);
                LDCommonItem day1Item = new LDCommonItem(item.reward[0]);
                Mecha_Name_Txt.text.SetTips(day1Item.Name);
            }
            else
            {
                m_Mecha_Day01.gameObject.SetActive(false);
                m_Mecha_Day02.gameObject.SetActive(false);
                m_ModelImage.gameObject.SetActive(false);
                m_Item.gameObject.SetActive(true);

                var before = item.mechaId[0];
                var after = item.mechaId[1];

                LDCommonTools.DestoryChildren(m_item_After.rectTransform);
                LDCommonTools.DestoryChildren(m_item_Before.rectTransform);

                LDUIPrefabTools.GetKnapsackItemUI(new LDCommonItem(before), m_item_Before.rectTransform);
                LDUIPrefabTools.GetKnapsackItemUI(new LDCommonItem(after), m_item_After.rectTransform);

                m_Quality_Before.text.SetText(LDCommonTipsTools.GetQualityTips(LDParseTools.IntParse(item.mechaQualityName[0])));
                m_Quality_After.text.SetText(LDCommonTipsTools.GetQualityTips(LDParseTools.IntParse(item.mechaQualityName[1])));


                LDCommonTools.DestoryChildren(m_Day2_item.rectTransform);
                // LDUIPrefabTools.GetKnapsackItemUI(new LDCommonItem(item.mechaId), m_Day2_item.rectTransform);
            }

            m_AD_Txt.text.SetTips(item.advertisementTxt);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_AD.rectTransform);
        }

        private void OnBuyClick()
        {
            FirstRechargeItem cfg = FirstRecharge.Data.Get(m_ShowId);
            Global.gApp.gSystemMgr.gPaymentMgr.SendMallBuyItemRequest(cfg.MallGoodsId);
        }

        private void OnDetailClick()
        {
            FirstRechargeItem cfg = FirstRecharge.Data.Get(m_ShowId);
            LDCommonItem day1Item = new LDCommonItem(cfg.reward[0]);
            day1Item.ShowDetails();
        }
    }
}