using System;
using System.Collections.Generic;

namespace LD
{
    public class LDDeepExploreSaleInfoRefreshData : LDUIDataBase
    {
        public int Price;
        public int ItemId;
        public long PlayerId;
        public LDDeepExploreSaleInfoRefreshData(int itemId, int price, long playerId)
        {
            ItemId = itemId;
            Price = price;
            PlayerId = playerId;
        }
    }

    public class LDDeepExploreSaleInfoRunData : LDUIDataBase
    {
        public int ActivityId;
        public int ItemId;
        public readonly List<LDDeepExploreSellData> SaleInfo = new List<LDDeepExploreSellData>();

        public LDDeepExploreSaleInfoRunData(int activityId, int itemId, List<LDDeepExploreSellData> sellInfo)
        {
            ActivityId = activityId;
            ItemId = itemId;
            SaleInfo.Clear();
            SaleInfo.AddRange(sellInfo);
        }
    }

    public partial class DeepExploreSellUI
    {
        private int m_ActivityId;
        private int m_ItemId;
        private int m_BasePrice;

        private int m_CurSelectNum;
        private int m_ItemMaxNum;

        private LDDeepExploreSaleInfoRunData m_RunData;

        private List<DeepExploreSellUI_SellList> m_ItemUIs = new List<DeepExploreSellUI_SellList>();
        private List<LDDeepExploreSellData> m_OrderList = new List<LDDeepExploreSellData>();
        protected override void OnInitImp()
        {
            m_Add_Button.AddListener(OnClickAdd);
            m_Reduce_Button.AddListener(OnClickReduce);
            m_Max_Button.AddListener(OnClickMax);
            m_btn_close.AddListener(TouchClose);

            m_Input_ui.inputField.AddListener(OnInputValueChange);
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(int value)
        {
            if (value == 1)
            {
                TouchClose();
            }
        }

        public void OnSetData(int activityId, int itemId, int basePrice)
        {
            m_ActivityId = activityId;
            m_ItemId = itemId;
            m_BasePrice = basePrice;
            m_ItemMaxNum = (int)Global.gApp.gSystemMgr.gBagMgr.GetItemCount(m_ItemId);

            m_CurSelectNum = m_ItemMaxNum;
            SendGetSaleInfo();
        }

        private void SendGetSaleInfo()
        {
            Global.gApp.gSystemMgr.gCircleActivityMgr.SendDeepExplore_SaleInfo(m_ActivityId, m_ItemId);
        }

        public override void OnFreshUI(LDUIDataBase runData)
        {
            if (runData is LDDeepExploreSaleInfoRunData data)
            {
                m_RunData = data;
                InitUIInfo();

                return;
            }

            if (runData is LDDeepExploreSaleInfoRefreshData refreshData)
            {
                RefreshNewPrice(refreshData);
            }
        }

        private void RefreshNewPrice(LDDeepExploreSaleInfoRefreshData refreshData)
        {
            foreach (LDDeepExploreSellData sellData in m_RunData.SaleInfo)
            {
                if (sellData.roleInfo.PlayerId == refreshData.PlayerId)
                {
                    sellData.price = refreshData.Price;
                    break;
                }
            }
            RefreshSaleListInfo();
        }

        private void InitUIInfo()
        {
            InitTargetItemInfo();
            InitSaleListInfo();
            SetSelectNumInfo();
        }

        private void InitTargetItemInfo()
        {
            long bagNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(m_ItemId);
            LDCommonItem item = new LDCommonItem(LDCommonType.Item, m_ItemId, bagNum);
            LDCommonTools.DestoryChildren(m_ItemDec.gameObject);
            LDUIPrefabTools.GetKnapsackItemUI(item, m_ItemDec.rectTransform);

            m_OnePrice_Txt.text.SetTips(36185, m_BasePrice);
        }

        private void SetSelectNumInfo()
        {
            // m_Reduce_Button.gameObject.SetActive(m_CurSelectNum > 1);
            // m_Add_Button.gameObject.SetActive(m_CurSelectNum < m_ItemMaxNum);
            // m_Max_Button.gameObject.SetActive(m_CurSelectNum < m_ItemMaxNum);
            m_Input_ui.inputField.text = m_CurSelectNum.ToString();
            RefreshSaleListInfo();
        }

        private void OnInputValueChange(string inputTxt)
        {
            int inputNum = LDParseTools.IntParse(inputTxt);
            m_CurSelectNum = Math.Clamp(inputNum, 1, m_ItemMaxNum);

            SetSelectNumInfo();//todo 确认下 初次调用时机
        }

        private void OnClickAdd()
        {
            m_CurSelectNum++;
            SetSelectNumInfo();
        }

        private void OnClickReduce()
        {
            m_CurSelectNum--;
            SetSelectNumInfo();
        }

        private void OnClickMax()
        {
            m_CurSelectNum = m_ItemMaxNum;
            SetSelectNumInfo();
        }

        private void InitSaleListInfo()
        {
            m_SellList.CacheInstanceList();
            m_ItemUIs.Clear();

            List<LDDeepExploreSellData> unOrderList = new List<LDDeepExploreSellData>();

            m_OrderList.Clear();
            for (int idx = 0; idx < m_RunData.SaleInfo.Count; idx++)
            {
                LDDeepExploreSellData sellData = m_RunData.SaleInfo[idx];
                if (idx != 0)
                {
                    unOrderList.Add(sellData);
                }
                else
                {
                    m_OrderList.Add(sellData);
                }
            }

            unOrderList.Sort((a, b) => { return b.price.CompareTo(a.price); });
            m_OrderList.AddRange(unOrderList);
            foreach (LDDeepExploreSellData sellData in m_OrderList)
            {
                DeepExploreSellUI_SellList itemUI = m_SellList.GetInstance(true);
                itemUI.InitUIInfo(sellData, m_CurSelectNum, m_ItemId, m_ActivityId);
                m_ItemUIs.Add(itemUI);
            }
        }

        private void RefreshSaleListInfo()
        {
            for (int idx = 0; idx < m_ItemUIs.Count; idx++)
            {
                DeepExploreSellUI_SellList itemUI = m_ItemUIs[idx];
                LDDeepExploreSellData sellData = m_OrderList[idx];
                itemUI.RefreshUIInfo(sellData, m_CurSelectNum);
            }
        }

        protected override void OnCloseImp()
        {
            Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.DeepExploreShopUI, 2);
        }
    }

    public partial class DeepExploreSellUI_SellList
    {
        private int m_ActivityId;
        private int m_ItemId;
        private int m_SelectNum;
        private LDDeepExploreSellData m_SellData;
        private readonly Dictionary<int, string> m_QualityPath = new()
        {
            { 0, "UI/N_Activity_DeepExplore/DeepExplore_Friend_BG_White" },
            { 1, "UI/N_Activity_DeepExplore/DeepExplore_Friend_BG_Green" },
            { 2, "UI/N_Activity_DeepExplore/DeepExplore_Friend_BG_Blue" },
            { 3, "UI/N_Activity_DeepExplore/DeepExplore_Friend_BG_Purple" },
            { 4, "UI/N_Activity_DeepExplore/DeepExplore_Friend_BG_Orange" },
        };

        public void InitUIInfo(LDDeepExploreSellData sellData, int selectNum, int itemId, int activityId)
        {
            m_ItemId = itemId;
            m_ActivityId = activityId;
            m_SellData = sellData;
            m_SelectNum = selectNum;

            LDUIPrefabTools.InitOtherRoleHead(m_RoleHead.gameObject, sellData.roleInfo.RoleHeadInfo);
            m_Level_Txt.text.SetText(sellData.roleInfo.Level);
            m_List_PlayerName_Txt.text.SetText(sellData.roleInfo.Name);
            m_List_Sell_Txt.text.SetTips(36185, sellData.price);

            m_SellBtn.AddListener(OnClickSell);
            LoadSprite(m_Quality.image, m_QualityPath[sellData.quality]);

            DeepExploreItemItem saleItem = Global.gApp.gSystemMgr.gCircleActivityMgr.GetExploreSaleItemCfg(activityId, itemId);
            LDCommonItem itemSale = new LDCommonItem(saleItem.basicPrice[0]);

            LoadSprite(m_Currency_Dec.image, itemSale.Icon);
            m_priceNumDiamond.text.SetText(sellData.price * selectNum);
        }

        public void RefreshUIInfo(LDDeepExploreSellData sellData, int selectNum)
        {
            m_SelectNum = selectNum;
            m_priceNumDiamond.text.SetText(sellData.price * selectNum);
            m_List_Sell_Txt.text.SetTips(36185, sellData.price);
        }

        private void OnClickSell()
        {
            Global.gApp.gSystemMgr.gCircleActivityMgr.SendDeepExplore_SaleRequest(m_ActivityId, m_ItemId, m_SellData.roleInfo.PlayerId, m_SellData.price, m_SelectNum);
        }
    }
}