namespace LD
{
    public partial class MechaTreasureUI_itemNode
    {
        public MechaTreasureRoundItem NodeCfg => m_Cfg;

        private int m_ActivityId;
        private int m_CurRound;
        private int m_LoopTimes;  //服务器从0开始  客户端从1开始

        private MechaTreasureRoundItem m_Cfg;
        private KnapsackItem m_KnapsackItem;

        public void RefreshView(MechaTreasureRoundItem itemCfg, int curCircle, int activityId, int loopTimes)
        {
            m_Cfg = itemCfg;
            m_CurRound = curCircle;
            m_LoopTimes = loopTimes;

            m_ActivityId = activityId;

            LDCommonTools.DestoryChildren(m_itemScale.rectTransform);

            string useReward = m_LoopTimes == 1 ? m_Cfg.extraReward : m_Cfg.loopExtraReward;
            LDCommonItem item = new LDCommonItem(useReward);
            m_KnapsackItem = LDUIPrefabTools.GetKnapsackItemUI(m_itemScale.rectTransform);
            LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem, item);

            m_itemTarget.text.SetText(m_Cfg.floorsNum);

            RefreshReceiveState();
        }

        public bool GetIsGetReward()
        {
            bool isGet = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckMechaTreasureProgressRewardReceived(m_ActivityId, m_Cfg.id, m_LoopTimes - 1);
            return isGet;
        }

        public int RefreshReceiveState()
        {
            bool isSelect = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckMechaTreasureProgressRewardReceived(m_ActivityId, m_Cfg.id, m_LoopTimes - 1);
            if (m_CurRound >= m_Cfg.floorsNum)
            {
                if (isSelect)
                {
                    m_KnapsackItem.ShowMask(true);
                    m_KnapsackItem.ShowCanGet(false);
                    m_KnapsackItem.SetClickCallback(null);
                }
                else
                {
                    m_KnapsackItem.ShowMask(false);
                    m_KnapsackItem.ShowCanGet(true);
                    m_KnapsackItem.SetClickCallback(OnClickReceive);
                    return m_Cfg.floorsNum;
                }
            }
            else
            {
                m_KnapsackItem.ShowMask(false);
                m_KnapsackItem.ShowCanGet(false);
                m_KnapsackItem.SetClickCallback(null);
            }

            return -1;
        }

        private void OnClickReceive()
        {
            Global.gApp.gSystemMgr.gCircleActivityMgr.SendReceiveProgressReward_MechaTreasure(m_ActivityId, m_Cfg.id, m_LoopTimes - 1);
        }
    }
}