using System.Collections.Generic;
using UnityEngine.UI;

namespace LD
{
    public partial class TacticBattleDetailsUI
    {
        private LDNetTacticMgr m_TacticMgr;
        protected override void OnInitImp()
        {
            m_TacticMgr = Global.gApp.gSystemMgr.gTacticMgr;
            m_btn_close.AddListener(TouchClose);
            ShowEmptyCitiaoNode(false);
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }

        public void OnInit()
        {
            FreshCitiaoContent();
        }
        
        #region Citiao

        private void ShowEmptyCitiaoNode(bool empty)
        {
            m_citiaoIconView.gameObject.SetActive(!empty);
            m_citiaoInfoView.gameObject.SetActive(!empty);
            m_citiaoEmptyNode.gameObject.SetActive(empty); 
        }

        private void FreshCitiaoContent()
        {
            m_citiaoItem.CacheInstanceList();
            m_citiaoInfoItem_good.CacheInstanceList();
            m_citiaoInfoItem_elite.CacheInstanceList();
            m_citiaoInfoItem_normal.CacheInstanceList();
            
            List<LDFightCitiaoUIData> data = m_TacticMgr.LDFightCitiaoUIDataList;
            foreach (LDFightCitiaoUIData uiData in data)
            {
                if (uiData.CitiaoIds.Count > 0)
                {
                    TacticBattleDetailsUI_citiaoItem citiaoItem = m_citiaoItem.GetInstance(true);
                    citiaoItem.RefreshUI(uiData, OnCitiaoGroupSelect);    
                }
            }

            int itemCount = m_citiaoItem.mCachedList.Count;
            if (itemCount > 0)
            {
                OnCitiaoGroupSelect(m_citiaoItem.mCachedList[0]);
            }
            else
            {
                ShowEmptyCitiaoNode(true);
            }
        }

        private void OnCitiaoGroupSelect(TacticBattleDetailsUI_citiaoItem obj)
        {
            m_citiaoInfoItem_good.CacheInstanceList();
            m_citiaoInfoItem_elite.CacheInstanceList();
            m_citiaoInfoItem_normal.CacheInstanceList();
            
            foreach (TacticBattleDetailsUI_citiaoItem citiaoItem in m_citiaoItem.mCachedList)
            {
                citiaoItem.SetSelect(obj.UIData.GroupId);
            }

            foreach (int val in obj.UIData.CitiaoIds)
            {
                CitiaoItem citiaoItem = Citiao.Data.Get(val);

                if (citiaoItem.citiaoName == 0 || citiaoItem.citiaoDesc == 0)
                {
                    Global.LogError($"战术特训： {citiaoItem.id} 技能没有名称或描述");
                }
                
                if (citiaoItem.citiaoType == LDCiTiaoType.Normal)
                {
                    TacticBattleDetailsUI_citiaoInfoItem_normal item = m_citiaoInfoItem_normal.GetInstance(true);
                    item.txt_citiaoName_normal.text.SetTips(citiaoItem.citiaoName);
                    item.txt_citiaoDes_normal.text.SetTips(citiaoItem.citiaoDesc);
                }
                else if (citiaoItem.citiaoType == LDCiTiaoType.Power)
                {
                    TacticBattleDetailsUI_citiaoInfoItem_good item = m_citiaoInfoItem_good.GetInstance(true);
                    item.txt_citiaoName_good.text.SetTips(citiaoItem.citiaoName);
                    item.txt_citiaoDes_good.text.SetTips(citiaoItem.citiaoDesc);
                }
                else if (citiaoItem.citiaoType == LDCiTiaoType.EliteBook)
                {
                    TacticBattleDetailsUI_citiaoInfoItem_elite item = m_citiaoInfoItem_elite.GetInstance(true);
                    item.txt_citiaoName_elite.text.SetTips(citiaoItem.citiaoName);
                    item.txt_citiaoDes_elite.text.SetTips(citiaoItem.citiaoDesc);
                }
            }
            
            LayoutGroup[] layouts = m_citiaoInfoViewContent.rectTransform.GetComponentsInChildren<LayoutGroup>();
            foreach (LayoutGroup layout in layouts)
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(layout.rectTransform());
            }
        }
        
        #endregion
    }
}