namespace LD
{
    public partial class Tactic_MessageBoxUI
    {
        private LDNetTacticMgr m_TacticMgr;
        private LDNetActivityInfo m_ActivityInfo;
        private int m_ActivityId;

        protected override void OnInitImp()
        {
            m_TacticMgr = Global.gApp.gSystemMgr.gTacticMgr;
            m_ActivityInfo = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetActivityInfo(LDNetActivityType.TacticType);
            m_ActivityId = m_ActivityInfo.ActivityId;
            m_YIYongYou.gameObject.SetActive(false);
            m_WeiYongYou.gameObject.SetActive(false);
            m_Orange_btn_Confirm.AddListener(OnBtnGetReward);
            m_Close_Btn.AddListener(TouchClose);
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
            LDCommonItem mecha = new LDCommonItem(m_TacticMgr.GetTacticBaseCfg().mechaId);
            LDNetMechaDTOItem mechaDTOItem = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaData(mecha.MechaCfg.id,true);
            bool hasMehca = mechaDTOItem != null;
            m_Txt.text.SetTips(37009, UiTools.Localize( mecha.MechaCfg.name));
            m_itemNode.CacheInstanceList();
            foreach (string val in m_TacticMgr.GetTacticBaseCfg().rankItem)
            {
                LDCommonItem commonItem = new LDCommonItem(val);
                Tactic_MessageBoxUI_itemNode itemUI = m_itemNode.GetInstance(true);
                LDCommonTools.DestoryChildren(itemUI.itemScale.rectTransform);
                LDUIPrefabTools.GetKnapsackItemUI(commonItem, itemUI.itemScale.rectTransform);
            }
            m_YIYongYou.gameObject.SetActive(hasMehca);
            m_WeiYongYou.gameObject.SetActive(!hasMehca);
        }

        private void OnBtnGetReward()
        {
            m_TacticMgr.SendTacticActivityMechaRewardRequest(m_ActivityId);
        }
    }
}