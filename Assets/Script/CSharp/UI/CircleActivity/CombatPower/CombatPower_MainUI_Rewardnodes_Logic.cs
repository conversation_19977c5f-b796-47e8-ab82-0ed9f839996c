using System;
using LD.Protocol;
using Vector3 = UnityEngine.Vector3;

namespace LD
{
    public partial class CombatPower_MainUI_Rewardnodes
    {
        private NewCombatPowerTasksItem m_Cfg;
        private LDNetCombatPowerActivityTaskInfo m_TaskInfo;
        private RectTransform_Container m_Parent;
        private int m_IsCurrentNode;
        private Action<NewCombatPowerTasksItem, Vector3> m_ShowEggInfo;
        private int m_CurIndex;

        public void RefreshUI(NewCombatPowerTasksItem cfg, RectTransform_Container parent, int isCurrentNode, int index, Action<NewCombatPowerTasksItem, Vector3> clickEgg)
        {
            m_CurIndex = index;
            m_Cfg = cfg;
            m_Parent = parent;
            m_ShowEggInfo = clickEgg;
            m_TaskInfo = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.GetTaskInfo(m_Cfg.id);

            this.gameObject.transform.SetParent(m_Parent.gameObject.transform, false);
            this.gameObject.transform.localPosition = Vector3.zero;
            string[] pos = LDCommonTools.Split(m_Cfg.rewardCoordinate, ",");
            m_Parent.gameObject.transform.localPosition = new Vector3(LDParseTools.FloatParse(pos[0]), LDParseTools.FloatParse(pos[1]), 0);
            m_egg_icon.AddListener(OnClickEgg);
            m_receiveBtn.AddListener(OnClickSendReceive);
            m_item_icon.AddListener(OnClickItem);
            RefreshInfo(isCurrentNode);
        }

        public void RefreshInfo(int isCurrentNode)
        {
            m_IsCurrentNode = isCurrentNode;
            m_receive.gameObject.SetActive((m_TaskInfo != null && m_TaskInfo.Status == CombatPowerActivityTaskStatus.Reward.GetHashCode()) && m_CurIndex != m_IsCurrentNode);
            m_receiveBtn.gameObject.SetActive((m_TaskInfo != null && m_TaskInfo.Status == CombatPowerActivityTaskStatus.Finish.GetHashCode()));
            m_stage_2.gameObject.SetActive(m_Cfg.itemType == 1);
            m_stage_3.gameObject.SetActive(m_Cfg.itemType != 1);
            m_condition.gameObject.SetActive(m_CurIndex != m_IsCurrentNode);

            LDNetActivityInfo activityInfo = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.ActivityInfo;
            bool isOpen = activityInfo?.IsOpen() ?? false;
            m_fxdailinqu.gameObject.SetActive((m_TaskInfo != null && m_TaskInfo.Status == CombatPowerActivityTaskStatus.Finish.GetHashCode()) && isOpen);

            switch (m_Cfg.itemType)
            {
                case 2:
                    m_item_icon.gameObject.SetActive(false);
                    m_egg_icon.gameObject.SetActive(true && m_CurIndex != m_IsCurrentNode);
                    LoadSprite(m_egg_icon.image, m_Cfg.eggImage);
                    if (m_TaskInfo != null && m_TaskInfo.Status == CombatPowerActivityTaskStatus.Reward.GetHashCode())
                    {
                        m_egg_icon.image.color = LDCommonColorTools.GetColor("#636363");
                    }
                    else
                    {
                        m_egg_icon.image.color = LDCommonColorTools.GetColor("#ffffff");
                    }

                    break;
                case 1:
                case 3:
                    m_egg_icon.gameObject.SetActive(false);
                    LDCommonItem item = new LDCommonItem(m_Cfg.item);
                    m_item_icon.gameObject.SetActive(true && m_CurIndex != m_IsCurrentNode);
                    LoadSprite(m_item_icon.image, item.Icon);
                    m_item_num.text.SetText(item.Num);
                    if (m_TaskInfo != null && m_TaskInfo.Status == CombatPowerActivityTaskStatus.Reward.GetHashCode())
                    {
                        m_item_icon.image.color = LDCommonColorTools.GetColor("#636363");
                    }
                    else
                    {
                        m_item_icon.image.color = LDCommonColorTools.GetColor("#ffffff");
                    }

                    break;
            }

            long historyPower = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.HistoryPower;
            // long curPro = m_TaskInfo?.Progress ?? 0;
            long maxPro = Global.gApp.gSystemMgr.gTaskMgr.GetTaskMaxProgress(m_Cfg.taskType);
            long showPower = maxPro;
            m_combat_power.text.SetText(UiTools.FormateMoney(showPower));
        }


        private void OnClickEgg()
        {
            if (m_Cfg.itemType == 2)
            {
                m_ShowEggInfo?.Invoke(m_Cfg, m_eggInfoPos.gameObject.transform.position);
            }
        }

        private void OnClickItem()
        {
            if (m_Cfg.itemType != 2)
            {
                LDCommonItem item = new LDCommonItem(m_Cfg.item);
                item.ShowDetails();
            }
        }

        public bool GetIsFinish()
        {
            if (m_TaskInfo == null)
                return false;

            return m_TaskInfo.Status == CombatPowerActivityTaskStatus.Reward.GetHashCode();
        }

        public bool GetIsProgressing()
        {
            if (m_TaskInfo == null)
                return false;

            return m_TaskInfo.Status == CombatPowerActivityTaskStatus.None.GetHashCode();
        }

        public void OnClickSendReceive()
        {
            if (m_TaskInfo != null && m_TaskInfo.Status == CombatPowerActivityTaskStatus.Finish.GetHashCode())
            {
                if (m_CurIndex == m_IsCurrentNode + 1)
                {
                    if (m_Cfg.itemType == 2)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<CombatPower_egg>(LDUICfg.CombatPower_egg).SetLoadedCall(ui => { ui.InitData(m_Cfg); });
                    }
                    else
                    {
                        Global.gApp.gSystemMgr.gCombatPowerMgr.SendReceiveTaskReward(m_Cfg.id);
                    }
                }
                else
                {
                    Global.gApp.gToastMgr.ShowGameTips(91254);
                }
            }
        }

        public int GetShowStage()
        {
            return m_Cfg.stage;
        }

        public int GetTaskId()
        {
            return m_Cfg.id;
        }
    }
}