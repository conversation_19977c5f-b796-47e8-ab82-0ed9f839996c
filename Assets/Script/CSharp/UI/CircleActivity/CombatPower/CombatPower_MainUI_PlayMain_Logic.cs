using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using LD.Protocol;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Vector3 = UnityEngine.Vector3;

namespace LD
{
    public partial class CombatPower_MainUI
    {
        private List<RectTransform_Container> m_NodeParents = new List<RectTransform_Container>();
        private List<CombatPower_MainUI_Rewardnodes> m_NodeList = new();
        private bool m_UpdateTime = false;
        private double m_DistanceTime = 0;

        private GameObject m_PlayerObj;
        private ProgressBar m_ProgressBarComp;

        private Text m_HelpText;

        private void InitMain()
        {
            m_NodeParents.Clear();
            m_NodeParents.Add(m_point_2);
            m_NodeParents.Add(m_point_3);
            m_NodeParents.Add(m_point_4);
            m_NodeParents.Add(m_point_5);
            m_NodeParents.Add(m_point_6);
            m_NodeParents.Add(m_point_7);
            m_OreTipsCloseBtn.AddListener(HideEggInfo);
            m_ExchangeShop_Btn.AddListener(OnExchangeShopClick);
            m_PreviewGift.AddListener(OnClickGainGift);
            RefreshPreviewGiftBtn();

            m_itemIcon.AddListener(OnTaskCountBoxClick);
            AddEventTrigger(m_info_tip.gameObject, EventTriggerType.PointerDown, OnButtonDown);
            AddEventTrigger(m_info_tip.gameObject, EventTriggerType.PointerUp, OnButtonUp);
        }

        private void OnClickGainGift()
        {
            Global.gApp.gSystemMgr.gActivityPreviewMgr.OpenPreviewGiftUI(m_ActivityId, (int)ActivityPreviewType.OpenServerActivity);
        }

        private void RefreshPreviewGiftBtn()
        {
            bool isShowGainEntrance = Global.gApp.gSystemMgr.gActivityPreviewMgr.GetIsShowGainEntrance(m_ActivityId, (int)ActivityPreviewType.OpenServerActivity);
            m_PreviewGift.gameObject.SetActive(isShowGainEntrance);
        }

        private void OnButtonDown(BaseEventData data)
        {
            if (m_HelpText == null)
            {
                m_HelpText = m_helpNode.gameObject.transform.Find("img_help_bg/txt_helpInfo")?.GetComponent<Text>();
                if (m_HelpText != null)
                {
                    m_HelpText.SetTips(77249, UiTools.FormateMoney(Global.gApp.gSystemMgr.gCombatPowerMgr.Data.HistoryPower, true));
                }
            }

            m_helpNode.gameObject.SetActive(true);
        }

        private void OnButtonUp(BaseEventData data)
        {
            m_helpNode.gameObject.SetActive(false);
        }

        private void RefreshUI(bool needAnim)
        {
            CreateNodes(true);
            RefreshPlayer();
            RefreshTime();
            RefreshTopTask();
            RefreshShopRedState();
            LDNetActivityInfo activityInfo = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.ActivityInfo;
            if (activityInfo.IsOpen())
            {
                m_EventEnd.gameObject.SetActive(false);
            }
            else
            {
                m_EventEnd.gameObject.SetActive(true);
            }
        }

        private void RefreshShopRedState()
        {
            // var redTip = m_RedTips.gameObject.GetComponent<RedTips>();
            // redTip.FreshState(Global.gApp.gSystemMgr.gActivityShopMgr.GetRedState(m_ActivityId));
        }

        private void OnOtherUICloseImp()
        {
            int curIndex = GetCurrentNodeIndex();
            foreach (CombatPower_MainUI_Rewardnodes rewardnodes in m_NodeList)
            {
                rewardnodes.RefreshInfo(curIndex);
            }

            RefreshTopTask();
            RefreshPlayer();
            RefreshPreviewGiftBtn();
            bool check = CheckNeedChangeMap();
            if (check)
            {
                CreateNodes(false);
                m_pointNode.gameObject.SetActive(false);
                MoveToPlayerToFirst();
                PlayShowNode();
                m_pointNode.gameObject.SetActive(true);
            }
        }

        #region 节点相关

        private int GetCurrentNodeIndex()
        {
            List<NewCombatPowerTasksItem> showItem = Global.gApp.gSystemMgr.gCombatPowerMgr.GetShowTaskItems();
            int curIndex = -2;
            for (int i = 0; i < showItem.Count; i++)
            {
                NewCombatPowerTasksItem item = showItem[i];
                var taskInfo = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.GetTaskInfo(item.id);
                if (taskInfo == null)
                {
                    curIndex = i - 1;
                    break;
                }

                if (taskInfo.Status != CombatPowerActivityTaskStatus.Reward.GetHashCode())
                {
                    curIndex = i - 1;
                    break;
                }
                else if (taskInfo.Status == CombatPowerActivityTaskStatus.Reward.GetHashCode())
                {
                    curIndex = i;
                }
            }

            return curIndex;
        }

        /// <summary>
        /// 创建节点，创建出出来，先隐藏，然后播放动画
        /// </summary>
        private void CreateNodes(bool defaultActive)
        {
            m_Rewardnodes.CacheInstanceList();
            List<NewCombatPowerTasksItem> showItem = Global.gApp.gSystemMgr.gCombatPowerMgr.GetShowTaskItems();
            int curIndex = GetCurrentNodeIndex();


            m_NodeList.Clear();
            long curPlayerPower = 0;
            for (int i = 0; i < showItem.Count; i++)
            {
                NewCombatPowerTasksItem item = showItem[i];
                CombatPower_MainUI_Rewardnodes itemUI = m_Rewardnodes.GetInstance(true);
                RectTransform_Container parent = m_NodeParents[i];
                itemUI.RefreshUI(item, parent, curIndex, i, ShowEggInfo);


                m_NodeList.Add(itemUI);
                itemUI.gameObject.SetActive(defaultActive);
            }

            NewCombatPowerTasksItem firstItem = showItem[0];
            LoadSprite(m_BG.image, firstItem.BG);

            InitPlayerInfo();
        }

        /// <summary>
        /// 播放显示节点，动画或者直接出来
        /// </summary>
        private void PlayShowNode()
        {
            foreach (CombatPower_MainUI_Rewardnodes rewardnodes in m_NodeList)
            {
                rewardnodes.gameObject.SetActive(true);
            }
        }

        private void InitPlayerInfo()
        {
            var mechaData = Global.gApp.gSystemMgr.gMechaDataMgr.GetCurBattleMecha();
            var m_MechaCfg = Mecha.Data.Get(mechaData.MechaId);
            if (!m_PlayerObj)
            {
                // string prefabPath = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaPrefabPath(m_MechaCfg.id);
                // m_PlayerObj = InstantiateObj(prefabPath, ResSceneType.NormalRes, m_Player_Node.transform);
                // Renderer[] renders = m_PlayerObj.GetComponentsInChildren<Renderer>(true);
                // Canvas parentCanvas = GetComponentInParent<Canvas>();
                // GetMaxOrder(out _, out int order);
                // foreach (Renderer render in renders)
                // {
                //     render.sortingOrder = 3 + order;
                // }
                //
                // Transform mode = m_PlayerObj.transform.Find("Mode");
                // if (mode.TryGetComponent(out Animator ani))
                // {
                //     ani.Play(LDAnimName.DiyNormalAnim[LDAnimName.Idle01], 1, 0);
                // }

                LDUIPrefabTools.InitSelfRoleHead(m_NormalRoleHead.gameObject);

                // m_combat_power.text.SetText(UiTools.FormateMoney(Global.gApp.gSystemMgr.gRoleMgr.GetHistoryMaxPowerNum()));
            }

            // Animator aniPlayer = m_Player.gameObject.GetComponent<Animator>();
            // aniPlayer.Play("fx_wurenjiqifei01", 0, 0);
        }

        private void RefreshPlayer()
        {
            int index = -1;
            float offset = 0;
            int curTaskId = 0;
            for (int i = 0; i < m_NodeList.Count; i++)
            {
                CombatPower_MainUI_Rewardnodes itemUI = m_NodeList[i];
                if (!itemUI.GetIsFinish())
                {
                    index = i - 1;
                    break;
                }
                else
                {
                    index = i;
                }
            }


            RectTransform_Container parent = null;
            if (index == -1)
            {
                parent = m_point_1;
            }
            else
            {
                parent = m_NodeParents[index];
                offset = 80f;
            }

            m_Player.gameObject.transform.localPosition = parent.gameObject.transform.localPosition + new Vector3(0, -offset, 0);

            NewCombatPowerTasksItem[] tasks = NewCombatPowerTasks.Data.items;
            List<NewCombatPowerTasksItem> temp = new();
            for (int i = 0; i < tasks.Length; i++)
            {
                NewCombatPowerTasksItem item = tasks[i];
                if (item.activityId == m_ActivityId)
                {
                    temp.Add(item);
                }
            }

            int taskId = temp[^1].id;
            var taskInfo = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.GetTaskInfo(taskId);
            long power = 0;
            if (taskInfo != null)
            {
                power = taskInfo.Progress;
            }

            m_combat_power.text.SetText(UiTools.FormateMoney(power));
        }

        private void MoveToPlayerToFirst()
        {
            // 执行移动动画
            // Animator anim = m_Player.gameObject.GetComponent<Animator>();
            // if (anim != null)
            // {
            //     anim.Play("fx_wurenjiqifei", -1, 0);
            // }
            //
            // Vector3 startPos = m_Player.gameObject.transform.localPosition;
            Vector3 endPos = m_point_1.gameObject.transform.localPosition;
            // AddTimer(1f, 1, (a, b) =>
            // {
            //     Tweener tweener = UiTools.TweenFromTo(0f, 1f, 2f, (a) => { m_Player.gameObject.transform.localPosition = startPos + (endPos - startPos) * a; });
            //     tweener.onComplete += () => { AddTimer(0.5f, 1, (c, d) => { PlayShowNode(); }); };
            // });
            m_Player.gameObject.transform.localPosition = endPos;
        }

        private bool CheckNeedChangeMap()
        {
            foreach (CombatPower_MainUI_Rewardnodes rewardnodes in m_NodeList)
            {
                if (!rewardnodes.GetIsFinish())
                {
                    return false;
                }
            }

            Dictionary<int, List<NewCombatPowerTasksItem>> showItemDict = Global.gApp.gSystemMgr.gCombatPowerMgr.GetShowTaskDict();
            int showStage = m_NodeList[0].GetShowStage();
            if (showStage >= showItemDict.Count)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        #endregion

        #region 蛋信息

        private void ShowEggInfo(NewCombatPowerTasksItem cfg, Vector3 pos)
        {
            m_eggInfo.gameObject.SetActive(true);
            m_eggInfo.gameObject.transform.position = pos;
            m_OreReward.CacheInstanceList();
            foreach (string s in cfg.eggShowItem)
            {
                CombatPower_MainUI_OreReward item = m_OreReward.GetInstance(true);
                LDCommonTools.DestoryChildren(item.scaleNode.rectTransform);
                LDCommonItem itemInfo = new LDCommonItem(s);
                KnapsackItem knapsackItem = LDUIPrefabTools.GetKnapsackItemUI(item.scaleNode.rectTransform);
                LDUIPrefabTools.InitKnapsackItem(knapsackItem, itemInfo);
            }
        }

        private void HideEggInfo()
        {
            m_eggInfo.gameObject.SetActive(false);
        }

        #endregion

        /// <summary>
        /// 任务进度
        /// </summary>
        private void RefreshTopTask()
        {
            if (m_ProgressBarComp == null)
            {
                m_ProgressBarComp = m_ProgressBar.gameObject.GetComponent<ProgressBar>();
            }

            int curCount = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.GetFinishTaskCount();
            NewCombatPowerCumulativeItem[] cumulativeCfgs = NewCombatPowerCumulative.Data.items;
            List<NewCombatPowerCumulativeItem> temp = new();
            for (int i = 0; i < cumulativeCfgs.Length; i++)
            {
                NewCombatPowerCumulativeItem cfg = cumulativeCfgs[i];
                if (cfg.activityId != m_ActivityId)
                {
                    continue;
                }

                temp.Add(cfg);
            }

            temp.Sort((a, b) => { return a.taskNum - b.taskNum; });
            NewCombatPowerCumulativeItem curCfg = null;
            for (int i = 0; i < temp.Count; i++)
            {
                NewCombatPowerCumulativeItem cfg = temp[i];
                bool isSelect = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.CheckIsGetCumulateReward(cfg.id);
                if (isSelect)
                    continue;

                curCfg = cfg;
                break;
            }

            if (curCfg == null)
            {
                curCfg = temp[^1];
            }

            m_ProgressBarComp.SetProgressImmediately(curCount, curCfg.taskNum);

            bool isSelectCur = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.CheckIsGetCumulateReward(curCfg.id);
            m_ReceivedAlready.gameObject.SetActive(isSelectCur);
            m_fxdailinqu.gameObject.SetActive(curCount >= curCfg.taskNum && !isSelectCur);

            m_ProText.text.SetTips(77241, curCfg.taskNum - curCount);
            m_ProText.gameObject.gameObject.SetActive(curCfg.taskNum - curCount > 0 && !isSelectCur);
            m_ProgressBarComp.SetProgressTextVisible(!isSelectCur);

            LDCommonItem rewardItem = new LDCommonItem(curCfg.rewards);
            LoadSprite(m_itemIcon.image, rewardItem.Icon);
            m_ItemCountText.text.SetText($"x{rewardItem.Num}");
        }

        private void OnTaskCountBoxClick()
        {
            int curCount = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.GetFinishTaskCount();
            NewCombatPowerCumulativeItem[] cumulativeCfgs = NewCombatPowerCumulative.Data.items;
            List<NewCombatPowerCumulativeItem> temp = new();
            for (int i = 0; i < cumulativeCfgs.Length; i++)
            {
                NewCombatPowerCumulativeItem cfg = cumulativeCfgs[i];
                if (cfg.activityId != m_ActivityId)
                {
                    continue;
                }

                temp.Add(cfg);
            }

            temp.Sort((a, b) => { return a.taskNum - b.taskNum; });
            NewCombatPowerCumulativeItem curCfg = null;
            for (int i = 0; i < temp.Count; i++)
            {
                NewCombatPowerCumulativeItem cfg = temp[i];
                bool isSelect = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.CheckIsGetCumulateReward(cfg.id);
                if (isSelect)
                    continue;

                curCfg = cfg;
                break;
            }

            if (curCfg != null)
            {
                if (curCount >= curCfg.taskNum)
                {
                    Global.gApp.gSystemMgr.gCombatPowerMgr.SendReceiveCumulateReward(curCfg.id);
                }
                else
                {
                    Global.gApp.gToastMgr.ShowGameTips(UiTools.Localize(77241, curCfg.taskNum - curCount));
                }
            }
        }

        private void OnExchangeShopClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync<ActivityExchangeUI>(LDUICfg.ActivityExchangeUI).SetLoadedCall(baseUI => { baseUI.OnSetData(m_ActivityId); });
        }

        #region 事件

        /// <summary>
        /// 时间
        /// </summary>
        private void RefreshTime()
        {
            //TODO
            LDNetActivityInfo activityInfo = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.ActivityInfo;
            if (activityInfo.IsOpen())
            {
                m_DistanceTime = activityInfo.EndTime;
                RefreshTimeText();
                m_UpdateTime = true;
                long dis = ((long)m_DistanceTime - DateTimeUtil.GetServerTime()) / 1000;
                AddTimer(dis, 1, OnTimeLimitedRechargeActivityFinishTimeCall);
            }
        }

        private void OnTimeLimitedRechargeActivityFinishTimeCall(float arg1, bool arg2)
        {
            TouchClose();
        }

        private bool RefreshTimeText()
        {
            double serverTime = DateTimeUtil.GetServerTime();
            double offset = m_DistanceTime - serverTime;
            if (offset <= 0)
            {
                RefreshTime();
                return false;
            }

            string time = Global.gApp.gGameData.GetExpireTimeTips5((long)m_DistanceTime);
            m_time.text.SetText(time);
            return true;
        }

        private void Update()
        {
            if (m_UpdateTime)
            {
                if (!RefreshTimeText())
                {
                    RefreshTime();
                }
            }
        }

        #endregion
    }
}