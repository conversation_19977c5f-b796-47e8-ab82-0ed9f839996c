namespace LD
{
    public partial class AeroplaneChessUI_itemNode
    {
        public FlyChessRoundItem NodeCfg => m_Cfg;

        private int m_ActivityId;
        private int m_CurRound;
        private int m_LoopTimes;

        private FlyChessRoundItem m_Cfg;
        private KnapsackItem m_KnapsackItem;

        public void RefreshView(FlyChessRoundItem itemCfg, int curCircle, int activityId, int loopTimes)
        {
            m_Cfg = itemCfg;
            m_CurRound = curCircle;
            m_ActivityId = activityId;
            m_LoopTimes = loopTimes;

            LDCommonTools.DestoryChildren(m_itemScale.rectTransform);

            string useReward = m_LoopTimes == 1 ? m_Cfg.reward : m_Cfg.loopReward;
            LDCommonItem item = new LDCommonItem(useReward);
            m_KnapsackItem = LDUIPrefabTools.GetKnapsackItemUI(m_itemScale.rectTransform);
            LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem, item);

            m_itemTarget.text.SetText(m_Cfg.round);
        }

        public int RefreshReceiveState()
        {
            bool isSelect = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckAeroplaneProgressRewardReceived(m_ActivityId, m_Cfg.id, m_LoopTimes - 1);
            if (m_CurRound >= m_Cfg.round)
            {
                if (isSelect)
                {
                    m_KnapsackItem.ShowMask(true);
                    m_KnapsackItem.ShowCanGet(false);
                    m_KnapsackItem.SetClickCallback(null);
                }
                else
                {
                    m_KnapsackItem.ShowMask(false);
                    m_KnapsackItem.ShowCanGet(true);
                    m_KnapsackItem.SetClickCallback(OnClickReceive);
                    return m_Cfg.round;
                }
            }
            else
            {
                m_KnapsackItem.ShowMask(false);
                m_KnapsackItem.ShowCanGet(false);
                m_KnapsackItem.SetClickCallback(null);
            }

            return -1;
        }

        private void OnClickReceive()
        {
            Global.gApp.gSystemMgr.gCircleActivityMgr.SendReceiveProgressReward_FlyChess(m_ActivityId, m_Cfg.id, m_LoopTimes - 1);
        }
    }
}