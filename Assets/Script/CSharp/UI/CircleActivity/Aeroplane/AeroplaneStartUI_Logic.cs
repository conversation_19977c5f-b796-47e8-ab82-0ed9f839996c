using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class AeroplaneStartUI
    {
        private int m_ActivityId;
        private long m_OpenTime;
        protected override void OnInitImp()
        {
            m_btn_go.AddListener(OnClickGo);
            m_btn_close.AddListener(TouchClose);
        }

        public override void OnFreshUI()
        {

        }

        public void InitData(int activityId)
        {
            m_ActivityId = activityId;


            InitRewardShow();
        }

        private void InitRewardShow()
        {
            LDCircleActivityInfoBase activityInfo = Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(m_ActivityId);
            m_OpenTime = activityInfo.circleActivity.openTime;

            DateTime dateTimeStart = DateTimeUtil.GetDate(m_OpenTime);
            DateTime dateTimeEnd = DateTimeUtil.GetDate(activityInfo.circleActivity.endTime);
            string startTime = $"{dateTimeStart.Year}.{dateTimeStart.Month}.{dateTimeStart.Day}";
            string endTime = $"{dateTimeEnd.Year}.{dateTimeEnd.Month}.{dateTimeEnd.Day}";
            m_txt_time.text.SetText($"{startTime}-{endTime}");
            SeasonActivityCfg cfgItem = Global.gApp.gSystemMgr.gCircleActivityMgr.GetSeasonActivityCfg(m_ActivityId);

            foreach (string rwd in cfgItem.EnterRewardShow)
            {
                AeroplaneStartUI_itemNode rewardUI = m_itemNode.GetInstance(true);
                LDCommonItem commonItem = new LDCommonItem(rwd);
                RectTransform rt = rewardUI.itemScale.rectTransform;
                LDCommonTools.DestoryChildren(rt);
                LDUIPrefabTools.GetKnapsackItemUI(commonItem, rt);
            }

            if (!string.IsNullOrEmpty(cfgItem.EnterUIBg))
            {
                LoadSprite(m_BG.image, cfgItem.EnterUIBg);
            }
        }

        private void OnClickGo()
        {
            List<int> showIds = Global.gApp.gSystemMgr.gCircleActivityMgr.GetShowIds();
            if (!showIds.Contains(m_ActivityId))
            {
                TouchClose();
                return;
            }

            Global.gApp.gUiMgr.OpenUIAsync<ActivityUI>(LDUICfg.ActivityUI).SetLoadedCall(baseUI =>
            {
                baseUI.InitData(m_ActivityId);
            });

            TouchClose();
        }

        protected override void OnCloseImp()
        {
            if (m_toggle_skip.toggle.isOn)
            {
                string key = $"CircleActivity_{m_ActivityId}_{m_OpenTime}";
                long nextShowTime = (long)DateTimeUtil.GetCrossDaySec(23, 59, 59) * 1000 + 1000;
                Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, key, nextShowTime);
            }
        }
    }
}