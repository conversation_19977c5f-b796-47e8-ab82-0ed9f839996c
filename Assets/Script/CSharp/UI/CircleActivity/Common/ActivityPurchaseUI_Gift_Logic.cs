using System;
using UnityEngine;

namespace LD
{
    public partial class ActivityPurchaseUI_Gift
    {
        private ActivityPurchaseItem m_Cfg;
        private Animator m_Anim;
        private int m_LastCount;
        private int m_MaxCount;

        private LDPaymentBtnUI m_PaymentBtn;

        public void RefreshView(ActivityPurchaseItem cfg)
        {
            if (m_PaymentBtn == null)
                m_PaymentBtn = m_Orange_btn_Buy.gameObject.AddComponent<LDPaymentBtnUI>();
            m_PaymentBtn.InitBtn(m_Orange_btn_Buy.button, this, OnBuyClick);

            if (m_Anim == null)
            {
                m_Anim = this.gameObject.GetComponent<Animator>();
            }

            if (cfg != null)
            {
                m_Cfg = cfg;
            }

            RefreshInfo();
            RefreshReward();
            RefreshBtn();
            m_Orange_btn_Free.AddListener(OnFreeClick);
            // m_Orange_btn_Buy.AddListener(OnBuyClick);
            m_Blue_btn_AD.AddListener(OnAdClick);
            m_Orange_btn_Diamonds.AddListener(OnDiamondClick);
        }

        public void SetAnimEnable(bool isEnabled)
        {
            if (m_Anim != null)
            {
                m_Anim.enabled = isEnabled;
            }
        }

        private void RefreshInfo()
        {
            m_Value.SetActive(m_Cfg.superValue > 0);
            if (m_Cfg.superValue > 0)
            {
                m_txt_value.text.SetTips(m_Cfg.superValue);
            }

            int buyTimes = Global.gApp.gSystemMgr.gCircleActivityMgr.GetPurchaseBuyCount(m_Cfg.id);
            m_txt_limitTime.text.SetTips(79564, Math.Max(0, m_Cfg.inventory - buyTimes));
            bool isSoldOut = buyTimes >= m_Cfg.inventory;
            m_txt_limitTime.gameObject.SetActive(!isSoldOut);
        }

        private void RefreshBtn()
        {
            SetBuyBtnInfo();

            SetOtherBtnInfo();
        }

        private void SetOtherBtnInfo()
        {
            bool isSoldOut = m_LastCount <= 0;
            m_Button_YiShouQing.SetActive(isSoldOut);

            m_SoldOut.SetActive(isSoldOut);

            bool isFree = m_Cfg.mallGoodsId == 0 && m_Cfg.ADid == 0 && m_Cfg.diamondsNum == 0;
            m_Button_Free.SetActive(isFree && !isSoldOut);

            m_RedTips.SetActive(!isSoldOut && (isFree || m_Cfg.ADid > 0));
        }

        private void SetBuyBtnInfo()
        {
            if (m_Cfg.ADid > 0)
            {
                SetBtnAd();
            }
            else if (m_Cfg.diamondsNum > 0)
            {
                SetBtnDiamond();
            }
            else if (m_Cfg.mallGoodsId > 0)
            {
                SetBtnCharge();
            }
        }

        private void SetBtnAd()
        {
            Global.gApp.gSystemMgr.gAdvertiseMgr.GetAdTimesInfo(m_Cfg.ADid, out m_LastCount, out m_MaxCount);
            bool isSoldOut = m_LastCount <= 0;
            m_Button_AD.SetActive(!isSoldOut);
            m_Button_Buy.SetActive(false);
            m_Button_Diamonds.SetActive(false);
        }

        private void SetBtnDiamond()
        {
            m_Button_Buy.SetActive(false);
            m_Button_AD.SetActive(false);

            m_MaxCount = m_Cfg.inventory;
            m_LastCount = m_MaxCount - Global.gApp.gSystemMgr.gCircleActivityMgr.GetPurchaseBuyCount(m_Cfg.id);
            m_Orange_lable_Diamonds.text.SetText(m_Cfg.diamondsNum);
            bool isSoldOut = m_LastCount <= 0;
            m_Button_Diamonds.SetActive(!isSoldOut);
        }

        private void SetBtnCharge()
        {
            m_Button_AD.SetActive(false);
            m_Button_Diamonds.SetActive(false);

            m_MaxCount = m_Cfg.inventory;
            m_LastCount = m_MaxCount - Global.gApp.gSystemMgr.gCircleActivityMgr.GetPurchaseBuyCount(m_Cfg.id);
            bool isSoldOut = m_LastCount <= 0;
            m_Button_Buy.SetActive(!isSoldOut);

            // string price = Global.gApp.gSdkMgr.gPaymentMgr.GetDisplayPrice(m_Cfg.mallGoodsId);
            // m_Orange_lable_S.text.SetText(price);
            m_PaymentBtn.ShowPrice(m_Cfg.mallGoodsId);
        }

        private void RefreshReward()
        {
            m_Item.CacheInstanceList();
            string[] rewardCfg = m_Cfg.Reward;
            foreach (string rwd in rewardCfg)
            {
                ActivityPurchaseUI_Gift_Item rewardUI = m_Item.GetInstance(true);
                LDCommonItem commonItem = new LDCommonItem(rwd);

                RectTransform rt = rewardUI.ScaleNode.rectTransform;
                LDCommonTools.DestoryChildren(rt);
                LDUIPrefabTools.GetKnapsackItemUI(commonItem, rt);
            }
        }

        private void OnFreeClick()
        {
            Global.gApp.gSystemMgr.gPeriodicalGiftMgr.SendReceiveFreeGift(m_Cfg.id);
        }

        private void OnDiamondClick()
        {
            int costNum = m_Cfg.diamondsNum;
            LDCommonItem commonItem = new LDCommonItem(LDCommonType.Item, LDSpecialItemId.Diamond, 1);
            if (commonItem.CurNum < costNum)
            {
                Global.gApp.gToastMgr.ShowGameTips(67062);
                return;
            }

            Global.gApp.gSystemMgr.gPeriodicalGiftMgr.SendReceiveDiamondGift(m_Cfg.id);
        }

        private void OnAdClick()
        {
            Global.gApp.gSdkMgr.gADMgr.Show(m_Cfg.ADid, LDUICfg.SuperValueActUI, 3);
        }

        private void OnBuyClick()
        {
            Global.gApp.gSystemMgr.gPaymentMgr.SendMallBuyItemRequest(m_Cfg.mallGoodsId);
        }
    }
}