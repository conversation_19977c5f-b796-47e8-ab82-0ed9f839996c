using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class ActivityQuestUI
    {
        private LDNetCircleActivityMgr m_ActMgr;
        private ActivityQuestItem m_ActivityQuestItem;
        private ActivityParamItem m_ActivityParamItem;
        private readonly List<ActivityQuestItem> m_QuestCfgItems = new List<ActivityQuestItem>();
        private readonly Dictionary<int, List<ActivityQuestItem>> m_QuestCfgGroupItems = new Dictionary<int, List<ActivityQuestItem>>();
        private readonly List<ActivityQuestUI_Item> m_ItemUIs = new List<ActivityQuestUI_Item>();

        private string m_TabType;
        private long m_CloseTime;
        private float m_PerSec = 1;
        private bool m_IsUseGroup = false;

        private readonly float m_ShowItemTimeOffset = 0.1f;
        private readonly List<int> m_WaitShowItemTimer = new();

        public override void Init(string tabType, int activityId, LDBaseUI parentUI)
        {
            base.Init(tabType, activityId, parentUI);

            m_TabType = tabType;
            m_ActMgr = Global.gApp.gSystemMgr.gCircleActivityMgr;

            SetQuestCfgList();

            m_ActivityParamItem = Global.gApp.gSystemMgr.gCircleActivityMgr.GetActivityParamItem(m_TabType, activityId);

            LDCircleActivityInfoBase activityInfo = Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(ActivityId);
            int param = LDParseTools.IntParse(m_ActivityParamItem.moduleParam[0]);
            if (param == 0)
            {
                m_CloseTime = activityInfo.circleActivity.endTime;
            }
            else
            {
                m_CloseTime = (long)DateTimeUtil.GetCrossDaySec(23, 59, 59) * 1000 + 1000;
            }
        }

        private void SetQuestCfgList()
        {
            m_QuestCfgItems.Clear();

            foreach (ActivityQuestItem questItem in ActivityQuest.Data.items)
            {
                if (questItem.activityID == ActivityId)
                {
                    if(questItem.groupMis != 0)
                    {
                        m_IsUseGroup = true;

                        if (m_QuestCfgGroupItems.TryGetValue(questItem. groupMis, out List<ActivityQuestItem> groupQuestItems))
                        {
                            groupQuestItems.Add(questItem);
                        }
                        else
                        {
                            groupQuestItems = new List<ActivityQuestItem> { questItem };
                            m_QuestCfgGroupItems[questItem.groupMis] = groupQuestItems;
                        }
                    }
                    else
                    {
                        m_QuestCfgItems.Add(questItem);
                    }

                }
            }
        }

        public override void RefreshUI()
        {
            LoadSprite(m_img_bgImg.image, m_ActivityParamItem.enterImage);
            m_txt_title.text.SetTips(m_ActivityParamItem.enterName);

            RefreshList(true);
            HideCurrency();
        }

        public void HideCurrency()
        {
            if (transform.gameObject.activeSelf)
            {
                m_ParentUI?.ForceShowCurrency(CurrencyTabUI.Empty);
            }
        }

        private void RefreshList(bool needAnim)
        {
            m_Item.CacheInstanceList();
            m_ItemUIs.Clear();

            foreach (int i in m_WaitShowItemTimer)
            {
                m_ParentUI.RemoveTimer(i);
            }

            m_WaitShowItemTimer.Clear();

            if (m_IsUseGroup)
            {
                RefreshUIListWithGroup(needAnim);
            }
            else
            {
                RefreshUIList(needAnim);
            }

            m_ItemUIs.Sort((a, b) => { return a.GetSortNum() - b.GetSortNum(); });
            int index = 0;
            foreach (ActivityQuestUI_Item ui in m_ItemUIs)
            {
                ui.transform.SetAsLastSibling();
                index++;
                int timerId = m_ParentUI.AddTimer(index * m_ShowItemTimeOffset, 1, (a, b) => { ui.gameObject.SetActive(true); });
                m_WaitShowItemTimer.Add(timerId);
            }
        }

        private void RefreshUIListWithGroup(bool needAnim)
        {
            foreach (KeyValuePair<int, List<ActivityQuestItem>> info in m_QuestCfgGroupItems)
            {
                int groupId = info.Key;
                List<ActivityQuestItem> groupQuestItems = info.Value;

                ActivityQuestUI_Item itemUI = m_Item.GetInstance(true);
                ActivityQuestItem showItem = new ActivityQuestItem();

                int minShowIdx = 999;
                int maxShowIdx = -1;
                foreach (ActivityQuestItem item in groupQuestItems)
                {
                    int taskId = item.id;
                    bool rewarded = m_ActMgr.IsRewardedTask(ActivityId, taskId);

                    if (!rewarded)
                    {
                        if (item.idxInGroup < minShowIdx)
                        {
                            minShowIdx = item.idxInGroup;
                            showItem = item;
                        }
                    }
                }
                if (minShowIdx == 999) //全部都领取了
                {
                    foreach (ActivityQuestItem item in groupQuestItems)
                    {
                        if (item.idxInGroup > maxShowIdx)
                        {
                            maxShowIdx = item.idxInGroup;
                            showItem = item;
                        }
                    }
                }

                int showTaskId = showItem.id;
                bool isFinished = m_ActMgr.IsFinishTask(ActivityId, showTaskId);
                bool isRewarded = m_ActMgr.IsRewardedTask(ActivityId, showTaskId);
                long progress = m_ActMgr.GetTaskProgress(ActivityId, showTaskId);
                itemUI.InitUI(showItem, isFinished, isRewarded, progress);
                m_ItemUIs.Add(itemUI);
                if (needAnim)
                {
                    itemUI.gameObject.SetActive(false);
                }
            }
        }



        private void RefreshUIList(bool needAnim)
        {
            foreach (ActivityQuestItem item in m_QuestCfgItems)
            {
                ActivityQuestUI_Item itemUI = m_Item.GetInstance(true);
                int taskId = item.id;
                bool isFinished = m_ActMgr.IsFinishTask(ActivityId, taskId);
                bool isRewarded = m_ActMgr.IsRewardedTask(ActivityId, taskId);
                long progress = m_ActMgr.GetTaskProgress(ActivityId, taskId);
                itemUI.InitUI(item, isFinished, isRewarded, progress);

                m_ItemUIs.Add(itemUI);
                if (needAnim)
                {
                    itemUI.gameObject.SetActive(false);
                }
            }
        }


        private void Update()
        {
            m_PerSec += UnityEngine.Time.deltaTime;
            if (m_PerSec >= 1f)
            {
                m_PerSec -= 1f;
                UpdateTime();
            }
        }

        private void UpdateTime()
        {
            if (DateTimeUtil.GetServerTime() - m_CloseTime > 0)
            {
                m_ParentUI.TouchClose();
                return;
            }

            string time = Global.gApp.gGameData.GetExpireTimeTips2(m_CloseTime);
            m_txt_time.text.SetText(time);
        }
    }

    #region Item Class

    public partial class ActivityQuestUI_Item
    {
        private ActivityQuestItem m_Cfg;
        private int m_Level;
        private bool m_IsFinished;
        private bool m_IsRewarded;
        private float m_Recharge;
        private float m_RechargeTarget;
        private int m_TotalCount;

        private void InitUIBase()
        {
            m_btn_go.AddListener(OnClickGo);
            m_btn_get.AddListener(OnClickGet);
        }

        public void InitUI(ActivityQuestItem cfg, bool isFinished, bool isRewarded, long progress)
        {
            InitUIBase();

            m_Cfg = cfg;
            m_IsFinished = isFinished;
            m_IsRewarded = isRewarded;

            RefreshBaseInfo(progress);
            RefreshRewards();
            RefreshBtns();
            RefreshRedTips();
        }

        private void RefreshBaseInfo(long progress)
        {
            m_img_questBG.gameObject.SetActive(true);
            m_txt_questPro.gameObject.SetActive(true);
            m_img_questTitleBG1.gameObject.SetActive(true);

            string desc = Global.gApp.gSystemMgr.gTaskMgr.GetTaskDes(m_Cfg.type, m_Cfg.info);
            m_txt_questInfo.text.SetText(desc);

            string curProgress = UiTools.FormateMoney(progress);
            string curCfgProgress = UiTools.FormateMoney(Global.gApp.gSystemMgr.gTaskMgr.GetTaskMaxProgress(m_Cfg.type));
            m_txt_questPro.text.SetText($"{curProgress}/{curCfgProgress}");
        }

        private void RefreshRewards()
        {
            m_Item.CacheInstanceList();
            string[] rewardCfg = m_Cfg.Reward;
            foreach (string rwd in rewardCfg)
            {
                ActivityQuestUI_Item_Item rewardUI = m_Item.GetInstance(true);
                LDCommonItem commonItem = new LDCommonItem(rwd);
                RectTransform rt = rewardUI.ScaleNode.rectTransform;
                LDCommonTools.DestoryChildren(rt);
                LDUIPrefabTools.GetKnapsackItemUI(commonItem, rt);
            }
        }

        private void RefreshBtns()
        {
            m_btn_claimed.gameObject.SetActive(m_IsRewarded);
            m_btn_get.gameObject.SetActive(m_IsFinished && !m_IsRewarded);
            m_btn_go.gameObject.SetActive(!m_IsFinished && !m_IsRewarded);
        }

        private void RefreshRedTips()
        {
            LDRedTipsState state = LDRedTipsState.None;
            if (m_IsFinished && !m_IsRewarded)
            {
                state = LDRedTipsState.RedPoint;
            }

            m_RedTips.gameObject.GetComponent<RedTips>().FreshState(state);
        }

        private void OnClickGet()
        {
            Global.gApp.gSystemMgr.gCircleActivityMgr.SendTaskRequest(m_Cfg.activityID, m_Cfg.id);
        }

        private void OnClickGo()
        {
            LDPageJumpTools.TryJumpPage((LDSystemEnum)m_Cfg.jump);
        }

        public int GetSortNum()
        {
            if (m_IsFinished)
            {
                return 0;
            }
            else
            {
                if (m_IsRewarded)
                {
                    return 2;
                }
                else
                {
                    return 1;
                }
            }

            return 1;
        }
    }

    #endregion Item Class
}