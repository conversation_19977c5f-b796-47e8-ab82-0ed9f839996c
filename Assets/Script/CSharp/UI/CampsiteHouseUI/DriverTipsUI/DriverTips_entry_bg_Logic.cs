namespace LD
{
    public partial class DriverTips_entry_bg
    {
        private LDNetCampsiteMgr m_CampsiteMgr;

        public void RefreshUI(DriverQualitySkillItem quaSkillCfg, int driverId)
        {
            m_CampsiteMgr = Global.gApp.gSystemMgr.gCampsiteMgr;


            int qua = quaSkillCfg.Requirement;
            // int driverQua = m_CampsiteMgr.GetDriverQuality(driverId);
            // bool isNext = driverQua != LDQualityType.Max && qua == LDQualityType.Next(driverQua);
            // bool isUnlock = qua <= driverQua;

            m_learned.gameObject.SetActive(true);//(isUnlock);
            LDUIPrefabTools.InitItemRank(m_ItemRank.gameObject, qua);
            m_CitiaoDesc.text.SetTips(quaSkillCfg.tips);
            m_unLearned.gameObject.SetActive(false);//(!isUnlock && !isNext);
            m_near_learned.gameObject.SetActive(false); //(isNext);
        }
    }
}