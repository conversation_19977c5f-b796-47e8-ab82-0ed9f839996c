using System.Collections.Generic;

namespace LD
{
    public partial class EquipmentLvUpUI
    {
        protected override void OnInitImp()
        {
            RefreshView();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }

        private void RefreshView()
        {
            var curLv = Global.gApp.gSystemMgr.gEquipmentMgr.Data.FurnaceLevel;
            var lastLv = curLv - 1;
            var curCfg = Manufacture.Data.Get(curLv);
            var lastCfg = Manufacture.Data.Get(lastLv);

            m_lvNUM.text.SetTips(65541, lastLv);
            m_lv_newNUM.text.SetTips(65541, curLv);


            Dictionary<int, int> lastPro = new();
            foreach (string s in lastCfg.manufactureProbability)
            {
                var lines = LDCommonTools.Split(s);
                var quality = LDParseTools.IntParse(lines[0]);
                var pro = LDParseTools.IntParse(lines[1]);
                lastPro.Add(quality, pro);
            }

            m_Quality.CacheInstanceList();
            foreach (string s in curCfg.manufactureProbability)
            {
                var lines = LDCommonTools.Split(s);
                var quality = LDParseTools.IntParse(lines[0]);
                var pro = LDParseTools.IntParse(lines[1]);

                var lastvalue = lastPro.ContainsKey(quality) ? lastPro[quality] : 0;
                if (pro > lastvalue)
                {
                    var item = m_Quality.GetInstance(true);

                    LoadSprite(item.quality_icon.image, LDCommonTipsTools.GetQualityBorder(quality));
                    item.QualityName.text.SetText(LDCommonTipsTools.GetQualityTips(quality));
                    item.gailvVal1.text.SetText($"{lastvalue / 100f}%");
                    item.gailvVal2.text.SetText($"{pro / 100f}%");
                }
            }
        }
    }
}