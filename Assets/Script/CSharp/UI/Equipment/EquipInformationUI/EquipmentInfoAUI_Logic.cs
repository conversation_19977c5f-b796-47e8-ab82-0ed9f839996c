using System;
using System.Collections.Generic;
using UnityEngine.UI;

namespace LD
{
    public partial class EquipmentInfoAUI
    {
        private LDNetEquipInfo m_CurEquipInfo;
        private bool m_IsUp;
        private bool m_IsEquip;
        private bool m_isLock;
        private string m_ChangeBreakDownDisposeKey;
        private Action m_CheckMoreAttrAction;
        private bool m_IsShowOtherEquip;

        private int m_SelectCitiaoIndex = -1;
        private int m_CitiaoTypeCount = 0;
        private int[] m_CitiaoGroup;


        protected override void OnInitImp()
        {
            m_Btn_BreakDown.AddListener(OnBreakDownClick);
            m_Btn_equip.AddListener(OnEquipClick);
            m_Btn_replace.AddListener(OnEquipClick);

            m_Btn_lock.AddListener(OnLockClick);
            m_ChangeDisposeBtn.AddListener(OnChangeDisposeBtnClick);
            m_CheckAttrlBtn.AddListener(OnCheckMoreAttrClick);
            m_reforge_btn.AddListener(OnClickRefinement);

            m_PreviewUnCurrent.AddListener(OnCurrentClick);
            m_PreviewUnSelect01.AddListener(OnPreview1Click);
            m_PreviewUnSelect02.AddListener(OnPreview2Click);
            m_PreviewUnSelect03.AddListener(OnPreview3Click);

            AddTimer(0.01f, 1, (_, _) => { TryGuide(); });
        }

        private void TryGuide()
        {
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Equipment, m_equipBtnNode.rectTransform, 3))
            {
                return;
            }
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
            RefreshEquipInfo(m_CurEquipInfo, m_IsUp, m_CheckMoreAttrAction, m_IsShowOtherEquip);
        }

        public override void OnFreshUI(int val)
        {
            if (val == 999)
            {
                TouchClose();
            }
        }

        /// <summary>
        /// 装备信息窗口逻辑
        /// </summary>
        /// <param name="equipInfo"></param>
        /// <param name="slotEquiping"></param>
        public void RefreshEquipInfo(LDNetEquipInfo equipInfo, bool isUp, Action checkMoreAttrAction, bool isOther = false)
        {
            m_IsShowOtherEquip = isOther;
            Global.Log($"equip uid:{equipInfo.Id} cfgId:{equipInfo.EquipId}");
            m_CurEquipInfo = equipInfo;
            m_IsUp = isUp;
            m_CheckMoreAttrAction = checkMoreAttrAction;
            m_Btn_lock.gameObject.SetActive(m_CurEquipInfo.EquipCfg.type == 2 && !m_IsShowOtherEquip);
            if (m_CurEquipInfo.EquipCfg.type == 2)
            {
                m_ChangeBreakDownDisposeKey = LDLocalDataKeys.EquipBreakDownAfterEquip_Weapon;
                m_maskNode.gameObject.SetActive(false);
            }
            else
            {
                m_ChangeBreakDownDisposeKey = LDLocalDataKeys.EquipBreakDownAfterEquip_Equipment;
                m_maskNode.gameObject.SetActive(true && !m_IsShowOtherEquip);
            }


            RefreshNodeActive();
            RefreshEquipInfo();
            RefreshEquipAttr();
            InitCitiaoInfo();
            RefreshLockStatus();
            RefreshChangeDisposeSelectStatus();
            RefreshSeeMoreAttrSelectStatus();
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_bgParent.gameObject.transform.rectTransform());
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_ContentGameObject.gameObject.transform.rectTransform());
        }

        private void RefreshNodeActive()
        {
            if (m_IsShowOtherEquip)
            {
                m_maskNode.gameObject.SetActive(false);
                m_btn_Node.gameObject.SetActive(false);
                m_reforgeBtnNode.gameObject.SetActive(false);
            }
            else
            {
                int slotId = m_CurEquipInfo.GetEquipSlotId();
                var isEquip = Global.gApp.gSystemMgr.gEquipmentMgr.CheckSlotIsEquip(slotId);
                if (m_CurEquipInfo.EquipCfg.type == 2)
                {
                    m_maskNode.gameObject.SetActive(false);
                }
                else
                {
                    m_maskNode.gameObject.SetActive(!isEquip);
                }


                m_equipBtnNode.gameObject.SetActive(!isEquip);
                m_replaceBtnNode.gameObject.SetActive(!m_IsUp && isEquip);
                m_BreakDownBtnNode.gameObject.SetActive(!m_IsUp && isEquip);
                bool isCanReforge = m_CurEquipInfo.CanReforge();
                m_btn_Node.gameObject.SetActive(!isEquip || isCanReforge);
                m_reforgeBtnNode.gameObject.SetActive(isCanReforge);
                m_ReforgeNode.gameObject.SetActive((!m_IsUp && isEquip));
            }

            m_inuse.gameObject.SetActive(false);
            m_Fenjie_Node.gameObject.SetActive(false);
        }

        private void RefreshChangeDisposeSelectStatus()
        {
            bool isSelect = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, m_ChangeBreakDownDisposeKey) == 1;
            m_ChangeDisposeBtncheckMark.gameObject.SetActive(isSelect);
        }

        private void RefreshSeeMoreAttrSelectStatus()
        {
            bool isSelect = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.EquipCanSeeMoreAttribution) == 1;
            m_checkMark.gameObject.SetActive(isSelect);
        }

        private void RefreshEquipInfo()
        {
            LDCommonItem equipCommonItem = m_CurEquipInfo.GetEquipCommonItem();
            KnapsackItem knapsackItem = LDUIPrefabTools.GetKnapsackItemUI(m_icon.rectTransform);
            LDUIPrefabTools.InitKnapsackItem(knapsackItem, equipCommonItem);
            knapsackItem.SetShowDetail(false);

            m_EquipName.text.SetTips(m_CurEquipInfo.EquipCfg.name);
            var slotCfg = Slot.Data.Get(m_CurEquipInfo.EquipCfg.slot);
            m_EquipType.text.SetTips(slotCfg.name);
            m_powerNode.gameObject.gameObject.SetActive(!m_IsShowOtherEquip);
            if (!m_IsShowOtherEquip)
            {
                var power = m_CurEquipInfo.GetEquipPower();
                m_powerNum.text.SetText(UiTools.FormateMoney(power));
            }

            LoadSprite(m_QualityBG.image, m_CurEquipInfo.GetEquipQualityItem().equipmentInfoTopBG);
        }

        public void RefreshEquipAttr()
        {
            Dictionary<int, LDEquipAttrAddition> upAttrDict = new();
            if (!m_IsUp)
            {
                int slotId = m_CurEquipInfo.GetEquipSlotId();
                var equipUp = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);
                var equipUpAttr = equipUp.AttrAddition;
                foreach (LDEquipAttrAddition addition in equipUpAttr)
                {
                    upAttrDict[addition.Id] = addition;
                }
            }

            var equipAttr = m_CurEquipInfo.AttrAddition;
            m_attr.CacheInstanceList();
            foreach (var attr in equipAttr)
            {
                var attrItem = m_attr.GetInstance(true);
                bool isUp = false;
                bool isNew = false;
                bool isDown = false;
                if (!m_IsUp)
                {
                    if (upAttrDict.TryGetValue(attr.Id, out var upAttrAddition))
                    {
                        if (attr.Val > upAttrAddition.Val)
                        {
                            isUp = true;
                        }
                        else if (attr.Val < upAttrAddition.Val)
                        {
                            isDown = true;
                        }
                    }
                    else
                    {
                        isNew = true;
                    }
                }

                attrItem.RefreshAttr(attr, isUp, isDown, isNew);
            }
        }

        private void RefreshLockStatus()
        {
            m_isLock = Global.gApp.gSystemMgr.gNetClientDataMgr.CheckEquipLocked(m_CurEquipInfo.Id);
            m_lockNode.gameObject.SetActive(m_isLock);
            m_unlockNode.gameObject.SetActive(!m_isLock);
        }

        private void RefreshCitiaoList()
        {
            m_citiaoNode.gameObject.SetActive(true);
            m_previewCitiaoNode.gameObject.SetActive(false);

            var citiaoList = m_CurEquipInfo.EquipEffects;
            m_CitiaoItem.CacheInstanceList();
            foreach (LDNetAttrEffect effect in citiaoList)
            {
                var itemUI = m_CitiaoItem.GetInstance(true);
                itemUI.RefreshCitiaoItem(effect, false, false, false);
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_CitiaoItem.gameObject.transform.parent.rectTransform());
        }

        public void RefreshMoreAttr()
        {
            RefreshEquipAttr();
            RefreshCitiaoList();
            AddTimer(0.1f, 1, (a, b) => { LayoutRebuilder.ForceRebuildLayoutImmediate(m_ContentGameObject.rectTransform); });
        }

        private void InitCitiaoInfo()
        {
            if (m_IsShowOtherEquip)
            {
                m_tabNode.gameObject.SetActive(false);
                RefreshCitiaoList();
            }
            else
            {
                var m_EquipmentCfg = m_CurEquipInfo.EquipCfg;

                var m_RandomPoolItem = AttrRandomPool.Data.Get(m_CurEquipInfo.Quality);

                int[] effectGroup = null;
                switch (m_EquipmentCfg.slot)
                {
                    case 1:
                        effectGroup = m_RandomPoolItem.weaponEffectSlot1;
                        break;
                    case 2:
                        effectGroup = m_RandomPoolItem.weaponEffectSlot2;
                        break;
                    case 3:
                        effectGroup = m_RandomPoolItem.weaponEffectSlot3;
                        break;
                    case 4:
                        effectGroup = m_RandomPoolItem.weaponEffectSlot4;
                        break;
                    default:
                        effectGroup = m_RandomPoolItem.equipmentEffect;
                        break;
                }

                m_CitiaoTypeCount = effectGroup.Length;
                m_CitiaoGroup = effectGroup;
                m_CitiaoItem.CacheInstanceList();
                OnCurrentClick();
                if (m_CitiaoTypeCount <= 0)
                {
                    m_tabNode.gameObject.SetActive(false);
                    return;
                }

                m_tabNode.gameObject.SetActive(true);
                m_btn_Preview01.gameObject.SetActive(m_CitiaoTypeCount > 0);
                m_btn_Preview02.gameObject.SetActive(m_CitiaoTypeCount > 1);
                m_btn_Preview03.gameObject.SetActive(m_CitiaoTypeCount > 2);
            }
        }

        #region 页签

        private void RefreshTabSelect(int index)
        {
            m_PreviewCurrent.gameObject.SetActive(m_SelectCitiaoIndex == -1);
            m_PreviewUnCurrent.gameObject.SetActive(m_SelectCitiaoIndex != -1);
            m_PreviewSelect01.gameObject.SetActive(m_SelectCitiaoIndex == 0);
            m_PreviewUnSelect01.gameObject.SetActive(m_SelectCitiaoIndex != 0);
            m_PreviewSelect02.gameObject.SetActive(m_SelectCitiaoIndex == 1);
            m_PreviewUnSelect02.gameObject.SetActive(m_SelectCitiaoIndex != 1);
            m_PreviewSelect03.gameObject.SetActive(m_SelectCitiaoIndex == 2);
            m_PreviewUnSelect03.gameObject.SetActive(m_SelectCitiaoIndex != 2);

            m_xinxi_Node.gameObject.SetActive(m_SelectCitiaoIndex == -1);
            m_btn_Node.gameObject.SetActive(m_SelectCitiaoIndex == -1);
        }

        private void RefreshCitiaoByindex(int index)
        {
            if (index == -1)
            {
                RefreshCitiaoList();
            }
            else
            {
                m_citiaoNode.gameObject.SetActive(false);
                m_previewCitiaoNode.gameObject.SetActive(true);
                int citiaoGroupId = m_CitiaoGroup[index];

                m_PreviewItem.CacheInstanceList();
                AttrEffectGroupItem attrEffectGroupCfg = AttrEffectGroup.Data.Get(citiaoGroupId);
                List<int> hasEffectIds = m_CurEquipInfo.GetEffectIds();
                foreach (string s in attrEffectGroupCfg.effectGroup)
                {
                    if (string.IsNullOrEmpty(s))
                    {
                        continue;
                    }

                    string[] effectInfo = s.Split('_');
                    LDNetAttrEffect effect = new LDNetAttrEffect(LDParseTools.IntParse(effectInfo[0]), 0);
                    EquipmentInfoAUI_PreviewItem itemUI = m_PreviewItem.GetInstance(true);
                    bool isCurrent = hasEffectIds.Contains(effect.EffectId);
                    itemUI.RefreshCitiaoItem(effect, isCurrent);
                    LayoutRebuilder.ForceRebuildLayoutImmediate(itemUI.rectTransform());
                }

                LayoutRebuilder.ForceRebuildLayoutImmediate(m_PreviewContent.rectTransform);
                LDCommonTools.ScrollVeiwVerticalJumpTo(m_previewCitiaoNode.gameObject, 0);
            }
        }

        private void OnCurrentClick()
        {
            m_SelectCitiaoIndex = -1;
            RefreshCitiaoByindex(m_SelectCitiaoIndex);
            RefreshTabSelect(m_SelectCitiaoIndex);
        }

        private void OnPreview1Click()
        {
            m_SelectCitiaoIndex = 0;
            RefreshCitiaoByindex(m_SelectCitiaoIndex);
            RefreshTabSelect(m_SelectCitiaoIndex);
        }

        private void OnPreview2Click()
        {
            m_SelectCitiaoIndex = 1;
            RefreshCitiaoByindex(m_SelectCitiaoIndex);
            RefreshTabSelect(m_SelectCitiaoIndex);
        }

        private void OnPreview3Click()
        {
            m_SelectCitiaoIndex = 2;
            RefreshCitiaoByindex(m_SelectCitiaoIndex);
            RefreshTabSelect(m_SelectCitiaoIndex);
        }

        #endregion

        #region click

        private void OnChangeDisposeBtnClick()
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, m_ChangeBreakDownDisposeKey, !m_ChangeDisposeBtncheckMark.gameObject.activeSelf ? 1 : 0);
            RefreshChangeDisposeSelectStatus();
        }

        private void OnCheckMoreAttrClick()
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.EquipCanSeeMoreAttribution, !m_checkMark.gameObject.activeSelf ? 1 : 0);
            RefreshSeeMoreAttrSelectStatus();
            m_CheckMoreAttrAction?.Invoke();
            RefreshMoreAttr();
        }

        private void OnBreakDownClick()
        {
            Global.gApp.gSystemMgr.gEquipmentMgr.SendEquipBreakDownRequest(new List<long>() { m_CurEquipInfo.Id });
            OnCloseClick();
        }

        private void OnEquipClick()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Equipment, 3);

            Global.gApp.gSystemMgr.gEquipmentMgr.SendEquipPutOnRequest(m_CurEquipInfo.Id, m_CurEquipInfo.GetEquipSlotId());
        }

        private void OnCloseClick()
        {
            Global.gApp.gUiMgr.CloseUI(LDUICfg.EquipmentInfoAUI);
        }

        private void OnLockClick()
        {
            Global.gApp.gSystemMgr.gNetClientDataMgr.SetEquipmentLock(m_CurEquipInfo.Id, !m_isLock);
            RefreshLockStatus();
        }

        private void OnClickRefinement()
        {
            Global.gApp.gUiMgr.OpenUIAsync<EquipmentReforgeUI>(LDUICfg.EquipmentReforgeUI).SetLoadedCall((ui) => { ui.InitEquip(m_CurEquipInfo); });
        }

        #endregion
    }
}