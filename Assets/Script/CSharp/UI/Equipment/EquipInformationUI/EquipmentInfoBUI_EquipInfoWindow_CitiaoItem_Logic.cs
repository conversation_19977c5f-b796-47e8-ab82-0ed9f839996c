namespace LD
{
    public partial class EquipmentInfoBUI_EquipInfoWindow_CitiaoItem
    {
        private LDNetAttrEffect m_CurEquipEffect;

        public void RefreshCitiaoItem(LDNetAttrEffect equipEffect, bool isUp, bool isDown, bool isNew)
        {
            m_CurEquipEffect = equipEffect;

            m_up_arrow.gameObject.SetActive(isUp);
            m_down_arrow.gameObject.SetActive(isDown);
            m_new_arrow.gameObject.SetActive(isNew);

            bool isSelect = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.EquipCanSeeMoreAttribution) == 1;
            var tips = equipEffect.GetDesc(isSelect);
            m_DescTxt.text.SetText(tips);
            LoadSprite(m_IconImg.image, equipEffect.EffectCfg.effectIcon);
            LoadSprite(m_citiao_quality.image, equipEffect.GetEffectPerfectImgPath());
        }
    }
}