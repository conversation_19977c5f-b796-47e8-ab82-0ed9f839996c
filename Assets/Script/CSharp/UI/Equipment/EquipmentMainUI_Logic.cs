using System.Collections.Generic;

namespace LD
{
    public class LDEquipmentUIData : LDUIDataBase
    {
        public int count;
        public List<LDCommonItem> rewards = new();
    }

    public partial class EquipmentMainUI
    {
        private int m_CurShowTab = -1;
        private EquipmentCreationUI m_EquipmentCreateUI;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_btn_create.AddListener(OnCreateClick);
            m_btn_bag.AddListener(OnBagClick);
            m_btn_laba.AddListener(OnLabaClick);

            m_EquipmentCreateUI = m_EquipmentCreationUI.gameObject.GetComponent<EquipmentCreationUI>();
            m_EquipmentCreateUI.ItemInit();
            OnCreateClick();
            Global.gApp.gSystemMgr.gEquipmentMgr.CheckToShowEquip();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(int val)
        {
            if (val == 1)
            {
                if (m_CurShowTab == 1)
                {
                    m_EquipmentCreateUI.OnCallBack();
                }
            }
        }

        public override void OnFreshUI(LDUIDataBase val)
        {
            if (val is LDEquipmentUIData data)
            {
                m_EquipmentCreateUI.ShowCreateAnim(data);
            }
        }

        protected override void RegEventImp(bool addListener)
        {
            Global.gApp.gMsgDispatcher.RegEvent<int, long, long, long>(MsgIds.ItemChange, OnItemChange, addListener);
            Global.gApp.gMsgDispatcher.RegEvent(MsgIds.EquipWeaponRedRefresh, RefreshWeaponRed, addListener);
        }

        private void OnItemChange(int a, long b, long c, long d)
        {
            if (m_CurShowTab == 1)
            {
                m_EquipmentCreateUI.RefreshCost();
            }
        }

        private void RefreshWeaponRed()
        {
            m_EquipmentCreateUI.RefreshWeaponRed();
        }

        protected override void OnOtherUICloseFresh(string uiName)
        {
            if (!Global.gApp.gUiMgr.m_ShowNewEquip)
            {
                m_EquipmentCreateUI.ShowBreakDownFlyItem();
                AddTimer(0.5f, 1, (a, b) => { m_EquipmentCreateUI.ShowEquipEffect(); });
                if (Global.gApp.gSystemMgr.gEquipmentMgr.Data.FurnaceLvUp)
                {
                    Global.gApp.gSystemMgr.gEquipmentMgr.Data.FurnaceLvUp = false;
                    Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.EquipmentLvUpUI);
                }
            }
        }

        private void ChangeView(int tabIndex)
        {
            if (m_CurShowTab == tabIndex)
            {
                return;
            }

            m_CurShowTab = tabIndex;
            ChangeTabState();

            switch (m_CurShowTab)
            {
                case 1:
                    m_EquipmentCreateUI.Show(this);
                    break;
                case 2:
                    m_EquipmentCreateUI.Hide();
                    break;
                case 3:
                    m_EquipmentCreateUI.Hide();
                    break;
            }
        }

        private void ChangeTabState()
        {
            m_img_create_select.gameObject.SetActive(m_CurShowTab == 1);
            m_img_create_unselect.gameObject.SetActive(m_CurShowTab != 1);

            m_img_bag_select.gameObject.SetActive(m_CurShowTab == 2);
            m_img_bag_unselect.gameObject.SetActive(m_CurShowTab != 2);

            m_img_laba_select.gameObject.SetActive(m_CurShowTab == 3);
            m_img_laba_unselect.gameObject.SetActive(m_CurShowTab != 3);
        }

        #region 点击事件

        private void OnCreateClick()
        {
            ChangeView(1);
        }

        private void OnBagClick()
        {
            ChangeView(2);
        }

        private void OnLabaClick()
        {
            ChangeView(3);
        }

        #endregion
    }
}