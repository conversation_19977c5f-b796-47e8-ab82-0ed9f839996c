using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;

namespace LD
{
    public partial class EquipmentAutoScrap
    {
        private bool m_IsDropdownInited;
        private int m_DropdownIndex;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_Sure_btn.AddListener(OnClickSure);
            RefreshView();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }

        private void RefreshView()
        {
            m_IsDropdownInited = false;
            RefreshDropdown();
        }

        private void RefreshDropdown()
        {
            var lv = Global.gApp.gSystemMgr.gEquipmentMgr.Data.FurnaceLevel;
            ManufactureItem cfg = Manufacture.Data.Get(lv);

            var auto = cfg.autoScrapShowQuality.ToList();

            int selectQuality = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.EquipmentAutoBreakDownQuality);


            m_Dropdown.gameObject.SetActive(true);
            m_DropdownIndex = auto.IndexOf(selectQuality) == -1 ? 0 : auto.IndexOf(selectQuality) + 1;

            List<string> subTypeNames = TabNames(cfg);
            Dropdown dropdown = m_Dropdown.gameObject.GetComponent<Dropdown>();
            dropdown.ClearOptions();
            dropdown.AddOptions(subTypeNames);
            dropdown.value = m_DropdownIndex;
            dropdown.AddListener(OnDropdown);

            m_IsDropdownInited = true;
        }

        private void OnDropdown(int index)
        {
            if (!m_IsDropdownInited)
            {
                return;
            }

            m_DropdownIndex = index;
        }

        private void OnClickSure()
        {
            var lv = Global.gApp.gSystemMgr.gEquipmentMgr.Data.FurnaceLevel;
            ManufactureItem cfg = Manufacture.Data.Get(lv);
            var auto = cfg.autoScrapShowQuality.ToList();
            if (m_DropdownIndex > 0)
            {
                int quality = auto[m_DropdownIndex - 1];

                Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.EquipmentAutoBreakDownQuality, quality);
            }
            else
            {
                Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.EquipmentAutoBreakDownQuality, 0);
            }

            Global.gApp.gToastMgr.ShowGameTips(65549);
            TouchClose();
        }

        private List<string> TabNames(ManufactureItem cfg)
        {
            List<string> tabNames = new List<string>();

            tabNames.Add(UiTools.Localize(68221));
            for (int i = 0; i < cfg.autoScrapShowQuality.Length; i++)
            {
                int qua = cfg.autoScrapShowQuality[i];
                string name = LDCommonTipsTools.GetQualityTips(qua);
                tabNames.Add(UiTools.Localize(65547, name));
            }

            return tabNames;
        }
        
    }
}