using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class CampsiteTowerMainUI
    {
        private int m_ShopId = 1003;

        private List<RectTransform_Container> m_SurvivorNodes = new();
        private List<RectTransform_Container> m_DriverNodes = new();


        private LDShowRollResultData m_RollResult;
        private bool m_IsPlayAnim;
        private ParticleSystem m_Saoguang;

        private Vector3 m_ShilianScale;

        private List<LDCommonItem> m_ShowItems = new();
        private int m_ShowPlayIndex;

        protected override void OnInitImp()
        {
            m_ShilianScale = m_shilianAnim.gameObject.transform.localScale;
            m_SurvivorNodes.Add(m_node1);
            m_SurvivorNodes.Add(m_node2);
            m_SurvivorNodes.Add(m_node3);
            m_SurvivorNodes.Add(m_node4);
            m_SurvivorNodes.Add(m_node5);
            m_SurvivorNodes.Add(m_node6);
            m_SurvivorNodes.Add(m_node7);
            m_SurvivorNodes.Add(m_node8);
            m_SurvivorNodes.Add(m_node9);
            m_SurvivorNodes.Add(m_node10);
            m_SurvivorNodes.Add(m_node11);
            m_SurvivorNodes.Add(m_node12);
            m_SurvivorNodes.Add(m_node13);
            m_SurvivorNodes.Add(m_node14);
            m_SurvivorNodes.Add(m_node15);
            m_SurvivorNodes.Add(m_node16);
            m_DriverNodes.Add(m_nodeD1);
            m_DriverNodes.Add(m_nodeD2);
            m_DriverNodes.Add(m_nodeD3);
            m_DriverNodes.Add(m_nodeD4);
            m_DriverNodes.Add(m_nodeD5);
            m_ad_btn.AddListener(OnAdvClick);
            m_danchou_btn.AddListener(OnSingleRoll);
            m_shilian_btn.AddListener(OnTenRoll);

            m_info_btn.AddListener(OnInfoClick);

            RefreshRollsInfo();
            m_btn_close.AddListener(CloseUI);
            m_ad_roll.gameObject.SetActive(false);
            OnFreshUI();

            bool isSkip = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.CampsiteTowerSkipAnim) == 1;
            m_toggle_skip.toggle.isOn = isSkip;
            m_toggle_skip.toggle.onValueChanged.AddListener((arg0 =>
            {
                Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.CampsiteTowerSkipAnim,
                    arg0 ? 1 : 0);
            }));
        }

        private void OnInfoClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.DriverRollTips);
        }

        protected override void OnCloseImp()
        {
            UiTools.CancelPreventFightPowerUI();
        }

        public override void OnFreshUI()
        {
            RefreshRollsInfo();
            RefreshTips(true);
        }

        protected override void OnOtherUICloseFresh(string uiName)
        {
            StartCoroutine(PlayShowItem(m_ShowPlayIndex));
        }

        protected override void RegEventImp(bool addListener)
        {
            base.RegEventImp(addListener);
            Global.gApp.gMsgDispatcher.RegEvent<LDShowRollResultData>(MsgIds.RollShopNotifyResult, OnRollShopNotifyResult, addListener);
            Global.gApp.gMsgDispatcher.RegEvent<int, long, long, long>(MsgIds.ItemChange, OnItemChange, addListener);
        }

        private void OnItemChange(int a, long b, long c, long d)
        {
            RefreshRollsInfo();
        }

        private void OnRollShopNotifyResult(LDShowRollResultData data)
        {
            m_RollResult = data;

            RefreshRollsInfo();
            PlayRollAnim();
        }

        private void RefreshRollsInfo()
        {
            LDNetRollsShopInfo rollsShopInfo = Global.gApp.gSystemMgr.gRollsShopInfoMgr.Data.GetRollInfoByID(m_ShopId);
            if (rollsShopInfo == null)
            {
                this.gameObject.SetActive(false);
                return;
            }
            else
            {
                this.gameObject.SetActive(true);
            }

            RollsShopItem cfgItem = RollsShop.Data.Get(m_ShopId);
            // int guaranteeCount = rollsShopInfo.GuaranteeCount; //保底次数
            // int curCount = rollsShopInfo.Count; //总次数

            // int maxCount = cfgItem.dropNum; //保底次数
            // ADItem ad = AD.Data.Get(cfgItem.adID);
            // int freeCount = ad.adCount; //广告次数
            // int advCount = Global.gApp.gSystemMgr.gAdvertiseMgr.Data.GetAdvertiseUsedCount(ad.adPlacementld); //广告次数

            // int leftCount = Mathf.Max(0, cfgItem.daily_times - curCount);

            // m_ad_roll.SetActive(freeCount > advCount);

            LDCommonItem singleFirst = new LDCommonItem(cfgItem.first_draw_once_consume);
            LDCommonItem singleOther = new LDCommonItem(cfgItem.draw_once_consume);

            LDCommonItem multiFirst = new LDCommonItem(cfgItem.first_multi_draw_consume);
            LDCommonItem multiOther = new LDCommonItem(cfgItem.multi_draw_consume);


            long singleFirstHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(singleFirst.Id);
            long singleOtherHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(singleOther.Id);

            long multiFirstHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(multiFirst.Id);
            long multiOtherHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(multiOther.Id);
            if (singleFirstHaveCount > 0)
            {
                //单抽优先消耗充足，显示优先消耗
                LoadSprite(m_Left_Icon.image, singleFirst.Icon);
                m_Left_Cost.text.text = $"{singleFirstHaveCount}/{singleFirst.Num.ToString()}";
            }
            else
            {
                LoadSprite(m_Left_Icon.image, singleOther.Icon);
                m_Left_Cost.text.SetCostText(singleOtherHaveCount, singleOther.Num);
                // m_Left_Cost.text.SetText(singleOther.Num);
            }

            if (multiFirstHaveCount >= 10)
            {
                LoadSprite(m_Right_Icon.image, multiFirst.Icon);
                m_Right_Cost.text.text = $"{multiFirstHaveCount}/{multiFirst.Num.ToString()}";
            }
            else
            {
                LoadSprite(m_Right_Icon.image, multiOther.Icon);
                m_Right_Cost.text.SetCostText(multiOtherHaveCount, multiOther.Num);
                // m_Right_Cost.text.SetText(multiOther.Num);
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Left_Cost.rectTransform);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Right_Cost.rectTransform);
        }

        private void OnAdvClick()
        {
            // if (m_IsPlayAnim)
            //     return;
            // RollsShopItem cfg = RollsShop.Data.Get(m_ShopId);
            // Global.gApp.gSdkMgr.gADMgr.Show(cfg.adID, LDUICfg.ShopUI, 1);
        }

        private void OnSingleRoll()
        {
            if (m_IsPlayAnim)
                return;
            bool isSend = Global.gApp.gSystemMgr.gRollsShopInfoMgr.SendRollShopBuy(m_ShopId, false);
            if (isSend)
            {
                UiTools.PreventFightPowerUI();
            }
        }

        private void OnTenRoll()
        {
            if (m_IsPlayAnim)
                return;
            bool isSend = Global.gApp.gSystemMgr.gRollsShopInfoMgr.SendRollShopBuy(m_ShopId, true);
            if (isSend)
            {
                UiTools.PreventFightPowerUI();
            }
        }

        private void CloseUI()
        {
            if (m_IsPlayAnim)
                return;
            TouchClose();
            UiTools.CancelPreventFightPowerUI();
        }

        private void RefreshTips(bool show)
        {
            m_Tips.gameObject.SetActive(show);
            if (show)
            {
                RollsShopItem cfgItem = RollsShop.Data.Get(m_ShopId);
                LDNetRollsShopInfo rollsShopInfo = Global.gApp.gSystemMgr.gRollsShopInfoMgr.Data.GetRollInfoByID(m_ShopId);
                int guaranteeCount = rollsShopInfo.GuaranteeCount; //保底次数

                int maxCount = cfgItem.dropNum; //保底次数
                m_Tips.text.SetTips(23012, cfgItem.dropNum - (guaranteeCount % maxCount));
            }
        }

        #region 抽卡动画

        private void PlayRollAnim()
        {
            if (m_toggle_skip.toggle.isOn)
            {
                m_ShowItems.Clear();
                foreach (LDNetRollItem rollItem in m_RollResult.RollItem)
                {
                    LDCommonItem item = rollItem.ItemInfo[0];
                    m_ShowItems.Add(item);
                }

                Global.gApp.gUiMgr.TryShowGetRewardUI(m_ShowItems);
                ClearItems();
                m_IsPlayAnim = false;
                RefreshTips(true);
                UiTools.CancelPreventFightPowerUI();
            }
            else
            {
                StartCoroutine(PlayRollAnimIE());
            }
        }

        private IEnumerator PlayRollAnimIE()
        {
            ClearItems();
            m_IsPlayAnim = true;
            RefreshTips(!m_IsPlayAnim);
            float beginWaitTime = 1f;

            if (m_RollResult.RollItem.Count > 1)
            {
                beginWaitTime = 1f;
                StartCoroutine(PlayShilianAnim());
            }
            else
            {
                beginWaitTime = 1.3f;
                StartCoroutine(PlayDanchouAnim());
            }

            yield return new WaitForSeconds(beginWaitTime);

            m_ShowItems.Clear();
            foreach (LDNetRollItem rollItem in m_RollResult.RollItem)
            {
                LDCommonItem item = rollItem.ItemInfo[0];
                m_ShowItems.Add(item);
                item.SetShowGainDriver(false);
            }

            StartCoroutine(PlayShowItem(0));
        }

        private IEnumerator PlayShowItem(int playIndex)
        {
            float itemIntervalTime = 0.5f;
            float driverIntervalTime = 1.8f;
            PlayShilianAnim(true);
            for (int i = playIndex; i < m_ShowItems.Count; i++)
            {
                m_ShowPlayIndex = i + 1;
                LDCommonItem item = m_ShowItems[i];
                if (item.Type == LDCommonType.Driver)
                {
                    CampsiteTowerMainUI_driverNode driverItem = m_driverNode.GetInstance(true);
                    var parent = GetDriverParentItem();
                    driverItem.gameObject.transform.SetParent(parent.gameObject.transform, false);
                    driverItem.RefreshItem(item);
                    yield return new WaitForSeconds(driverIntervalTime);

                    Global.gApp.gUiMgr.OpenUIAsync<NewDriverUI>(LDUICfg.NewDriverUI).SetLoadedCall(newDriverUI =>
                        newDriverUI.InitView(item)
                    );
                    PlayShilianAnim(false);
                    yield break;
                }
                else
                {
                    CampsiteTowerMainUI_survivorNode survivorItem = m_survivorNode.GetInstance(true);
                    var parent = GetSurvivorParentItem();
                    survivorItem.gameObject.transform.SetParent(parent.gameObject.transform, false);
                    survivorItem.RefreshItem(item);
                    yield return new WaitForSeconds(itemIntervalTime);
                }
            }

            yield return new WaitForSeconds(1f);

            Global.gApp.gUiMgr.TryShowGetRewardUI(m_ShowItems);
            ClearItems();
            m_IsPlayAnim = false;
            RefreshTips(true);
            UiTools.CancelPreventFightPowerUI();
        }

        private RectTransform_Container GetSurvivorParentItem()
        {
            System.Random random = new System.Random();
            int randomNumber = random.Next(0, m_SurvivorNodes.Count);
            var parent = m_SurvivorNodes[randomNumber];
            if (parent.gameObject.transform.childCount > 0)
            {
                return GetSurvivorParentItem();
            }

            return parent;
        }

        private RectTransform_Container GetDriverParentItem()
        {
            System.Random random = new System.Random();
            int randomNumber = random.Next(0, m_DriverNodes.Count);
            var parent = m_DriverNodes[randomNumber];
            if (parent.gameObject.transform.childCount > 0)
            {
                bool haveEmpty = false;
                foreach (var node in m_DriverNodes)
                {
                    if (node.gameObject.transform.childCount <= 0)
                    {
                        haveEmpty = true;
                        break;
                    }
                }

                if (haveEmpty)
                {
                    return GetDriverParentItem();
                }
                else
                {
                    return m_DriverNodes[randomNumber];
                }
            }

            return parent;
        }

        private void ClearItems()
        {
            m_survivorNode.CacheInstanceList();
            m_driverNode.CacheInstanceList();
            m_normalAnim.SetActive(false);
            m_danchouAnim.SetActive(false);
            m_shilianAnim.SetActive(false);
            PlayNormalAnim();
        }


        private void PlayNormalAnim()
        {
            m_normalAnim.SetActive(true);
        }

        private IEnumerator PlayDanchouAnim()
        {
            m_normalAnim.SetActive(false);
            m_danchouAnim.SetActive(true);
            // while (m_IsPlayAnim)
            {
                // var rot = m_danchouAnim.gameObject.transform.localRotation.eulerAngles;
                // m_danchouAnim.gameObject.transform.localRotation = Quaternion.Euler(rot + new Vector3(0, 0, -5f));
                yield return new WaitForEndOfFrame();
            }
        }

        private IEnumerator PlayShilianAnim()
        {
            m_normalAnim.SetActive(false);
            m_shilianAnim.SetActive(true);
            // float time = 0;
            // while (m_IsPlayAnim)
            // {
            //     m_shilianAnim.gameObject.transform.localScale = m_ShilianScale * Mathf.Sin(time) * 0.9f;
            //     time += Time.deltaTime;
            yield return new WaitForEndOfFrame();
            // }
        }

        private void PlayShilianAnim(bool isPlay)
        {
            m_shilianAnim.animator.speed = isPlay ? 1 : 0;
            var childs = m_shilianAnim.gameObject.GetComponentsInChildren<ParticleSystem>();
            foreach (ParticleSystem system in childs)
            {
                var main = system.main;
                if (isPlay)
                {
                    main.simulationSpeed = 1;
                }
                else
                {
                    main.simulationSpeed = 0;
                }
            }
        }

        #endregion
    }
}