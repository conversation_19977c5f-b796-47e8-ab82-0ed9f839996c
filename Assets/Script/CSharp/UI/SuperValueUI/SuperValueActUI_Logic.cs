using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class SuperValueActUI
    {
        private List<int> m_FunctionList = new List<int>() { 3, 4 };

        private LDNetSuperValueActMgr m_ActMgr;
        private List<SuperValueActUI_Tab> m_TabUIs = new List<SuperValueActUI_Tab>();
        private List<LDBaseActivityChildUI> m_UIs = new List<LDBaseActivityChildUI>();
        private int m_CurTabId;

        protected override void OnInitImp()
        {
            m_ActMgr = Global.gApp.gSystemMgr.gSuperValueActMgr;
            m_btn_close.AddListener(TouchClose);

            LDSDKEvent.SendClickEvent("70150220");

            RefreshTab();
        }

        public override void OnFreshUI()
        {
        }

        public void InitData(int id)
        {
            foreach (SuperValueActUI_Tab ui in m_TabUIs)
            {
                if (ui.TabID == id)
                {
                    int defaultId = ui.TabID;
                    OnTab(defaultId);
                    break;
                }
            }
        }

        private void RefreshTab()
        {
            m_Tab.CacheInstanceList();
            m_TabUIs.Clear();
            m_UIs.Clear();


            foreach (LDNetActivityInfo data in m_ActMgr.GetAllActData())
            {
                if (data.GetActType() == LDNetActivityType.DailyRecharge)
                {
                    if (!Global.gApp.gSystemMgr.gDailyRechargeActMgr.IsUnlock())
                    {
                        continue;
                    }
                }

                SuperValueActUI_Tab tabUI = m_Tab.GetInstance(true);
                GameObject panel = CreatePanel(data);
                LDBaseActivityChildUI activityChildUI = panel.GetComponent<LDBaseActivityChildUI>();
                activityChildUI.Init(data, this);
                activityChildUI.RegEvent(true); // 注册事件
                tabUI.RefreshUI(data, panel, OnTab);
                m_TabUIs.Add(tabUI);
                m_UIs.Add(activityChildUI);
            }

            for (int i = 0; i < m_FunctionList.Count; i++)
            {
                int funcId = m_FunctionList[i];
                bool isOpen = false;
                switch (funcId)
                {
                    case 3:
                        isOpen = Global.gApp.gSystemMgr.gPeriodicalGiftMgr.IsUnlock(false);
                        break;
                    case 4:
                        isOpen = Global.gApp.gSystemMgr.gVipMgr.IsUnlock(false);
                        break;
                }

                if (!isOpen)
                    continue;

                SuperValueActUI_Tab tabUI = m_Tab.GetInstance(true);
                GameObject panel = CreatePanel(funcId);
                LDBaseActivityChildUI activityChildUI = panel.GetComponent<LDBaseActivityChildUI>();
                activityChildUI.Init(funcId, this);
                activityChildUI.RegEvent(true); // 注册事件
                tabUI.RefreshUI(funcId, panel, OnTab);
                m_TabUIs.Add(tabUI);
                m_UIs.Add(activityChildUI);
            }

            SelectDefaultTab();
        }

        private void SelectDefaultTab()
        {
            foreach (SuperValueActUI_Tab ui in m_TabUIs)
            {
                if (ui.IsOpen())
                {
                    int defaultId = ui.TabID;
                    OnTab(defaultId);
                    return;
                }
            }

            Global.LogError($"SuperValueActUI  没有在开启中的页面了");
            TouchClose();
        }

        private GameObject CreatePanel(LDNetActivityInfo info)
        {
            GameObject panelGo = null;
            if (info.GetActType().Equals(LDNetActivityType.DailyRecharge))
            {
                panelGo = Global.gApp.gResMgr.InstantiateLoadObj(DailyRechargeUI.DailyRechargeUIPath, ResSceneType.NormalUI, m_ChildUI.rectTransform);
            }

            if (panelGo == null)
            {
                Global.LogError($"SuperValueActUI : panelGo = null    actId = {info.ActivityId} actType = {info.GetActType()}");
            }

            return panelGo;
        }

        private GameObject CreatePanel(int superValueFuncId)
        {
            GameObject panelGo = null;
            switch (superValueFuncId)
            {
                case 3:
                    panelGo = Global.gApp.gResMgr.InstantiateLoadObj(PeriodicalGiftUI.PeriodicalGiftUIPath, ResSceneType.NormalUI, m_ChildUI.rectTransform);
                    break;
                case 4:
                    panelGo = Global.gApp.gResMgr.InstantiateLoadObj(VipPrivilegeUI.VIPUIPath, ResSceneType.NormalUI, m_ChildUI.rectTransform);
                    break;
            }

            if (panelGo == null)
            {
                Global.LogError($"SuperValue function ui :panel go == null, super value id:{superValueFuncId}");
            }

            return panelGo;
        }

        private void OnTab(int id)
        {
            ForceShowCurrency(CurrencyTabUI.Empty);
            for (int i = 0; i < m_TabUIs.Count; i++)
            {
                if (m_TabUIs[i].SetSelection(id))
                {
                    m_CurTabId = id;
                }
            }
        }

        private void OnCrossDay()
        {
            for (int i = m_TabUIs.Count - 1; i >= 0; i--)
            {
                if (!m_TabUIs[i].IsOpen())
                {
                    m_TabUIs[i].gameObject.SetActive(false);
                    m_TabUIs[i].Panel.gameObject.SetActive(false);
                    if (m_TabUIs[i].TabID == m_CurTabId)
                    {
                        SelectDefaultTab();
                    }

                    m_TabUIs.RemoveAt(i);
                }
            }
        }

        public void OnJump(int val)
        {
            for (int i = 0; i < m_TabUIs.Count; i++)
            {
                if (m_TabUIs[i].TabID == m_CurTabId)
                {
                    m_TabUIs[i].GetChildUI().OnJump(val);
                }
            }
        }

        protected override void RegEventImp(bool addListener)
        {
            Global.gApp.gMsgDispatcher.RegEvent(MsgIds.SuperValueUIRedRefresh, OnSuperValueUIRedRefresh, addListener);
            Global.gApp.gMsgDispatcher.RegEvent(MsgIds.OnServerCrossDay, OnCrossDay, addListener);

            if (!addListener)
            {
                foreach (var ui in m_UIs)
                {
                    ui.RegEvent(addListener);
                }
            }
        }

        private void OnSuperValueUIRedRefresh()
        {
            foreach (SuperValueActUI_Tab item in m_TabUIs)
            {
                item.RefreshRedState();
            }
        }

        protected override void OnCloseImp()
        {
        }
    }


    public partial class SuperValueActUI_Tab
    {
        private LDNetActivityInfo m_ActInfo;
        private Action<int> m_OnClick;
        private LDBaseActivityChildUI m_ChildUI;

        public GameObject Panel { get; private set; }
        public int TabID { get; private set; } // SuperValue表id

        public void RefreshUI(LDNetActivityInfo actInfo, GameObject panel, Action<int> onClick)
        {
            m_ActInfo = actInfo;
            Panel = panel;
            m_OnClick = onClick;
            panel.TryGetComponent(out m_ChildUI);

            m_img_btn_Off_bg.button.AddListener(OnClick);


            string actType = actInfo.GetActType();
            if (actType == LDNetActivityType.DailyRecharge)
            {
                RefreshDailyRecharge();
            }
        }

        private void RefreshDailyRecharge()
        {
            DailyRechargeInfoItem dailyRechargeInfoItem = Global.gApp.gSystemMgr.gDailyRechargeActMgr.GetActUIInfo(m_ActInfo.ActivityId);
            SuperValueItem superValueItem = SuperValue.Data.Get(dailyRechargeInfoItem.SuperValueId);
            TabID = superValueItem.id;

            LoadSprite(m_img_btn_On.image, superValueItem.UIBottomMenu[0]);
            LoadSprite(m_img_btn_Off.image, superValueItem.UIBottomMenu[1]);
            m_txt_btn_On.text.SetTips(superValueItem.nameTxt);
            m_txt_btn_Off.text.SetTips(superValueItem.nameTxt);

            RefreshRedState();
        }

        public void RefreshRedState()
        {
            if (!m_IsFunc)
            {
                string actType = m_ActInfo.GetActType();
                if (actType == LDNetActivityType.DailyRecharge)
                {
                    LDRedTipsState redState = Global.gApp.gSystemMgr.gDailyRechargeActMgr.GetRedState(m_ActInfo.ActivityId);
                    m_RedTips.gameObject.GetComponent<RedTips>().FreshState(redState);
                }
            }
            else
            {
                LDRedTipsState funcRed = LDRedTipsState.None;
                switch (TabID)
                {
                    case 3:
                        funcRed = Global.gApp.gSystemMgr.gPeriodicalGiftMgr.GetRedState(LDSystemEnum.PeriodicalGift);
                        break;
                    case 4:
                        funcRed = Global.gApp.gSystemMgr.gVipMgr.GetRedState(LDSystemEnum.VipPrivilege);
                        break;
                }

                m_RedTips.gameObject.GetComponent<RedTips>().FreshState(funcRed);
            }
        }

        public bool SetSelection(int tabId)
        {
            bool isSelect = tabId == TabID;

            m_img_btn_On_bg.gameObject.SetActive(isSelect);
            m_img_btn_Off_bg.gameObject.SetActive(!isSelect);
            Panel.SetActive(isSelect);
            
            if (isSelect)
            {
                m_ChildUI?.RefreshUI();
            }

            return isSelect;
        }

        public LDBaseActivityChildUI GetChildUI()
        {
            return m_ChildUI;
        }

        private void OnClick()
        {
            m_OnClick?.Invoke(TabID);
        }

        public bool IsOpen()
        {
            if (!m_IsFunc)
            {
                return m_ActInfo.IsOpen();
            }
            else
            {
                return true;
            }
        }
    }

    public partial class SuperValueActUI_Tab
    {
        private bool m_IsFunc = false;

        public void RefreshUI(int funcId, GameObject panel, Action<int> onClick)
        {
            TabID = funcId;
            Panel = panel;
            m_OnClick = onClick;
            m_IsFunc = true;

            panel.TryGetComponent(out m_ChildUI);
            m_img_btn_Off_bg.button.AddListener(OnClick);
            RefreshFuncBtn();
            RefreshRedState();
        }

        private void RefreshFuncBtn()
        {
            SuperValueItem superValueItem = SuperValue.Data.Get(TabID);
            LoadSprite(m_img_btn_On.image, superValueItem.UIBottomMenu[0]);
            LoadSprite(m_img_btn_Off.image, superValueItem.UIBottomMenu[1]);
            m_txt_btn_On.text.SetTips(superValueItem.nameTxt);
            m_txt_btn_Off.text.SetTips(superValueItem.nameTxt);
        }
    }
}