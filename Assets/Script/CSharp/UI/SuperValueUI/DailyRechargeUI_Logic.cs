using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class DailyRechargeUI
    {
        public static readonly string DailyRechargeUIPath = "PrefabsN/UI/SuperValue/DailyRechargeUI";
        private LDNetDailyRechargeActMgr m_ActMgr;
        private LDNetActivityInfo m_ActInfo;
        private DailyRechargeInfoItem m_DailyRechargeInfoItem;
        private List<DailyRechargeRewardItem> m_RewardCfgs;

        private float m_PerSec;
        private bool m_IsTab1 = true;

        private float m_ShowItemTimeOffset = 0.1f;
        private List<int> m_WaitShowItemTimer = new();


        private LDPaymentBtnUI m_PaymentBtn1;
        private LDPaymentBtnUI m_PaymentBtn1Un;

        private LDPaymentBtnUI m_PaymentBtn2;
        private LDPaymentBtnUI m_PaymentBtn2Un;

        public override void Init(LDNetActivityInfo info, LDBaseUI parentUI)
        {
            base.Init(info, parentUI);
            m_ActMgr = Global.gApp.gSystemMgr.gDailyRechargeActMgr;
            m_ActInfo = info;
            m_RewardCfgs = m_ActMgr.GetActRewardCfgs(info.ActivityId);

            if (m_PaymentBtn1 == null)
            {
                m_PaymentBtn1 = m_Recharge01Select.gameObject.AddComponent<LDPaymentBtnUI>();
                m_PaymentBtn1.InitBtn(m_Recharge01Select.gameObject, this, null);
            }

            if (m_PaymentBtn1Un == null)
            {
                m_PaymentBtn1Un = m_Recharge01UnSelect.gameObject.AddComponent<LDPaymentBtnUI>();
                m_PaymentBtn1Un.InitBtn(m_Recharge01UnSelect.button, this, null);
            }

            if (m_PaymentBtn2 == null)
            {
                m_PaymentBtn2 = m_Recharge02Select.gameObject.AddComponent<LDPaymentBtnUI>();
                m_PaymentBtn2.InitBtn(m_Recharge02Select.gameObject, this, null);
            }

            if (m_PaymentBtn2Un == null)
            {
                m_PaymentBtn2Un = m_Recharge02UnSelect.gameObject.AddComponent<LDPaymentBtnUI>();
                m_PaymentBtn2Un.InitBtn(m_Recharge02UnSelect.button, this, null);
            }

            m_Help_Btn.gameObject.SetActive(Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
            m_Help_Btn.AddListener(() => { m_helpNode.gameObject.SetActive(true); });
            m_BtnHelpBg.AddListener(() => { m_helpNode.gameObject.SetActive(false); });
            m_helpNode.gameObject.SetActive(false);


            m_Recharge01UnSelect.button.AddListener(OnTab1Click);
            m_Recharge02UnSelect.button.AddListener(OnTab2Click);
        }

        public override void RefreshUI()
        {
            RefreshUI(true);
        }

        public void RefreshUI(bool needAnim)
        {
            m_DailyRechargeInfoItem = Global.gApp.gSystemMgr.gDailyRechargeActMgr.GetActUIInfo(m_ActInfo.ActivityId);

            RefreshBaseUI();
            RefreshTab(needAnim);
        }

        private void RefreshBaseUI()
        {
            LoadSprite(m_Image.image, m_DailyRechargeInfoItem.rewardItemDec);
            m_AD_Txt.text.SetTips(m_DailyRechargeInfoItem.adTxt);
            UpdateTime();
        }

        private void RefreshList(int level, bool needAnim) // 档位
        {
            m_Item.CacheInstanceList();
            int finishDay = m_ActMgr.GetFinishDay(m_ActInfo.ActivityId, level);
            List<float> rechargeData = m_ActMgr.GetValidRechargeData(m_ActInfo.ActivityId, level);
            float rechargeTarget = m_ActMgr.GetRechargeLevelTarget(m_ActInfo.ActivityId, level);
            List<DailyRechargeUI_Item> itemUIs = new List<DailyRechargeUI_Item>();
            foreach (int i in m_WaitShowItemTimer)
            {
                m_ParentUI.RemoveTimer(i);
            }

            m_WaitShowItemTimer.Clear();
            for (int i = 0; i < m_RewardCfgs.Count; i++)
            {
                DailyRechargeRewardItem item = m_RewardCfgs[i];
                DailyRechargeUI_Item itemUI = m_Item.GetInstance(true);
                bool isFinished = item.day <= finishDay;
                bool isRewarded = m_ActMgr.IsRewarded(m_ActInfo.ActivityId, item.day, level);
                bool isLock = m_ActMgr.IsTodayFinished(m_ActInfo.ActivityId, level) ? !isFinished : item.day > finishDay + 1;
                float recharge = 0f;
                if (i < rechargeData.Count)
                {
                    recharge = rechargeData[i];
                }

                itemUI.InitUI(item, level, isFinished, isRewarded, isLock, recharge, rechargeTarget);
                itemUI.SetIsBest(false);
                itemUI.RefreshUI();
                itemUIs.Add(itemUI);
                itemUI.SetAnimEnable(needAnim);
                if (needAnim)
                {
                    itemUI.gameObject.SetActive(false);
                }
            }

            // 额外奖励
            DailyRechargeUI_Item itemExtraUI = m_Item.GetInstance(true);
            bool isExtraFinished = m_ActMgr.GetFinishDay(m_ActInfo.ActivityId, level) >= m_RewardCfgs.Count;
            bool isExtraRewarded = m_ActMgr.IsExtraRewarded(m_ActInfo.ActivityId, level);
            bool isExtraLock = !isExtraFinished;
            itemExtraUI.InitExtraUI(m_ActInfo.ActivityId, level, isExtraFinished, isExtraRewarded, isExtraLock, m_RewardCfgs.Count);
            itemExtraUI.SetIsBest(true);
            itemExtraUI.RefreshUI();
            itemUIs.Add(itemExtraUI);
            itemExtraUI.rectTransform().SetParent(m_LastDay.gameObject.transform);
            itemExtraUI.rectTransform().localPosition = Vector3.zero;
            itemExtraUI.SetAnimEnable(needAnim);
            if (needAnim)
            {
                itemExtraUI.gameObject.SetActive(false);
            }


            itemUIs.Sort((a, b) => { return a.GetSortNum() - b.GetSortNum(); });
            int index = 0;
            foreach (DailyRechargeUI_Item ui in itemUIs)
            {
                ui.transform.SetAsLastSibling();
                bool isBest = ui.GetIsBest();
                if (isBest)
                {
                    ui.gameObject.SetActive(true);
                }
                else
                {
                    index++;
                    int timerId = m_ParentUI.AddTimer(index * m_ShowItemTimeOffset, 1, (a, b) => { ui.gameObject.SetActive(true); });
                    m_WaitShowItemTimer.Add(timerId);
                }
            }
        }

        private void RefreshTab(bool needAnim)
        {
            float target1 = m_ActMgr.GetRechargeLevelTarget(m_ActInfo.ActivityId, 1);
            float target2 = m_ActMgr.GetRechargeLevelTarget(m_ActInfo.ActivityId, 2);
            string formatTarget1 = Global.gApp.gSdkMgr.gPaymentMgr.GetCurrencyPrice(target1, true, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
            string formatTarget2 = Global.gApp.gSdkMgr.gPaymentMgr.GetCurrencyPrice(target2, true, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);

            m_PaymentBtn1.ShowText(formatTarget1, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
            m_PaymentBtn1Un.ShowText(formatTarget1, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
            m_PaymentBtn2.ShowText(formatTarget2, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
            m_PaymentBtn2Un.ShowText(formatTarget2, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);

            // m_txt_RechargeSelect01.text.SetText(formatTarget1);
            // m_txt_RechargeUnselect01.text.SetText(formatTarget1);
            // m_txt_RechargeSelect02.text.SetText(formatTarget2);
            // m_txt_RechargeUnselect02.text.SetText(formatTarget2);
            m_Recharge01RedTips.gameObject.GetComponent<RedTips>().FreshState(m_ActMgr.GetLevelTabRedState(m_ActInfo.ActivityId, 1));
            m_Recharge02RedTips.gameObject.GetComponent<RedTips>().FreshState(m_ActMgr.GetLevelTabRedState(m_ActInfo.ActivityId, 2));


            if (m_IsTab1)
            {
                OnTab1(needAnim);
            }
            else
            {
                OnTab2(needAnim);
            }
        }

        private void UpdateTime()
        {
            string time = Global.gApp.gGameData.GetExpireTimeTips2(m_ActInfo.EndTime);
            m_Time_Txt.text.SetText(time);
        }

        private void Update()
        {
            m_PerSec += Time.deltaTime;
            if (m_PerSec >= 1f)
            {
                m_PerSec -= 1f;

                UpdateTime();
            }
        }

        private void OnTab1Click()
        {
            OnTab1(true);
        }

        private void OnTab1(bool needAnim)
        {
            m_IsTab1 = true;
            m_Recharge01Select.gameObject.SetActive(true);
            m_Recharge01UnSelect.gameObject.SetActive(false);
            m_Recharge02Select.gameObject.SetActive(false);
            m_Recharge02UnSelect.gameObject.SetActive(true);
            RefreshList(1, needAnim);
        }

        private void OnTab2Click()
        {
            OnTab2(true);
        }

        private void OnTab2(bool needAnim)
        {
            m_IsTab1 = false;
            m_Recharge01Select.gameObject.SetActive(false);
            m_Recharge01UnSelect.gameObject.SetActive(true);
            m_Recharge02Select.gameObject.SetActive(true);
            m_Recharge02UnSelect.gameObject.SetActive(false);
            RefreshList(2, needAnim);
        }

        public override void RegEvent(bool addListener)
        {
            Global.gApp.gMsgDispatcher.RegEvent<int>(MsgIds.ActDailyRechargeRefresh, OnActDailyRechargeRefresh, addListener);
        }

        private void OnActDailyRechargeRefresh(int actId)
        {
            if (m_ActInfo.ActivityId == actId)
            {
                RefreshUI(false);
            }
        }
    }

    #region Item Class

    public partial class DailyRechargeUI_Item
    {
        private LDNetDailyRechargeActMgr m_ActMgr;
        private DailyRechargeRewardItem m_Cfg;
        private int m_Level;
        private bool m_IsFinished;
        private bool m_IsRewarded;
        private bool m_IsLock;
        private float m_Recharge;
        private float m_RechargeTarget;
        private Animator m_Anim;
        private bool m_IsBest = false;
        private int m_ActId;
        private int m_TotalCount;

        private void InitUIBase()
        {
            m_ActMgr = Global.gApp.gSystemMgr.gDailyRechargeActMgr;
            if (m_Anim == null)
            {
                m_Anim = this.gameObject.GetComponent<Animator>();
            }

            m_RechargeBtn.button.AddListener(OnRecharge);
            m_LockBtn.button.AddListener(OnLock);
            m_RewardedBtn.button.AddListener(OnRewarded);
            m_GetBtn.button.AddListener(OnGet);
        }

        public void InitUI(DailyRechargeRewardItem cfg, int level, bool isFinished, bool isRewarded, bool isLock, float recharge, float rechargeTarget)
        {
            InitUIBase();

            m_Cfg = cfg;
            m_Level = level;
            m_IsFinished = isFinished;
            m_IsRewarded = isRewarded;
            m_IsLock = isLock;
            m_Recharge = recharge;
            m_RechargeTarget = rechargeTarget;
            m_ActId = m_Cfg.activityID;
        }

        public void InitExtraUI(int actId, int level, bool isFinished, bool isRewarded, bool isLock, int count)
        {
            InitUIBase();
            m_ActId = actId;
            m_Level = level;
            m_IsFinished = isFinished;
            m_IsRewarded = isRewarded;
            m_IsLock = isLock;
            m_TotalCount = count;
        }

        public void SetIsBest(bool isBest)
        {
            m_IsBest = isBest;
        }

        public bool GetIsBest()
        {
            return m_IsBest;
        }

        public void RefreshUI()
        {
            RefreshBaseInfo();
            RefreshRewards();
            RefreshProgress();
            RefreshBtns();
            RefreshRedTips();
        }

        public void SetAnimEnable(bool isEnabled)
        {
            if (m_Anim != null)
            {
                m_Anim.enabled = isEnabled;
                if (!isEnabled)
                {
                    m_Anim.Play("fxani_huatiao", 0, 1);
                }
            }
        }

        public void RefreshBaseInfo()
        {
            if (!m_IsBest)
            {
                m_Day_BG_Normal.gameObject.SetActive(true);
                m_Day_BG_Expensive.gameObject.SetActive(false);
                m_BG.gameObject.SetActive(true);
                m_BG_Expensive.gameObject.SetActive(false);
                string formatTarget = Global.gApp.gSdkMgr.gPaymentMgr.GetCurrencyPrice(m_RechargeTarget, true, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
                m_Day_Txt.text.SetTips(79606, m_Cfg.day, formatTarget);
            }
            else
            {
                m_Day_BG_Normal.gameObject.SetActive(false);
                m_Day_BG_Expensive.gameObject.SetActive(true);
                m_BG.gameObject.SetActive(false);
                m_BG_Expensive.gameObject.SetActive(true);
                m_Extra_Txt.text.SetTips(79610, m_TotalCount);
            }
        }

        public void RefreshRewards()
        {
            m_Item.CacheInstanceList();
            if (!m_IsBest)
            {
                string[] rewardCfg;
                if (m_Level == 1)
                {
                    rewardCfg = m_Cfg.Reward01;
                }
                else
                {
                    rewardCfg = m_Cfg.Reward02;
                }

                foreach (string rwd in rewardCfg)
                {
                    DailyRechargeUI_Item_Item rewardUI = m_Item.GetInstance(true);
                    LDCommonItem commonItem = new LDCommonItem(rwd);
                    RectTransform rt = rewardUI.ScaleNode.rectTransform;
                    LDCommonTools.DestoryChildren(rt);
                    LDUIPrefabTools.GetKnapsackItemUI(commonItem, rt);
                }
            }
            else
            {
                List<LDCommonItem> rewards = new List<LDCommonItem>();
                foreach (DailyRechargeEndItem item in DailyRechargeEnd.Data.items)
                {
                    if (item.activityID == m_ActId)
                    {
                        if (m_Level == 1)
                        {
                            foreach (string rwd in item.Reward01)
                            {
                                rewards.Add(new LDCommonItem(rwd));
                            }
                        }
                        else if (m_Level == 2)
                        {
                            foreach (string rwd in item.Reward02)
                            {
                                rewards.Add(new LDCommonItem(rwd));
                            }
                        }

                        break;
                    }
                }

                foreach (LDCommonItem rwd in rewards)
                {
                    DailyRechargeUI_Item_Item rewardUI = m_Item.GetInstance(true);
                    RectTransform rt = rewardUI.ScaleNode.rectTransform;
                    LDCommonTools.DestoryChildren(rt);
                    LDUIPrefabTools.GetKnapsackItemUI(rwd, rt);
                }
            }
        }

        private void RefreshProgress()
        {
            m_Num.text.SetText(string.Empty);
            m_img_exp_preview.gameObject.SetActive(false);

            if (m_IsBest)
            {
                m_Num.text.SetText(m_ActMgr.GetFinishedDayStr(m_ActId, m_Level));
            }
            else
            {
                float val = Mathf.Min(m_RechargeTarget, m_Recharge);
                string formatVal = Global.gApp.gSdkMgr.gPaymentMgr.GetCurrencyPrice(val, true, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
                string formatTarget = Global.gApp.gSdkMgr.gPaymentMgr.GetCurrencyPrice(m_RechargeTarget, true, Global.gApp.gSystemMgr.gPaymentMgr.Data.IsUseProxyCurrency);
                m_Num.text.SetText($"{formatVal}/{formatTarget}");
                m_img_exp_preview.gameObject.SetActive(true);
                m_img_exp_preview.image.fillAmount = val / m_RechargeTarget;
            }
        }

        private void RefreshBtns()
        {
            m_RewardedBtn.gameObject.SetActive(m_IsRewarded);
            m_LockBtn.gameObject.SetActive(m_IsLock);
            m_GetBtn.gameObject.SetActive(m_IsFinished && !m_IsRewarded);
            m_RechargeBtn.gameObject.SetActive(!m_IsLock && !m_IsFinished);
        }

        private void RefreshRedTips()
        {
            LDRedTipsState state = LDRedTipsState.None;
            if (m_IsFinished && !m_IsRewarded)
            {
                state = LDRedTipsState.RedPoint;
            }

            m_RedTips.gameObject.GetComponent<RedTips>().FreshState(state);
        }

        public int GetSortNum()
        {
            if (m_IsRewarded)
            {
                return 1;
            }

            return 0;
        }

        private void OnRecharge()
        {
            //LDPageJumpTools.TryJumpPage(LDSystemEnum.Shop_Diamond, true);
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.QuickRechargeUI);
        }

        private void OnLock()
        {
        }

        private void OnRewarded()
        {
        }

        private void OnGet()
        {
            if (!m_IsBest)
            {
                Global.gApp.gSystemMgr.gDailyRechargeActMgr.SendReward(m_ActId, m_Level, m_Cfg.day);
            }
            else
            {
                Global.gApp.gSystemMgr.gDailyRechargeActMgr.SendExtraReward(m_ActId, m_Level);
            }
        }
    }

    #endregion Item Class
}