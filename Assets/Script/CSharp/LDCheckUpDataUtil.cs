using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDCheckUpDataUtil: LDBaseMono
    {
        public LD.Version.UpdateVer RomateResVersion = new LD.Version.UpdateVer();
        public void StartCheckUpdate()
        {
            if(RuntimeSettings.ApplyUpdate)
            {
                Global.gApp.gTimerMgr.AddTimer(60, -1, DownLoadRemateVersionByHttp_Step7);
            }
        }
        /// <summary>
        /// 下载服务器 的版本号 先进行 http 下载然后在进行ip 下载
        /// </summary>
        private void DownLoadRemateVersionByHttp_Step7(float a,bool b)
        {
            if (Global.gApp.CurScene is CampsiteScene)
            {
                if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.UpConfirm) != null)
                {
                    return;
                }
                foreach (string url in RuntimeSettings.VersionURL)
                {
                    Load(MainIoUtils.GetVersionHttpPath(url, RuntimeSettings.CurResVersion), OnDownLoadRemateVersionByIp_Step7, 5);
                }
            }
        }
        private void OnDownLoadRemateVersionByIp_Step7(string txt, bool result, long code)
        {
            //404 表示服务器没配置更新 文件 直接下一步了
            if (!result)
            {
                return;
            }
            OnGetRemoteVersion_Step7(txt, result, code);
        }
        private void OnGetRemoteVersion_Step7(string txt, bool result, long code)
        {
            // 不加载的时候 version 里面 默认 都是-1 
            if (!string.IsNullOrEmpty(txt))
            {
                // 记录一下远端服务器 Version文件
                RomateResVersion.LoadRemoteVersion(txt);
                CompareAppVersion_Step8();
            }
        }
        private void CompareAppVersion_Step8()
        {
            if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.UpConfirm) != null)
            {
                return;
            }
            // 服务器的引擎版本高 需要 更新整包 。挑战到 appStore
            if (RomateResVersion.GetBigVersion() > RuntimeSettings.CurResVersion.GetBigVersion())
            {
                Global.gApp.gUiMgr.OpenUIAsync<UpConfirm>(LDUICfg.UpConfirm).SetLoadedCall(
                    confirmUI =>
                    {
                        confirmUI.SetInfo(Global.gApp.QuitGame, -1, -1);
                    }
                    );
                return;
            }
            // 引擎版本号相等 服务器 资源版本号高 则更新 游戏 
            else if (RomateResVersion.GetBigVersion() == RuntimeSettings.CurResVersion.GetBigVersion() &
               RomateResVersion.GetSmallVersion() > RuntimeSettings.CurResVersion.GetSmallVersion())
            {
                string customVersion = RomateResVersion.MainVersionData.custom;
                if (customVersion != null && customVersion.Contains(RuntimeSettings.CurResVersion.MainVersionData.v))
                {
                    return;
                }
                else if (customVersion != null && customVersion.Equals("dqx"))
                {
                    return;
                }
                Global.gApp.gUiMgr.OpenUIAsync<UpConfirm>(LDUICfg.UpConfirm).SetLoadedCall(confirmUI =>
                {
                    confirmUI.SetInfo(Global.gApp.QuitGame, -1, -1);
                });
            }
        }
    }
}
