using UnityEngine;

namespace LD
{
    
    public abstract class BaseScene
    {
        public static float TimeScale { set; get;}

        public LDBattleCamera BattleCamera { private set; get; }
        protected int m_PauseRef = 0;
        public LDTimerMgr TimerMgrRender { private set; get; }
        public static float GetDtTime()
        {
            return Time.deltaTime * TimeScale;
        }
        public virtual void Init()
        {
            Global.gApp.CurScene = this;
            TimerMgrRender = new LDTimerMgr();
            TimeScale = 1;
            m_PauseRef = 0;
            CloseLoadUI();
        }
        public virtual void CloseLoadUI()
        {
            Global.gApp.gUiMgr.CloseLoadingUI();
        }
        protected virtual void InitCamera(string path)
        {
            BattleCamera = new LDBattleCamera();
            BattleCamera.Init(path);
        }
        protected virtual void PlayBGM(string bgmPath)
        {
            Global.gApp.gAudioSource.PlayBGM(bgmPath);
        }
        public virtual void Update(float dt)
        {
            TimerMgrRender.OnDUpdate(dt);
        }
        public virtual void OnDestroy()
        {
            Global.gApp.CurScene = null;
            TimeScale = 1;
            BattleCamera.DestroySelf();
        }
    }
}
