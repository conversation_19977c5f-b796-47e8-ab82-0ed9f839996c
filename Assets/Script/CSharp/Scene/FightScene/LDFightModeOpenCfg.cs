using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDPassType
    {
        public static int None = 0;
        public static int MainPass = 1;
        public static int Tower = 2;
        public static int Coin = 3;
        public static int Expedition = 4;
        public static int PveTeam = 5;
        public static int Season = 6;
        public static int Guild = 7;
        public static int Arena = 8;
        public static int GuildLeague = 9;
        public static int LeaguePve = 10;
        public static int CSArena = 11;
        public static int ActScore = 12;
        
        public static int CrossArena = 98; // 暂时不用
        public static int All = 99;// 全部
    }
    public class LDFightModeOpenCfg
    {
        // 能使用战斗加速的 场景
        public static HashSet<int> FightSpeedMode = new HashSet<int>()
        {
                LDPassType.MainPass,LDPassType.Season,LDPassType.Tower,LDPassType.Coin,LDPassType.Expedition,LDPassType.Guild,
            LDPassType.LeaguePve,LDPassType.ActScore
        };      
        // 广告试用的 场景
        public static HashSet<int> MagnetMode = new HashSet<int>()
        {
            LDPassType.MainPass,LDPassType.Season,LDPassType.Tower,LDPassType.ActScore
        };

        // 广告试用的 场景
        public static HashSet<int> ContinueFightMode = new HashSet<int>()
        {
            LDPassType.MainPass,LDPassType.Season 
        };

        public static bool CanContiuneFight()
        {
            if(!ContinueFightMode.Contains(Global.gApp.CurFightScene.PassType))
            {
                return false;
            }

            if(Global.gApp.CurFightScene.gPassHandler is LDGuidPassHandle_4000)
            {
                return false;
            }

            if (Global.gApp.CurFightScene.PassType == LDPassType.Season)
            {
                int missionId = Global.gApp.CurFightScene.gPassData.MisssionId;
                if (Global.gApp.gSystemMgr.gSeasonMgr.IsEndlessMode(missionId)) // 无尽模式不让续打
                {
                    return false;
                }
            }
            string condition1 = GlobalCfg.Data.Get(LDGlobalConfigId.ContinueFighting).valueString;
            if (Global.gApp.gSystemMgr.gFilterMgr.Filter(condition1))
            {
                return true;
            }
            return false;
        }
    }
}
