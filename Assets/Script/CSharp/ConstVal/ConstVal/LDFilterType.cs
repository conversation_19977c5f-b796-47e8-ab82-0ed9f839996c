using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LDFilterType
{
    //无条件
    public static int NONE = 0; 

    // 根据filter 修改行为
    
    public static int MapEventType = 1;      //1.完成x次指定类型的探索事件（类型，数量）
    public static int MainPass = 2;     //2.通关指定关卡（PassID）
    public static int MapEvent = 3;     //3.完成指定探索事件（MapEventID）
    public static int Destination = 4;      //4.前往指定目标点（MapMainID，目标点）
    public static int MapProgress = 6;      //6 地图探索度
    public static int Crystal = 7;      //7.信号塔等级
}
