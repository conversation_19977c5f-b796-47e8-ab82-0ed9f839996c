using System.Collections.Generic;
using Shining.VibrationSystem;

namespace LD
{
    public class LDVibration
    {
        private Dictionary<long, Vibe> m_Vibes = new Dictionary<long, Vibe>();

        public void Vibrate(long ms)
        {
            try
            {
                Vibe vibe = GetVibe(ms);
                if (vibe != null)
                {
                    Vibrations.instance.Vibrate(vibe);
                }
            }
            catch
            {
                // ignored
            }
        }

        private Vibe GetVibe(long ms)
        {
            if (m_Vibes.ContainsKey(ms))
            {
                return m_Vibes[ms];
            }

            Vibe vibe = new Vibe(ms);
            m_Vibes.Add(ms, vibe);
            return vibe;
        }
    }
}