using UnityEngine;

namespace LD
{
    public class LDArenaBodyBlockInstaller : IDYDestroy
    {
        private LDBodyBlock m_BodyBlock;
        private Transform m_BodyBlockTsf;
        private ArenaMechaDiyUI m_DIYUI;
        private LDCreature m_Creature2;
        private Vector3 m_RecordMousePos;
        private Vector3 tartForward;
        private bool m_CanAttatch = false;
        private bool m_FromCreature = false;

        public LDArenaBodyBlockInstaller(ArenaMechaDiyUI diyUI, LDCreature creature2)
        {
            m_DIYUI = diyUI;
            m_Creature2 = creature2;
            m_Creature2.TryAddMeshCollider(m_Creature2.ModeNode.gameObject);
        }

        public void InitByUI(DIYPartCfgItem partCfgItem, LDCreature creature2)
        {
            m_Creature2 = creature2;
            LDDIYPartData partData = new LDDIYPartData();
            partData.PId = partCfgItem.id;
            partData.SId = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartSkinId(partCfgItem.id);
            partData.HSId = Global.gApp.gSystemMgr.gMechaPartSkinePart.ISPartSkineHide(partData.SId) ? 1 : 0;
            LDBodyBlock bodyBlock2 = new LDBodyBlock();
            bodyBlock2.Init(partData, creature2);
            m_BodyBlock = bodyBlock2;
            m_BodyBlockTsf = m_BodyBlock.BodyBlockNode;
            m_FromCreature = false;
            OnDUpdate(0);
            UpdataShadowMat();
        }

        public void InitByRayBlock(Collider meshCollider)
        {
            m_DIYUI.OnCanSave();
            m_FromCreature = true;
            m_BodyBlock = m_DIYUI.GetCreature().GetBodyBlockByMeshCollider(meshCollider);
            m_BodyBlockTsf = m_BodyBlock.BodyBlockNode;
            m_BodyBlock.DeAttach();
            OnDUpdate(0);
            UpdataShadowMat();
        }

        private Vector3 GetMousePos()
        {
            return Input.mousePosition + new Vector3(0, 50, 0);
        }

        private RaycastHit GetRayHit(out bool rayBody)
        {
            Ray ray = m_DIYUI.RayCamera.ScreenPointToRay(GetMousePos());

            RaycastHit raycastHit = new RaycastHit();
            if (Physics.Raycast(ray, out raycastHit, Mathf.Infinity, LDFightConstVal.MechaBodyMask))
            {
                Vector3 worldPos = raycastHit.point;
                m_BodyBlockTsf.position = worldPos;
                SetTargetForward(raycastHit.normal);
                rayBody = true;
                return raycastHit;
            }
            else
            {
                Vector3 forward = m_DIYUI.RayCamera.transform.forward;
                forward = -forward;
                forward.y = 0;
                Plane basePlane = new Plane(forward, m_BodyBlock.Creature.MechaNode.transform.position);
                Vector3 worldPos = TransScreenPos2WorldPos(basePlane);

                var dir = (m_BodyBlock.Creature.MechaNode.transform.position - worldPos).normalized;
                ray = new Ray(worldPos, dir);
                rayBody = false;
                if (Physics.Raycast(ray, out raycastHit, (m_BodyBlock.Creature.MechaNode.transform.position - worldPos).magnitude, LDFightConstVal.MechaBodyMask))
                {
                    Vector3 worldHit = raycastHit.point;
                    if ((worldHit - worldPos).magnitude < 1f)
                    {
                        m_BodyBlockTsf.position = worldHit;
                        SetTargetForward(raycastHit.normal);
                        rayBody = true;
                        return raycastHit;
                    }
                    else
                    {
                        SetTargetForward(raycastHit.normal);
                        rayBody = false;
                        return raycastHit;
                    }
                }

                return raycastHit;
            }
        }

        RaycastHit raycastHit;

        private void SetTargetForward(Vector3 forward)
        {
            tartForward = forward;
        }

        private float GetAdjuestVal(float val)
        {
            float splitVal = 90;
            float leftHoldVal = 10;
            float rightHoldVal = splitVal - leftHoldVal;
            if (val > 0)
            {
                float val2 = val % splitVal;
                if (val2 < leftHoldVal)
                {
                    val -= val2;
                }
                else if (val2 > rightHoldVal)
                {
                    val += (splitVal - val2);
                }
            }
            else if (val < 0)
            {
                float absVal = Mathf.Abs(val);
                float val2 = absVal % splitVal;
                if (val2 < leftHoldVal)
                {
                    val += val2;
                }
                else if (val2 > rightHoldVal)
                {
                    val -= (splitVal - val2);
                }
            }

            return val;
        }

        private void HandleInput()
        {
            raycastHit = GetRayHit(out m_CanAttatch);
            if (m_CanAttatch)
            {
                SetTargetForward(raycastHit.normal);
                //m_BodyBlockTsf.SetParent(null, true);
                //m_BodyBlockTsf.localScale = Vector3.one;
                if (m_BodyBlock.PartItem.install == 1)
                {
                    Vector3 forward = tartForward;
                    Vector3 right = m_BodyBlock.Creature.MechaNode.transform.forward;
                    Vector3 up = Vector3.Cross(right, forward).normalized;
                    m_BodyBlockTsf.rotation = Quaternion.Lerp(m_BodyBlockTsf.rotation, Quaternion.LookRotation(forward, up), 20 * Time.deltaTime);
                    Vector3 angel = m_BodyBlockTsf.localEulerAngles;
                    angel.x = GetAdjuestVal(angel.x);
                    angel.y = GetAdjuestVal(angel.y);
                    angel.z = GetAdjuestVal(angel.z);
                    m_BodyBlockTsf.localEulerAngles = angel;
                }
                else
                {
                    m_BodyBlockTsf.forward = Vector3.Lerp(m_BodyBlockTsf.forward, tartForward, 20 * Time.deltaTime);

                    Vector3 angel = m_BodyBlockTsf.localEulerAngles;
                    angel.x = GetAdjuestVal(angel.x);
                    angel.y = GetAdjuestVal(angel.y);
                    angel.z = GetAdjuestVal(angel.z);
                    m_BodyBlockTsf.localEulerAngles = angel;
                }
            }
            else
            {
                Vector3 planeforward = m_DIYUI.RayCamera.transform.forward;
                planeforward = -planeforward;
                planeforward.y = 0;
                Plane basePlane = new Plane(planeforward, m_BodyBlock.Creature.MechaNode.transform.position);
                Vector3 initPos = TransScreenPos2WorldPos(basePlane);
                m_BodyBlockTsf.position = initPos;
                if (m_BodyBlock.PartItem.install == 1)
                {
                    Vector3 forward = tartForward;
                    Vector3 right = m_BodyBlock.Creature.MechaNode.transform.forward;
                    Vector3 up = Vector3.Cross(right, forward).normalized;
                    m_BodyBlockTsf.rotation = Quaternion.Lerp(m_BodyBlockTsf.rotation, Quaternion.LookRotation(forward, up), 10 * Time.deltaTime);
                }
                else
                {
                    m_BodyBlockTsf.forward = Vector3.Lerp(m_BodyBlockTsf.forward, tartForward, 10 * Time.deltaTime);
                }
            }
        }

        public void OnDUpdate(float dt)
        {
            if (Input.GetMouseButton(0))
            {
                HandleInput();
            }
            else
            {
                if (m_CanAttatch)
                {
                    Transform parentBone = FindTargetNode(raycastHit);
                    if (parentBone != null)
                    {
                        m_BodyBlock.TryAttach(parentBone.name);
                    }
                    else
                    {
                        m_BodyBlock.DestroyBodyBlock();
                    }

                    m_DIYUI.OnCanSave();
                }
                else
                {
                    m_BodyBlock.DeAttach();
                }

                m_DIYUI.DestroyBodyBlockInstaller(m_BodyBlock);
                m_BodyBlock = null;
            }

            AddEffect();
            m_RecordMousePos = GetMousePos();
        }

        private GameObject m_AttatchGo;
        private GameObject m_JinZhiGo;

        private void DestroyJinZhiGo()
        {
            if (m_JinZhiGo != null)
            {
                Global.gApp.gResMgr.DestroyGameObj(m_JinZhiGo);
                m_JinZhiGo = null;
            }
        }

        private void DestroyAttatchGo()
        {
            if (m_AttatchGo != null)
            {
                Global.gApp.gResMgr.DestroyGameObj(m_AttatchGo);
                m_AttatchGo = null;
            }
        }

        private void AddEffect()
        {
            if (m_BodyBlock != null)
            {
                if (m_CanAttatch)
                {
                    DestroyJinZhiGo();
                    if ((GetMousePos() - m_RecordMousePos).sqrMagnitude >= 1)
                    {
                        if (m_AttatchGo == null)
                        {
                            m_AttatchGo = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.UI_DIY_FangZhi, ResSceneType.NormalRes, m_BodyBlock.BodyBlockNode);
                        }
                    }
                    else
                    {
                        DestroyAttatchGo();
                    }
                }
                else
                {
                    DestroyAttatchGo();
                    if (m_JinZhiGo == null)
                    {
                        m_JinZhiGo = Global.gApp.gResMgr.InstantiateEffectObj(LDEffectCfg.UI_DIY_JinZhi, ResSceneType.NormalRes, m_BodyBlock.BodyBlockNode);
                    }
                }
            }
        }

        private Transform FindTargetNode(RaycastHit raycastHit)
        {
            //Transform nearTsf = null;
            //float disSqr = 1000;
            //Vector3 poisition = m_BodyBlockTsf.position;
            //foreach (KeyValuePair<string, Transform> boneData in m_BodyBlock.Creature.BoneData)
            //{
            //    float newDisSqr = (poisition - boneData.Value.position).sqrMagnitude;
            //    if (newDisSqr < disSqr)
            //    {
            //        nearTsf = boneData.Value;
            //        disSqr = newDisSqr;
            //    }
            //}
            //return nearTsf;

            try
            {
                Mesh mesh = null;
                SkinnedMeshRenderer skinnedMeshRenderer = null;
                foreach (SkinnedMeshRenderer item in m_Creature2.RootSkineMeshRender)
                {
                    if (item.gameObject == raycastHit.collider.gameObject)
                    {
                        mesh = item.sharedMesh;
                        skinnedMeshRenderer = item;
                        break;
                    }
                }

                if (mesh == null)
                {
                    return raycastHit.collider.transform;
                }

                BoneWeight[] boneWeights = mesh.boneWeights;
                float maxWeight = raycastHit.barycentricCoordinate[0];
                int maxWeightTriangleIndex = 0;
                for (int i = 1; i < 3; ++i)
                {
                    if (raycastHit.barycentricCoordinate[i] > maxWeight)
                    {
                        maxWeight = raycastHit.barycentricCoordinate[i];
                        maxWeightTriangleIndex = i;
                    }
                }

                int vertexIndex = mesh.triangles[raycastHit.triangleIndex * 3 + maxWeightTriangleIndex];

                BoneWeight bw = boneWeights[vertexIndex];
                float maxBoneWeight = bw.weight0;
                int boneIndex = bw.boneIndex0;
                if (bw.weight1 > maxBoneWeight)
                {
                    maxBoneWeight = bw.weight1;
                    boneIndex = bw.boneIndex1;
                }

                if (bw.weight2 > maxBoneWeight)
                {
                    maxBoneWeight = bw.weight2;
                    boneIndex = bw.boneIndex2;
                }

                if (bw.weight3 > maxBoneWeight)
                {
                    //maxBoneWeight = bw.weight3;
                    boneIndex = bw.boneIndex3;
                }

                return skinnedMeshRenderer.bones[boneIndex];
            }
            catch (System.Exception e)
            {
                Debug.LogError(" e " + e.Message);
                return null;
            }
        }

        public Vector3 TransScreenPos2WorldPos(Plane targetPlane)
        {
            Vector3 touchPos = GetMousePos();
            var tempRay = m_DIYUI.RayCamera.ScreenPointToRay(touchPos);
            Vector3 worldPos = Vector3.zero;
            if (targetPlane.Raycast(tempRay, out float entry))
            {
                // 获取交点的世界坐标
                worldPos = tempRay.GetPoint(entry);
            }

            return worldPos;
        }

        private void UpdataShadowMat()
        {
            if (m_BodyBlock != null && m_BodyBlock.BodyBlockNode != null)
            {
                m_Creature2.UpdateDIYShadow(m_BodyBlock.BodyBlockNode.transform);
            }
        }

        public void OnDestroySelf()
        {
            UpdataShadowMat();
            if (m_BodyBlock != null && !m_BodyBlock.Attached)
            {
                m_BodyBlock.DestroyBodyBlock();
                m_BodyBlock = null;
            }

            DestroyAttatchGo();
            DestroyJinZhiGo();
            if (m_CanAttatch)
            {
                if (!m_FromCreature)
                {
                    m_DIYUI.GetCreature().PlayUIShow();
                }

                m_DIYUI.GetCreature().SetAnimSpeed(1);
                m_DIYUI.TryGuide();
            }

            Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_DIY_Last);
        }
    }
}