using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDRepondNetDataContent
    {
        public LDRepondNetDataContent Next = null;
        public byte[] Data = null;
        public int DataLength = -1;
        public int CrcVal = -1;
        public int MsgId = -1;
        public int Version = -1;
    }
    public class LDRequestNetDataContent
    {
        public byte[] Data = null;
        public int DataLength = -1;
        public int CrcVal = -1;
        public int MsgId = -1;

        public LDRequestNetDataContent(opcode opcode, IMessage message,bool log = true)
        {
            if (!RuntimeSettings.Release && log)
            {
                JsonFormatter jsonFormatter = new JsonFormatter(new JsonFormatter.Settings(true));
                Global.LogRequest(opcode.ToString() + " : " + jsonFormatter.Format(message));
            }

            byte[] data = message.ToByteArray();
            MsgId = (int)opcode;
            ByteBuffer byteBuffer = new ByteBuffer();
            byteBuffer.WriteInt(MsgId);
            // kcp 不需要 额外的头信息
            {
                int msg_version = 1;
                CrcVal = EncryptUtils.ElfHash(data);
                DataLength = data.Length + 4 + 4;
                byteBuffer.WriteInt(DataLength);
                byteBuffer.WriteInt(msg_version);
                byteBuffer.WriteInt(CrcVal);
            }
            byteBuffer.WriteBytes(data);
            Data = byteBuffer.ToBytes();
            byteBuffer.Close();
            //Debug.Log(data.Length);
        }
    }
}
