//using Google.Protobuf.Examples.AddressBook;
using LD.Net;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
	public class ClientTest : MonoBehaviour
	{
		LDSocket http;
		bool m_Connected = false;
		void Start()
		{
			//http = new LDSocket();
			//http.Init("127.0.0.1", 12345);
			//if(http.Connect(5000))
   //         {
			//	m_Connected = true;
			//	http.StartReceive(5000);
			//}
		}

		// Update is called once per frame
		void Update()
		{

			if (m_Connected)
			{
    //            byte[] data = http.GetData();
				//if(data!= null)
    //            {
				//	Person copy = Person.Parser.ParseFrom(data);
				//	//string str = System.Text.Encoding.UTF8.GetString(data, 0, data.Length);
				//	Debug.Log(copy.ToString());
				//}
            }

        }
        private void OnDestroy()
        {
			//http?.Dispose();

		}
    }
}
