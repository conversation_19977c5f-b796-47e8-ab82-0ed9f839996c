// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/Common.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/Common.proto</summary>
  public static partial class CommonReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/Common.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static CommonReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChVDMlNQcm90by9Db21tb24ucHJvdG8iKgoHVmVjdG9yMxIJCgF4GAEgASgF",
            "EgkKAXkYAiABKAUSCQoBehgDIAEoBSJGCgxBY3Rpdml0eUluZm8SEgoKYWN0",
            "aXZpdHlJZBgBIAEoBRIRCgliZWdpblRpbWUYAiABKAMSDwoHZW5kVGltZRgD",
            "IAEoAyL7AQoSQWN0aXZpdHlDb21tb25JbmZvEj0KDXNob3BJdGVtQ291bnQY",
            "ASADKAsyJi5BY3Rpdml0eUNvbW1vbkluZm8uU2hvcEl0ZW1Db3VudEVudHJ5",
            "EjsKDGFjdGl2aXR5Q29pbhgCIAMoCzIlLkFjdGl2aXR5Q29tbW9uSW5mby5B",
            "Y3Rpdml0eUNvaW5FbnRyeRo0ChJTaG9wSXRlbUNvdW50RW50cnkSCwoDa2V5",
            "GAEgASgFEg0KBXZhbHVlGAIgASgFOgI4ARozChFBY3Rpdml0eUNvaW5FbnRy",
            "eRILCgNrZXkYASABKAUSDQoFdmFsdWUYAiABKAU6AjgBIrYBCiBBY3Rpdml0",
            "eUNvbW1vbkluZm9Ob3RpZnlSZXNwb25zZRISCgphY3Rpdml0eUlkGAEgASgF",
            "EkkKDGFjdGl2aXR5Q29pbhgCIAMoCzIzLkFjdGl2aXR5Q29tbW9uSW5mb05v",
            "dGlmeVJlc3BvbnNlLkFjdGl2aXR5Q29pbkVudHJ5GjMKEUFjdGl2aXR5Q29p",
            "bkVudHJ5EgsKA2tleRgBIAEoBRINCgV2YWx1ZRgCIAEoBToCOAEitQEKHkFj",
            "dGl2aXR5U2hvcEluZm9Ob3RpZnlSZXNwb25zZRISCgphY3Rpdml0eUlkGAEg",
            "ASgFEkkKDXNob3BJdGVtQ291bnQYAiADKAsyMi5BY3Rpdml0eVNob3BJbmZv",
            "Tm90aWZ5UmVzcG9uc2UuU2hvcEl0ZW1Db3VudEVudHJ5GjQKElNob3BJdGVt",
            "Q291bnRFbnRyeRILCgNrZXkYASABKAUSDQoFdmFsdWUYAiABKAU6AjgBIjMK",
            "FkFjdGl2aXR5U2hvcEJ1eVJlcXVlc3QSCgoCaWQYASABKAUSDQoFY291bnQY",
            "AiABKAUinwEKF0FjdGl2aXR5U2hvcEJ1eVJlc3BvbnNlEgoKAmlkGAEgASgF",
            "EkIKDXNob3BJdGVtQ291bnQYAiADKAsyKy5BY3Rpdml0eVNob3BCdXlSZXNw",
            "b25zZS5TaG9wSXRlbUNvdW50RW50cnkaNAoSU2hvcEl0ZW1Db3VudEVudHJ5",
            "EgsKA2tleRgBIAEoBRINCgV2YWx1ZRgCIAEoBToCOAEiOQoJQ1Rhc2tJbmZv",
            "EgoKAmlkGAEgASgFEg4KBnN0YXR1cxgCIAEoBRIQCghwcm9ncmVzcxgDIAEo",
            "AyItChpDRGFpbHlSZWZyZXNoRXZlbnRSZXNwb25zZRIPCgdjdXJUaW1lGAEg",
            "ASgDIkoKDU1hcnF1ZWVSZWNvcmQSEAoIcGxheWVySWQYASABKAMSDAoEbmFt",
            "ZRgCIAEoCRIKCgJpZBgDIAEoBRINCgVwYXJhbRgEIAMoCSJgCgtGaWdodENp",
            "dGlhbxIPCgdncm91cElkGAEgASgFEhEKCWNpdGlhb0lkcxgCIAMoBRIPCgdt",
            "ZWNoYUlkGAMgASgFEg0KBWN1ckx2GAQgASgFEg0KBW1heEx2GAUgASgFKjIK",
            "DEJhdHRsZVN0YXR1cxILCgdGQUlMVVJFEAASCwoHU1VDQ0VTUxABEggKBFFV",
            "SVQQAiowCg5BY3Rpdml0eVN0YXR1cxILCgdQUkVQQVJFEAASCAoET1BFThAB",
            "EgcKA0VORBACKjUKC0NUYXNrU3RhdHVzEgoKBkNfTk9ORRAAEgwKCENfRklO",
            "SVNIEAESDAoIQ19SRVdBUkQQAkIjChNjb20uZ29sZGVuLnByb3RvY29sqgIL",
            "TEQuUHJvdG9jb2xiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::LD.Protocol.BattleStatus), typeof(global::LD.Protocol.ActivityStatus), typeof(global::LD.Protocol.CTaskStatus), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.Vector3), global::LD.Protocol.Vector3.Parser, new[]{ "X", "Y", "Z" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ActivityInfo), global::LD.Protocol.ActivityInfo.Parser, new[]{ "ActivityId", "BeginTime", "EndTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ActivityCommonInfo), global::LD.Protocol.ActivityCommonInfo.Parser, new[]{ "ShopItemCount", "ActivityCoin" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ActivityCommonInfoNotifyResponse), global::LD.Protocol.ActivityCommonInfoNotifyResponse.Parser, new[]{ "ActivityId", "ActivityCoin" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ActivityShopInfoNotifyResponse), global::LD.Protocol.ActivityShopInfoNotifyResponse.Parser, new[]{ "ActivityId", "ShopItemCount" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ActivityShopBuyRequest), global::LD.Protocol.ActivityShopBuyRequest.Parser, new[]{ "Id", "Count" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ActivityShopBuyResponse), global::LD.Protocol.ActivityShopBuyResponse.Parser, new[]{ "Id", "ShopItemCount" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.CTaskInfo), global::LD.Protocol.CTaskInfo.Parser, new[]{ "Id", "Status", "Progress" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.CDailyRefreshEventResponse), global::LD.Protocol.CDailyRefreshEventResponse.Parser, new[]{ "CurTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.MarqueeRecord), global::LD.Protocol.MarqueeRecord.Parser, new[]{ "PlayerId", "Name", "Id", "Param" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.FightCitiao), global::LD.Protocol.FightCitiao.Parser, new[]{ "GroupId", "CitiaoIds", "MechaId", "CurLv", "MaxLv" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum BattleStatus {
    [pbr::OriginalName("FAILURE")] Failure = 0,
    [pbr::OriginalName("SUCCESS")] Success = 1,
    [pbr::OriginalName("QUIT")] Quit = 2,
  }

  public enum ActivityStatus {
    [pbr::OriginalName("PREPARE")] Prepare = 0,
    [pbr::OriginalName("OPEN")] Open = 1,
    [pbr::OriginalName("END")] End = 2,
  }

  public enum CTaskStatus {
    /// <summary>
    /// 任务没有完成
    /// </summary>
    [pbr::OriginalName("C_NONE")] CNone = 0,
    /// <summary>
    /// 任务完成了
    /// </summary>
    [pbr::OriginalName("C_FINISH")] CFinish = 1,
    /// <summary>
    /// 已经领取奖励
    /// </summary>
    [pbr::OriginalName("C_REWARD")] CReward = 2,
  }

  #endregion

  #region Messages
  public sealed partial class Vector3 : pb::IMessage<Vector3> {
    private static readonly pb::MessageParser<Vector3> _parser = new pb::MessageParser<Vector3>(() => new Vector3());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<Vector3> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public Vector3() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public Vector3(Vector3 other) : this() {
      x_ = other.x_;
      y_ = other.y_;
      z_ = other.z_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public Vector3 Clone() {
      return new Vector3(this);
    }

    /// <summary>Field number for the "x" field.</summary>
    public const int XFieldNumber = 1;
    private int x_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int X {
      get { return x_; }
      set {
        x_ = value;
      }
    }

    /// <summary>Field number for the "y" field.</summary>
    public const int YFieldNumber = 2;
    private int y_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Y {
      get { return y_; }
      set {
        y_ = value;
      }
    }

    /// <summary>Field number for the "z" field.</summary>
    public const int ZFieldNumber = 3;
    private int z_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Z {
      get { return z_; }
      set {
        z_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as Vector3);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(Vector3 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (X != other.X) return false;
      if (Y != other.Y) return false;
      if (Z != other.Z) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (X != 0) hash ^= X.GetHashCode();
      if (Y != 0) hash ^= Y.GetHashCode();
      if (Z != 0) hash ^= Z.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (X != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(X);
      }
      if (Y != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Y);
      }
      if (Z != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Z);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (X != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(X);
      }
      if (Y != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Y);
      }
      if (Z != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Z);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(Vector3 other) {
      if (other == null) {
        return;
      }
      if (other.X != 0) {
        X = other.X;
      }
      if (other.Y != 0) {
        Y = other.Y;
      }
      if (other.Z != 0) {
        Z = other.Z;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            X = input.ReadInt32();
            break;
          }
          case 16: {
            Y = input.ReadInt32();
            break;
          }
          case 24: {
            Z = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ActivityInfo : pb::IMessage<ActivityInfo> {
    private static readonly pb::MessageParser<ActivityInfo> _parser = new pb::MessageParser<ActivityInfo>(() => new ActivityInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ActivityInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityInfo(ActivityInfo other) : this() {
      activityId_ = other.activityId_;
      beginTime_ = other.beginTime_;
      endTime_ = other.endTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityInfo Clone() {
      return new ActivityInfo(this);
    }

    /// <summary>Field number for the "activityId" field.</summary>
    public const int ActivityIdFieldNumber = 1;
    private int activityId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ActivityId {
      get { return activityId_; }
      set {
        activityId_ = value;
      }
    }

    /// <summary>Field number for the "beginTime" field.</summary>
    public const int BeginTimeFieldNumber = 2;
    private long beginTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long BeginTime {
      get { return beginTime_; }
      set {
        beginTime_ = value;
      }
    }

    /// <summary>Field number for the "endTime" field.</summary>
    public const int EndTimeFieldNumber = 3;
    private long endTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long EndTime {
      get { return endTime_; }
      set {
        endTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ActivityInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ActivityInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ActivityId != other.ActivityId) return false;
      if (BeginTime != other.BeginTime) return false;
      if (EndTime != other.EndTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ActivityId != 0) hash ^= ActivityId.GetHashCode();
      if (BeginTime != 0L) hash ^= BeginTime.GetHashCode();
      if (EndTime != 0L) hash ^= EndTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ActivityId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ActivityId);
      }
      if (BeginTime != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(BeginTime);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(EndTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ActivityId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ActivityId);
      }
      if (BeginTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(BeginTime);
      }
      if (EndTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(EndTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ActivityInfo other) {
      if (other == null) {
        return;
      }
      if (other.ActivityId != 0) {
        ActivityId = other.ActivityId;
      }
      if (other.BeginTime != 0L) {
        BeginTime = other.BeginTime;
      }
      if (other.EndTime != 0L) {
        EndTime = other.EndTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ActivityId = input.ReadInt32();
            break;
          }
          case 16: {
            BeginTime = input.ReadInt64();
            break;
          }
          case 24: {
            EndTime = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ActivityCommonInfo : pb::IMessage<ActivityCommonInfo> {
    private static readonly pb::MessageParser<ActivityCommonInfo> _parser = new pb::MessageParser<ActivityCommonInfo>(() => new ActivityCommonInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ActivityCommonInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityCommonInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityCommonInfo(ActivityCommonInfo other) : this() {
      shopItemCount_ = other.shopItemCount_.Clone();
      activityCoin_ = other.activityCoin_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityCommonInfo Clone() {
      return new ActivityCommonInfo(this);
    }

    /// <summary>Field number for the "shopItemCount" field.</summary>
    public const int ShopItemCountFieldNumber = 1;
    private static readonly pbc::MapField<int, int>.Codec _map_shopItemCount_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 10);
    private readonly pbc::MapField<int, int> shopItemCount_ = new pbc::MapField<int, int>();
    /// <summary>
    /// 物品购买数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> ShopItemCount {
      get { return shopItemCount_; }
    }

    /// <summary>Field number for the "activityCoin" field.</summary>
    public const int ActivityCoinFieldNumber = 2;
    private static readonly pbc::MapField<int, int>.Codec _map_activityCoin_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 18);
    private readonly pbc::MapField<int, int> activityCoin_ = new pbc::MapField<int, int>();
    /// <summary>
    /// 活动内货币
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> ActivityCoin {
      get { return activityCoin_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ActivityCommonInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ActivityCommonInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!ShopItemCount.Equals(other.ShopItemCount)) return false;
      if (!ActivityCoin.Equals(other.ActivityCoin)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= ShopItemCount.GetHashCode();
      hash ^= ActivityCoin.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      shopItemCount_.WriteTo(output, _map_shopItemCount_codec);
      activityCoin_.WriteTo(output, _map_activityCoin_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += shopItemCount_.CalculateSize(_map_shopItemCount_codec);
      size += activityCoin_.CalculateSize(_map_activityCoin_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ActivityCommonInfo other) {
      if (other == null) {
        return;
      }
      shopItemCount_.Add(other.shopItemCount_);
      activityCoin_.Add(other.activityCoin_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            shopItemCount_.AddEntriesFrom(input, _map_shopItemCount_codec);
            break;
          }
          case 18: {
            activityCoin_.AddEntriesFrom(input, _map_activityCoin_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class ActivityCommonInfoNotifyResponse : pb::IMessage<ActivityCommonInfoNotifyResponse> {
    private static readonly pb::MessageParser<ActivityCommonInfoNotifyResponse> _parser = new pb::MessageParser<ActivityCommonInfoNotifyResponse>(() => new ActivityCommonInfoNotifyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ActivityCommonInfoNotifyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityCommonInfoNotifyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityCommonInfoNotifyResponse(ActivityCommonInfoNotifyResponse other) : this() {
      activityId_ = other.activityId_;
      activityCoin_ = other.activityCoin_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityCommonInfoNotifyResponse Clone() {
      return new ActivityCommonInfoNotifyResponse(this);
    }

    /// <summary>Field number for the "activityId" field.</summary>
    public const int ActivityIdFieldNumber = 1;
    private int activityId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ActivityId {
      get { return activityId_; }
      set {
        activityId_ = value;
      }
    }

    /// <summary>Field number for the "activityCoin" field.</summary>
    public const int ActivityCoinFieldNumber = 2;
    private static readonly pbc::MapField<int, int>.Codec _map_activityCoin_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 18);
    private readonly pbc::MapField<int, int> activityCoin_ = new pbc::MapField<int, int>();
    /// <summary>
    /// 活动内货币
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> ActivityCoin {
      get { return activityCoin_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ActivityCommonInfoNotifyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ActivityCommonInfoNotifyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ActivityId != other.ActivityId) return false;
      if (!ActivityCoin.Equals(other.ActivityCoin)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ActivityId != 0) hash ^= ActivityId.GetHashCode();
      hash ^= ActivityCoin.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ActivityId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ActivityId);
      }
      activityCoin_.WriteTo(output, _map_activityCoin_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ActivityId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ActivityId);
      }
      size += activityCoin_.CalculateSize(_map_activityCoin_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ActivityCommonInfoNotifyResponse other) {
      if (other == null) {
        return;
      }
      if (other.ActivityId != 0) {
        ActivityId = other.ActivityId;
      }
      activityCoin_.Add(other.activityCoin_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ActivityId = input.ReadInt32();
            break;
          }
          case 18: {
            activityCoin_.AddEntriesFrom(input, _map_activityCoin_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class ActivityShopInfoNotifyResponse : pb::IMessage<ActivityShopInfoNotifyResponse> {
    private static readonly pb::MessageParser<ActivityShopInfoNotifyResponse> _parser = new pb::MessageParser<ActivityShopInfoNotifyResponse>(() => new ActivityShopInfoNotifyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ActivityShopInfoNotifyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopInfoNotifyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopInfoNotifyResponse(ActivityShopInfoNotifyResponse other) : this() {
      activityId_ = other.activityId_;
      shopItemCount_ = other.shopItemCount_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopInfoNotifyResponse Clone() {
      return new ActivityShopInfoNotifyResponse(this);
    }

    /// <summary>Field number for the "activityId" field.</summary>
    public const int ActivityIdFieldNumber = 1;
    private int activityId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ActivityId {
      get { return activityId_; }
      set {
        activityId_ = value;
      }
    }

    /// <summary>Field number for the "shopItemCount" field.</summary>
    public const int ShopItemCountFieldNumber = 2;
    private static readonly pbc::MapField<int, int>.Codec _map_shopItemCount_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 18);
    private readonly pbc::MapField<int, int> shopItemCount_ = new pbc::MapField<int, int>();
    /// <summary>
    /// 物品购买数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> ShopItemCount {
      get { return shopItemCount_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ActivityShopInfoNotifyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ActivityShopInfoNotifyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ActivityId != other.ActivityId) return false;
      if (!ShopItemCount.Equals(other.ShopItemCount)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ActivityId != 0) hash ^= ActivityId.GetHashCode();
      hash ^= ShopItemCount.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ActivityId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ActivityId);
      }
      shopItemCount_.WriteTo(output, _map_shopItemCount_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ActivityId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ActivityId);
      }
      size += shopItemCount_.CalculateSize(_map_shopItemCount_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ActivityShopInfoNotifyResponse other) {
      if (other == null) {
        return;
      }
      if (other.ActivityId != 0) {
        ActivityId = other.ActivityId;
      }
      shopItemCount_.Add(other.shopItemCount_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ActivityId = input.ReadInt32();
            break;
          }
          case 18: {
            shopItemCount_.AddEntriesFrom(input, _map_shopItemCount_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class ActivityShopBuyRequest : pb::IMessage<ActivityShopBuyRequest> {
    private static readonly pb::MessageParser<ActivityShopBuyRequest> _parser = new pb::MessageParser<ActivityShopBuyRequest>(() => new ActivityShopBuyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ActivityShopBuyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopBuyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopBuyRequest(ActivityShopBuyRequest other) : this() {
      id_ = other.id_;
      count_ = other.count_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopBuyRequest Clone() {
      return new ActivityShopBuyRequest(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    ///OpenShop id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int CountFieldNumber = 2;
    private int count_;
    /// <summary>
    ///购买次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ActivityShopBuyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ActivityShopBuyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Count != other.Count) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Count != 0) hash ^= Count.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Count != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Count);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ActivityShopBuyRequest other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Count != 0) {
        Count = other.Count;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Count = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ActivityShopBuyResponse : pb::IMessage<ActivityShopBuyResponse> {
    private static readonly pb::MessageParser<ActivityShopBuyResponse> _parser = new pb::MessageParser<ActivityShopBuyResponse>(() => new ActivityShopBuyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ActivityShopBuyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopBuyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopBuyResponse(ActivityShopBuyResponse other) : this() {
      id_ = other.id_;
      shopItemCount_ = other.shopItemCount_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActivityShopBuyResponse Clone() {
      return new ActivityShopBuyResponse(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    ///OpenShop id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "shopItemCount" field.</summary>
    public const int ShopItemCountFieldNumber = 2;
    private static readonly pbc::MapField<int, int>.Codec _map_shopItemCount_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 18);
    private readonly pbc::MapField<int, int> shopItemCount_ = new pbc::MapField<int, int>();
    /// <summary>
    /// 物品购买数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> ShopItemCount {
      get { return shopItemCount_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ActivityShopBuyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ActivityShopBuyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (!ShopItemCount.Equals(other.ShopItemCount)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      hash ^= ShopItemCount.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      shopItemCount_.WriteTo(output, _map_shopItemCount_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      size += shopItemCount_.CalculateSize(_map_shopItemCount_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ActivityShopBuyResponse other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      shopItemCount_.Add(other.shopItemCount_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            shopItemCount_.AddEntriesFrom(input, _map_shopItemCount_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class CTaskInfo : pb::IMessage<CTaskInfo> {
    private static readonly pb::MessageParser<CTaskInfo> _parser = new pb::MessageParser<CTaskInfo>(() => new CTaskInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<CTaskInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public CTaskInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public CTaskInfo(CTaskInfo other) : this() {
      id_ = other.id_;
      status_ = other.status_;
      progress_ = other.progress_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public CTaskInfo Clone() {
      return new CTaskInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 2;
    private int status_;
    /// <summary>
    /// 任务状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "progress" field.</summary>
    public const int ProgressFieldNumber = 3;
    private long progress_;
    /// <summary>
    /// 任务完成进度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Progress {
      get { return progress_; }
      set {
        progress_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as CTaskInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(CTaskInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Status != other.Status) return false;
      if (Progress != other.Progress) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Status != 0) hash ^= Status.GetHashCode();
      if (Progress != 0L) hash ^= Progress.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Status != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Status);
      }
      if (Progress != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Progress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Status != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Status);
      }
      if (Progress != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Progress);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(CTaskInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Status != 0) {
        Status = other.Status;
      }
      if (other.Progress != 0L) {
        Progress = other.Progress;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Status = input.ReadInt32();
            break;
          }
          case 24: {
            Progress = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class CDailyRefreshEventResponse : pb::IMessage<CDailyRefreshEventResponse> {
    private static readonly pb::MessageParser<CDailyRefreshEventResponse> _parser = new pb::MessageParser<CDailyRefreshEventResponse>(() => new CDailyRefreshEventResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<CDailyRefreshEventResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public CDailyRefreshEventResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public CDailyRefreshEventResponse(CDailyRefreshEventResponse other) : this() {
      curTime_ = other.curTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public CDailyRefreshEventResponse Clone() {
      return new CDailyRefreshEventResponse(this);
    }

    /// <summary>Field number for the "curTime" field.</summary>
    public const int CurTimeFieldNumber = 1;
    private long curTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long CurTime {
      get { return curTime_; }
      set {
        curTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as CDailyRefreshEventResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(CDailyRefreshEventResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (CurTime != other.CurTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (CurTime != 0L) hash ^= CurTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (CurTime != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(CurTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (CurTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CurTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(CDailyRefreshEventResponse other) {
      if (other == null) {
        return;
      }
      if (other.CurTime != 0L) {
        CurTime = other.CurTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            CurTime = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class MarqueeRecord : pb::IMessage<MarqueeRecord> {
    private static readonly pb::MessageParser<MarqueeRecord> _parser = new pb::MessageParser<MarqueeRecord>(() => new MarqueeRecord());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<MarqueeRecord> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MarqueeRecord() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MarqueeRecord(MarqueeRecord other) : this() {
      playerId_ = other.playerId_;
      name_ = other.name_;
      id_ = other.id_;
      param_ = other.param_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MarqueeRecord Clone() {
      return new MarqueeRecord(this);
    }

    /// <summary>Field number for the "playerId" field.</summary>
    public const int PlayerIdFieldNumber = 1;
    private long playerId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long PlayerId {
      get { return playerId_; }
      set {
        playerId_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 3;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "param" field.</summary>
    public const int ParamFieldNumber = 4;
    private static readonly pb::FieldCodec<string> _repeated_param_codec
        = pb::FieldCodec.ForString(34);
    private readonly pbc::RepeatedField<string> param_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<string> Param {
      get { return param_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as MarqueeRecord);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(MarqueeRecord other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (PlayerId != other.PlayerId) return false;
      if (Name != other.Name) return false;
      if (Id != other.Id) return false;
      if(!param_.Equals(other.param_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (PlayerId != 0L) hash ^= PlayerId.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Id != 0) hash ^= Id.GetHashCode();
      hash ^= param_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (PlayerId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(PlayerId);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (Id != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Id);
      }
      param_.WriteTo(output, _repeated_param_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (PlayerId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(PlayerId);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      size += param_.CalculateSize(_repeated_param_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(MarqueeRecord other) {
      if (other == null) {
        return;
      }
      if (other.PlayerId != 0L) {
        PlayerId = other.PlayerId;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      param_.Add(other.param_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            PlayerId = input.ReadInt64();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            Id = input.ReadInt32();
            break;
          }
          case 34: {
            param_.AddEntriesFrom(input, _repeated_param_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///战斗词条
  /// </summary>
  public sealed partial class FightCitiao : pb::IMessage<FightCitiao> {
    private static readonly pb::MessageParser<FightCitiao> _parser = new pb::MessageParser<FightCitiao>(() => new FightCitiao());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<FightCitiao> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CommonReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public FightCitiao() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public FightCitiao(FightCitiao other) : this() {
      groupId_ = other.groupId_;
      citiaoIds_ = other.citiaoIds_.Clone();
      mechaId_ = other.mechaId_;
      curLv_ = other.curLv_;
      maxLv_ = other.maxLv_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public FightCitiao Clone() {
      return new FightCitiao(this);
    }

    /// <summary>Field number for the "groupId" field.</summary>
    public const int GroupIdFieldNumber = 1;
    private int groupId_;
    /// <summary>
    ///词条组id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int GroupId {
      get { return groupId_; }
      set {
        groupId_ = value;
      }
    }

    /// <summary>Field number for the "citiaoIds" field.</summary>
    public const int CitiaoIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<int> _repeated_citiaoIds_codec
        = pb::FieldCodec.ForInt32(18);
    private readonly pbc::RepeatedField<int> citiaoIds_ = new pbc::RepeatedField<int>();
    /// <summary>
    ///词条id列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> CitiaoIds {
      get { return citiaoIds_; }
    }

    /// <summary>Field number for the "mechaId" field.</summary>
    public const int MechaIdFieldNumber = 3;
    private int mechaId_;
    /// <summary>
    ///机甲id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MechaId {
      get { return mechaId_; }
      set {
        mechaId_ = value;
      }
    }

    /// <summary>Field number for the "curLv" field.</summary>
    public const int CurLvFieldNumber = 4;
    private int curLv_;
    /// <summary>
    ///当前等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CurLv {
      get { return curLv_; }
      set {
        curLv_ = value;
      }
    }

    /// <summary>Field number for the "maxLv" field.</summary>
    public const int MaxLvFieldNumber = 5;
    private int maxLv_;
    /// <summary>
    ///最大等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MaxLv {
      get { return maxLv_; }
      set {
        maxLv_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as FightCitiao);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(FightCitiao other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GroupId != other.GroupId) return false;
      if(!citiaoIds_.Equals(other.citiaoIds_)) return false;
      if (MechaId != other.MechaId) return false;
      if (CurLv != other.CurLv) return false;
      if (MaxLv != other.MaxLv) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (GroupId != 0) hash ^= GroupId.GetHashCode();
      hash ^= citiaoIds_.GetHashCode();
      if (MechaId != 0) hash ^= MechaId.GetHashCode();
      if (CurLv != 0) hash ^= CurLv.GetHashCode();
      if (MaxLv != 0) hash ^= MaxLv.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (GroupId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(GroupId);
      }
      citiaoIds_.WriteTo(output, _repeated_citiaoIds_codec);
      if (MechaId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(MechaId);
      }
      if (CurLv != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(CurLv);
      }
      if (MaxLv != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(MaxLv);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (GroupId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(GroupId);
      }
      size += citiaoIds_.CalculateSize(_repeated_citiaoIds_codec);
      if (MechaId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MechaId);
      }
      if (CurLv != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(CurLv);
      }
      if (MaxLv != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MaxLv);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(FightCitiao other) {
      if (other == null) {
        return;
      }
      if (other.GroupId != 0) {
        GroupId = other.GroupId;
      }
      citiaoIds_.Add(other.citiaoIds_);
      if (other.MechaId != 0) {
        MechaId = other.MechaId;
      }
      if (other.CurLv != 0) {
        CurLv = other.CurLv;
      }
      if (other.MaxLv != 0) {
        MaxLv = other.MaxLv;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            GroupId = input.ReadInt32();
            break;
          }
          case 18:
          case 16: {
            citiaoIds_.AddEntriesFrom(input, _repeated_citiaoIds_codec);
            break;
          }
          case 24: {
            MechaId = input.ReadInt32();
            break;
          }
          case 32: {
            CurLv = input.ReadInt32();
            break;
          }
          case 40: {
            MaxLv = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
