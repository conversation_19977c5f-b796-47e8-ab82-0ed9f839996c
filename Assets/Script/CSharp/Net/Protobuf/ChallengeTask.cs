// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/ChallengeTask.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/ChallengeTask.proto</summary>
  public static partial class ChallengeTaskReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/ChallengeTask.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ChallengeTaskReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChxDMlNQcm90by9DaGFsbGVuZ2VUYXNrLnByb3RvIkEKEUNoYWxsZW5nZVRh",
            "c2tJbmZvEgoKAmlkGAEgASgFEg4KBnN0YXR1cxgCIAEoBRIQCghwcm9ncmVz",
            "cxgEIAEoAyJ8CiBDaGFsbGVuZ2VUYXNrSW5mb0FsbFN5bmNSZXNwb25zZRIg",
            "CgR0YXNrGAEgAygLMhIuQ2hhbGxlbmdlVGFza0luZm8SDQoFcG9pbnQYAiAB",
            "KAUSFAoMcG9pbnRSZXdhcmRzGAMgAygFEhEKCWJlZ2luVGltZRgEIAEoAyJB",
            "Ch1DaGFsbGVuZ2VUYXNrSW5mb1N5bmNSZXNwb25zZRIgCgR0YXNrGAEgAygL",
            "MhIuQ2hhbGxlbmdlVGFza0luZm8iRwohUmVjZWl2ZUNoYWxsZW5nZVRhc2tS",
            "ZXdhcmRSZXF1ZXN0Eg8KB3Rhc2tfaWQYASABKAUSEQoJdGFza190eXBlGAIg",
            "ASgFIkcKI1JlY2VpdmVkQ2hhbGxlbmdlVGFza1Jld2FyZFJlc3BvbnNlEiAK",
            "BHRhc2sYASABKAsyEi5DaGFsbGVuZ2VUYXNrSW5mbyI6CiJSZWNlaXZlQ2hh",
            "bGxlbmdlUG9pbnRSZXdhcmRSZXF1ZXN0EhQKDHBvaW50UmV3YXJkcxgBIAMo",
            "BSI8CiRSZWNlaXZlZENoYWxsZW5nZVBvaW50UmV3YXJkUmVzcG9uc2USFAoM",
            "cG9pbnRSZXdhcmRzGAEgAygFKjcKE0NoYWxsZW5nZVRhc2tTdGF0dXMSCAoE",
            "Tk9ORRAAEgoKBkZJTklTSBABEgoKBlJFV0FSRBACQiMKE2NvbS5nb2xkZW4u",
            "cHJvdG9jb2yqAgtMRC5Qcm90b2NvbGIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::LD.Protocol.ChallengeTaskStatus), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ChallengeTaskInfo), global::LD.Protocol.ChallengeTaskInfo.Parser, new[]{ "Id", "Status", "Progress" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ChallengeTaskInfoAllSyncResponse), global::LD.Protocol.ChallengeTaskInfoAllSyncResponse.Parser, new[]{ "Task", "Point", "PointRewards", "BeginTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ChallengeTaskInfoSyncResponse), global::LD.Protocol.ChallengeTaskInfoSyncResponse.Parser, new[]{ "Task" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceiveChallengeTaskRewardRequest), global::LD.Protocol.ReceiveChallengeTaskRewardRequest.Parser, new[]{ "TaskId", "TaskType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceivedChallengeTaskRewardResponse), global::LD.Protocol.ReceivedChallengeTaskRewardResponse.Parser, new[]{ "Task" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceiveChallengePointRewardRequest), global::LD.Protocol.ReceiveChallengePointRewardRequest.Parser, new[]{ "PointRewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceivedChallengePointRewardResponse), global::LD.Protocol.ReceivedChallengePointRewardResponse.Parser, new[]{ "PointRewards" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum ChallengeTaskStatus {
    /// <summary>
    /// 任务没有完成
    /// </summary>
    [pbr::OriginalName("NONE")] None = 0,
    /// <summary>
    /// 任务完成了
    /// </summary>
    [pbr::OriginalName("FINISH")] Finish = 1,
    /// <summary>
    /// 已经领取奖励
    /// </summary>
    [pbr::OriginalName("REWARD")] Reward = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  /// 任务信息
  /// </summary>
  public sealed partial class ChallengeTaskInfo : pb::IMessage<ChallengeTaskInfo> {
    private static readonly pb::MessageParser<ChallengeTaskInfo> _parser = new pb::MessageParser<ChallengeTaskInfo>(() => new ChallengeTaskInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ChallengeTaskInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ChallengeTaskReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfo(ChallengeTaskInfo other) : this() {
      id_ = other.id_;
      status_ = other.status_;
      progress_ = other.progress_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfo Clone() {
      return new ChallengeTaskInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 2;
    private int status_;
    /// <summary>
    /// 任务状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "progress" field.</summary>
    public const int ProgressFieldNumber = 4;
    private long progress_;
    /// <summary>
    /// 任务完成进度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Progress {
      get { return progress_; }
      set {
        progress_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ChallengeTaskInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ChallengeTaskInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Status != other.Status) return false;
      if (Progress != other.Progress) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Status != 0) hash ^= Status.GetHashCode();
      if (Progress != 0L) hash ^= Progress.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Status != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Status);
      }
      if (Progress != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Progress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Status != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Status);
      }
      if (Progress != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Progress);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ChallengeTaskInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Status != 0) {
        Status = other.Status;
      }
      if (other.Progress != 0L) {
        Progress = other.Progress;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Status = input.ReadInt32();
            break;
          }
          case 32: {
            Progress = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 所有任务
  /// </summary>
  public sealed partial class ChallengeTaskInfoAllSyncResponse : pb::IMessage<ChallengeTaskInfoAllSyncResponse> {
    private static readonly pb::MessageParser<ChallengeTaskInfoAllSyncResponse> _parser = new pb::MessageParser<ChallengeTaskInfoAllSyncResponse>(() => new ChallengeTaskInfoAllSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ChallengeTaskInfoAllSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ChallengeTaskReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfoAllSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfoAllSyncResponse(ChallengeTaskInfoAllSyncResponse other) : this() {
      task_ = other.task_.Clone();
      point_ = other.point_;
      pointRewards_ = other.pointRewards_.Clone();
      beginTime_ = other.beginTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfoAllSyncResponse Clone() {
      return new ChallengeTaskInfoAllSyncResponse(this);
    }

    /// <summary>Field number for the "task" field.</summary>
    public const int TaskFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.ChallengeTaskInfo> _repeated_task_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.ChallengeTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ChallengeTaskInfo> task_ = new pbc::RepeatedField<global::LD.Protocol.ChallengeTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ChallengeTaskInfo> Task {
      get { return task_; }
    }

    /// <summary>Field number for the "point" field.</summary>
    public const int PointFieldNumber = 2;
    private int point_;
    /// <summary>
    ///积分数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Point {
      get { return point_; }
      set {
        point_ = value;
      }
    }

    /// <summary>Field number for the "pointRewards" field.</summary>
    public const int PointRewardsFieldNumber = 3;
    private static readonly pb::FieldCodec<int> _repeated_pointRewards_codec
        = pb::FieldCodec.ForInt32(26);
    private readonly pbc::RepeatedField<int> pointRewards_ = new pbc::RepeatedField<int>();
    /// <summary>
    ///领取的积分奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> PointRewards {
      get { return pointRewards_; }
    }

    /// <summary>Field number for the "beginTime" field.</summary>
    public const int BeginTimeFieldNumber = 4;
    private long beginTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long BeginTime {
      get { return beginTime_; }
      set {
        beginTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ChallengeTaskInfoAllSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ChallengeTaskInfoAllSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!task_.Equals(other.task_)) return false;
      if (Point != other.Point) return false;
      if(!pointRewards_.Equals(other.pointRewards_)) return false;
      if (BeginTime != other.BeginTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= task_.GetHashCode();
      if (Point != 0) hash ^= Point.GetHashCode();
      hash ^= pointRewards_.GetHashCode();
      if (BeginTime != 0L) hash ^= BeginTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      task_.WriteTo(output, _repeated_task_codec);
      if (Point != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Point);
      }
      pointRewards_.WriteTo(output, _repeated_pointRewards_codec);
      if (BeginTime != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(BeginTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += task_.CalculateSize(_repeated_task_codec);
      if (Point != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Point);
      }
      size += pointRewards_.CalculateSize(_repeated_pointRewards_codec);
      if (BeginTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(BeginTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ChallengeTaskInfoAllSyncResponse other) {
      if (other == null) {
        return;
      }
      task_.Add(other.task_);
      if (other.Point != 0) {
        Point = other.Point;
      }
      pointRewards_.Add(other.pointRewards_);
      if (other.BeginTime != 0L) {
        BeginTime = other.BeginTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            task_.AddEntriesFrom(input, _repeated_task_codec);
            break;
          }
          case 16: {
            Point = input.ReadInt32();
            break;
          }
          case 26:
          case 24: {
            pointRewards_.AddEntriesFrom(input, _repeated_pointRewards_codec);
            break;
          }
          case 32: {
            BeginTime = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 任务信息
  /// </summary>
  public sealed partial class ChallengeTaskInfoSyncResponse : pb::IMessage<ChallengeTaskInfoSyncResponse> {
    private static readonly pb::MessageParser<ChallengeTaskInfoSyncResponse> _parser = new pb::MessageParser<ChallengeTaskInfoSyncResponse>(() => new ChallengeTaskInfoSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ChallengeTaskInfoSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ChallengeTaskReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfoSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfoSyncResponse(ChallengeTaskInfoSyncResponse other) : this() {
      task_ = other.task_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ChallengeTaskInfoSyncResponse Clone() {
      return new ChallengeTaskInfoSyncResponse(this);
    }

    /// <summary>Field number for the "task" field.</summary>
    public const int TaskFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.ChallengeTaskInfo> _repeated_task_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.ChallengeTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ChallengeTaskInfo> task_ = new pbc::RepeatedField<global::LD.Protocol.ChallengeTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ChallengeTaskInfo> Task {
      get { return task_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ChallengeTaskInfoSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ChallengeTaskInfoSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!task_.Equals(other.task_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= task_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      task_.WriteTo(output, _repeated_task_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += task_.CalculateSize(_repeated_task_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ChallengeTaskInfoSyncResponse other) {
      if (other == null) {
        return;
      }
      task_.Add(other.task_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            task_.AddEntriesFrom(input, _repeated_task_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 请求领取任务奖励
  /// </summary>
  public sealed partial class ReceiveChallengeTaskRewardRequest : pb::IMessage<ReceiveChallengeTaskRewardRequest> {
    private static readonly pb::MessageParser<ReceiveChallengeTaskRewardRequest> _parser = new pb::MessageParser<ReceiveChallengeTaskRewardRequest>(() => new ReceiveChallengeTaskRewardRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceiveChallengeTaskRewardRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ChallengeTaskReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveChallengeTaskRewardRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveChallengeTaskRewardRequest(ReceiveChallengeTaskRewardRequest other) : this() {
      taskId_ = other.taskId_;
      taskType_ = other.taskType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveChallengeTaskRewardRequest Clone() {
      return new ReceiveChallengeTaskRewardRequest(this);
    }

    /// <summary>Field number for the "task_id" field.</summary>
    public const int TaskIdFieldNumber = 1;
    private int taskId_;
    /// <summary>
    /// 任务配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int TaskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    /// <summary>Field number for the "task_type" field.</summary>
    public const int TaskTypeFieldNumber = 2;
    private int taskType_;
    /// <summary>
    /// 任务类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int TaskType {
      get { return taskType_; }
      set {
        taskType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceiveChallengeTaskRewardRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceiveChallengeTaskRewardRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TaskId != other.TaskId) return false;
      if (TaskType != other.TaskType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (TaskId != 0) hash ^= TaskId.GetHashCode();
      if (TaskType != 0) hash ^= TaskType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (TaskId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(TaskId);
      }
      if (TaskType != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TaskType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (TaskId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TaskId);
      }
      if (TaskType != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TaskType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceiveChallengeTaskRewardRequest other) {
      if (other == null) {
        return;
      }
      if (other.TaskId != 0) {
        TaskId = other.TaskId;
      }
      if (other.TaskType != 0) {
        TaskType = other.TaskType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TaskId = input.ReadInt32();
            break;
          }
          case 16: {
            TaskType = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ReceivedChallengeTaskRewardResponse : pb::IMessage<ReceivedChallengeTaskRewardResponse> {
    private static readonly pb::MessageParser<ReceivedChallengeTaskRewardResponse> _parser = new pb::MessageParser<ReceivedChallengeTaskRewardResponse>(() => new ReceivedChallengeTaskRewardResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceivedChallengeTaskRewardResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ChallengeTaskReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedChallengeTaskRewardResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedChallengeTaskRewardResponse(ReceivedChallengeTaskRewardResponse other) : this() {
      task_ = other.task_ != null ? other.task_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedChallengeTaskRewardResponse Clone() {
      return new ReceivedChallengeTaskRewardResponse(this);
    }

    /// <summary>Field number for the "task" field.</summary>
    public const int TaskFieldNumber = 1;
    private global::LD.Protocol.ChallengeTaskInfo task_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.ChallengeTaskInfo Task {
      get { return task_; }
      set {
        task_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceivedChallengeTaskRewardResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceivedChallengeTaskRewardResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Task, other.Task)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (task_ != null) hash ^= Task.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (task_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Task);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (task_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Task);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceivedChallengeTaskRewardResponse other) {
      if (other == null) {
        return;
      }
      if (other.task_ != null) {
        if (task_ == null) {
          Task = new global::LD.Protocol.ChallengeTaskInfo();
        }
        Task.MergeFrom(other.Task);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (task_ == null) {
              Task = new global::LD.Protocol.ChallengeTaskInfo();
            }
            input.ReadMessage(Task);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 请求领取积分奖励
  /// </summary>
  public sealed partial class ReceiveChallengePointRewardRequest : pb::IMessage<ReceiveChallengePointRewardRequest> {
    private static readonly pb::MessageParser<ReceiveChallengePointRewardRequest> _parser = new pb::MessageParser<ReceiveChallengePointRewardRequest>(() => new ReceiveChallengePointRewardRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceiveChallengePointRewardRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ChallengeTaskReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveChallengePointRewardRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveChallengePointRewardRequest(ReceiveChallengePointRewardRequest other) : this() {
      pointRewards_ = other.pointRewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveChallengePointRewardRequest Clone() {
      return new ReceiveChallengePointRewardRequest(this);
    }

    /// <summary>Field number for the "pointRewards" field.</summary>
    public const int PointRewardsFieldNumber = 1;
    private static readonly pb::FieldCodec<int> _repeated_pointRewards_codec
        = pb::FieldCodec.ForInt32(10);
    private readonly pbc::RepeatedField<int> pointRewards_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> PointRewards {
      get { return pointRewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceiveChallengePointRewardRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceiveChallengePointRewardRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!pointRewards_.Equals(other.pointRewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= pointRewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      pointRewards_.WriteTo(output, _repeated_pointRewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += pointRewards_.CalculateSize(_repeated_pointRewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceiveChallengePointRewardRequest other) {
      if (other == null) {
        return;
      }
      pointRewards_.Add(other.pointRewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            pointRewards_.AddEntriesFrom(input, _repeated_pointRewards_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class ReceivedChallengePointRewardResponse : pb::IMessage<ReceivedChallengePointRewardResponse> {
    private static readonly pb::MessageParser<ReceivedChallengePointRewardResponse> _parser = new pb::MessageParser<ReceivedChallengePointRewardResponse>(() => new ReceivedChallengePointRewardResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceivedChallengePointRewardResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ChallengeTaskReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedChallengePointRewardResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedChallengePointRewardResponse(ReceivedChallengePointRewardResponse other) : this() {
      pointRewards_ = other.pointRewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedChallengePointRewardResponse Clone() {
      return new ReceivedChallengePointRewardResponse(this);
    }

    /// <summary>Field number for the "pointRewards" field.</summary>
    public const int PointRewardsFieldNumber = 1;
    private static readonly pb::FieldCodec<int> _repeated_pointRewards_codec
        = pb::FieldCodec.ForInt32(10);
    private readonly pbc::RepeatedField<int> pointRewards_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> PointRewards {
      get { return pointRewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceivedChallengePointRewardResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceivedChallengePointRewardResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!pointRewards_.Equals(other.pointRewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= pointRewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      pointRewards_.WriteTo(output, _repeated_pointRewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += pointRewards_.CalculateSize(_repeated_pointRewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceivedChallengePointRewardResponse other) {
      if (other == null) {
        return;
      }
      pointRewards_.Add(other.pointRewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            pointRewards_.AddEntriesFrom(input, _repeated_pointRewards_codec);
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
