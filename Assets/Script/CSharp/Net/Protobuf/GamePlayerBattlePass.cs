// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/GamePlayerBattlePass.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/GamePlayerBattlePass.proto</summary>
  public static partial class GamePlayerBattlePassReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/GamePlayerBattlePass.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static GamePlayerBattlePassReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiNDMlNQcm90by9HYW1lUGxheWVyQmF0dGxlUGFzcy5wcm90bxoTQzJTUHJv",
            "dG8vSXRlbS5wcm90bxoVQzJTUHJvdG8vQ29tbW9uLnByb3RvIlEKFEdQQmF0",
            "dGxlUGFzc0l0ZW1JbmZvEgoKAmlkGAEgASgFEg8KB2FkdmFuY2UYAiABKAgS",
            "DAoEZnJlZRgDIAEoCBIOCgZ1bmxvY2sYBCABKAgiSQocR1BCYXR0bGVQYXNz",
            "SW5mb1N5bmNSZXNwb25zZRIpCg5iYXR0bGVQYXNzSW5mbxgBIAMoCzIRLkdQ",
            "QmF0dGxlUGFzc0luZm8ipAEKEEdQQmF0dGxlUGFzc0luZm8SFAoMYmF0dGxl",
            "UGFzc0lkGAEgASgFEi0KDmJhdHRsZUl0ZW1JbmZvGAIgAygLMhUuR1BCYXR0",
            "bGVQYXNzSXRlbUluZm8SGQoFdGFza3MYAyADKAsyCi5DVGFza0luZm8SDwoH",
            "YWR2YW5jZRgEIAEoCBIfCgt1bmxvY2tUYXNrcxgFIAMoCzIKLkNUYXNrSW5m",
            "byI1CiJHUEJhdHRsZVBhc3NJdGVtVW5sb2NrU3luY1Jlc3BvbnNlEg8KB2l0",
            "ZW1JZHMYASADKAUiNgoXR1BCYXR0bGVQYXNzSXRlbUJ1eUluZm8SCgoCaWQY",
            "ASABKAUSDwoHYnV5VHlwZRgCIAEoBSJEChpHUEJhdHRsZVBhc3NJdGVtQnV5",
            "UmVxdWVzdBImCgRpbmZvGAEgAygLMhguR1BCYXR0bGVQYXNzSXRlbUJ1eUlu",
            "Zm8iZgobR1BCYXR0bGVQYXNzSXRlbUJ1eVJlc3BvbnNlEiYKBGluZm8YASAD",
            "KAsyGC5HUEJhdHRsZVBhc3NJdGVtQnV5SW5mbxIfCgZyZXdhcmQYAiADKAsy",
            "Dy5SZXdhcmRSZXNwb25zZSJDChlHUEJhdHRsZVBhc3NVbmxvY2tSZXF1ZXN0",
            "EhQKDGJhdHRsZVBhc3NJZBgBIAEoBRIQCghyZXdhcmRJZBgCIAEoBSJEChpH",
            "UEJhdHRsZVBhc3NVbmxvY2tSZXNwb25zZRIUCgxiYXR0bGVQYXNzSWQYASAB",
            "KAUSEAoIcmV3YXJkSWQYAiABKAUiTQokR1BCYXR0bGVQYXNzUmVjZWl2ZVRh",
            "c2tSZXdhcmRSZXF1ZXN0EhQKDGJhdHRsZVBhc3NJZBgBIAEoBRIPCgd0YXNr",
            "SWRzGAIgAygFIicKJUdQQmF0dGxlUGFzc1JlY2VpdmVUYXNrUmV3YXJkUmVz",
            "cG9uc2UiOQocR1BCYXR0bGVQYXNzVGFza1N5bmNSZXNwb25zZRIZCgV0YXNr",
            "cxgBIAMoCzIKLkNUYXNrSW5mbyI/CiJHUEJhdHRsZVBhc3NVbmxvY2tUYXNr",
            "U3luY1Jlc3BvbnNlEhkKBXRhc2tzGAEgAygLMgouQ1Rhc2tJbmZvKiwKE0dQ",
            "QmF0dGxlUGFzc0J1eVR5cGUSCAoERlJFRRAAEgsKB0FEVkFOQ0UQAUIjChNj",
            "b20uZ29sZGVuLnByb3RvY29sqgILTEQuUHJvdG9jb2xiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::LD.Protocol.ItemReflection.Descriptor, global::LD.Protocol.CommonReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::LD.Protocol.GPBattlePassBuyType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassItemInfo), global::LD.Protocol.GPBattlePassItemInfo.Parser, new[]{ "Id", "Advance", "Free", "Unlock" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassInfoSyncResponse), global::LD.Protocol.GPBattlePassInfoSyncResponse.Parser, new[]{ "BattlePassInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassInfo), global::LD.Protocol.GPBattlePassInfo.Parser, new[]{ "BattlePassId", "BattleItemInfo", "Tasks", "Advance", "UnlockTasks" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassItemUnlockSyncResponse), global::LD.Protocol.GPBattlePassItemUnlockSyncResponse.Parser, new[]{ "ItemIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassItemBuyInfo), global::LD.Protocol.GPBattlePassItemBuyInfo.Parser, new[]{ "Id", "BuyType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassItemBuyRequest), global::LD.Protocol.GPBattlePassItemBuyRequest.Parser, new[]{ "Info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassItemBuyResponse), global::LD.Protocol.GPBattlePassItemBuyResponse.Parser, new[]{ "Info", "Reward" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassUnlockRequest), global::LD.Protocol.GPBattlePassUnlockRequest.Parser, new[]{ "BattlePassId", "RewardId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassUnlockResponse), global::LD.Protocol.GPBattlePassUnlockResponse.Parser, new[]{ "BattlePassId", "RewardId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassReceiveTaskRewardRequest), global::LD.Protocol.GPBattlePassReceiveTaskRewardRequest.Parser, new[]{ "BattlePassId", "TaskIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassReceiveTaskRewardResponse), global::LD.Protocol.GPBattlePassReceiveTaskRewardResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassTaskSyncResponse), global::LD.Protocol.GPBattlePassTaskSyncResponse.Parser, new[]{ "Tasks" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.GPBattlePassUnlockTaskSyncResponse), global::LD.Protocol.GPBattlePassUnlockTaskSyncResponse.Parser, new[]{ "Tasks" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum GPBattlePassBuyType {
    [pbr::OriginalName("FREE")] Free = 0,
    [pbr::OriginalName("ADVANCE")] Advance = 1,
  }

  #endregion

  #region Messages
  public sealed partial class GPBattlePassItemInfo : pb::IMessage<GPBattlePassItemInfo> {
    private static readonly pb::MessageParser<GPBattlePassItemInfo> _parser = new pb::MessageParser<GPBattlePassItemInfo>(() => new GPBattlePassItemInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassItemInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemInfo(GPBattlePassItemInfo other) : this() {
      id_ = other.id_;
      advance_ = other.advance_;
      free_ = other.free_;
      unlock_ = other.unlock_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemInfo Clone() {
      return new GPBattlePassItemInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "advance" field.</summary>
    public const int AdvanceFieldNumber = 2;
    private bool advance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Advance {
      get { return advance_; }
      set {
        advance_ = value;
      }
    }

    /// <summary>Field number for the "free" field.</summary>
    public const int FreeFieldNumber = 3;
    private bool free_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Free {
      get { return free_; }
      set {
        free_ = value;
      }
    }

    /// <summary>Field number for the "unlock" field.</summary>
    public const int UnlockFieldNumber = 4;
    private bool unlock_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Unlock {
      get { return unlock_; }
      set {
        unlock_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassItemInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassItemInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Advance != other.Advance) return false;
      if (Free != other.Free) return false;
      if (Unlock != other.Unlock) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Advance != false) hash ^= Advance.GetHashCode();
      if (Free != false) hash ^= Free.GetHashCode();
      if (Unlock != false) hash ^= Unlock.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Advance != false) {
        output.WriteRawTag(16);
        output.WriteBool(Advance);
      }
      if (Free != false) {
        output.WriteRawTag(24);
        output.WriteBool(Free);
      }
      if (Unlock != false) {
        output.WriteRawTag(32);
        output.WriteBool(Unlock);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Advance != false) {
        size += 1 + 1;
      }
      if (Free != false) {
        size += 1 + 1;
      }
      if (Unlock != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassItemInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Advance != false) {
        Advance = other.Advance;
      }
      if (other.Free != false) {
        Free = other.Free;
      }
      if (other.Unlock != false) {
        Unlock = other.Unlock;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Advance = input.ReadBool();
            break;
          }
          case 24: {
            Free = input.ReadBool();
            break;
          }
          case 32: {
            Unlock = input.ReadBool();
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassInfoSyncResponse : pb::IMessage<GPBattlePassInfoSyncResponse> {
    private static readonly pb::MessageParser<GPBattlePassInfoSyncResponse> _parser = new pb::MessageParser<GPBattlePassInfoSyncResponse>(() => new GPBattlePassInfoSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassInfoSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassInfoSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassInfoSyncResponse(GPBattlePassInfoSyncResponse other) : this() {
      battlePassInfo_ = other.battlePassInfo_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassInfoSyncResponse Clone() {
      return new GPBattlePassInfoSyncResponse(this);
    }

    /// <summary>Field number for the "battlePassInfo" field.</summary>
    public const int BattlePassInfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.GPBattlePassInfo> _repeated_battlePassInfo_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.GPBattlePassInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.GPBattlePassInfo> battlePassInfo_ = new pbc::RepeatedField<global::LD.Protocol.GPBattlePassInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.GPBattlePassInfo> BattlePassInfo {
      get { return battlePassInfo_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassInfoSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassInfoSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!battlePassInfo_.Equals(other.battlePassInfo_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= battlePassInfo_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      battlePassInfo_.WriteTo(output, _repeated_battlePassInfo_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += battlePassInfo_.CalculateSize(_repeated_battlePassInfo_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassInfoSyncResponse other) {
      if (other == null) {
        return;
      }
      battlePassInfo_.Add(other.battlePassInfo_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            battlePassInfo_.AddEntriesFrom(input, _repeated_battlePassInfo_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassInfo : pb::IMessage<GPBattlePassInfo> {
    private static readonly pb::MessageParser<GPBattlePassInfo> _parser = new pb::MessageParser<GPBattlePassInfo>(() => new GPBattlePassInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassInfo(GPBattlePassInfo other) : this() {
      battlePassId_ = other.battlePassId_;
      battleItemInfo_ = other.battleItemInfo_.Clone();
      tasks_ = other.tasks_.Clone();
      advance_ = other.advance_;
      unlockTasks_ = other.unlockTasks_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassInfo Clone() {
      return new GPBattlePassInfo(this);
    }

    /// <summary>Field number for the "battlePassId" field.</summary>
    public const int BattlePassIdFieldNumber = 1;
    private int battlePassId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattlePassId {
      get { return battlePassId_; }
      set {
        battlePassId_ = value;
      }
    }

    /// <summary>Field number for the "battleItemInfo" field.</summary>
    public const int BattleItemInfoFieldNumber = 2;
    private static readonly pb::FieldCodec<global::LD.Protocol.GPBattlePassItemInfo> _repeated_battleItemInfo_codec
        = pb::FieldCodec.ForMessage(18, global::LD.Protocol.GPBattlePassItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemInfo> battleItemInfo_ = new pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemInfo> BattleItemInfo {
      get { return battleItemInfo_; }
    }

    /// <summary>Field number for the "tasks" field.</summary>
    public const int TasksFieldNumber = 3;
    private static readonly pb::FieldCodec<global::LD.Protocol.CTaskInfo> _repeated_tasks_codec
        = pb::FieldCodec.ForMessage(26, global::LD.Protocol.CTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.CTaskInfo> tasks_ = new pbc::RepeatedField<global::LD.Protocol.CTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.CTaskInfo> Tasks {
      get { return tasks_; }
    }

    /// <summary>Field number for the "advance" field.</summary>
    public const int AdvanceFieldNumber = 4;
    private bool advance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Advance {
      get { return advance_; }
      set {
        advance_ = value;
      }
    }

    /// <summary>Field number for the "unlockTasks" field.</summary>
    public const int UnlockTasksFieldNumber = 5;
    private static readonly pb::FieldCodec<global::LD.Protocol.CTaskInfo> _repeated_unlockTasks_codec
        = pb::FieldCodec.ForMessage(42, global::LD.Protocol.CTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.CTaskInfo> unlockTasks_ = new pbc::RepeatedField<global::LD.Protocol.CTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.CTaskInfo> UnlockTasks {
      get { return unlockTasks_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BattlePassId != other.BattlePassId) return false;
      if(!battleItemInfo_.Equals(other.battleItemInfo_)) return false;
      if(!tasks_.Equals(other.tasks_)) return false;
      if (Advance != other.Advance) return false;
      if(!unlockTasks_.Equals(other.unlockTasks_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (BattlePassId != 0) hash ^= BattlePassId.GetHashCode();
      hash ^= battleItemInfo_.GetHashCode();
      hash ^= tasks_.GetHashCode();
      if (Advance != false) hash ^= Advance.GetHashCode();
      hash ^= unlockTasks_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (BattlePassId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(BattlePassId);
      }
      battleItemInfo_.WriteTo(output, _repeated_battleItemInfo_codec);
      tasks_.WriteTo(output, _repeated_tasks_codec);
      if (Advance != false) {
        output.WriteRawTag(32);
        output.WriteBool(Advance);
      }
      unlockTasks_.WriteTo(output, _repeated_unlockTasks_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (BattlePassId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattlePassId);
      }
      size += battleItemInfo_.CalculateSize(_repeated_battleItemInfo_codec);
      size += tasks_.CalculateSize(_repeated_tasks_codec);
      if (Advance != false) {
        size += 1 + 1;
      }
      size += unlockTasks_.CalculateSize(_repeated_unlockTasks_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassInfo other) {
      if (other == null) {
        return;
      }
      if (other.BattlePassId != 0) {
        BattlePassId = other.BattlePassId;
      }
      battleItemInfo_.Add(other.battleItemInfo_);
      tasks_.Add(other.tasks_);
      if (other.Advance != false) {
        Advance = other.Advance;
      }
      unlockTasks_.Add(other.unlockTasks_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BattlePassId = input.ReadInt32();
            break;
          }
          case 18: {
            battleItemInfo_.AddEntriesFrom(input, _repeated_battleItemInfo_codec);
            break;
          }
          case 26: {
            tasks_.AddEntriesFrom(input, _repeated_tasks_codec);
            break;
          }
          case 32: {
            Advance = input.ReadBool();
            break;
          }
          case 42: {
            unlockTasks_.AddEntriesFrom(input, _repeated_unlockTasks_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassItemUnlockSyncResponse : pb::IMessage<GPBattlePassItemUnlockSyncResponse> {
    private static readonly pb::MessageParser<GPBattlePassItemUnlockSyncResponse> _parser = new pb::MessageParser<GPBattlePassItemUnlockSyncResponse>(() => new GPBattlePassItemUnlockSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassItemUnlockSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemUnlockSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemUnlockSyncResponse(GPBattlePassItemUnlockSyncResponse other) : this() {
      itemIds_ = other.itemIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemUnlockSyncResponse Clone() {
      return new GPBattlePassItemUnlockSyncResponse(this);
    }

    /// <summary>Field number for the "itemIds" field.</summary>
    public const int ItemIdsFieldNumber = 1;
    private static readonly pb::FieldCodec<int> _repeated_itemIds_codec
        = pb::FieldCodec.ForInt32(10);
    private readonly pbc::RepeatedField<int> itemIds_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> ItemIds {
      get { return itemIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassItemUnlockSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassItemUnlockSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!itemIds_.Equals(other.itemIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= itemIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      itemIds_.WriteTo(output, _repeated_itemIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += itemIds_.CalculateSize(_repeated_itemIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassItemUnlockSyncResponse other) {
      if (other == null) {
        return;
      }
      itemIds_.Add(other.itemIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            itemIds_.AddEntriesFrom(input, _repeated_itemIds_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassItemBuyInfo : pb::IMessage<GPBattlePassItemBuyInfo> {
    private static readonly pb::MessageParser<GPBattlePassItemBuyInfo> _parser = new pb::MessageParser<GPBattlePassItemBuyInfo>(() => new GPBattlePassItemBuyInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassItemBuyInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyInfo(GPBattlePassItemBuyInfo other) : this() {
      id_ = other.id_;
      buyType_ = other.buyType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyInfo Clone() {
      return new GPBattlePassItemBuyInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "buyType" field.</summary>
    public const int BuyTypeFieldNumber = 2;
    private int buyType_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BuyType {
      get { return buyType_; }
      set {
        buyType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassItemBuyInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassItemBuyInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (BuyType != other.BuyType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (BuyType != 0) hash ^= BuyType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (BuyType != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(BuyType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (BuyType != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BuyType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassItemBuyInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.BuyType != 0) {
        BuyType = other.BuyType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            BuyType = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassItemBuyRequest : pb::IMessage<GPBattlePassItemBuyRequest> {
    private static readonly pb::MessageParser<GPBattlePassItemBuyRequest> _parser = new pb::MessageParser<GPBattlePassItemBuyRequest>(() => new GPBattlePassItemBuyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassItemBuyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyRequest(GPBattlePassItemBuyRequest other) : this() {
      info_ = other.info_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyRequest Clone() {
      return new GPBattlePassItemBuyRequest(this);
    }

    /// <summary>Field number for the "info" field.</summary>
    public const int InfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.GPBattlePassItemBuyInfo> _repeated_info_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.GPBattlePassItemBuyInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemBuyInfo> info_ = new pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemBuyInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemBuyInfo> Info {
      get { return info_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassItemBuyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassItemBuyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!info_.Equals(other.info_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= info_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      info_.WriteTo(output, _repeated_info_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += info_.CalculateSize(_repeated_info_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassItemBuyRequest other) {
      if (other == null) {
        return;
      }
      info_.Add(other.info_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            info_.AddEntriesFrom(input, _repeated_info_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassItemBuyResponse : pb::IMessage<GPBattlePassItemBuyResponse> {
    private static readonly pb::MessageParser<GPBattlePassItemBuyResponse> _parser = new pb::MessageParser<GPBattlePassItemBuyResponse>(() => new GPBattlePassItemBuyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassItemBuyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyResponse(GPBattlePassItemBuyResponse other) : this() {
      info_ = other.info_.Clone();
      reward_ = other.reward_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassItemBuyResponse Clone() {
      return new GPBattlePassItemBuyResponse(this);
    }

    /// <summary>Field number for the "info" field.</summary>
    public const int InfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.GPBattlePassItemBuyInfo> _repeated_info_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.GPBattlePassItemBuyInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemBuyInfo> info_ = new pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemBuyInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.GPBattlePassItemBuyInfo> Info {
      get { return info_; }
    }

    /// <summary>Field number for the "reward" field.</summary>
    public const int RewardFieldNumber = 2;
    private static readonly pb::FieldCodec<global::LD.Protocol.RewardResponse> _repeated_reward_codec
        = pb::FieldCodec.ForMessage(18, global::LD.Protocol.RewardResponse.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.RewardResponse> reward_ = new pbc::RepeatedField<global::LD.Protocol.RewardResponse>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.RewardResponse> Reward {
      get { return reward_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassItemBuyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassItemBuyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!info_.Equals(other.info_)) return false;
      if(!reward_.Equals(other.reward_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= info_.GetHashCode();
      hash ^= reward_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      info_.WriteTo(output, _repeated_info_codec);
      reward_.WriteTo(output, _repeated_reward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += info_.CalculateSize(_repeated_info_codec);
      size += reward_.CalculateSize(_repeated_reward_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassItemBuyResponse other) {
      if (other == null) {
        return;
      }
      info_.Add(other.info_);
      reward_.Add(other.reward_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            info_.AddEntriesFrom(input, _repeated_info_codec);
            break;
          }
          case 18: {
            reward_.AddEntriesFrom(input, _repeated_reward_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassUnlockRequest : pb::IMessage<GPBattlePassUnlockRequest> {
    private static readonly pb::MessageParser<GPBattlePassUnlockRequest> _parser = new pb::MessageParser<GPBattlePassUnlockRequest>(() => new GPBattlePassUnlockRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassUnlockRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockRequest(GPBattlePassUnlockRequest other) : this() {
      battlePassId_ = other.battlePassId_;
      rewardId_ = other.rewardId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockRequest Clone() {
      return new GPBattlePassUnlockRequest(this);
    }

    /// <summary>Field number for the "battlePassId" field.</summary>
    public const int BattlePassIdFieldNumber = 1;
    private int battlePassId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattlePassId {
      get { return battlePassId_; }
      set {
        battlePassId_ = value;
      }
    }

    /// <summary>Field number for the "rewardId" field.</summary>
    public const int RewardIdFieldNumber = 2;
    private int rewardId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RewardId {
      get { return rewardId_; }
      set {
        rewardId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassUnlockRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassUnlockRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BattlePassId != other.BattlePassId) return false;
      if (RewardId != other.RewardId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (BattlePassId != 0) hash ^= BattlePassId.GetHashCode();
      if (RewardId != 0) hash ^= RewardId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (BattlePassId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(BattlePassId);
      }
      if (RewardId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(RewardId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (BattlePassId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattlePassId);
      }
      if (RewardId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RewardId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassUnlockRequest other) {
      if (other == null) {
        return;
      }
      if (other.BattlePassId != 0) {
        BattlePassId = other.BattlePassId;
      }
      if (other.RewardId != 0) {
        RewardId = other.RewardId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BattlePassId = input.ReadInt32();
            break;
          }
          case 16: {
            RewardId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassUnlockResponse : pb::IMessage<GPBattlePassUnlockResponse> {
    private static readonly pb::MessageParser<GPBattlePassUnlockResponse> _parser = new pb::MessageParser<GPBattlePassUnlockResponse>(() => new GPBattlePassUnlockResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassUnlockResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockResponse(GPBattlePassUnlockResponse other) : this() {
      battlePassId_ = other.battlePassId_;
      rewardId_ = other.rewardId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockResponse Clone() {
      return new GPBattlePassUnlockResponse(this);
    }

    /// <summary>Field number for the "battlePassId" field.</summary>
    public const int BattlePassIdFieldNumber = 1;
    private int battlePassId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattlePassId {
      get { return battlePassId_; }
      set {
        battlePassId_ = value;
      }
    }

    /// <summary>Field number for the "rewardId" field.</summary>
    public const int RewardIdFieldNumber = 2;
    private int rewardId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RewardId {
      get { return rewardId_; }
      set {
        rewardId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassUnlockResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassUnlockResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BattlePassId != other.BattlePassId) return false;
      if (RewardId != other.RewardId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (BattlePassId != 0) hash ^= BattlePassId.GetHashCode();
      if (RewardId != 0) hash ^= RewardId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (BattlePassId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(BattlePassId);
      }
      if (RewardId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(RewardId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (BattlePassId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattlePassId);
      }
      if (RewardId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RewardId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassUnlockResponse other) {
      if (other == null) {
        return;
      }
      if (other.BattlePassId != 0) {
        BattlePassId = other.BattlePassId;
      }
      if (other.RewardId != 0) {
        RewardId = other.RewardId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BattlePassId = input.ReadInt32();
            break;
          }
          case 16: {
            RewardId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassReceiveTaskRewardRequest : pb::IMessage<GPBattlePassReceiveTaskRewardRequest> {
    private static readonly pb::MessageParser<GPBattlePassReceiveTaskRewardRequest> _parser = new pb::MessageParser<GPBattlePassReceiveTaskRewardRequest>(() => new GPBattlePassReceiveTaskRewardRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassReceiveTaskRewardRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassReceiveTaskRewardRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassReceiveTaskRewardRequest(GPBattlePassReceiveTaskRewardRequest other) : this() {
      battlePassId_ = other.battlePassId_;
      taskIds_ = other.taskIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassReceiveTaskRewardRequest Clone() {
      return new GPBattlePassReceiveTaskRewardRequest(this);
    }

    /// <summary>Field number for the "battlePassId" field.</summary>
    public const int BattlePassIdFieldNumber = 1;
    private int battlePassId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattlePassId {
      get { return battlePassId_; }
      set {
        battlePassId_ = value;
      }
    }

    /// <summary>Field number for the "taskIds" field.</summary>
    public const int TaskIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<int> _repeated_taskIds_codec
        = pb::FieldCodec.ForInt32(18);
    private readonly pbc::RepeatedField<int> taskIds_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> TaskIds {
      get { return taskIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassReceiveTaskRewardRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassReceiveTaskRewardRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BattlePassId != other.BattlePassId) return false;
      if(!taskIds_.Equals(other.taskIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (BattlePassId != 0) hash ^= BattlePassId.GetHashCode();
      hash ^= taskIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (BattlePassId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(BattlePassId);
      }
      taskIds_.WriteTo(output, _repeated_taskIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (BattlePassId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattlePassId);
      }
      size += taskIds_.CalculateSize(_repeated_taskIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassReceiveTaskRewardRequest other) {
      if (other == null) {
        return;
      }
      if (other.BattlePassId != 0) {
        BattlePassId = other.BattlePassId;
      }
      taskIds_.Add(other.taskIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BattlePassId = input.ReadInt32();
            break;
          }
          case 18:
          case 16: {
            taskIds_.AddEntriesFrom(input, _repeated_taskIds_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class GPBattlePassReceiveTaskRewardResponse : pb::IMessage<GPBattlePassReceiveTaskRewardResponse> {
    private static readonly pb::MessageParser<GPBattlePassReceiveTaskRewardResponse> _parser = new pb::MessageParser<GPBattlePassReceiveTaskRewardResponse>(() => new GPBattlePassReceiveTaskRewardResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassReceiveTaskRewardResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassReceiveTaskRewardResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassReceiveTaskRewardResponse(GPBattlePassReceiveTaskRewardResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassReceiveTaskRewardResponse Clone() {
      return new GPBattlePassReceiveTaskRewardResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassReceiveTaskRewardResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassReceiveTaskRewardResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassReceiveTaskRewardResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  /// <summary>
  /// 任务列表
  /// </summary>
  public sealed partial class GPBattlePassTaskSyncResponse : pb::IMessage<GPBattlePassTaskSyncResponse> {
    private static readonly pb::MessageParser<GPBattlePassTaskSyncResponse> _parser = new pb::MessageParser<GPBattlePassTaskSyncResponse>(() => new GPBattlePassTaskSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassTaskSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassTaskSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassTaskSyncResponse(GPBattlePassTaskSyncResponse other) : this() {
      tasks_ = other.tasks_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassTaskSyncResponse Clone() {
      return new GPBattlePassTaskSyncResponse(this);
    }

    /// <summary>Field number for the "tasks" field.</summary>
    public const int TasksFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.CTaskInfo> _repeated_tasks_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.CTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.CTaskInfo> tasks_ = new pbc::RepeatedField<global::LD.Protocol.CTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.CTaskInfo> Tasks {
      get { return tasks_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassTaskSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassTaskSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!tasks_.Equals(other.tasks_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= tasks_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      tasks_.WriteTo(output, _repeated_tasks_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += tasks_.CalculateSize(_repeated_tasks_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassTaskSyncResponse other) {
      if (other == null) {
        return;
      }
      tasks_.Add(other.tasks_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            tasks_.AddEntriesFrom(input, _repeated_tasks_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 解锁任务列表
  /// </summary>
  public sealed partial class GPBattlePassUnlockTaskSyncResponse : pb::IMessage<GPBattlePassUnlockTaskSyncResponse> {
    private static readonly pb::MessageParser<GPBattlePassUnlockTaskSyncResponse> _parser = new pb::MessageParser<GPBattlePassUnlockTaskSyncResponse>(() => new GPBattlePassUnlockTaskSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GPBattlePassUnlockTaskSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.GamePlayerBattlePassReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockTaskSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockTaskSyncResponse(GPBattlePassUnlockTaskSyncResponse other) : this() {
      tasks_ = other.tasks_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GPBattlePassUnlockTaskSyncResponse Clone() {
      return new GPBattlePassUnlockTaskSyncResponse(this);
    }

    /// <summary>Field number for the "tasks" field.</summary>
    public const int TasksFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.CTaskInfo> _repeated_tasks_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.CTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.CTaskInfo> tasks_ = new pbc::RepeatedField<global::LD.Protocol.CTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.CTaskInfo> Tasks {
      get { return tasks_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GPBattlePassUnlockTaskSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GPBattlePassUnlockTaskSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!tasks_.Equals(other.tasks_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= tasks_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      tasks_.WriteTo(output, _repeated_tasks_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += tasks_.CalculateSize(_repeated_tasks_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GPBattlePassUnlockTaskSyncResponse other) {
      if (other == null) {
        return;
      }
      tasks_.Add(other.tasks_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            tasks_.AddEntriesFrom(input, _repeated_tasks_codec);
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
