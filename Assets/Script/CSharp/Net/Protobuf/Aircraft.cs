// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/Aircraft.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/Aircraft.proto</summary>
  public static partial class AircraftReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/Aircraft.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static AircraftReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChdDMlNQcm90by9BaXJjcmFmdC5wcm90byLNAQoUQWlyY3JhZnRTeW5jUmVz",
            "cG9uc2USJAoNYWlyY3JhZnRJbmZvcxgBIAMoCzINLkFpcmNyYWZ0SW5mbxIr",
            "ChBhaXJjcmFmdFNsb3RJbmZvGAIgASgLMhEuQWlyY3JhZnRTbG90SW5mbxIy",
            "ChRhaXJjcmFmdFN1cHJlbWVJbmZvcxgDIAMoCzIULkFpcmNyYWZ0U3VwcmVt",
            "ZUluZm8SFwoPcmVmaW5lbWVudExldmVsGAQgASgFEhUKDXJlZmluZW1lbnRF",
            "eHAYBSABKAUimQEKDEFpcmNyYWZ0SW5mbxISCgphaXJjcmFmdElkGAEgASgF",
            "Eg8KB3F1YWxpdHkYAiABKAUSDQoFbGV2ZWwYAyABKAUSDAoEbHVjaxgEIAEo",
            "BRI3ChZhaXJjcmFmdFJlZmluZW1lbnRJbmZvGAUgASgLMhcuQWlyY3JhZnRS",
            "ZWZpbmVtZW50SW5mbxIOCgZza2luSWQYBiABKAUidgoQQWlyY3JhZnRTbG90",
            "SW5mbxIxCghzbG90SW5mbxgBIAMoCzIfLkFpcmNyYWZ0U2xvdEluZm8uU2xv",
            "dEluZm9FbnRyeRovCg1TbG90SW5mb0VudHJ5EgsKA2tleRgBIAEoBRINCgV2",
            "YWx1ZRgCIAEoBToCOAEiZgoWQWlyY3JhZnRSZWZpbmVtZW50SW5mbxIgCgdl",
            "ZmZlY3RzGAEgAygLMg8uQWlyY3JhZnRFZmZlY3QSKgoRcmVmaW5lbWVudEVm",
            "ZmVjdHMYAiADKAsyDy5BaXJjcmFmdEVmZmVjdCJHCg5BaXJjcmFmdEVmZmVj",
            "dBIQCghlZmZlY3RJZBgBIAEoBRITCgtlZmZlY3RWYWx1ZRgCIAEoARIOCgZp",
            "c0xvY2sYAyABKAgiOAoTQWlyY3JhZnRTdXByZW1lSW5mbxISCgphaXJjcmFm",
            "dElkGAEgASgFEg0KBWlzR2V0GAIgASgIIiwKFkFpcmNyYWZ0TGV2ZWxVcFJl",
            "cXVlc3QSEgoKYWlyY3JhZnRJZBgBIAEoBSI+ChdBaXJjcmFmdExldmVsVXBS",
            "ZXNwb25zZRIjCgxhaXJjcmFmdEluZm8YASABKAsyDS5BaXJjcmFmdEluZm8i",
            "KgoUQWlyY3JhZnRSZXNldFJlcXVlc3QSEgoKYWlyY3JhZnRJZBgBIAEoBSI8",
            "ChVBaXJjcmFmdFJlc2V0UmVzcG9uc2USIwoMYWlyY3JhZnRJbmZvGAEgASgL",
            "Mg0uQWlyY3JhZnRJbmZvIiwKFkFpcmNyYWZ0R3JhZGVVcFJlcXVlc3QSEgoK",
            "YWlyY3JhZnRJZBgBIAEoBSJRChdBaXJjcmFmdEdyYWRlVXBSZXNwb25zZRIj",
            "CgxhaXJjcmFmdEluZm8YASABKAsyDS5BaXJjcmFmdEluZm8SEQoJaXNBZHZh",
            "bmNlGAIgASgIIkMKGUFpcmNyYWZ0UmVmaW5lbWVudFJlcXVlc3QSEgoKYWly",
            "Y3JhZnRJZBgBIAEoBRISCgpsb2NrSW5kZXhzGAIgAygFInEKGkFpcmNyYWZ0",
            "UmVmaW5lbWVudFJlc3BvbnNlEiMKDGFpcmNyYWZ0SW5mbxgBIAEoCzINLkFp",
            "cmNyYWZ0SW5mbxIXCg9yZWZpbmVtZW50TGV2ZWwYAiABKAUSFQoNcmVmaW5l",
            "bWVudEV4cBgDIAEoBSJCCh9BaXJjcmFmdFNlbGVjdFJlZmluZW1lbnRSZXF1",
            "ZXN0EhIKCmFpcmNyYWZ0SWQYASABKAUSCwoDb3ByGAIgASgIIkcKIEFpcmNy",
            "YWZ0U2VsZWN0UmVmaW5lbWVudFJlc3BvbnNlEiMKDGFpcmNyYWZ0SW5mbxgB",
            "IAEoCzINLkFpcmNyYWZ0SW5mbyIvChlBaXJjcmFmdFN1cHJlbWVHZXRSZXF1",
            "ZXN0EhIKCmFpcmNyYWZ0SWQYASABKAUiTwoaQWlyY3JhZnRTdXByZW1lR2V0",
            "UmVzcG9uc2USMQoTYWlyY3JhZnRTdXByZW1lSW5mbxgBIAEoCzIULkFpcmNy",
            "YWZ0U3VwcmVtZUluZm8iQwoUQWlyY3JhZnRQdXRPblJlcXVlc3QSKwoQYWly",
            "Y3JhZnRTbG90SW5mbxgBIAEoCzIRLkFpcmNyYWZ0U2xvdEluZm8iRAoVQWly",
            "Y3JhZnRQdXRPblJlc3BvbnNlEisKEGFpcmNyYWZ0U2xvdEluZm8YASABKAsy",
            "ES5BaXJjcmFmdFNsb3RJbmZvIisKFUFpcmNyYWZ0VW5sb2NrUmVxdWVzdBIS",
            "CgphaXJjcmFmdElkGAEgASgFIiwKFkFpcmNyYWZ0VW5sb2NrUmVzcG9uc2US",
            "EgoKYWlyY3JhZnRJZBgBIAEoBUIjChNjb20uZ29sZGVuLnByb3RvY29sqgIL",
            "TEQuUHJvdG9jb2xiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftSyncResponse), global::LD.Protocol.AircraftSyncResponse.Parser, new[]{ "AircraftInfos", "AircraftSlotInfo", "AircraftSupremeInfos", "RefinementLevel", "RefinementExp" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftInfo), global::LD.Protocol.AircraftInfo.Parser, new[]{ "AircraftId", "Quality", "Level", "Luck", "AircraftRefinementInfo", "SkinId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftSlotInfo), global::LD.Protocol.AircraftSlotInfo.Parser, new[]{ "SlotInfo" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftRefinementInfo), global::LD.Protocol.AircraftRefinementInfo.Parser, new[]{ "Effects", "RefinementEffects" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftEffect), global::LD.Protocol.AircraftEffect.Parser, new[]{ "EffectId", "EffectValue", "IsLock" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftSupremeInfo), global::LD.Protocol.AircraftSupremeInfo.Parser, new[]{ "AircraftId", "IsGet" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftLevelUpRequest), global::LD.Protocol.AircraftLevelUpRequest.Parser, new[]{ "AircraftId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftLevelUpResponse), global::LD.Protocol.AircraftLevelUpResponse.Parser, new[]{ "AircraftInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftResetRequest), global::LD.Protocol.AircraftResetRequest.Parser, new[]{ "AircraftId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftResetResponse), global::LD.Protocol.AircraftResetResponse.Parser, new[]{ "AircraftInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftGradeUpRequest), global::LD.Protocol.AircraftGradeUpRequest.Parser, new[]{ "AircraftId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftGradeUpResponse), global::LD.Protocol.AircraftGradeUpResponse.Parser, new[]{ "AircraftInfo", "IsAdvance" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftRefinementRequest), global::LD.Protocol.AircraftRefinementRequest.Parser, new[]{ "AircraftId", "LockIndexs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftRefinementResponse), global::LD.Protocol.AircraftRefinementResponse.Parser, new[]{ "AircraftInfo", "RefinementLevel", "RefinementExp" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftSelectRefinementRequest), global::LD.Protocol.AircraftSelectRefinementRequest.Parser, new[]{ "AircraftId", "Opr" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftSelectRefinementResponse), global::LD.Protocol.AircraftSelectRefinementResponse.Parser, new[]{ "AircraftInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftSupremeGetRequest), global::LD.Protocol.AircraftSupremeGetRequest.Parser, new[]{ "AircraftId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftSupremeGetResponse), global::LD.Protocol.AircraftSupremeGetResponse.Parser, new[]{ "AircraftSupremeInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftPutOnRequest), global::LD.Protocol.AircraftPutOnRequest.Parser, new[]{ "AircraftSlotInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftPutOnResponse), global::LD.Protocol.AircraftPutOnResponse.Parser, new[]{ "AircraftSlotInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftUnlockRequest), global::LD.Protocol.AircraftUnlockRequest.Parser, new[]{ "AircraftId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.AircraftUnlockResponse), global::LD.Protocol.AircraftUnlockResponse.Parser, new[]{ "AircraftId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  ///机甲平台数据同步(新增机甲平台也会发送,此时aircraftInfos只有增量信息)
  /// </summary>
  public sealed partial class AircraftSyncResponse : pb::IMessage<AircraftSyncResponse> {
    private static readonly pb::MessageParser<AircraftSyncResponse> _parser = new pb::MessageParser<AircraftSyncResponse>(() => new AircraftSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSyncResponse(AircraftSyncResponse other) : this() {
      aircraftInfos_ = other.aircraftInfos_.Clone();
      aircraftSlotInfo_ = other.aircraftSlotInfo_ != null ? other.aircraftSlotInfo_.Clone() : null;
      aircraftSupremeInfos_ = other.aircraftSupremeInfos_.Clone();
      refinementLevel_ = other.refinementLevel_;
      refinementExp_ = other.refinementExp_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSyncResponse Clone() {
      return new AircraftSyncResponse(this);
    }

    /// <summary>Field number for the "aircraftInfos" field.</summary>
    public const int AircraftInfosFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.AircraftInfo> _repeated_aircraftInfos_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.AircraftInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.AircraftInfo> aircraftInfos_ = new pbc::RepeatedField<global::LD.Protocol.AircraftInfo>();
    /// <summary>
    ///所有机甲平台信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.AircraftInfo> AircraftInfos {
      get { return aircraftInfos_; }
    }

    /// <summary>Field number for the "aircraftSlotInfo" field.</summary>
    public const int AircraftSlotInfoFieldNumber = 2;
    private global::LD.Protocol.AircraftSlotInfo aircraftSlotInfo_;
    /// <summary>
    ///机甲平台上阵信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftSlotInfo AircraftSlotInfo {
      get { return aircraftSlotInfo_; }
      set {
        aircraftSlotInfo_ = value;
      }
    }

    /// <summary>Field number for the "aircraftSupremeInfos" field.</summary>
    public const int AircraftSupremeInfosFieldNumber = 3;
    private static readonly pb::FieldCodec<global::LD.Protocol.AircraftSupremeInfo> _repeated_aircraftSupremeInfos_codec
        = pb::FieldCodec.ForMessage(26, global::LD.Protocol.AircraftSupremeInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.AircraftSupremeInfo> aircraftSupremeInfos_ = new pbc::RepeatedField<global::LD.Protocol.AircraftSupremeInfo>();
    /// <summary>
    ///至尊机甲领取信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.AircraftSupremeInfo> AircraftSupremeInfos {
      get { return aircraftSupremeInfos_; }
    }

    /// <summary>Field number for the "refinementLevel" field.</summary>
    public const int RefinementLevelFieldNumber = 4;
    private int refinementLevel_;
    /// <summary>
    ///洗练等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RefinementLevel {
      get { return refinementLevel_; }
      set {
        refinementLevel_ = value;
      }
    }

    /// <summary>Field number for the "refinementExp" field.</summary>
    public const int RefinementExpFieldNumber = 5;
    private int refinementExp_;
    /// <summary>
    ///洗练经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RefinementExp {
      get { return refinementExp_; }
      set {
        refinementExp_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!aircraftInfos_.Equals(other.aircraftInfos_)) return false;
      if (!object.Equals(AircraftSlotInfo, other.AircraftSlotInfo)) return false;
      if(!aircraftSupremeInfos_.Equals(other.aircraftSupremeInfos_)) return false;
      if (RefinementLevel != other.RefinementLevel) return false;
      if (RefinementExp != other.RefinementExp) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= aircraftInfos_.GetHashCode();
      if (aircraftSlotInfo_ != null) hash ^= AircraftSlotInfo.GetHashCode();
      hash ^= aircraftSupremeInfos_.GetHashCode();
      if (RefinementLevel != 0) hash ^= RefinementLevel.GetHashCode();
      if (RefinementExp != 0) hash ^= RefinementExp.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      aircraftInfos_.WriteTo(output, _repeated_aircraftInfos_codec);
      if (aircraftSlotInfo_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(AircraftSlotInfo);
      }
      aircraftSupremeInfos_.WriteTo(output, _repeated_aircraftSupremeInfos_codec);
      if (RefinementLevel != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(RefinementLevel);
      }
      if (RefinementExp != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(RefinementExp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += aircraftInfos_.CalculateSize(_repeated_aircraftInfos_codec);
      if (aircraftSlotInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftSlotInfo);
      }
      size += aircraftSupremeInfos_.CalculateSize(_repeated_aircraftSupremeInfos_codec);
      if (RefinementLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RefinementLevel);
      }
      if (RefinementExp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RefinementExp);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftSyncResponse other) {
      if (other == null) {
        return;
      }
      aircraftInfos_.Add(other.aircraftInfos_);
      if (other.aircraftSlotInfo_ != null) {
        if (aircraftSlotInfo_ == null) {
          AircraftSlotInfo = new global::LD.Protocol.AircraftSlotInfo();
        }
        AircraftSlotInfo.MergeFrom(other.AircraftSlotInfo);
      }
      aircraftSupremeInfos_.Add(other.aircraftSupremeInfos_);
      if (other.RefinementLevel != 0) {
        RefinementLevel = other.RefinementLevel;
      }
      if (other.RefinementExp != 0) {
        RefinementExp = other.RefinementExp;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            aircraftInfos_.AddEntriesFrom(input, _repeated_aircraftInfos_codec);
            break;
          }
          case 18: {
            if (aircraftSlotInfo_ == null) {
              AircraftSlotInfo = new global::LD.Protocol.AircraftSlotInfo();
            }
            input.ReadMessage(AircraftSlotInfo);
            break;
          }
          case 26: {
            aircraftSupremeInfos_.AddEntriesFrom(input, _repeated_aircraftSupremeInfos_codec);
            break;
          }
          case 32: {
            RefinementLevel = input.ReadInt32();
            break;
          }
          case 40: {
            RefinementExp = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台信息
  /// </summary>
  public sealed partial class AircraftInfo : pb::IMessage<AircraftInfo> {
    private static readonly pb::MessageParser<AircraftInfo> _parser = new pb::MessageParser<AircraftInfo>(() => new AircraftInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftInfo(AircraftInfo other) : this() {
      aircraftId_ = other.aircraftId_;
      quality_ = other.quality_;
      level_ = other.level_;
      luck_ = other.luck_;
      aircraftRefinementInfo_ = other.aircraftRefinementInfo_ != null ? other.aircraftRefinementInfo_.Clone() : null;
      skinId_ = other.skinId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftInfo Clone() {
      return new AircraftInfo(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    /// <summary>Field number for the "quality" field.</summary>
    public const int QualityFieldNumber = 2;
    private int quality_;
    /// <summary>
    ///品质
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Quality {
      get { return quality_; }
      set {
        quality_ = value;
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 3;
    private int level_;
    /// <summary>
    ///等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "luck" field.</summary>
    public const int LuckFieldNumber = 4;
    private int luck_;
    /// <summary>
    ///幸运值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Luck {
      get { return luck_; }
      set {
        luck_ = value;
      }
    }

    /// <summary>Field number for the "aircraftRefinementInfo" field.</summary>
    public const int AircraftRefinementInfoFieldNumber = 5;
    private global::LD.Protocol.AircraftRefinementInfo aircraftRefinementInfo_;
    /// <summary>
    ///洗练信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftRefinementInfo AircraftRefinementInfo {
      get { return aircraftRefinementInfo_; }
      set {
        aircraftRefinementInfo_ = value;
      }
    }

    /// <summary>Field number for the "skinId" field.</summary>
    public const int SkinIdFieldNumber = 6;
    private int skinId_;
    /// <summary>
    ///机甲平台皮肤
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SkinId {
      get { return skinId_; }
      set {
        skinId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      if (Quality != other.Quality) return false;
      if (Level != other.Level) return false;
      if (Luck != other.Luck) return false;
      if (!object.Equals(AircraftRefinementInfo, other.AircraftRefinementInfo)) return false;
      if (SkinId != other.SkinId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (Quality != 0) hash ^= Quality.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (Luck != 0) hash ^= Luck.GetHashCode();
      if (aircraftRefinementInfo_ != null) hash ^= AircraftRefinementInfo.GetHashCode();
      if (SkinId != 0) hash ^= SkinId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (Quality != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Quality);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Level);
      }
      if (Luck != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Luck);
      }
      if (aircraftRefinementInfo_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(AircraftRefinementInfo);
      }
      if (SkinId != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(SkinId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (Quality != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Quality);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Level);
      }
      if (Luck != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Luck);
      }
      if (aircraftRefinementInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftRefinementInfo);
      }
      if (SkinId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SkinId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftInfo other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      if (other.Quality != 0) {
        Quality = other.Quality;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.Luck != 0) {
        Luck = other.Luck;
      }
      if (other.aircraftRefinementInfo_ != null) {
        if (aircraftRefinementInfo_ == null) {
          AircraftRefinementInfo = new global::LD.Protocol.AircraftRefinementInfo();
        }
        AircraftRefinementInfo.MergeFrom(other.AircraftRefinementInfo);
      }
      if (other.SkinId != 0) {
        SkinId = other.SkinId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
          case 16: {
            Quality = input.ReadInt32();
            break;
          }
          case 24: {
            Level = input.ReadInt32();
            break;
          }
          case 32: {
            Luck = input.ReadInt32();
            break;
          }
          case 42: {
            if (aircraftRefinementInfo_ == null) {
              AircraftRefinementInfo = new global::LD.Protocol.AircraftRefinementInfo();
            }
            input.ReadMessage(AircraftRefinementInfo);
            break;
          }
          case 48: {
            SkinId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台槽位信息
  /// </summary>
  public sealed partial class AircraftSlotInfo : pb::IMessage<AircraftSlotInfo> {
    private static readonly pb::MessageParser<AircraftSlotInfo> _parser = new pb::MessageParser<AircraftSlotInfo>(() => new AircraftSlotInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftSlotInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSlotInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSlotInfo(AircraftSlotInfo other) : this() {
      slotInfo_ = other.slotInfo_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSlotInfo Clone() {
      return new AircraftSlotInfo(this);
    }

    /// <summary>Field number for the "slotInfo" field.</summary>
    public const int SlotInfoFieldNumber = 1;
    private static readonly pbc::MapField<int, int>.Codec _map_slotInfo_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 10);
    private readonly pbc::MapField<int, int> slotInfo_ = new pbc::MapField<int, int>();
    /// <summary>
    ///key:槽位位置, value:机甲平台id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> SlotInfo {
      get { return slotInfo_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftSlotInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftSlotInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!SlotInfo.Equals(other.SlotInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= SlotInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      slotInfo_.WriteTo(output, _map_slotInfo_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += slotInfo_.CalculateSize(_map_slotInfo_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftSlotInfo other) {
      if (other == null) {
        return;
      }
      slotInfo_.Add(other.slotInfo_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            slotInfo_.AddEntriesFrom(input, _map_slotInfo_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///平台洗练信息
  /// </summary>
  public sealed partial class AircraftRefinementInfo : pb::IMessage<AircraftRefinementInfo> {
    private static readonly pb::MessageParser<AircraftRefinementInfo> _parser = new pb::MessageParser<AircraftRefinementInfo>(() => new AircraftRefinementInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftRefinementInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementInfo(AircraftRefinementInfo other) : this() {
      effects_ = other.effects_.Clone();
      refinementEffects_ = other.refinementEffects_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementInfo Clone() {
      return new AircraftRefinementInfo(this);
    }

    /// <summary>Field number for the "effects" field.</summary>
    public const int EffectsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.AircraftEffect> _repeated_effects_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.AircraftEffect.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.AircraftEffect> effects_ = new pbc::RepeatedField<global::LD.Protocol.AircraftEffect>();
    /// <summary>
    ///生效的洗练结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.AircraftEffect> Effects {
      get { return effects_; }
    }

    /// <summary>Field number for the "refinementEffects" field.</summary>
    public const int RefinementEffectsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::LD.Protocol.AircraftEffect> _repeated_refinementEffects_codec
        = pb::FieldCodec.ForMessage(18, global::LD.Protocol.AircraftEffect.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.AircraftEffect> refinementEffects_ = new pbc::RepeatedField<global::LD.Protocol.AircraftEffect>();
    /// <summary>
    ///临时的洗练结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.AircraftEffect> RefinementEffects {
      get { return refinementEffects_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftRefinementInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftRefinementInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!effects_.Equals(other.effects_)) return false;
      if(!refinementEffects_.Equals(other.refinementEffects_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= effects_.GetHashCode();
      hash ^= refinementEffects_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      effects_.WriteTo(output, _repeated_effects_codec);
      refinementEffects_.WriteTo(output, _repeated_refinementEffects_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += effects_.CalculateSize(_repeated_effects_codec);
      size += refinementEffects_.CalculateSize(_repeated_refinementEffects_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftRefinementInfo other) {
      if (other == null) {
        return;
      }
      effects_.Add(other.effects_);
      refinementEffects_.Add(other.refinementEffects_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            effects_.AddEntriesFrom(input, _repeated_effects_codec);
            break;
          }
          case 18: {
            refinementEffects_.AddEntriesFrom(input, _repeated_refinementEffects_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///单条洗练效果
  /// </summary>
  public sealed partial class AircraftEffect : pb::IMessage<AircraftEffect> {
    private static readonly pb::MessageParser<AircraftEffect> _parser = new pb::MessageParser<AircraftEffect>(() => new AircraftEffect());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftEffect> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftEffect() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftEffect(AircraftEffect other) : this() {
      effectId_ = other.effectId_;
      effectValue_ = other.effectValue_;
      isLock_ = other.isLock_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftEffect Clone() {
      return new AircraftEffect(this);
    }

    /// <summary>Field number for the "effectId" field.</summary>
    public const int EffectIdFieldNumber = 1;
    private int effectId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int EffectId {
      get { return effectId_; }
      set {
        effectId_ = value;
      }
    }

    /// <summary>Field number for the "effectValue" field.</summary>
    public const int EffectValueFieldNumber = 2;
    private double effectValue_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public double EffectValue {
      get { return effectValue_; }
      set {
        effectValue_ = value;
      }
    }

    /// <summary>Field number for the "isLock" field.</summary>
    public const int IsLockFieldNumber = 3;
    private bool isLock_;
    /// <summary>
    ///是否锁定
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool IsLock {
      get { return isLock_; }
      set {
        isLock_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftEffect);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftEffect other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (EffectId != other.EffectId) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(EffectValue, other.EffectValue)) return false;
      if (IsLock != other.IsLock) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (EffectId != 0) hash ^= EffectId.GetHashCode();
      if (EffectValue != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(EffectValue);
      if (IsLock != false) hash ^= IsLock.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (EffectId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(EffectId);
      }
      if (EffectValue != 0D) {
        output.WriteRawTag(17);
        output.WriteDouble(EffectValue);
      }
      if (IsLock != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsLock);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (EffectId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(EffectId);
      }
      if (EffectValue != 0D) {
        size += 1 + 8;
      }
      if (IsLock != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftEffect other) {
      if (other == null) {
        return;
      }
      if (other.EffectId != 0) {
        EffectId = other.EffectId;
      }
      if (other.EffectValue != 0D) {
        EffectValue = other.EffectValue;
      }
      if (other.IsLock != false) {
        IsLock = other.IsLock;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            EffectId = input.ReadInt32();
            break;
          }
          case 17: {
            EffectValue = input.ReadDouble();
            break;
          }
          case 24: {
            IsLock = input.ReadBool();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///ss机甲领取信息
  /// </summary>
  public sealed partial class AircraftSupremeInfo : pb::IMessage<AircraftSupremeInfo> {
    private static readonly pb::MessageParser<AircraftSupremeInfo> _parser = new pb::MessageParser<AircraftSupremeInfo>(() => new AircraftSupremeInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftSupremeInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeInfo(AircraftSupremeInfo other) : this() {
      aircraftId_ = other.aircraftId_;
      isGet_ = other.isGet_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeInfo Clone() {
      return new AircraftSupremeInfo(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///ss机甲配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    /// <summary>Field number for the "isGet" field.</summary>
    public const int IsGetFieldNumber = 2;
    private bool isGet_;
    /// <summary>
    ///是否领取
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool IsGet {
      get { return isGet_; }
      set {
        isGet_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftSupremeInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftSupremeInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      if (IsGet != other.IsGet) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (IsGet != false) hash ^= IsGet.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (IsGet != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsGet);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (IsGet != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftSupremeInfo other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      if (other.IsGet != false) {
        IsGet = other.IsGet;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
          case 16: {
            IsGet = input.ReadBool();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///请求机甲平台升级
  /// </summary>
  public sealed partial class AircraftLevelUpRequest : pb::IMessage<AircraftLevelUpRequest> {
    private static readonly pb::MessageParser<AircraftLevelUpRequest> _parser = new pb::MessageParser<AircraftLevelUpRequest>(() => new AircraftLevelUpRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftLevelUpRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftLevelUpRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftLevelUpRequest(AircraftLevelUpRequest other) : this() {
      aircraftId_ = other.aircraftId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftLevelUpRequest Clone() {
      return new AircraftLevelUpRequest(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftLevelUpRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftLevelUpRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftLevelUpRequest other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台升级回执
  /// </summary>
  public sealed partial class AircraftLevelUpResponse : pb::IMessage<AircraftLevelUpResponse> {
    private static readonly pb::MessageParser<AircraftLevelUpResponse> _parser = new pb::MessageParser<AircraftLevelUpResponse>(() => new AircraftLevelUpResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftLevelUpResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftLevelUpResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftLevelUpResponse(AircraftLevelUpResponse other) : this() {
      aircraftInfo_ = other.aircraftInfo_ != null ? other.aircraftInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftLevelUpResponse Clone() {
      return new AircraftLevelUpResponse(this);
    }

    /// <summary>Field number for the "aircraftInfo" field.</summary>
    public const int AircraftInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftInfo aircraftInfo_;
    /// <summary>
    ///平台信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftInfo AircraftInfo {
      get { return aircraftInfo_; }
      set {
        aircraftInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftLevelUpResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftLevelUpResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftInfo, other.AircraftInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftInfo_ != null) hash ^= AircraftInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftLevelUpResponse other) {
      if (other == null) {
        return;
      }
      if (other.aircraftInfo_ != null) {
        if (aircraftInfo_ == null) {
          AircraftInfo = new global::LD.Protocol.AircraftInfo();
        }
        AircraftInfo.MergeFrom(other.AircraftInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftInfo_ == null) {
              AircraftInfo = new global::LD.Protocol.AircraftInfo();
            }
            input.ReadMessage(AircraftInfo);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///请求机甲平台重置
  /// </summary>
  public sealed partial class AircraftResetRequest : pb::IMessage<AircraftResetRequest> {
    private static readonly pb::MessageParser<AircraftResetRequest> _parser = new pb::MessageParser<AircraftResetRequest>(() => new AircraftResetRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftResetRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftResetRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftResetRequest(AircraftResetRequest other) : this() {
      aircraftId_ = other.aircraftId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftResetRequest Clone() {
      return new AircraftResetRequest(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftResetRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftResetRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftResetRequest other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台重置回执
  /// </summary>
  public sealed partial class AircraftResetResponse : pb::IMessage<AircraftResetResponse> {
    private static readonly pb::MessageParser<AircraftResetResponse> _parser = new pb::MessageParser<AircraftResetResponse>(() => new AircraftResetResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftResetResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftResetResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftResetResponse(AircraftResetResponse other) : this() {
      aircraftInfo_ = other.aircraftInfo_ != null ? other.aircraftInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftResetResponse Clone() {
      return new AircraftResetResponse(this);
    }

    /// <summary>Field number for the "aircraftInfo" field.</summary>
    public const int AircraftInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftInfo aircraftInfo_;
    /// <summary>
    ///平台信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftInfo AircraftInfo {
      get { return aircraftInfo_; }
      set {
        aircraftInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftResetResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftResetResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftInfo, other.AircraftInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftInfo_ != null) hash ^= AircraftInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftResetResponse other) {
      if (other == null) {
        return;
      }
      if (other.aircraftInfo_ != null) {
        if (aircraftInfo_ == null) {
          AircraftInfo = new global::LD.Protocol.AircraftInfo();
        }
        AircraftInfo.MergeFrom(other.AircraftInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftInfo_ == null) {
              AircraftInfo = new global::LD.Protocol.AircraftInfo();
            }
            input.ReadMessage(AircraftInfo);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///请求机甲平台升阶
  /// </summary>
  public sealed partial class AircraftGradeUpRequest : pb::IMessage<AircraftGradeUpRequest> {
    private static readonly pb::MessageParser<AircraftGradeUpRequest> _parser = new pb::MessageParser<AircraftGradeUpRequest>(() => new AircraftGradeUpRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftGradeUpRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftGradeUpRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftGradeUpRequest(AircraftGradeUpRequest other) : this() {
      aircraftId_ = other.aircraftId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftGradeUpRequest Clone() {
      return new AircraftGradeUpRequest(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftGradeUpRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftGradeUpRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftGradeUpRequest other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台升阶回执
  /// </summary>
  public sealed partial class AircraftGradeUpResponse : pb::IMessage<AircraftGradeUpResponse> {
    private static readonly pb::MessageParser<AircraftGradeUpResponse> _parser = new pb::MessageParser<AircraftGradeUpResponse>(() => new AircraftGradeUpResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftGradeUpResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftGradeUpResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftGradeUpResponse(AircraftGradeUpResponse other) : this() {
      aircraftInfo_ = other.aircraftInfo_ != null ? other.aircraftInfo_.Clone() : null;
      isAdvance_ = other.isAdvance_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftGradeUpResponse Clone() {
      return new AircraftGradeUpResponse(this);
    }

    /// <summary>Field number for the "aircraftInfo" field.</summary>
    public const int AircraftInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftInfo aircraftInfo_;
    /// <summary>
    ///平台信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftInfo AircraftInfo {
      get { return aircraftInfo_; }
      set {
        aircraftInfo_ = value;
      }
    }

    /// <summary>Field number for the "isAdvance" field.</summary>
    public const int IsAdvanceFieldNumber = 2;
    private bool isAdvance_;
    /// <summary>
    /// true:提前突破
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool IsAdvance {
      get { return isAdvance_; }
      set {
        isAdvance_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftGradeUpResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftGradeUpResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftInfo, other.AircraftInfo)) return false;
      if (IsAdvance != other.IsAdvance) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftInfo_ != null) hash ^= AircraftInfo.GetHashCode();
      if (IsAdvance != false) hash ^= IsAdvance.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftInfo);
      }
      if (IsAdvance != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsAdvance);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftInfo);
      }
      if (IsAdvance != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftGradeUpResponse other) {
      if (other == null) {
        return;
      }
      if (other.aircraftInfo_ != null) {
        if (aircraftInfo_ == null) {
          AircraftInfo = new global::LD.Protocol.AircraftInfo();
        }
        AircraftInfo.MergeFrom(other.AircraftInfo);
      }
      if (other.IsAdvance != false) {
        IsAdvance = other.IsAdvance;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftInfo_ == null) {
              AircraftInfo = new global::LD.Protocol.AircraftInfo();
            }
            input.ReadMessage(AircraftInfo);
            break;
          }
          case 16: {
            IsAdvance = input.ReadBool();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///请求机甲平台洗练
  /// </summary>
  public sealed partial class AircraftRefinementRequest : pb::IMessage<AircraftRefinementRequest> {
    private static readonly pb::MessageParser<AircraftRefinementRequest> _parser = new pb::MessageParser<AircraftRefinementRequest>(() => new AircraftRefinementRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftRefinementRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementRequest(AircraftRefinementRequest other) : this() {
      aircraftId_ = other.aircraftId_;
      lockIndexs_ = other.lockIndexs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementRequest Clone() {
      return new AircraftRefinementRequest(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    /// <summary>Field number for the "lockIndexs" field.</summary>
    public const int LockIndexsFieldNumber = 2;
    private static readonly pb::FieldCodec<int> _repeated_lockIndexs_codec
        = pb::FieldCodec.ForInt32(18);
    private readonly pbc::RepeatedField<int> lockIndexs_ = new pbc::RepeatedField<int>();
    /// <summary>
    ///锁定洗练结果的索引(从0开始)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> LockIndexs {
      get { return lockIndexs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftRefinementRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftRefinementRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      if(!lockIndexs_.Equals(other.lockIndexs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      hash ^= lockIndexs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      lockIndexs_.WriteTo(output, _repeated_lockIndexs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      size += lockIndexs_.CalculateSize(_repeated_lockIndexs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftRefinementRequest other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      lockIndexs_.Add(other.lockIndexs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
          case 18:
          case 16: {
            lockIndexs_.AddEntriesFrom(input, _repeated_lockIndexs_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台洗练回执
  /// </summary>
  public sealed partial class AircraftRefinementResponse : pb::IMessage<AircraftRefinementResponse> {
    private static readonly pb::MessageParser<AircraftRefinementResponse> _parser = new pb::MessageParser<AircraftRefinementResponse>(() => new AircraftRefinementResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftRefinementResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementResponse(AircraftRefinementResponse other) : this() {
      aircraftInfo_ = other.aircraftInfo_ != null ? other.aircraftInfo_.Clone() : null;
      refinementLevel_ = other.refinementLevel_;
      refinementExp_ = other.refinementExp_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftRefinementResponse Clone() {
      return new AircraftRefinementResponse(this);
    }

    /// <summary>Field number for the "aircraftInfo" field.</summary>
    public const int AircraftInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftInfo aircraftInfo_;
    /// <summary>
    ///平台信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftInfo AircraftInfo {
      get { return aircraftInfo_; }
      set {
        aircraftInfo_ = value;
      }
    }

    /// <summary>Field number for the "refinementLevel" field.</summary>
    public const int RefinementLevelFieldNumber = 2;
    private int refinementLevel_;
    /// <summary>
    ///洗练等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RefinementLevel {
      get { return refinementLevel_; }
      set {
        refinementLevel_ = value;
      }
    }

    /// <summary>Field number for the "refinementExp" field.</summary>
    public const int RefinementExpFieldNumber = 3;
    private int refinementExp_;
    /// <summary>
    ///洗练经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RefinementExp {
      get { return refinementExp_; }
      set {
        refinementExp_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftRefinementResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftRefinementResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftInfo, other.AircraftInfo)) return false;
      if (RefinementLevel != other.RefinementLevel) return false;
      if (RefinementExp != other.RefinementExp) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftInfo_ != null) hash ^= AircraftInfo.GetHashCode();
      if (RefinementLevel != 0) hash ^= RefinementLevel.GetHashCode();
      if (RefinementExp != 0) hash ^= RefinementExp.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftInfo);
      }
      if (RefinementLevel != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(RefinementLevel);
      }
      if (RefinementExp != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(RefinementExp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftInfo);
      }
      if (RefinementLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RefinementLevel);
      }
      if (RefinementExp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RefinementExp);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftRefinementResponse other) {
      if (other == null) {
        return;
      }
      if (other.aircraftInfo_ != null) {
        if (aircraftInfo_ == null) {
          AircraftInfo = new global::LD.Protocol.AircraftInfo();
        }
        AircraftInfo.MergeFrom(other.AircraftInfo);
      }
      if (other.RefinementLevel != 0) {
        RefinementLevel = other.RefinementLevel;
      }
      if (other.RefinementExp != 0) {
        RefinementExp = other.RefinementExp;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftInfo_ == null) {
              AircraftInfo = new global::LD.Protocol.AircraftInfo();
            }
            input.ReadMessage(AircraftInfo);
            break;
          }
          case 16: {
            RefinementLevel = input.ReadInt32();
            break;
          }
          case 24: {
            RefinementExp = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///选择洗练结果
  /// </summary>
  public sealed partial class AircraftSelectRefinementRequest : pb::IMessage<AircraftSelectRefinementRequest> {
    private static readonly pb::MessageParser<AircraftSelectRefinementRequest> _parser = new pb::MessageParser<AircraftSelectRefinementRequest>(() => new AircraftSelectRefinementRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftSelectRefinementRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSelectRefinementRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSelectRefinementRequest(AircraftSelectRefinementRequest other) : this() {
      aircraftId_ = other.aircraftId_;
      opr_ = other.opr_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSelectRefinementRequest Clone() {
      return new AircraftSelectRefinementRequest(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    /// <summary>Field number for the "opr" field.</summary>
    public const int OprFieldNumber = 2;
    private bool opr_;
    /// <summary>
    ///true:替换原本属性,false:取消洗练结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Opr {
      get { return opr_; }
      set {
        opr_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftSelectRefinementRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftSelectRefinementRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      if (Opr != other.Opr) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (Opr != false) hash ^= Opr.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (Opr != false) {
        output.WriteRawTag(16);
        output.WriteBool(Opr);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (Opr != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftSelectRefinementRequest other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      if (other.Opr != false) {
        Opr = other.Opr;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
          case 16: {
            Opr = input.ReadBool();
            break;
          }
        }
      }
    }

  }

  public sealed partial class AircraftSelectRefinementResponse : pb::IMessage<AircraftSelectRefinementResponse> {
    private static readonly pb::MessageParser<AircraftSelectRefinementResponse> _parser = new pb::MessageParser<AircraftSelectRefinementResponse>(() => new AircraftSelectRefinementResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftSelectRefinementResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSelectRefinementResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSelectRefinementResponse(AircraftSelectRefinementResponse other) : this() {
      aircraftInfo_ = other.aircraftInfo_ != null ? other.aircraftInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSelectRefinementResponse Clone() {
      return new AircraftSelectRefinementResponse(this);
    }

    /// <summary>Field number for the "aircraftInfo" field.</summary>
    public const int AircraftInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftInfo aircraftInfo_;
    /// <summary>
    ///平台信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftInfo AircraftInfo {
      get { return aircraftInfo_; }
      set {
        aircraftInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftSelectRefinementResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftSelectRefinementResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftInfo, other.AircraftInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftInfo_ != null) hash ^= AircraftInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftSelectRefinementResponse other) {
      if (other == null) {
        return;
      }
      if (other.aircraftInfo_ != null) {
        if (aircraftInfo_ == null) {
          AircraftInfo = new global::LD.Protocol.AircraftInfo();
        }
        AircraftInfo.MergeFrom(other.AircraftInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftInfo_ == null) {
              AircraftInfo = new global::LD.Protocol.AircraftInfo();
            }
            input.ReadMessage(AircraftInfo);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///请求领取ss机甲
  /// </summary>
  public sealed partial class AircraftSupremeGetRequest : pb::IMessage<AircraftSupremeGetRequest> {
    private static readonly pb::MessageParser<AircraftSupremeGetRequest> _parser = new pb::MessageParser<AircraftSupremeGetRequest>(() => new AircraftSupremeGetRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftSupremeGetRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeGetRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeGetRequest(AircraftSupremeGetRequest other) : this() {
      aircraftId_ = other.aircraftId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeGetRequest Clone() {
      return new AircraftSupremeGetRequest(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftSupremeGetRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftSupremeGetRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftSupremeGetRequest other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///ss机甲领取回执
  /// </summary>
  public sealed partial class AircraftSupremeGetResponse : pb::IMessage<AircraftSupremeGetResponse> {
    private static readonly pb::MessageParser<AircraftSupremeGetResponse> _parser = new pb::MessageParser<AircraftSupremeGetResponse>(() => new AircraftSupremeGetResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftSupremeGetResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeGetResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeGetResponse(AircraftSupremeGetResponse other) : this() {
      aircraftSupremeInfo_ = other.aircraftSupremeInfo_ != null ? other.aircraftSupremeInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftSupremeGetResponse Clone() {
      return new AircraftSupremeGetResponse(this);
    }

    /// <summary>Field number for the "aircraftSupremeInfo" field.</summary>
    public const int AircraftSupremeInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftSupremeInfo aircraftSupremeInfo_;
    /// <summary>
    ///至尊机甲领取信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftSupremeInfo AircraftSupremeInfo {
      get { return aircraftSupremeInfo_; }
      set {
        aircraftSupremeInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftSupremeGetResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftSupremeGetResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftSupremeInfo, other.AircraftSupremeInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftSupremeInfo_ != null) hash ^= AircraftSupremeInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftSupremeInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftSupremeInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftSupremeInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftSupremeInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftSupremeGetResponse other) {
      if (other == null) {
        return;
      }
      if (other.aircraftSupremeInfo_ != null) {
        if (aircraftSupremeInfo_ == null) {
          AircraftSupremeInfo = new global::LD.Protocol.AircraftSupremeInfo();
        }
        AircraftSupremeInfo.MergeFrom(other.AircraftSupremeInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftSupremeInfo_ == null) {
              AircraftSupremeInfo = new global::LD.Protocol.AircraftSupremeInfo();
            }
            input.ReadMessage(AircraftSupremeInfo);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///请求机甲平台上阵
  /// </summary>
  public sealed partial class AircraftPutOnRequest : pb::IMessage<AircraftPutOnRequest> {
    private static readonly pb::MessageParser<AircraftPutOnRequest> _parser = new pb::MessageParser<AircraftPutOnRequest>(() => new AircraftPutOnRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftPutOnRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftPutOnRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftPutOnRequest(AircraftPutOnRequest other) : this() {
      aircraftSlotInfo_ = other.aircraftSlotInfo_ != null ? other.aircraftSlotInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftPutOnRequest Clone() {
      return new AircraftPutOnRequest(this);
    }

    /// <summary>Field number for the "aircraftSlotInfo" field.</summary>
    public const int AircraftSlotInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftSlotInfo aircraftSlotInfo_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftSlotInfo AircraftSlotInfo {
      get { return aircraftSlotInfo_; }
      set {
        aircraftSlotInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftPutOnRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftPutOnRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftSlotInfo, other.AircraftSlotInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftSlotInfo_ != null) hash ^= AircraftSlotInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftSlotInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftSlotInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftSlotInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftSlotInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftPutOnRequest other) {
      if (other == null) {
        return;
      }
      if (other.aircraftSlotInfo_ != null) {
        if (aircraftSlotInfo_ == null) {
          AircraftSlotInfo = new global::LD.Protocol.AircraftSlotInfo();
        }
        AircraftSlotInfo.MergeFrom(other.AircraftSlotInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftSlotInfo_ == null) {
              AircraftSlotInfo = new global::LD.Protocol.AircraftSlotInfo();
            }
            input.ReadMessage(AircraftSlotInfo);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台上阵回执
  /// </summary>
  public sealed partial class AircraftPutOnResponse : pb::IMessage<AircraftPutOnResponse> {
    private static readonly pb::MessageParser<AircraftPutOnResponse> _parser = new pb::MessageParser<AircraftPutOnResponse>(() => new AircraftPutOnResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftPutOnResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftPutOnResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftPutOnResponse(AircraftPutOnResponse other) : this() {
      aircraftSlotInfo_ = other.aircraftSlotInfo_ != null ? other.aircraftSlotInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftPutOnResponse Clone() {
      return new AircraftPutOnResponse(this);
    }

    /// <summary>Field number for the "aircraftSlotInfo" field.</summary>
    public const int AircraftSlotInfoFieldNumber = 1;
    private global::LD.Protocol.AircraftSlotInfo aircraftSlotInfo_;
    /// <summary>
    ///机甲平台上阵信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.AircraftSlotInfo AircraftSlotInfo {
      get { return aircraftSlotInfo_; }
      set {
        aircraftSlotInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftPutOnResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftPutOnResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(AircraftSlotInfo, other.AircraftSlotInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (aircraftSlotInfo_ != null) hash ^= AircraftSlotInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (aircraftSlotInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(AircraftSlotInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (aircraftSlotInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AircraftSlotInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftPutOnResponse other) {
      if (other == null) {
        return;
      }
      if (other.aircraftSlotInfo_ != null) {
        if (aircraftSlotInfo_ == null) {
          AircraftSlotInfo = new global::LD.Protocol.AircraftSlotInfo();
        }
        AircraftSlotInfo.MergeFrom(other.AircraftSlotInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (aircraftSlotInfo_ == null) {
              AircraftSlotInfo = new global::LD.Protocol.AircraftSlotInfo();
            }
            input.ReadMessage(AircraftSlotInfo);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///解锁机甲平台
  /// </summary>
  public sealed partial class AircraftUnlockRequest : pb::IMessage<AircraftUnlockRequest> {
    private static readonly pb::MessageParser<AircraftUnlockRequest> _parser = new pb::MessageParser<AircraftUnlockRequest>(() => new AircraftUnlockRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftUnlockRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftUnlockRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftUnlockRequest(AircraftUnlockRequest other) : this() {
      aircraftId_ = other.aircraftId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftUnlockRequest Clone() {
      return new AircraftUnlockRequest(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    /// <summary>
    ///配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftUnlockRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftUnlockRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftUnlockRequest other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///机甲平台解锁回执
  /// </summary>
  public sealed partial class AircraftUnlockResponse : pb::IMessage<AircraftUnlockResponse> {
    private static readonly pb::MessageParser<AircraftUnlockResponse> _parser = new pb::MessageParser<AircraftUnlockResponse>(() => new AircraftUnlockResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AircraftUnlockResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.AircraftReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftUnlockResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftUnlockResponse(AircraftUnlockResponse other) : this() {
      aircraftId_ = other.aircraftId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AircraftUnlockResponse Clone() {
      return new AircraftUnlockResponse(this);
    }

    /// <summary>Field number for the "aircraftId" field.</summary>
    public const int AircraftIdFieldNumber = 1;
    private int aircraftId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AircraftId {
      get { return aircraftId_; }
      set {
        aircraftId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AircraftUnlockResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AircraftUnlockResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AircraftId != other.AircraftId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (AircraftId != 0) hash ^= AircraftId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (AircraftId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(AircraftId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (AircraftId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AircraftId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AircraftUnlockResponse other) {
      if (other == null) {
        return;
      }
      if (other.AircraftId != 0) {
        AircraftId = other.AircraftId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AircraftId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
