// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/CombatPower.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/CombatPower.proto</summary>
  public static partial class CombatPowerReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/CombatPower.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static CombatPowerReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChpDMlNQcm90by9Db21iYXRQb3dlci5wcm90bxoTQzJTUHJvdG8vUm9sZS5w",
            "cm90bxoWQzJTUHJvdG8vRGl5UGFydC5wcm90bxoUQzJTUHJvdG8vRXF1aXAu",
            "cHJvdG8aF0MyU1Byb3RvL1Byb3BlcnR5LnByb3RvIk8KDFJvbGVBaXJDcmFm",
            "dBIrChByb2xlQWlyQ3JhZnRJbmZvGAEgAygLMhEuUm9sZUFpckNyYWZ0SW5m",
            "bxISCgpmaWdodHBvd2VyGAIgASgDIoIBCglSb2xlRXF1aXASJgoGZXF1aXBz",
            "GAEgAygLMhYuUm9sZUVxdWlwLkVxdWlwc0VudHJ5EhIKCmZpZ2h0cG93ZXIY",
            "AiABKAMaOQoLRXF1aXBzRW50cnkSCwoDa2V5GAEgASgFEhkKBXZhbHVlGAIg",
            "ASgLMgouRXF1aXBJbmZvOgI4ASKlAgoOUm9sZU1lY2hhQXNzaXQSPQoPbWVj",
            "aGFBc3Npc3RJbmZvGAEgAygLMiQuUm9sZU1lY2hhQXNzaXQuTWVjaGFBc3Np",
            "c3RJbmZvRW50cnkSEgoKZmlnaHRwb3dlchgCIAEoAxI/ChBtZWNoYUFzc2lz",
            "dExldmVsGAMgAygLMiUuUm9sZU1lY2hhQXNzaXQuTWVjaGFBc3Npc3RMZXZl",
            "bEVudHJ5GkYKFE1lY2hhQXNzaXN0SW5mb0VudHJ5EgsKA2tleRgBIAEoBRId",
            "CgV2YWx1ZRgCIAEoCzIOLlJvbGVNZWNoYUluZm86AjgBGjcKFU1lY2hhQXNz",
            "aXN0TGV2ZWxFbnRyeRILCgNrZXkYASABKAUSDQoFdmFsdWUYAiABKAU6AjgB",
            "IncKB1JvbGVVQVYSHgoDdWF2GAEgAygLMhEuUm9sZVVBVi5VYXZFbnRyeRIS",
            "CgpmaWdodHBvd2VyGAIgASgDGjgKCFVhdkVudHJ5EgsKA2tleRgBIAEoBRIb",
            "CgV2YWx1ZRgCIAEoCzIMLlJvbGVVQVZJbmZvOgI4ASJjCgtSb2xlRGl5UGFy",
            "dBIfCgVwYXJ0cxgBIAMoCzIQLlJvbGVEaXlQYXJ0SW5mbxISCgpmaWdodHBv",
            "d2VyGAIgASgDEh8KCHByb3BlcnR5GAMgAygLMg0uUHJvcGVydHlJbmZvIlsK",
            "EE1lY2hhRW5oYW5jZW1lbnQSCwoDYXRrGAEgASgDEgsKA2RlZhgCIAEoAxIK",
            "CgJocBgDIAEoAxISCgpmaWdodHBvd2VyGAQgASgDEg0KBWxldmVsGAUgASgF",
            "IqIEChFQbGF5ZXJDb21iYXRQb3dlchIQCghwbGF5ZXJJZBgBIAEoAxIMCgRu",
            "YW1lGAIgASgJEgwKBGhlYWQYAyABKAUSDwoHaGVhZEJveBgEIAEoBRINCgVs",
            "ZXZlbBgFIAEoBRISCgpjdXJNZWNoYUlkGAYgASgFEjoKDHByb3BlcnR5SW5m",
            "bxgHIAMoCzIkLlBsYXllckNvbWJhdFBvd2VyLlByb3BlcnR5SW5mb0VudHJ5",
            "EiEKCW1lY2hhSW5mbxgIIAEoCzIOLlJvbGVNZWNoYUluZm8SGwoFcGFydHMY",
            "CSABKAsyDC5Sb2xlRGl5UGFydBIVCgN1YXYYCiABKAsyCC5Sb2xlVUFWEiQK",
            "C21lY2hhQXNzaXN0GAsgASgLMg8uUm9sZU1lY2hhQXNzaXQSGgoGZXF1aXBz",
            "GAwgASgLMgouUm9sZUVxdWlwEhQKDGRyaXZlcnNGaWdodBgNIAEoAxIjCgxy",
            "b2xlQWlyQ3JhZnQYDiABKAsyDS5Sb2xlQWlyQ3JhZnQSEgoKZmlnaHRwb3dl",
            "chgPIAEoAxImCgtlbmhhbmNlbWVudBgQIAEoCzIRLk1lY2hhRW5oYW5jZW1l",
            "bnQSEwoLbWVjaGFTa2luSWQYESABKAUSFQoNbWVjaGFTa2luSGlkZRgSIAEo",
            "CBozChFQcm9wZXJ0eUluZm9FbnRyeRILCgNrZXkYASABKAUSDQoFdmFsdWUY",
            "AiABKAM6AjgBIiwKGFBsYXllckNvbWJhdFBvd2VyUmVxdWVzdBIQCghwbGF5",
            "ZXJJZBgBIAEoAyJKChlQbGF5ZXJDb21iYXRQb3dlclJlc3BvbnNlEi0KEXBs",
            "YXllckNvbWJhdFBvd2VyGAEgAygLMhIuUGxheWVyQ29tYmF0UG93ZXJCIwoT",
            "Y29tLmdvbGRlbi5wcm90b2NvbKoCC0xELlByb3RvY29sYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::LD.Protocol.RoleReflection.Descriptor, global::LD.Protocol.DiyPartReflection.Descriptor, global::LD.Protocol.EquipReflection.Descriptor, global::LD.Protocol.PropertyReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RoleAirCraft), global::LD.Protocol.RoleAirCraft.Parser, new[]{ "RoleAirCraftInfo", "Fightpower" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RoleEquip), global::LD.Protocol.RoleEquip.Parser, new[]{ "Equips", "Fightpower" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RoleMechaAssit), global::LD.Protocol.RoleMechaAssit.Parser, new[]{ "MechaAssistInfo", "Fightpower", "MechaAssistLevel" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RoleUAV), global::LD.Protocol.RoleUAV.Parser, new[]{ "Uav", "Fightpower" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RoleDiyPart), global::LD.Protocol.RoleDiyPart.Parser, new[]{ "Parts", "Fightpower", "Property" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.MechaEnhancement), global::LD.Protocol.MechaEnhancement.Parser, new[]{ "Atk", "Def", "Hp", "Fightpower", "Level" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerCombatPower), global::LD.Protocol.PlayerCombatPower.Parser, new[]{ "PlayerId", "Name", "Head", "HeadBox", "Level", "CurMechaId", "PropertyInfo", "MechaInfo", "Parts", "Uav", "MechaAssist", "Equips", "DriversFight", "RoleAirCraft", "Fightpower", "Enhancement", "MechaSkinId", "MechaSkinHide" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerCombatPowerRequest), global::LD.Protocol.PlayerCombatPowerRequest.Parser, new[]{ "PlayerId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerCombatPowerResponse), global::LD.Protocol.PlayerCombatPowerResponse.Parser, new[]{ "PlayerCombatPower" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class RoleAirCraft : pb::IMessage<RoleAirCraft> {
    private static readonly pb::MessageParser<RoleAirCraft> _parser = new pb::MessageParser<RoleAirCraft>(() => new RoleAirCraft());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RoleAirCraft> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleAirCraft() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleAirCraft(RoleAirCraft other) : this() {
      roleAirCraftInfo_ = other.roleAirCraftInfo_.Clone();
      fightpower_ = other.fightpower_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleAirCraft Clone() {
      return new RoleAirCraft(this);
    }

    /// <summary>Field number for the "roleAirCraftInfo" field.</summary>
    public const int RoleAirCraftInfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.RoleAirCraftInfo> _repeated_roleAirCraftInfo_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.RoleAirCraftInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.RoleAirCraftInfo> roleAirCraftInfo_ = new pbc::RepeatedField<global::LD.Protocol.RoleAirCraftInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.RoleAirCraftInfo> RoleAirCraftInfo {
      get { return roleAirCraftInfo_; }
    }

    /// <summary>Field number for the "fightpower" field.</summary>
    public const int FightpowerFieldNumber = 2;
    private long fightpower_;
    /// <summary>
    ///总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Fightpower {
      get { return fightpower_; }
      set {
        fightpower_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RoleAirCraft);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RoleAirCraft other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!roleAirCraftInfo_.Equals(other.roleAirCraftInfo_)) return false;
      if (Fightpower != other.Fightpower) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= roleAirCraftInfo_.GetHashCode();
      if (Fightpower != 0L) hash ^= Fightpower.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      roleAirCraftInfo_.WriteTo(output, _repeated_roleAirCraftInfo_codec);
      if (Fightpower != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Fightpower);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += roleAirCraftInfo_.CalculateSize(_repeated_roleAirCraftInfo_codec);
      if (Fightpower != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fightpower);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RoleAirCraft other) {
      if (other == null) {
        return;
      }
      roleAirCraftInfo_.Add(other.roleAirCraftInfo_);
      if (other.Fightpower != 0L) {
        Fightpower = other.Fightpower;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            roleAirCraftInfo_.AddEntriesFrom(input, _repeated_roleAirCraftInfo_codec);
            break;
          }
          case 16: {
            Fightpower = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RoleEquip : pb::IMessage<RoleEquip> {
    private static readonly pb::MessageParser<RoleEquip> _parser = new pb::MessageParser<RoleEquip>(() => new RoleEquip());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RoleEquip> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleEquip() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleEquip(RoleEquip other) : this() {
      equips_ = other.equips_.Clone();
      fightpower_ = other.fightpower_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleEquip Clone() {
      return new RoleEquip(this);
    }

    /// <summary>Field number for the "equips" field.</summary>
    public const int EquipsFieldNumber = 1;
    private static readonly pbc::MapField<int, global::LD.Protocol.EquipInfo>.Codec _map_equips_codec
        = new pbc::MapField<int, global::LD.Protocol.EquipInfo>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::LD.Protocol.EquipInfo.Parser), 10);
    private readonly pbc::MapField<int, global::LD.Protocol.EquipInfo> equips_ = new pbc::MapField<int, global::LD.Protocol.EquipInfo>();
    /// <summary>
    ///装备
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, global::LD.Protocol.EquipInfo> Equips {
      get { return equips_; }
    }

    /// <summary>Field number for the "fightpower" field.</summary>
    public const int FightpowerFieldNumber = 2;
    private long fightpower_;
    /// <summary>
    ///总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Fightpower {
      get { return fightpower_; }
      set {
        fightpower_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RoleEquip);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RoleEquip other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Equips.Equals(other.Equips)) return false;
      if (Fightpower != other.Fightpower) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= Equips.GetHashCode();
      if (Fightpower != 0L) hash ^= Fightpower.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      equips_.WriteTo(output, _map_equips_codec);
      if (Fightpower != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Fightpower);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += equips_.CalculateSize(_map_equips_codec);
      if (Fightpower != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fightpower);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RoleEquip other) {
      if (other == null) {
        return;
      }
      equips_.Add(other.equips_);
      if (other.Fightpower != 0L) {
        Fightpower = other.Fightpower;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            equips_.AddEntriesFrom(input, _map_equips_codec);
            break;
          }
          case 16: {
            Fightpower = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RoleMechaAssit : pb::IMessage<RoleMechaAssit> {
    private static readonly pb::MessageParser<RoleMechaAssit> _parser = new pb::MessageParser<RoleMechaAssit>(() => new RoleMechaAssit());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RoleMechaAssit> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleMechaAssit() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleMechaAssit(RoleMechaAssit other) : this() {
      mechaAssistInfo_ = other.mechaAssistInfo_.Clone();
      fightpower_ = other.fightpower_;
      mechaAssistLevel_ = other.mechaAssistLevel_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleMechaAssit Clone() {
      return new RoleMechaAssit(this);
    }

    /// <summary>Field number for the "mechaAssistInfo" field.</summary>
    public const int MechaAssistInfoFieldNumber = 1;
    private static readonly pbc::MapField<int, global::LD.Protocol.RoleMechaInfo>.Codec _map_mechaAssistInfo_codec
        = new pbc::MapField<int, global::LD.Protocol.RoleMechaInfo>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::LD.Protocol.RoleMechaInfo.Parser), 10);
    private readonly pbc::MapField<int, global::LD.Protocol.RoleMechaInfo> mechaAssistInfo_ = new pbc::MapField<int, global::LD.Protocol.RoleMechaInfo>();
    /// <summary>
    ///助战机甲
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, global::LD.Protocol.RoleMechaInfo> MechaAssistInfo {
      get { return mechaAssistInfo_; }
    }

    /// <summary>Field number for the "fightpower" field.</summary>
    public const int FightpowerFieldNumber = 2;
    private long fightpower_;
    /// <summary>
    ///总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Fightpower {
      get { return fightpower_; }
      set {
        fightpower_ = value;
      }
    }

    /// <summary>Field number for the "mechaAssistLevel" field.</summary>
    public const int MechaAssistLevelFieldNumber = 3;
    private static readonly pbc::MapField<int, int>.Codec _map_mechaAssistLevel_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 26);
    private readonly pbc::MapField<int, int> mechaAssistLevel_ = new pbc::MapField<int, int>();
    /// <summary>
    ///助战等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> MechaAssistLevel {
      get { return mechaAssistLevel_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RoleMechaAssit);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RoleMechaAssit other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!MechaAssistInfo.Equals(other.MechaAssistInfo)) return false;
      if (Fightpower != other.Fightpower) return false;
      if (!MechaAssistLevel.Equals(other.MechaAssistLevel)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= MechaAssistInfo.GetHashCode();
      if (Fightpower != 0L) hash ^= Fightpower.GetHashCode();
      hash ^= MechaAssistLevel.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      mechaAssistInfo_.WriteTo(output, _map_mechaAssistInfo_codec);
      if (Fightpower != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Fightpower);
      }
      mechaAssistLevel_.WriteTo(output, _map_mechaAssistLevel_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += mechaAssistInfo_.CalculateSize(_map_mechaAssistInfo_codec);
      if (Fightpower != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fightpower);
      }
      size += mechaAssistLevel_.CalculateSize(_map_mechaAssistLevel_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RoleMechaAssit other) {
      if (other == null) {
        return;
      }
      mechaAssistInfo_.Add(other.mechaAssistInfo_);
      if (other.Fightpower != 0L) {
        Fightpower = other.Fightpower;
      }
      mechaAssistLevel_.Add(other.mechaAssistLevel_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            mechaAssistInfo_.AddEntriesFrom(input, _map_mechaAssistInfo_codec);
            break;
          }
          case 16: {
            Fightpower = input.ReadInt64();
            break;
          }
          case 26: {
            mechaAssistLevel_.AddEntriesFrom(input, _map_mechaAssistLevel_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class RoleUAV : pb::IMessage<RoleUAV> {
    private static readonly pb::MessageParser<RoleUAV> _parser = new pb::MessageParser<RoleUAV>(() => new RoleUAV());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RoleUAV> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleUAV() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleUAV(RoleUAV other) : this() {
      uav_ = other.uav_.Clone();
      fightpower_ = other.fightpower_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleUAV Clone() {
      return new RoleUAV(this);
    }

    /// <summary>Field number for the "uav" field.</summary>
    public const int UavFieldNumber = 1;
    private static readonly pbc::MapField<int, global::LD.Protocol.RoleUAVInfo>.Codec _map_uav_codec
        = new pbc::MapField<int, global::LD.Protocol.RoleUAVInfo>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::LD.Protocol.RoleUAVInfo.Parser), 10);
    private readonly pbc::MapField<int, global::LD.Protocol.RoleUAVInfo> uav_ = new pbc::MapField<int, global::LD.Protocol.RoleUAVInfo>();
    /// <summary>
    ///无人机
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, global::LD.Protocol.RoleUAVInfo> Uav {
      get { return uav_; }
    }

    /// <summary>Field number for the "fightpower" field.</summary>
    public const int FightpowerFieldNumber = 2;
    private long fightpower_;
    /// <summary>
    ///总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Fightpower {
      get { return fightpower_; }
      set {
        fightpower_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RoleUAV);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RoleUAV other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Uav.Equals(other.Uav)) return false;
      if (Fightpower != other.Fightpower) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= Uav.GetHashCode();
      if (Fightpower != 0L) hash ^= Fightpower.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      uav_.WriteTo(output, _map_uav_codec);
      if (Fightpower != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Fightpower);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += uav_.CalculateSize(_map_uav_codec);
      if (Fightpower != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fightpower);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RoleUAV other) {
      if (other == null) {
        return;
      }
      uav_.Add(other.uav_);
      if (other.Fightpower != 0L) {
        Fightpower = other.Fightpower;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            uav_.AddEntriesFrom(input, _map_uav_codec);
            break;
          }
          case 16: {
            Fightpower = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RoleDiyPart : pb::IMessage<RoleDiyPart> {
    private static readonly pb::MessageParser<RoleDiyPart> _parser = new pb::MessageParser<RoleDiyPart>(() => new RoleDiyPart());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RoleDiyPart> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleDiyPart() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleDiyPart(RoleDiyPart other) : this() {
      parts_ = other.parts_.Clone();
      fightpower_ = other.fightpower_;
      property_ = other.property_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RoleDiyPart Clone() {
      return new RoleDiyPart(this);
    }

    /// <summary>Field number for the "parts" field.</summary>
    public const int PartsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.RoleDiyPartInfo> _repeated_parts_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.RoleDiyPartInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.RoleDiyPartInfo> parts_ = new pbc::RepeatedField<global::LD.Protocol.RoleDiyPartInfo>();
    /// <summary>
    ///部件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.RoleDiyPartInfo> Parts {
      get { return parts_; }
    }

    /// <summary>Field number for the "fightpower" field.</summary>
    public const int FightpowerFieldNumber = 2;
    private long fightpower_;
    /// <summary>
    ///总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Fightpower {
      get { return fightpower_; }
      set {
        fightpower_ = value;
      }
    }

    /// <summary>Field number for the "property" field.</summary>
    public const int PropertyFieldNumber = 3;
    private static readonly pb::FieldCodec<global::LD.Protocol.PropertyInfo> _repeated_property_codec
        = pb::FieldCodec.ForMessage(26, global::LD.Protocol.PropertyInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.PropertyInfo> property_ = new pbc::RepeatedField<global::LD.Protocol.PropertyInfo>();
    /// <summary>
    /// 属性
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.PropertyInfo> Property {
      get { return property_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RoleDiyPart);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RoleDiyPart other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!parts_.Equals(other.parts_)) return false;
      if (Fightpower != other.Fightpower) return false;
      if(!property_.Equals(other.property_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= parts_.GetHashCode();
      if (Fightpower != 0L) hash ^= Fightpower.GetHashCode();
      hash ^= property_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      parts_.WriteTo(output, _repeated_parts_codec);
      if (Fightpower != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Fightpower);
      }
      property_.WriteTo(output, _repeated_property_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += parts_.CalculateSize(_repeated_parts_codec);
      if (Fightpower != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fightpower);
      }
      size += property_.CalculateSize(_repeated_property_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RoleDiyPart other) {
      if (other == null) {
        return;
      }
      parts_.Add(other.parts_);
      if (other.Fightpower != 0L) {
        Fightpower = other.Fightpower;
      }
      property_.Add(other.property_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            parts_.AddEntriesFrom(input, _repeated_parts_codec);
            break;
          }
          case 16: {
            Fightpower = input.ReadInt64();
            break;
          }
          case 26: {
            property_.AddEntriesFrom(input, _repeated_property_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class MechaEnhancement : pb::IMessage<MechaEnhancement> {
    private static readonly pb::MessageParser<MechaEnhancement> _parser = new pb::MessageParser<MechaEnhancement>(() => new MechaEnhancement());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<MechaEnhancement> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MechaEnhancement() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MechaEnhancement(MechaEnhancement other) : this() {
      atk_ = other.atk_;
      def_ = other.def_;
      hp_ = other.hp_;
      fightpower_ = other.fightpower_;
      level_ = other.level_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MechaEnhancement Clone() {
      return new MechaEnhancement(this);
    }

    /// <summary>Field number for the "atk" field.</summary>
    public const int AtkFieldNumber = 1;
    private long atk_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Atk {
      get { return atk_; }
      set {
        atk_ = value;
      }
    }

    /// <summary>Field number for the "def" field.</summary>
    public const int DefFieldNumber = 2;
    private long def_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Def {
      get { return def_; }
      set {
        def_ = value;
      }
    }

    /// <summary>Field number for the "hp" field.</summary>
    public const int HpFieldNumber = 3;
    private long hp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Hp {
      get { return hp_; }
      set {
        hp_ = value;
      }
    }

    /// <summary>Field number for the "fightpower" field.</summary>
    public const int FightpowerFieldNumber = 4;
    private long fightpower_;
    /// <summary>
    ///总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Fightpower {
      get { return fightpower_; }
      set {
        fightpower_ = value;
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 5;
    private int level_;
    /// <summary>
    /// 总等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as MechaEnhancement);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(MechaEnhancement other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Atk != other.Atk) return false;
      if (Def != other.Def) return false;
      if (Hp != other.Hp) return false;
      if (Fightpower != other.Fightpower) return false;
      if (Level != other.Level) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Atk != 0L) hash ^= Atk.GetHashCode();
      if (Def != 0L) hash ^= Def.GetHashCode();
      if (Hp != 0L) hash ^= Hp.GetHashCode();
      if (Fightpower != 0L) hash ^= Fightpower.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Atk != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Atk);
      }
      if (Def != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Def);
      }
      if (Hp != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Hp);
      }
      if (Fightpower != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Fightpower);
      }
      if (Level != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Atk != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Atk);
      }
      if (Def != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Def);
      }
      if (Hp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Hp);
      }
      if (Fightpower != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fightpower);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Level);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(MechaEnhancement other) {
      if (other == null) {
        return;
      }
      if (other.Atk != 0L) {
        Atk = other.Atk;
      }
      if (other.Def != 0L) {
        Def = other.Def;
      }
      if (other.Hp != 0L) {
        Hp = other.Hp;
      }
      if (other.Fightpower != 0L) {
        Fightpower = other.Fightpower;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Atk = input.ReadInt64();
            break;
          }
          case 16: {
            Def = input.ReadInt64();
            break;
          }
          case 24: {
            Hp = input.ReadInt64();
            break;
          }
          case 32: {
            Fightpower = input.ReadInt64();
            break;
          }
          case 40: {
            Level = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class PlayerCombatPower : pb::IMessage<PlayerCombatPower> {
    private static readonly pb::MessageParser<PlayerCombatPower> _parser = new pb::MessageParser<PlayerCombatPower>(() => new PlayerCombatPower());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerCombatPower> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPower() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPower(PlayerCombatPower other) : this() {
      playerId_ = other.playerId_;
      name_ = other.name_;
      head_ = other.head_;
      headBox_ = other.headBox_;
      level_ = other.level_;
      curMechaId_ = other.curMechaId_;
      propertyInfo_ = other.propertyInfo_.Clone();
      mechaInfo_ = other.mechaInfo_ != null ? other.mechaInfo_.Clone() : null;
      parts_ = other.parts_ != null ? other.parts_.Clone() : null;
      uav_ = other.uav_ != null ? other.uav_.Clone() : null;
      mechaAssist_ = other.mechaAssist_ != null ? other.mechaAssist_.Clone() : null;
      equips_ = other.equips_ != null ? other.equips_.Clone() : null;
      driversFight_ = other.driversFight_;
      roleAirCraft_ = other.roleAirCraft_ != null ? other.roleAirCraft_.Clone() : null;
      fightpower_ = other.fightpower_;
      enhancement_ = other.enhancement_ != null ? other.enhancement_.Clone() : null;
      mechaSkinId_ = other.mechaSkinId_;
      mechaSkinHide_ = other.mechaSkinHide_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPower Clone() {
      return new PlayerCombatPower(this);
    }

    /// <summary>Field number for the "playerId" field.</summary>
    public const int PlayerIdFieldNumber = 1;
    private long playerId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long PlayerId {
      get { return playerId_; }
      set {
        playerId_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "head" field.</summary>
    public const int HeadFieldNumber = 3;
    private int head_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Head {
      get { return head_; }
      set {
        head_ = value;
      }
    }

    /// <summary>Field number for the "headBox" field.</summary>
    public const int HeadBoxFieldNumber = 4;
    private int headBox_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int HeadBox {
      get { return headBox_; }
      set {
        headBox_ = value;
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 5;
    private int level_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "curMechaId" field.</summary>
    public const int CurMechaIdFieldNumber = 6;
    private int curMechaId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CurMechaId {
      get { return curMechaId_; }
      set {
        curMechaId_ = value;
      }
    }

    /// <summary>Field number for the "propertyInfo" field.</summary>
    public const int PropertyInfoFieldNumber = 7;
    private static readonly pbc::MapField<int, long>.Codec _map_propertyInfo_codec
        = new pbc::MapField<int, long>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt64(16, 0L), 58);
    private readonly pbc::MapField<int, long> propertyInfo_ = new pbc::MapField<int, long>();
    /// <summary>
    ///基础属性
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, long> PropertyInfo {
      get { return propertyInfo_; }
    }

    /// <summary>Field number for the "mechaInfo" field.</summary>
    public const int MechaInfoFieldNumber = 8;
    private global::LD.Protocol.RoleMechaInfo mechaInfo_;
    /// <summary>
    ///上阵机甲
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.RoleMechaInfo MechaInfo {
      get { return mechaInfo_; }
      set {
        mechaInfo_ = value;
      }
    }

    /// <summary>Field number for the "parts" field.</summary>
    public const int PartsFieldNumber = 9;
    private global::LD.Protocol.RoleDiyPart parts_;
    /// <summary>
    ///部件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.RoleDiyPart Parts {
      get { return parts_; }
      set {
        parts_ = value;
      }
    }

    /// <summary>Field number for the "uav" field.</summary>
    public const int UavFieldNumber = 10;
    private global::LD.Protocol.RoleUAV uav_;
    /// <summary>
    ///无人机
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.RoleUAV Uav {
      get { return uav_; }
      set {
        uav_ = value;
      }
    }

    /// <summary>Field number for the "mechaAssist" field.</summary>
    public const int MechaAssistFieldNumber = 11;
    private global::LD.Protocol.RoleMechaAssit mechaAssist_;
    /// <summary>
    ///助战机甲
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.RoleMechaAssit MechaAssist {
      get { return mechaAssist_; }
      set {
        mechaAssist_ = value;
      }
    }

    /// <summary>Field number for the "equips" field.</summary>
    public const int EquipsFieldNumber = 12;
    private global::LD.Protocol.RoleEquip equips_;
    /// <summary>
    ///装备
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.RoleEquip Equips {
      get { return equips_; }
      set {
        equips_ = value;
      }
    }

    /// <summary>Field number for the "driversFight" field.</summary>
    public const int DriversFightFieldNumber = 13;
    private long driversFight_;
    /// <summary>
    ///驾驶员战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long DriversFight {
      get { return driversFight_; }
      set {
        driversFight_ = value;
      }
    }

    /// <summary>Field number for the "roleAirCraft" field.</summary>
    public const int RoleAirCraftFieldNumber = 14;
    private global::LD.Protocol.RoleAirCraft roleAirCraft_;
    /// <summary>
    ///飞行器
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.RoleAirCraft RoleAirCraft {
      get { return roleAirCraft_; }
      set {
        roleAirCraft_ = value;
      }
    }

    /// <summary>Field number for the "fightpower" field.</summary>
    public const int FightpowerFieldNumber = 15;
    private long fightpower_;
    /// <summary>
    ///总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Fightpower {
      get { return fightpower_; }
      set {
        fightpower_ = value;
      }
    }

    /// <summary>Field number for the "enhancement" field.</summary>
    public const int EnhancementFieldNumber = 16;
    private global::LD.Protocol.MechaEnhancement enhancement_;
    /// <summary>
    ///强化
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.MechaEnhancement Enhancement {
      get { return enhancement_; }
      set {
        enhancement_ = value;
      }
    }

    /// <summary>Field number for the "mechaSkinId" field.</summary>
    public const int MechaSkinIdFieldNumber = 17;
    private int mechaSkinId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MechaSkinId {
      get { return mechaSkinId_; }
      set {
        mechaSkinId_ = value;
      }
    }

    /// <summary>Field number for the "mechaSkinHide" field.</summary>
    public const int MechaSkinHideFieldNumber = 18;
    private bool mechaSkinHide_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool MechaSkinHide {
      get { return mechaSkinHide_; }
      set {
        mechaSkinHide_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerCombatPower);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerCombatPower other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (PlayerId != other.PlayerId) return false;
      if (Name != other.Name) return false;
      if (Head != other.Head) return false;
      if (HeadBox != other.HeadBox) return false;
      if (Level != other.Level) return false;
      if (CurMechaId != other.CurMechaId) return false;
      if (!PropertyInfo.Equals(other.PropertyInfo)) return false;
      if (!object.Equals(MechaInfo, other.MechaInfo)) return false;
      if (!object.Equals(Parts, other.Parts)) return false;
      if (!object.Equals(Uav, other.Uav)) return false;
      if (!object.Equals(MechaAssist, other.MechaAssist)) return false;
      if (!object.Equals(Equips, other.Equips)) return false;
      if (DriversFight != other.DriversFight) return false;
      if (!object.Equals(RoleAirCraft, other.RoleAirCraft)) return false;
      if (Fightpower != other.Fightpower) return false;
      if (!object.Equals(Enhancement, other.Enhancement)) return false;
      if (MechaSkinId != other.MechaSkinId) return false;
      if (MechaSkinHide != other.MechaSkinHide) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (PlayerId != 0L) hash ^= PlayerId.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Head != 0) hash ^= Head.GetHashCode();
      if (HeadBox != 0) hash ^= HeadBox.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (CurMechaId != 0) hash ^= CurMechaId.GetHashCode();
      hash ^= PropertyInfo.GetHashCode();
      if (mechaInfo_ != null) hash ^= MechaInfo.GetHashCode();
      if (parts_ != null) hash ^= Parts.GetHashCode();
      if (uav_ != null) hash ^= Uav.GetHashCode();
      if (mechaAssist_ != null) hash ^= MechaAssist.GetHashCode();
      if (equips_ != null) hash ^= Equips.GetHashCode();
      if (DriversFight != 0L) hash ^= DriversFight.GetHashCode();
      if (roleAirCraft_ != null) hash ^= RoleAirCraft.GetHashCode();
      if (Fightpower != 0L) hash ^= Fightpower.GetHashCode();
      if (enhancement_ != null) hash ^= Enhancement.GetHashCode();
      if (MechaSkinId != 0) hash ^= MechaSkinId.GetHashCode();
      if (MechaSkinHide != false) hash ^= MechaSkinHide.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (PlayerId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(PlayerId);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (Head != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Head);
      }
      if (HeadBox != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(HeadBox);
      }
      if (Level != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Level);
      }
      if (CurMechaId != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(CurMechaId);
      }
      propertyInfo_.WriteTo(output, _map_propertyInfo_codec);
      if (mechaInfo_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(MechaInfo);
      }
      if (parts_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(Parts);
      }
      if (uav_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(Uav);
      }
      if (mechaAssist_ != null) {
        output.WriteRawTag(90);
        output.WriteMessage(MechaAssist);
      }
      if (equips_ != null) {
        output.WriteRawTag(98);
        output.WriteMessage(Equips);
      }
      if (DriversFight != 0L) {
        output.WriteRawTag(104);
        output.WriteInt64(DriversFight);
      }
      if (roleAirCraft_ != null) {
        output.WriteRawTag(114);
        output.WriteMessage(RoleAirCraft);
      }
      if (Fightpower != 0L) {
        output.WriteRawTag(120);
        output.WriteInt64(Fightpower);
      }
      if (enhancement_ != null) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(Enhancement);
      }
      if (MechaSkinId != 0) {
        output.WriteRawTag(136, 1);
        output.WriteInt32(MechaSkinId);
      }
      if (MechaSkinHide != false) {
        output.WriteRawTag(144, 1);
        output.WriteBool(MechaSkinHide);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (PlayerId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(PlayerId);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Head != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Head);
      }
      if (HeadBox != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HeadBox);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Level);
      }
      if (CurMechaId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(CurMechaId);
      }
      size += propertyInfo_.CalculateSize(_map_propertyInfo_codec);
      if (mechaInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MechaInfo);
      }
      if (parts_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Parts);
      }
      if (uav_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Uav);
      }
      if (mechaAssist_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MechaAssist);
      }
      if (equips_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Equips);
      }
      if (DriversFight != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(DriversFight);
      }
      if (roleAirCraft_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RoleAirCraft);
      }
      if (Fightpower != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fightpower);
      }
      if (enhancement_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(Enhancement);
      }
      if (MechaSkinId != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(MechaSkinId);
      }
      if (MechaSkinHide != false) {
        size += 2 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerCombatPower other) {
      if (other == null) {
        return;
      }
      if (other.PlayerId != 0L) {
        PlayerId = other.PlayerId;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Head != 0) {
        Head = other.Head;
      }
      if (other.HeadBox != 0) {
        HeadBox = other.HeadBox;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.CurMechaId != 0) {
        CurMechaId = other.CurMechaId;
      }
      propertyInfo_.Add(other.propertyInfo_);
      if (other.mechaInfo_ != null) {
        if (mechaInfo_ == null) {
          MechaInfo = new global::LD.Protocol.RoleMechaInfo();
        }
        MechaInfo.MergeFrom(other.MechaInfo);
      }
      if (other.parts_ != null) {
        if (parts_ == null) {
          Parts = new global::LD.Protocol.RoleDiyPart();
        }
        Parts.MergeFrom(other.Parts);
      }
      if (other.uav_ != null) {
        if (uav_ == null) {
          Uav = new global::LD.Protocol.RoleUAV();
        }
        Uav.MergeFrom(other.Uav);
      }
      if (other.mechaAssist_ != null) {
        if (mechaAssist_ == null) {
          MechaAssist = new global::LD.Protocol.RoleMechaAssit();
        }
        MechaAssist.MergeFrom(other.MechaAssist);
      }
      if (other.equips_ != null) {
        if (equips_ == null) {
          Equips = new global::LD.Protocol.RoleEquip();
        }
        Equips.MergeFrom(other.Equips);
      }
      if (other.DriversFight != 0L) {
        DriversFight = other.DriversFight;
      }
      if (other.roleAirCraft_ != null) {
        if (roleAirCraft_ == null) {
          RoleAirCraft = new global::LD.Protocol.RoleAirCraft();
        }
        RoleAirCraft.MergeFrom(other.RoleAirCraft);
      }
      if (other.Fightpower != 0L) {
        Fightpower = other.Fightpower;
      }
      if (other.enhancement_ != null) {
        if (enhancement_ == null) {
          Enhancement = new global::LD.Protocol.MechaEnhancement();
        }
        Enhancement.MergeFrom(other.Enhancement);
      }
      if (other.MechaSkinId != 0) {
        MechaSkinId = other.MechaSkinId;
      }
      if (other.MechaSkinHide != false) {
        MechaSkinHide = other.MechaSkinHide;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            PlayerId = input.ReadInt64();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            Head = input.ReadInt32();
            break;
          }
          case 32: {
            HeadBox = input.ReadInt32();
            break;
          }
          case 40: {
            Level = input.ReadInt32();
            break;
          }
          case 48: {
            CurMechaId = input.ReadInt32();
            break;
          }
          case 58: {
            propertyInfo_.AddEntriesFrom(input, _map_propertyInfo_codec);
            break;
          }
          case 66: {
            if (mechaInfo_ == null) {
              MechaInfo = new global::LD.Protocol.RoleMechaInfo();
            }
            input.ReadMessage(MechaInfo);
            break;
          }
          case 74: {
            if (parts_ == null) {
              Parts = new global::LD.Protocol.RoleDiyPart();
            }
            input.ReadMessage(Parts);
            break;
          }
          case 82: {
            if (uav_ == null) {
              Uav = new global::LD.Protocol.RoleUAV();
            }
            input.ReadMessage(Uav);
            break;
          }
          case 90: {
            if (mechaAssist_ == null) {
              MechaAssist = new global::LD.Protocol.RoleMechaAssit();
            }
            input.ReadMessage(MechaAssist);
            break;
          }
          case 98: {
            if (equips_ == null) {
              Equips = new global::LD.Protocol.RoleEquip();
            }
            input.ReadMessage(Equips);
            break;
          }
          case 104: {
            DriversFight = input.ReadInt64();
            break;
          }
          case 114: {
            if (roleAirCraft_ == null) {
              RoleAirCraft = new global::LD.Protocol.RoleAirCraft();
            }
            input.ReadMessage(RoleAirCraft);
            break;
          }
          case 120: {
            Fightpower = input.ReadInt64();
            break;
          }
          case 130: {
            if (enhancement_ == null) {
              Enhancement = new global::LD.Protocol.MechaEnhancement();
            }
            input.ReadMessage(Enhancement);
            break;
          }
          case 136: {
            MechaSkinId = input.ReadInt32();
            break;
          }
          case 144: {
            MechaSkinHide = input.ReadBool();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 获取战力对比信息
  /// </summary>
  public sealed partial class PlayerCombatPowerRequest : pb::IMessage<PlayerCombatPowerRequest> {
    private static readonly pb::MessageParser<PlayerCombatPowerRequest> _parser = new pb::MessageParser<PlayerCombatPowerRequest>(() => new PlayerCombatPowerRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerCombatPowerRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPowerRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPowerRequest(PlayerCombatPowerRequest other) : this() {
      playerId_ = other.playerId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPowerRequest Clone() {
      return new PlayerCombatPowerRequest(this);
    }

    /// <summary>Field number for the "playerId" field.</summary>
    public const int PlayerIdFieldNumber = 1;
    private long playerId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long PlayerId {
      get { return playerId_; }
      set {
        playerId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerCombatPowerRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerCombatPowerRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (PlayerId != other.PlayerId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (PlayerId != 0L) hash ^= PlayerId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (PlayerId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(PlayerId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (PlayerId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(PlayerId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerCombatPowerRequest other) {
      if (other == null) {
        return;
      }
      if (other.PlayerId != 0L) {
        PlayerId = other.PlayerId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            PlayerId = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class PlayerCombatPowerResponse : pb::IMessage<PlayerCombatPowerResponse> {
    private static readonly pb::MessageParser<PlayerCombatPowerResponse> _parser = new pb::MessageParser<PlayerCombatPowerResponse>(() => new PlayerCombatPowerResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerCombatPowerResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.CombatPowerReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPowerResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPowerResponse(PlayerCombatPowerResponse other) : this() {
      playerCombatPower_ = other.playerCombatPower_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerCombatPowerResponse Clone() {
      return new PlayerCombatPowerResponse(this);
    }

    /// <summary>Field number for the "playerCombatPower" field.</summary>
    public const int PlayerCombatPowerFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.PlayerCombatPower> _repeated_playerCombatPower_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.PlayerCombatPower.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.PlayerCombatPower> playerCombatPower_ = new pbc::RepeatedField<global::LD.Protocol.PlayerCombatPower>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.PlayerCombatPower> PlayerCombatPower {
      get { return playerCombatPower_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerCombatPowerResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerCombatPowerResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!playerCombatPower_.Equals(other.playerCombatPower_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= playerCombatPower_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      playerCombatPower_.WriteTo(output, _repeated_playerCombatPower_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += playerCombatPower_.CalculateSize(_repeated_playerCombatPower_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerCombatPowerResponse other) {
      if (other == null) {
        return;
      }
      playerCombatPower_.Add(other.playerCombatPower_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            playerCombatPower_.AddEntriesFrom(input, _repeated_playerCombatPower_codec);
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
