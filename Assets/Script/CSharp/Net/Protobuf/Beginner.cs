// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/Beginner.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
/// <summary>Holder for reflection information generated from C2SProto/Beginner.proto</summary>
public static partial class BeginnerReflection {

  #region Descriptor
  /// <summary>File descriptor for C2SProto/Beginner.proto</summary>
  public static pbr::FileDescriptor Descriptor {
    get { return descriptor; }
  }
  private static pbr::FileDescriptor descriptor;

  static BeginnerReflection() {
    byte[] descriptorData = global::System.Convert.FromBase64String(
        string.Concat(
          "ChdDMlNQcm90by9CZWdpbm5lci5wcm90byItChlCZWdpbm5lckd1aWRlU3lu",
          "Y1Jlc3BvbnNlEhAKCGd1aWRlSWRzGAEgAygFIi4KGkJlZ2lubmVyR3VpZGVV",
          "cGRhdGVSZXF1ZXN0EhAKCGd1aWRlSWRzGAEgAygFIi8KG0JlZ2lubmVyR3Vp",
          "ZGVVcGRhdGVSZXNwb25zZRIQCghndWlkZUlkcxgBIAMoBUIVChNjb20uZ29s",
          "ZGVuLnByb3RvY29sYgZwcm90bzM="));
    descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
        new pbr::FileDescriptor[] { },
        new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
          new pbr::GeneratedClrTypeInfo(typeof(global::BeginnerGuideSyncResponse), global::BeginnerGuideSyncResponse.Parser, new[]{ "GuideIds" }, null, null, null, null),
          new pbr::GeneratedClrTypeInfo(typeof(global::BeginnerGuideUpdateRequest), global::BeginnerGuideUpdateRequest.Parser, new[]{ "GuideIds" }, null, null, null, null),
          new pbr::GeneratedClrTypeInfo(typeof(global::BeginnerGuideUpdateResponse), global::BeginnerGuideUpdateResponse.Parser, new[]{ "GuideIds" }, null, null, null, null)
        }));
  }
  #endregion

}
#region Messages
/// <summary>
/// 同步新手引导进度信息.
/// </summary>
public sealed partial class BeginnerGuideSyncResponse : pb::IMessage<BeginnerGuideSyncResponse> {
  private static readonly pb::MessageParser<BeginnerGuideSyncResponse> _parser = new pb::MessageParser<BeginnerGuideSyncResponse>(() => new BeginnerGuideSyncResponse());
  private pb::UnknownFieldSet _unknownFields;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pb::MessageParser<BeginnerGuideSyncResponse> Parser { get { return _parser; } }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pbr::MessageDescriptor Descriptor {
    get { return global::BeginnerReflection.Descriptor.MessageTypes[0]; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  pbr::MessageDescriptor pb::IMessage.Descriptor {
    get { return Descriptor; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideSyncResponse() {
    OnConstruction();
  }

  partial void OnConstruction();

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideSyncResponse(BeginnerGuideSyncResponse other) : this() {
    guideIds_ = other.guideIds_.Clone();
    _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideSyncResponse Clone() {
    return new BeginnerGuideSyncResponse(this);
  }

  /// <summary>Field number for the "guideIds" field.</summary>
  public const int GuideIdsFieldNumber = 1;
  private static readonly pb::FieldCodec<int> _repeated_guideIds_codec
      = pb::FieldCodec.ForInt32(10);
  private readonly pbc::RepeatedField<int> guideIds_ = new pbc::RepeatedField<int>();
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public pbc::RepeatedField<int> GuideIds {
    get { return guideIds_; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override bool Equals(object other) {
    return Equals(other as BeginnerGuideSyncResponse);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public bool Equals(BeginnerGuideSyncResponse other) {
    if (ReferenceEquals(other, null)) {
      return false;
    }
    if (ReferenceEquals(other, this)) {
      return true;
    }
    if(!guideIds_.Equals(other.guideIds_)) return false;
    return Equals(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override int GetHashCode() {
    int hash = 1;
    hash ^= guideIds_.GetHashCode();
    if (_unknownFields != null) {
      hash ^= _unknownFields.GetHashCode();
    }
    return hash;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override string ToString() {
    return pb::JsonFormatter.ToDiagnosticString(this);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void WriteTo(pb::CodedOutputStream output) {
    guideIds_.WriteTo(output, _repeated_guideIds_codec);
    if (_unknownFields != null) {
      _unknownFields.WriteTo(output);
    }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public int CalculateSize() {
    int size = 0;
    size += guideIds_.CalculateSize(_repeated_guideIds_codec);
    if (_unknownFields != null) {
      size += _unknownFields.CalculateSize();
    }
    return size;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(BeginnerGuideSyncResponse other) {
    if (other == null) {
      return;
    }
    guideIds_.Add(other.guideIds_);
    _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(pb::CodedInputStream input) {
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
      switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
          break;
        case 10:
        case 8: {
          guideIds_.AddEntriesFrom(input, _repeated_guideIds_codec);
          break;
        }
      }
    }
  }

}

/// <summary>
/// 请求更新新手引导进度信息
/// </summary>
public sealed partial class BeginnerGuideUpdateRequest : pb::IMessage<BeginnerGuideUpdateRequest> {
  private static readonly pb::MessageParser<BeginnerGuideUpdateRequest> _parser = new pb::MessageParser<BeginnerGuideUpdateRequest>(() => new BeginnerGuideUpdateRequest());
  private pb::UnknownFieldSet _unknownFields;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pb::MessageParser<BeginnerGuideUpdateRequest> Parser { get { return _parser; } }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pbr::MessageDescriptor Descriptor {
    get { return global::BeginnerReflection.Descriptor.MessageTypes[1]; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  pbr::MessageDescriptor pb::IMessage.Descriptor {
    get { return Descriptor; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideUpdateRequest() {
    OnConstruction();
  }

  partial void OnConstruction();

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideUpdateRequest(BeginnerGuideUpdateRequest other) : this() {
    guideIds_ = other.guideIds_.Clone();
    _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideUpdateRequest Clone() {
    return new BeginnerGuideUpdateRequest(this);
  }

  /// <summary>Field number for the "guideIds" field.</summary>
  public const int GuideIdsFieldNumber = 1;
  private static readonly pb::FieldCodec<int> _repeated_guideIds_codec
      = pb::FieldCodec.ForInt32(10);
  private readonly pbc::RepeatedField<int> guideIds_ = new pbc::RepeatedField<int>();
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public pbc::RepeatedField<int> GuideIds {
    get { return guideIds_; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override bool Equals(object other) {
    return Equals(other as BeginnerGuideUpdateRequest);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public bool Equals(BeginnerGuideUpdateRequest other) {
    if (ReferenceEquals(other, null)) {
      return false;
    }
    if (ReferenceEquals(other, this)) {
      return true;
    }
    if(!guideIds_.Equals(other.guideIds_)) return false;
    return Equals(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override int GetHashCode() {
    int hash = 1;
    hash ^= guideIds_.GetHashCode();
    if (_unknownFields != null) {
      hash ^= _unknownFields.GetHashCode();
    }
    return hash;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override string ToString() {
    return pb::JsonFormatter.ToDiagnosticString(this);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void WriteTo(pb::CodedOutputStream output) {
    guideIds_.WriteTo(output, _repeated_guideIds_codec);
    if (_unknownFields != null) {
      _unknownFields.WriteTo(output);
    }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public int CalculateSize() {
    int size = 0;
    size += guideIds_.CalculateSize(_repeated_guideIds_codec);
    if (_unknownFields != null) {
      size += _unknownFields.CalculateSize();
    }
    return size;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(BeginnerGuideUpdateRequest other) {
    if (other == null) {
      return;
    }
    guideIds_.Add(other.guideIds_);
    _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(pb::CodedInputStream input) {
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
      switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
          break;
        case 10:
        case 8: {
          guideIds_.AddEntriesFrom(input, _repeated_guideIds_codec);
          break;
        }
      }
    }
  }

}

/// <summary>
/// 更新新手引导进度信息回包
/// </summary>
public sealed partial class BeginnerGuideUpdateResponse : pb::IMessage<BeginnerGuideUpdateResponse> {
  private static readonly pb::MessageParser<BeginnerGuideUpdateResponse> _parser = new pb::MessageParser<BeginnerGuideUpdateResponse>(() => new BeginnerGuideUpdateResponse());
  private pb::UnknownFieldSet _unknownFields;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pb::MessageParser<BeginnerGuideUpdateResponse> Parser { get { return _parser; } }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pbr::MessageDescriptor Descriptor {
    get { return global::BeginnerReflection.Descriptor.MessageTypes[2]; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  pbr::MessageDescriptor pb::IMessage.Descriptor {
    get { return Descriptor; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideUpdateResponse() {
    OnConstruction();
  }

  partial void OnConstruction();

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideUpdateResponse(BeginnerGuideUpdateResponse other) : this() {
    guideIds_ = other.guideIds_.Clone();
    _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public BeginnerGuideUpdateResponse Clone() {
    return new BeginnerGuideUpdateResponse(this);
  }

  /// <summary>Field number for the "guideIds" field.</summary>
  public const int GuideIdsFieldNumber = 1;
  private static readonly pb::FieldCodec<int> _repeated_guideIds_codec
      = pb::FieldCodec.ForInt32(10);
  private readonly pbc::RepeatedField<int> guideIds_ = new pbc::RepeatedField<int>();
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public pbc::RepeatedField<int> GuideIds {
    get { return guideIds_; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override bool Equals(object other) {
    return Equals(other as BeginnerGuideUpdateResponse);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public bool Equals(BeginnerGuideUpdateResponse other) {
    if (ReferenceEquals(other, null)) {
      return false;
    }
    if (ReferenceEquals(other, this)) {
      return true;
    }
    if(!guideIds_.Equals(other.guideIds_)) return false;
    return Equals(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override int GetHashCode() {
    int hash = 1;
    hash ^= guideIds_.GetHashCode();
    if (_unknownFields != null) {
      hash ^= _unknownFields.GetHashCode();
    }
    return hash;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override string ToString() {
    return pb::JsonFormatter.ToDiagnosticString(this);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void WriteTo(pb::CodedOutputStream output) {
    guideIds_.WriteTo(output, _repeated_guideIds_codec);
    if (_unknownFields != null) {
      _unknownFields.WriteTo(output);
    }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public int CalculateSize() {
    int size = 0;
    size += guideIds_.CalculateSize(_repeated_guideIds_codec);
    if (_unknownFields != null) {
      size += _unknownFields.CalculateSize();
    }
    return size;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(BeginnerGuideUpdateResponse other) {
    if (other == null) {
      return;
    }
    guideIds_.Add(other.guideIds_);
    _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(pb::CodedInputStream input) {
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
      switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
          break;
        case 10:
        case 8: {
          guideIds_.AddEntriesFrom(input, _repeated_guideIds_codec);
          break;
        }
      }
    }
  }

}

#endregion


#endregion Designer generated code
