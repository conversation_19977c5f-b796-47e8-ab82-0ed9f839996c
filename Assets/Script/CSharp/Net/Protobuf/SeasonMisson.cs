// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/SeasonMisson.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/SeasonMisson.proto</summary>
  public static partial class SeasonMissonReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/SeasonMisson.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static SeasonMissonReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChtDMlNQcm90by9TZWFzb25NaXNzb24ucHJvdG8aE0MyU1Byb3RvL0l0ZW0u",
            "cHJvdG8aE0MyU1Byb3RvL1JhbmsucHJvdG8aF0MyU1Byb3RvL1Byb3BlcnR5",
            "LnByb3RvIlsKGFNlYXNvbkJhdHRsZVBhc3NJdGVtSW5mbxIKCgJpZBgBIAEo",
            "BRIPCgdhZHZhbmNlGAIgASgIEhQKDHN1cGVyQWR2YW5jZRgDIAEoCBIMCgRm",
            "cmVlGAQgASgIIlkKJFNlYXNvbkJhdHRsZVBhc3NJdGVtSW5mb1N5bmNSZXNw",
            "b25zZRIxCg5iYXR0bGVJdGVtSW5mbxgBIAMoCzIZLlNlYXNvbkJhdHRsZVBh",
            "c3NJdGVtSW5mbyI6ChtTZWFzb25CYXR0bGVQYXNzSXRlbUJ1eUluZm8SCgoC",
            "aWQYASABKAUSDwoHYnV5VHlwZRgCIAEoBSJMCh5TZWFzb25CYXR0bGVQYXNz",
            "SXRlbUJ1eVJlcXVlc3QSKgoEaW5mbxgBIAMoCzIcLlNlYXNvbkJhdHRsZVBh",
            "c3NJdGVtQnV5SW5mbyJuCh9TZWFzb25CYXR0bGVQYXNzSXRlbUJ1eVJlc3Bv",
            "bnNlEioKBGluZm8YASADKAsyHC5TZWFzb25CYXR0bGVQYXNzSXRlbUJ1eUlu",
            "Zm8SHwoGcmV3YXJkGAIgAygLMg8uUmV3YXJkUmVzcG9uc2UiZwoRU2Vhc29u",
            "TWlzc2lvbkluZm8SCgoCaWQYASABKAUSEAoIb3BlblRpbWUYAiABKAMSDwoH",
            "ZW5kVGltZRgDIAEoAxITCgtzaG9wRW5kVGltZRgEIAEoAxIOCgZhdHRhY2sY",
            "BSABKAMiYQodU2Vhc29uTWlzc2lvbkluZm9TeW5jUmVzcG9uc2USLQoRc2Vh",
            "c29uTWlzc2lvbkluZm8YASADKAsyEi5TZWFzb25NaXNzaW9uSW5mbxIRCglz",
            "ZWFzb25JZHMYAiADKAUiPgoOU2Vhc29uVGFza0luZm8SCgoCaWQYASABKAUS",
            "DgoGc3RhdHVzGAIgASgFEhAKCHByb2dyZXNzGAQgASgDIjsKDFNNaXNzaW9u",
            "SW5mbxIKCgJpZBgBIAEoBRILCgN3aW4YAiABKAgSEgoKYmF0dGxlVGltZRgD",
            "IAEoBSIrCg5TZWFzb25TaG9wSW5mbxIKCgJpZBgBIAEoBRINCgVjb3VudBgC",
            "IAEoBSK1AQoXUGxheWVyU2Vhc29uTWlzc2lvbkluZm8SEQoJc2Vhc29uX2lk",
            "GAEgASgFEh4KBXRhc2tzGAIgAygLMg8uU2Vhc29uVGFza0luZm8SHwoIbWlz",
            "c2lvbnMYAyADKAsyDS5TTWlzc2lvbkluZm8SHgoFc2hvcHMYBCADKAsyDy5T",
            "ZWFzb25TaG9wSW5mbxIMCgR3ZWVrGAUgASgFEhgKEHBhc3NNYXhNaXNzaW9u",
            "SWQYBiABKAUiYAojUGxheWVyU2Vhc29uTWlzc2lvbkluZm9TeW5jUmVzcG9u",
            "c2USOQoXcGxheWVyU2Vhc29uTWlzc2lvbkluZm8YASADKAsyGC5QbGF5ZXJT",
            "ZWFzb25NaXNzaW9uSW5mbyJOChpTZWFzb25UYXNrSW5mb1N5bmNSZXNwb25z",
            "ZRIRCglzZWFzb25faWQYASABKAUSHQoEdGFzaxgCIAMoCzIPLlNlYXNvblRh",
            "c2tJbmZvIkQKHlJlY2VpdmVTZWFzb25UYXNrUmV3YXJkUmVxdWVzdBIRCglz",
            "ZWFzb25faWQYASABKAUSDwoHdGFza19pZBgCIAEoBSJUCiBSZWNlaXZlZFNl",
            "YXNvblRhc2tSZXdhcmRSZXNwb25zZRIRCglzZWFzb25faWQYASABKAUSHQoE",
            "dGFzaxgCIAEoCzIPLlNlYXNvblRhc2tJbmZvIkkKFFNlYXNvblNob3BCdXlS",
            "ZXF1ZXN0EhEKCXNlYXNvbl9pZBgBIAEoBRIPCgdzaG9wX2lkGAIgASgFEg0K",
            "BWNvdW50GAMgASgFIkkKFVNlYXNvblNob3BCdXlSZXNwb25zZRIRCglzZWFz",
            "b25faWQYASABKAUSHQoEc2hvcBgCIAEoCzIPLlNlYXNvblNob3BJbmZvIkcK",
            "H1BsYXllclNlYXNvbk1pc3Npb25TdGFydFJlcXVlc3QSEQoJbWlzc2lvbklk",
            "GAEgASgFEhEKCXNlYXNvbl9pZBgCIAEoBSJ8CiBQbGF5ZXJTZWFzb25NaXNz",
            "aW9uU3RhcnRSZXNwb25zZRIRCgltaXNzaW9uSWQYASABKAUSEQoJYmF0dGxl",
            "X2lkGAIgASgDEhEKCXNlYXNvbl9pZBgDIAEoBRIfCghwcm9wZXJ0eRgEIAMo",
            "CzINLlByb3BlcnR5SW5mbyJiCh9QbGF5ZXJTZWFzb25NaXNzaW9uU3dlZXBS",
            "ZXF1ZXN0EhYKDm1pc3Npb25TdGFydElkGAEgASgFEhQKDG1pc3Npb25FbmRJ",
            "ZBgCIAEoBRIRCglzZWFzb25faWQYAyABKAUihAEKIFBsYXllclNlYXNvbk1p",
            "c3Npb25Td2VlcFJlc3BvbnNlEhYKDm1pc3Npb25TdGFydElkGAEgASgFEhQK",
            "DG1pc3Npb25FbmRJZBgCIAEoBRIRCglzZWFzb25faWQYAyABKAUSHwoGcmV3",
            "YXJkGAQgAygLMg8uUmV3YXJkUmVzcG9uc2UixAEKHVBsYXllclNlYXNvbk1p",
            "c3Npb25FbmRSZXF1ZXN0EhEKCW1pc3Npb25JZBgBIAEoBRISCgpiYXR0bGVU",
            "aW1lGAIgASgFEhQKDGJhdHRsZVN0YXR1cxgDIAEoBRIQCghwcm9ncmVzcxgE",
            "IAEoBRIbCghpdGVtSW5mbxgFIAMoCzIJLkl0ZW1JbmZvEhEKCWtpbGxDb3Vu",
            "dBgGIAEoBRIRCgliYXR0bGVfaWQYCCABKAMSEQoJc2Vhc29uX2lkGAkgASgF",
            "IrMCCh5QbGF5ZXJTZWFzb25NaXNzaW9uRW5kUmVzcG9uc2USEQoJbWlzc2lv",
            "bklkGAEgASgFEiIKC21pc3Npb25JbmZvGAIgASgLMg0uU01pc3Npb25JbmZv",
            "EhsKCGl0ZW1JbmZvGAMgAygLMgkuSXRlbUluZm8SEQoJa2lsbENvdW50GAQg",
            "ASgFEhEKCWJhdHRsZV9pZBgFIAEoAxIRCglzZWFzb25faWQYBiABKAUSIwoQ",
            "b3JkaW5hcnlJdGVtSW5mbxgHIAMoCzIJLkl0ZW1JbmZvEh8KDGJvc3NJdGVt",
            "SW5mbxgIIAMoCzIJLkl0ZW1JbmZvEhIKCmJhdHRsZVRpbWUYCSABKAUSKgoX",
            "cGVha0NvbXBldGl0aW9uSXRlbUluZm8YCiADKAsyCS5JdGVtSW5mbyJXCiVS",
            "YW5rU2Vhc29uTWlzc2lvblJld2FyZFJlY2VpdmVSZXF1ZXN0EhsKE3JlY2Vp",
            "dmVNYXhNaXNzaW9uSWQYASABKAUSEQoJc2Vhc29uX2lkGAIgASgFIlgKJlJh",
            "bmtTZWFzb25NaXNzaW9uUmV3YXJkUmVjZWl2ZVJlc3BvbnNlEhsKE3JlY2Vp",
            "dmVNYXhNaXNzaW9uSWQYASABKAUSEQoJc2Vhc29uX2lkGAIgASgFIlAKJ1Jh",
            "bmtTZWFzb25NaXNzaW9uRmlyc3RQYXNzUmV3YXJkUmVxdWVzdBISCgptaXNz",
            "aW9uSWRzGAEgAygFEhEKCXNlYXNvbl9pZBgCIAEoBSLDAQooUmFua1NlYXNv",
            "bk1pc3Npb25GaXJzdFBhc3NSZXdhcmRSZXNwb25zZRJHCgdyZWNvcmRzGAEg",
            "AygLMjYuUmFua1NlYXNvbk1pc3Npb25GaXJzdFBhc3NSZXdhcmRSZXNwb25z",
            "ZS5SZWNvcmRzRW50cnkSEQoJc2Vhc29uX2lkGAIgASgFGjsKDFJlY29yZHNF",
            "bnRyeRILCgNrZXkYASABKAUSGgoFdmFsdWUYAiABKAsyCy5SYW5rUmVjb3Jk",
            "OgI4ASJHCh9SYW5rU2Vhc29uTWlzc2lvblRvcFJvbGVSZXF1ZXN0EhEKCW1p",
            "c3Npb25JZBgBIAEoBRIRCglzZWFzb25faWQYAiABKAUiZgogUmFua1NlYXNv",
            "bk1pc3Npb25Ub3BSb2xlUmVzcG9uc2USEQoJbWlzc2lvbklkGAEgASgFEhEK",
            "CXNlYXNvbl9pZBgCIAEoBRIcCgdyZWNvcmRzGAMgAygLMgsuUmFua1JlY29y",
            "ZCI8ChRSYW5rU2Vhc29uTWF4TWlzc2lvbhIRCgltaXNzaW9uSWQYASABKAUS",
            "EQoJc2Vhc29uX2lkGAIgASgFIh0KG1JhbmtTZWFzb25NYXhNaXNzaW9uUmVx",
            "dWVzdCJDChxSYW5rU2Vhc29uTWF4TWlzc2lvblJlc3BvbnNlEiMKBGluZm8Y",
            "ASADKAsyFS5SYW5rU2Vhc29uTWF4TWlzc2lvbipDChdTZWFzb25CYXR0bGVQ",
            "YXNzQnV5VHlwZRIICgRGUkVFEAASCwoHQURWQU5DRRABEhEKDVNVUEVSX0FE",
            "VkFOQ0UQAio0ChBTZWFzb25UYXNrU3RhdHVzEggKBE5PTkUQABIKCgZGSU5J",
            "U0gQARIKCgZSRVdBUkQQAkIjChNjb20uZ29sZGVuLnByb3RvY29sqgILTEQu",
            "UHJvdG9jb2xiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::LD.Protocol.ItemReflection.Descriptor, global::LD.Protocol.RankReflection.Descriptor, global::LD.Protocol.PropertyReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::LD.Protocol.SeasonBattlePassBuyType), typeof(global::LD.Protocol.SeasonTaskStatus), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonBattlePassItemInfo), global::LD.Protocol.SeasonBattlePassItemInfo.Parser, new[]{ "Id", "Advance", "SuperAdvance", "Free" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonBattlePassItemInfoSyncResponse), global::LD.Protocol.SeasonBattlePassItemInfoSyncResponse.Parser, new[]{ "BattleItemInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonBattlePassItemBuyInfo), global::LD.Protocol.SeasonBattlePassItemBuyInfo.Parser, new[]{ "Id", "BuyType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonBattlePassItemBuyRequest), global::LD.Protocol.SeasonBattlePassItemBuyRequest.Parser, new[]{ "Info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonBattlePassItemBuyResponse), global::LD.Protocol.SeasonBattlePassItemBuyResponse.Parser, new[]{ "Info", "Reward" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonMissionInfo), global::LD.Protocol.SeasonMissionInfo.Parser, new[]{ "Id", "OpenTime", "EndTime", "ShopEndTime", "Attack" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonMissionInfoSyncResponse), global::LD.Protocol.SeasonMissionInfoSyncResponse.Parser, new[]{ "SeasonMissionInfo", "SeasonIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonTaskInfo), global::LD.Protocol.SeasonTaskInfo.Parser, new[]{ "Id", "Status", "Progress" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SMissionInfo), global::LD.Protocol.SMissionInfo.Parser, new[]{ "Id", "Win", "BattleTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonShopInfo), global::LD.Protocol.SeasonShopInfo.Parser, new[]{ "Id", "Count" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionInfo), global::LD.Protocol.PlayerSeasonMissionInfo.Parser, new[]{ "SeasonId", "Tasks", "Missions", "Shops", "Week", "PassMaxMissionId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionInfoSyncResponse), global::LD.Protocol.PlayerSeasonMissionInfoSyncResponse.Parser, new[]{ "PlayerSeasonMissionInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonTaskInfoSyncResponse), global::LD.Protocol.SeasonTaskInfoSyncResponse.Parser, new[]{ "SeasonId", "Task" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceiveSeasonTaskRewardRequest), global::LD.Protocol.ReceiveSeasonTaskRewardRequest.Parser, new[]{ "SeasonId", "TaskId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceivedSeasonTaskRewardResponse), global::LD.Protocol.ReceivedSeasonTaskRewardResponse.Parser, new[]{ "SeasonId", "Task" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonShopBuyRequest), global::LD.Protocol.SeasonShopBuyRequest.Parser, new[]{ "SeasonId", "ShopId", "Count" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.SeasonShopBuyResponse), global::LD.Protocol.SeasonShopBuyResponse.Parser, new[]{ "SeasonId", "Shop" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionStartRequest), global::LD.Protocol.PlayerSeasonMissionStartRequest.Parser, new[]{ "MissionId", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionStartResponse), global::LD.Protocol.PlayerSeasonMissionStartResponse.Parser, new[]{ "MissionId", "BattleId", "SeasonId", "Property" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionSweepRequest), global::LD.Protocol.PlayerSeasonMissionSweepRequest.Parser, new[]{ "MissionStartId", "MissionEndId", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionSweepResponse), global::LD.Protocol.PlayerSeasonMissionSweepResponse.Parser, new[]{ "MissionStartId", "MissionEndId", "SeasonId", "Reward" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionEndRequest), global::LD.Protocol.PlayerSeasonMissionEndRequest.Parser, new[]{ "MissionId", "BattleTime", "BattleStatus", "Progress", "ItemInfo", "KillCount", "BattleId", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PlayerSeasonMissionEndResponse), global::LD.Protocol.PlayerSeasonMissionEndResponse.Parser, new[]{ "MissionId", "MissionInfo", "ItemInfo", "KillCount", "BattleId", "SeasonId", "OrdinaryItemInfo", "BossItemInfo", "BattleTime", "PeakCompetitionItemInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMissionRewardReceiveRequest), global::LD.Protocol.RankSeasonMissionRewardReceiveRequest.Parser, new[]{ "ReceiveMaxMissionId", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMissionRewardReceiveResponse), global::LD.Protocol.RankSeasonMissionRewardReceiveResponse.Parser, new[]{ "ReceiveMaxMissionId", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMissionFirstPassRewardRequest), global::LD.Protocol.RankSeasonMissionFirstPassRewardRequest.Parser, new[]{ "MissionIds", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMissionFirstPassRewardResponse), global::LD.Protocol.RankSeasonMissionFirstPassRewardResponse.Parser, new[]{ "Records", "SeasonId" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMissionTopRoleRequest), global::LD.Protocol.RankSeasonMissionTopRoleRequest.Parser, new[]{ "MissionId", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMissionTopRoleResponse), global::LD.Protocol.RankSeasonMissionTopRoleResponse.Parser, new[]{ "MissionId", "SeasonId", "Records" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMaxMission), global::LD.Protocol.RankSeasonMaxMission.Parser, new[]{ "MissionId", "SeasonId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMaxMissionRequest), global::LD.Protocol.RankSeasonMaxMissionRequest.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RankSeasonMaxMissionResponse), global::LD.Protocol.RankSeasonMaxMissionResponse.Parser, new[]{ "Info" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum SeasonBattlePassBuyType {
    [pbr::OriginalName("FREE")] Free = 0,
    [pbr::OriginalName("ADVANCE")] Advance = 1,
    [pbr::OriginalName("SUPER_ADVANCE")] SuperAdvance = 2,
  }

  public enum SeasonTaskStatus {
    /// <summary>
    /// 任务没有完成
    /// </summary>
    [pbr::OriginalName("NONE")] None = 0,
    /// <summary>
    /// 任务完成了
    /// </summary>
    [pbr::OriginalName("FINISH")] Finish = 1,
    /// <summary>
    /// 已经领取奖励
    /// </summary>
    [pbr::OriginalName("REWARD")] Reward = 2,
  }

  #endregion

  #region Messages
  public sealed partial class SeasonBattlePassItemInfo : pb::IMessage<SeasonBattlePassItemInfo> {
    private static readonly pb::MessageParser<SeasonBattlePassItemInfo> _parser = new pb::MessageParser<SeasonBattlePassItemInfo>(() => new SeasonBattlePassItemInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonBattlePassItemInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemInfo(SeasonBattlePassItemInfo other) : this() {
      id_ = other.id_;
      advance_ = other.advance_;
      superAdvance_ = other.superAdvance_;
      free_ = other.free_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemInfo Clone() {
      return new SeasonBattlePassItemInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "advance" field.</summary>
    public const int AdvanceFieldNumber = 2;
    private bool advance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Advance {
      get { return advance_; }
      set {
        advance_ = value;
      }
    }

    /// <summary>Field number for the "superAdvance" field.</summary>
    public const int SuperAdvanceFieldNumber = 3;
    private bool superAdvance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool SuperAdvance {
      get { return superAdvance_; }
      set {
        superAdvance_ = value;
      }
    }

    /// <summary>Field number for the "free" field.</summary>
    public const int FreeFieldNumber = 4;
    private bool free_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Free {
      get { return free_; }
      set {
        free_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonBattlePassItemInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonBattlePassItemInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Advance != other.Advance) return false;
      if (SuperAdvance != other.SuperAdvance) return false;
      if (Free != other.Free) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Advance != false) hash ^= Advance.GetHashCode();
      if (SuperAdvance != false) hash ^= SuperAdvance.GetHashCode();
      if (Free != false) hash ^= Free.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Advance != false) {
        output.WriteRawTag(16);
        output.WriteBool(Advance);
      }
      if (SuperAdvance != false) {
        output.WriteRawTag(24);
        output.WriteBool(SuperAdvance);
      }
      if (Free != false) {
        output.WriteRawTag(32);
        output.WriteBool(Free);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Advance != false) {
        size += 1 + 1;
      }
      if (SuperAdvance != false) {
        size += 1 + 1;
      }
      if (Free != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonBattlePassItemInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Advance != false) {
        Advance = other.Advance;
      }
      if (other.SuperAdvance != false) {
        SuperAdvance = other.SuperAdvance;
      }
      if (other.Free != false) {
        Free = other.Free;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Advance = input.ReadBool();
            break;
          }
          case 24: {
            SuperAdvance = input.ReadBool();
            break;
          }
          case 32: {
            Free = input.ReadBool();
            break;
          }
        }
      }
    }

  }

  public sealed partial class SeasonBattlePassItemInfoSyncResponse : pb::IMessage<SeasonBattlePassItemInfoSyncResponse> {
    private static readonly pb::MessageParser<SeasonBattlePassItemInfoSyncResponse> _parser = new pb::MessageParser<SeasonBattlePassItemInfoSyncResponse>(() => new SeasonBattlePassItemInfoSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonBattlePassItemInfoSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemInfoSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemInfoSyncResponse(SeasonBattlePassItemInfoSyncResponse other) : this() {
      battleItemInfo_ = other.battleItemInfo_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemInfoSyncResponse Clone() {
      return new SeasonBattlePassItemInfoSyncResponse(this);
    }

    /// <summary>Field number for the "battleItemInfo" field.</summary>
    public const int BattleItemInfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.SeasonBattlePassItemInfo> _repeated_battleItemInfo_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.SeasonBattlePassItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemInfo> battleItemInfo_ = new pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemInfo> BattleItemInfo {
      get { return battleItemInfo_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonBattlePassItemInfoSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonBattlePassItemInfoSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!battleItemInfo_.Equals(other.battleItemInfo_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= battleItemInfo_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      battleItemInfo_.WriteTo(output, _repeated_battleItemInfo_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += battleItemInfo_.CalculateSize(_repeated_battleItemInfo_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonBattlePassItemInfoSyncResponse other) {
      if (other == null) {
        return;
      }
      battleItemInfo_.Add(other.battleItemInfo_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            battleItemInfo_.AddEntriesFrom(input, _repeated_battleItemInfo_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class SeasonBattlePassItemBuyInfo : pb::IMessage<SeasonBattlePassItemBuyInfo> {
    private static readonly pb::MessageParser<SeasonBattlePassItemBuyInfo> _parser = new pb::MessageParser<SeasonBattlePassItemBuyInfo>(() => new SeasonBattlePassItemBuyInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonBattlePassItemBuyInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyInfo(SeasonBattlePassItemBuyInfo other) : this() {
      id_ = other.id_;
      buyType_ = other.buyType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyInfo Clone() {
      return new SeasonBattlePassItemBuyInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "buyType" field.</summary>
    public const int BuyTypeFieldNumber = 2;
    private int buyType_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BuyType {
      get { return buyType_; }
      set {
        buyType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonBattlePassItemBuyInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonBattlePassItemBuyInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (BuyType != other.BuyType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (BuyType != 0) hash ^= BuyType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (BuyType != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(BuyType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (BuyType != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BuyType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonBattlePassItemBuyInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.BuyType != 0) {
        BuyType = other.BuyType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            BuyType = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class SeasonBattlePassItemBuyRequest : pb::IMessage<SeasonBattlePassItemBuyRequest> {
    private static readonly pb::MessageParser<SeasonBattlePassItemBuyRequest> _parser = new pb::MessageParser<SeasonBattlePassItemBuyRequest>(() => new SeasonBattlePassItemBuyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonBattlePassItemBuyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyRequest(SeasonBattlePassItemBuyRequest other) : this() {
      info_ = other.info_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyRequest Clone() {
      return new SeasonBattlePassItemBuyRequest(this);
    }

    /// <summary>Field number for the "info" field.</summary>
    public const int InfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.SeasonBattlePassItemBuyInfo> _repeated_info_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.SeasonBattlePassItemBuyInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemBuyInfo> info_ = new pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemBuyInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemBuyInfo> Info {
      get { return info_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonBattlePassItemBuyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonBattlePassItemBuyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!info_.Equals(other.info_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= info_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      info_.WriteTo(output, _repeated_info_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += info_.CalculateSize(_repeated_info_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonBattlePassItemBuyRequest other) {
      if (other == null) {
        return;
      }
      info_.Add(other.info_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            info_.AddEntriesFrom(input, _repeated_info_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class SeasonBattlePassItemBuyResponse : pb::IMessage<SeasonBattlePassItemBuyResponse> {
    private static readonly pb::MessageParser<SeasonBattlePassItemBuyResponse> _parser = new pb::MessageParser<SeasonBattlePassItemBuyResponse>(() => new SeasonBattlePassItemBuyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonBattlePassItemBuyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyResponse(SeasonBattlePassItemBuyResponse other) : this() {
      info_ = other.info_.Clone();
      reward_ = other.reward_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonBattlePassItemBuyResponse Clone() {
      return new SeasonBattlePassItemBuyResponse(this);
    }

    /// <summary>Field number for the "info" field.</summary>
    public const int InfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.SeasonBattlePassItemBuyInfo> _repeated_info_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.SeasonBattlePassItemBuyInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemBuyInfo> info_ = new pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemBuyInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SeasonBattlePassItemBuyInfo> Info {
      get { return info_; }
    }

    /// <summary>Field number for the "reward" field.</summary>
    public const int RewardFieldNumber = 2;
    private static readonly pb::FieldCodec<global::LD.Protocol.RewardResponse> _repeated_reward_codec
        = pb::FieldCodec.ForMessage(18, global::LD.Protocol.RewardResponse.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.RewardResponse> reward_ = new pbc::RepeatedField<global::LD.Protocol.RewardResponse>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.RewardResponse> Reward {
      get { return reward_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonBattlePassItemBuyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonBattlePassItemBuyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!info_.Equals(other.info_)) return false;
      if(!reward_.Equals(other.reward_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= info_.GetHashCode();
      hash ^= reward_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      info_.WriteTo(output, _repeated_info_codec);
      reward_.WriteTo(output, _repeated_reward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += info_.CalculateSize(_repeated_info_codec);
      size += reward_.CalculateSize(_repeated_reward_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonBattlePassItemBuyResponse other) {
      if (other == null) {
        return;
      }
      info_.Add(other.info_);
      reward_.Add(other.reward_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            info_.AddEntriesFrom(input, _repeated_info_codec);
            break;
          }
          case 18: {
            reward_.AddEntriesFrom(input, _repeated_reward_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///以上为 战令 相关
  /// </summary>
  public sealed partial class SeasonMissionInfo : pb::IMessage<SeasonMissionInfo> {
    private static readonly pb::MessageParser<SeasonMissionInfo> _parser = new pb::MessageParser<SeasonMissionInfo>(() => new SeasonMissionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonMissionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonMissionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonMissionInfo(SeasonMissionInfo other) : this() {
      id_ = other.id_;
      openTime_ = other.openTime_;
      endTime_ = other.endTime_;
      shopEndTime_ = other.shopEndTime_;
      attack_ = other.attack_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonMissionInfo Clone() {
      return new SeasonMissionInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "openTime" field.</summary>
    public const int OpenTimeFieldNumber = 2;
    private long openTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long OpenTime {
      get { return openTime_; }
      set {
        openTime_ = value;
      }
    }

    /// <summary>Field number for the "endTime" field.</summary>
    public const int EndTimeFieldNumber = 3;
    private long endTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long EndTime {
      get { return endTime_; }
      set {
        endTime_ = value;
      }
    }

    /// <summary>Field number for the "shopEndTime" field.</summary>
    public const int ShopEndTimeFieldNumber = 4;
    private long shopEndTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long ShopEndTime {
      get { return shopEndTime_; }
      set {
        shopEndTime_ = value;
      }
    }

    /// <summary>Field number for the "attack" field.</summary>
    public const int AttackFieldNumber = 5;
    private long attack_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Attack {
      get { return attack_; }
      set {
        attack_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonMissionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonMissionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (OpenTime != other.OpenTime) return false;
      if (EndTime != other.EndTime) return false;
      if (ShopEndTime != other.ShopEndTime) return false;
      if (Attack != other.Attack) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (OpenTime != 0L) hash ^= OpenTime.GetHashCode();
      if (EndTime != 0L) hash ^= EndTime.GetHashCode();
      if (ShopEndTime != 0L) hash ^= ShopEndTime.GetHashCode();
      if (Attack != 0L) hash ^= Attack.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (OpenTime != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(OpenTime);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(EndTime);
      }
      if (ShopEndTime != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(ShopEndTime);
      }
      if (Attack != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Attack);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (OpenTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(OpenTime);
      }
      if (EndTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(EndTime);
      }
      if (ShopEndTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ShopEndTime);
      }
      if (Attack != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Attack);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonMissionInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.OpenTime != 0L) {
        OpenTime = other.OpenTime;
      }
      if (other.EndTime != 0L) {
        EndTime = other.EndTime;
      }
      if (other.ShopEndTime != 0L) {
        ShopEndTime = other.ShopEndTime;
      }
      if (other.Attack != 0L) {
        Attack = other.Attack;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            OpenTime = input.ReadInt64();
            break;
          }
          case 24: {
            EndTime = input.ReadInt64();
            break;
          }
          case 32: {
            ShopEndTime = input.ReadInt64();
            break;
          }
          case 40: {
            Attack = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  public sealed partial class SeasonMissionInfoSyncResponse : pb::IMessage<SeasonMissionInfoSyncResponse> {
    private static readonly pb::MessageParser<SeasonMissionInfoSyncResponse> _parser = new pb::MessageParser<SeasonMissionInfoSyncResponse>(() => new SeasonMissionInfoSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonMissionInfoSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonMissionInfoSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonMissionInfoSyncResponse(SeasonMissionInfoSyncResponse other) : this() {
      seasonMissionInfo_ = other.seasonMissionInfo_.Clone();
      seasonIds_ = other.seasonIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonMissionInfoSyncResponse Clone() {
      return new SeasonMissionInfoSyncResponse(this);
    }

    /// <summary>Field number for the "seasonMissionInfo" field.</summary>
    public const int SeasonMissionInfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.SeasonMissionInfo> _repeated_seasonMissionInfo_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.SeasonMissionInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SeasonMissionInfo> seasonMissionInfo_ = new pbc::RepeatedField<global::LD.Protocol.SeasonMissionInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SeasonMissionInfo> SeasonMissionInfo {
      get { return seasonMissionInfo_; }
    }

    /// <summary>Field number for the "seasonIds" field.</summary>
    public const int SeasonIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<int> _repeated_seasonIds_codec
        = pb::FieldCodec.ForInt32(18);
    private readonly pbc::RepeatedField<int> seasonIds_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> SeasonIds {
      get { return seasonIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonMissionInfoSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonMissionInfoSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!seasonMissionInfo_.Equals(other.seasonMissionInfo_)) return false;
      if(!seasonIds_.Equals(other.seasonIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= seasonMissionInfo_.GetHashCode();
      hash ^= seasonIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      seasonMissionInfo_.WriteTo(output, _repeated_seasonMissionInfo_codec);
      seasonIds_.WriteTo(output, _repeated_seasonIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += seasonMissionInfo_.CalculateSize(_repeated_seasonMissionInfo_codec);
      size += seasonIds_.CalculateSize(_repeated_seasonIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonMissionInfoSyncResponse other) {
      if (other == null) {
        return;
      }
      seasonMissionInfo_.Add(other.seasonMissionInfo_);
      seasonIds_.Add(other.seasonIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            seasonMissionInfo_.AddEntriesFrom(input, _repeated_seasonMissionInfo_codec);
            break;
          }
          case 18:
          case 16: {
            seasonIds_.AddEntriesFrom(input, _repeated_seasonIds_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 任务信息
  /// </summary>
  public sealed partial class SeasonTaskInfo : pb::IMessage<SeasonTaskInfo> {
    private static readonly pb::MessageParser<SeasonTaskInfo> _parser = new pb::MessageParser<SeasonTaskInfo>(() => new SeasonTaskInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonTaskInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonTaskInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonTaskInfo(SeasonTaskInfo other) : this() {
      id_ = other.id_;
      status_ = other.status_;
      progress_ = other.progress_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonTaskInfo Clone() {
      return new SeasonTaskInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 2;
    private int status_;
    /// <summary>
    /// 任务状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "progress" field.</summary>
    public const int ProgressFieldNumber = 4;
    private long progress_;
    /// <summary>
    /// 任务完成进度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Progress {
      get { return progress_; }
      set {
        progress_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonTaskInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonTaskInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Status != other.Status) return false;
      if (Progress != other.Progress) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Status != 0) hash ^= Status.GetHashCode();
      if (Progress != 0L) hash ^= Progress.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Status != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Status);
      }
      if (Progress != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Progress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Status != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Status);
      }
      if (Progress != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Progress);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonTaskInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Status != 0) {
        Status = other.Status;
      }
      if (other.Progress != 0L) {
        Progress = other.Progress;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Status = input.ReadInt32();
            break;
          }
          case 32: {
            Progress = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///关卡信息
  /// </summary>
  public sealed partial class SMissionInfo : pb::IMessage<SMissionInfo> {
    private static readonly pb::MessageParser<SMissionInfo> _parser = new pb::MessageParser<SMissionInfo>(() => new SMissionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SMissionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SMissionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SMissionInfo(SMissionInfo other) : this() {
      id_ = other.id_;
      win_ = other.win_;
      battleTime_ = other.battleTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SMissionInfo Clone() {
      return new SMissionInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    ///关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "win" field.</summary>
    public const int WinFieldNumber = 2;
    private bool win_;
    /// <summary>
    ///是否胜利
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Win {
      get { return win_; }
      set {
        win_ = value;
      }
    }

    /// <summary>Field number for the "battleTime" field.</summary>
    public const int BattleTimeFieldNumber = 3;
    private int battleTime_;
    /// <summary>
    ///战斗时长
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattleTime {
      get { return battleTime_; }
      set {
        battleTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SMissionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SMissionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Win != other.Win) return false;
      if (BattleTime != other.BattleTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Win != false) hash ^= Win.GetHashCode();
      if (BattleTime != 0) hash ^= BattleTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Win != false) {
        output.WriteRawTag(16);
        output.WriteBool(Win);
      }
      if (BattleTime != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(BattleTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Win != false) {
        size += 1 + 1;
      }
      if (BattleTime != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattleTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SMissionInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Win != false) {
        Win = other.Win;
      }
      if (other.BattleTime != 0) {
        BattleTime = other.BattleTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Win = input.ReadBool();
            break;
          }
          case 24: {
            BattleTime = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///商品信息
  /// </summary>
  public sealed partial class SeasonShopInfo : pb::IMessage<SeasonShopInfo> {
    private static readonly pb::MessageParser<SeasonShopInfo> _parser = new pb::MessageParser<SeasonShopInfo>(() => new SeasonShopInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonShopInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopInfo(SeasonShopInfo other) : this() {
      id_ = other.id_;
      count_ = other.count_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopInfo Clone() {
      return new SeasonShopInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    ///商品id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int CountFieldNumber = 2;
    private int count_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonShopInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonShopInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Count != other.Count) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Count != 0) hash ^= Count.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Count != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Count);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonShopInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Count != 0) {
        Count = other.Count;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Count = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class PlayerSeasonMissionInfo : pb::IMessage<PlayerSeasonMissionInfo> {
    private static readonly pb::MessageParser<PlayerSeasonMissionInfo> _parser = new pb::MessageParser<PlayerSeasonMissionInfo>(() => new PlayerSeasonMissionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionInfo(PlayerSeasonMissionInfo other) : this() {
      seasonId_ = other.seasonId_;
      tasks_ = other.tasks_.Clone();
      missions_ = other.missions_.Clone();
      shops_ = other.shops_.Clone();
      week_ = other.week_;
      passMaxMissionId_ = other.passMaxMissionId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionInfo Clone() {
      return new PlayerSeasonMissionInfo(this);
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 1;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "tasks" field.</summary>
    public const int TasksFieldNumber = 2;
    private static readonly pb::FieldCodec<global::LD.Protocol.SeasonTaskInfo> _repeated_tasks_codec
        = pb::FieldCodec.ForMessage(18, global::LD.Protocol.SeasonTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SeasonTaskInfo> tasks_ = new pbc::RepeatedField<global::LD.Protocol.SeasonTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SeasonTaskInfo> Tasks {
      get { return tasks_; }
    }

    /// <summary>Field number for the "missions" field.</summary>
    public const int MissionsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::LD.Protocol.SMissionInfo> _repeated_missions_codec
        = pb::FieldCodec.ForMessage(26, global::LD.Protocol.SMissionInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SMissionInfo> missions_ = new pbc::RepeatedField<global::LD.Protocol.SMissionInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SMissionInfo> Missions {
      get { return missions_; }
    }

    /// <summary>Field number for the "shops" field.</summary>
    public const int ShopsFieldNumber = 4;
    private static readonly pb::FieldCodec<global::LD.Protocol.SeasonShopInfo> _repeated_shops_codec
        = pb::FieldCodec.ForMessage(34, global::LD.Protocol.SeasonShopInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SeasonShopInfo> shops_ = new pbc::RepeatedField<global::LD.Protocol.SeasonShopInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SeasonShopInfo> Shops {
      get { return shops_; }
    }

    /// <summary>Field number for the "week" field.</summary>
    public const int WeekFieldNumber = 5;
    private int week_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Week {
      get { return week_; }
      set {
        week_ = value;
      }
    }

    /// <summary>Field number for the "passMaxMissionId" field.</summary>
    public const int PassMaxMissionIdFieldNumber = 6;
    private int passMaxMissionId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int PassMaxMissionId {
      get { return passMaxMissionId_; }
      set {
        passMaxMissionId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (SeasonId != other.SeasonId) return false;
      if(!tasks_.Equals(other.tasks_)) return false;
      if(!missions_.Equals(other.missions_)) return false;
      if(!shops_.Equals(other.shops_)) return false;
      if (Week != other.Week) return false;
      if (PassMaxMissionId != other.PassMaxMissionId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      hash ^= tasks_.GetHashCode();
      hash ^= missions_.GetHashCode();
      hash ^= shops_.GetHashCode();
      if (Week != 0) hash ^= Week.GetHashCode();
      if (PassMaxMissionId != 0) hash ^= PassMaxMissionId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (SeasonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(SeasonId);
      }
      tasks_.WriteTo(output, _repeated_tasks_codec);
      missions_.WriteTo(output, _repeated_missions_codec);
      shops_.WriteTo(output, _repeated_shops_codec);
      if (Week != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Week);
      }
      if (PassMaxMissionId != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(PassMaxMissionId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      size += tasks_.CalculateSize(_repeated_tasks_codec);
      size += missions_.CalculateSize(_repeated_missions_codec);
      size += shops_.CalculateSize(_repeated_shops_codec);
      if (Week != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Week);
      }
      if (PassMaxMissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PassMaxMissionId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionInfo other) {
      if (other == null) {
        return;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      tasks_.Add(other.tasks_);
      missions_.Add(other.missions_);
      shops_.Add(other.shops_);
      if (other.Week != 0) {
        Week = other.Week;
      }
      if (other.PassMaxMissionId != 0) {
        PassMaxMissionId = other.PassMaxMissionId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 18: {
            tasks_.AddEntriesFrom(input, _repeated_tasks_codec);
            break;
          }
          case 26: {
            missions_.AddEntriesFrom(input, _repeated_missions_codec);
            break;
          }
          case 34: {
            shops_.AddEntriesFrom(input, _repeated_shops_codec);
            break;
          }
          case 40: {
            Week = input.ReadInt32();
            break;
          }
          case 48: {
            PassMaxMissionId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class PlayerSeasonMissionInfoSyncResponse : pb::IMessage<PlayerSeasonMissionInfoSyncResponse> {
    private static readonly pb::MessageParser<PlayerSeasonMissionInfoSyncResponse> _parser = new pb::MessageParser<PlayerSeasonMissionInfoSyncResponse>(() => new PlayerSeasonMissionInfoSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionInfoSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionInfoSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionInfoSyncResponse(PlayerSeasonMissionInfoSyncResponse other) : this() {
      playerSeasonMissionInfo_ = other.playerSeasonMissionInfo_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionInfoSyncResponse Clone() {
      return new PlayerSeasonMissionInfoSyncResponse(this);
    }

    /// <summary>Field number for the "playerSeasonMissionInfo" field.</summary>
    public const int PlayerSeasonMissionInfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.PlayerSeasonMissionInfo> _repeated_playerSeasonMissionInfo_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.PlayerSeasonMissionInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.PlayerSeasonMissionInfo> playerSeasonMissionInfo_ = new pbc::RepeatedField<global::LD.Protocol.PlayerSeasonMissionInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.PlayerSeasonMissionInfo> PlayerSeasonMissionInfo {
      get { return playerSeasonMissionInfo_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionInfoSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionInfoSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!playerSeasonMissionInfo_.Equals(other.playerSeasonMissionInfo_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= playerSeasonMissionInfo_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      playerSeasonMissionInfo_.WriteTo(output, _repeated_playerSeasonMissionInfo_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += playerSeasonMissionInfo_.CalculateSize(_repeated_playerSeasonMissionInfo_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionInfoSyncResponse other) {
      if (other == null) {
        return;
      }
      playerSeasonMissionInfo_.Add(other.playerSeasonMissionInfo_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            playerSeasonMissionInfo_.AddEntriesFrom(input, _repeated_playerSeasonMissionInfo_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///单独更新任务
  /// </summary>
  public sealed partial class SeasonTaskInfoSyncResponse : pb::IMessage<SeasonTaskInfoSyncResponse> {
    private static readonly pb::MessageParser<SeasonTaskInfoSyncResponse> _parser = new pb::MessageParser<SeasonTaskInfoSyncResponse>(() => new SeasonTaskInfoSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonTaskInfoSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonTaskInfoSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonTaskInfoSyncResponse(SeasonTaskInfoSyncResponse other) : this() {
      seasonId_ = other.seasonId_;
      task_ = other.task_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonTaskInfoSyncResponse Clone() {
      return new SeasonTaskInfoSyncResponse(this);
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 1;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "task" field.</summary>
    public const int TaskFieldNumber = 2;
    private static readonly pb::FieldCodec<global::LD.Protocol.SeasonTaskInfo> _repeated_task_codec
        = pb::FieldCodec.ForMessage(18, global::LD.Protocol.SeasonTaskInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.SeasonTaskInfo> task_ = new pbc::RepeatedField<global::LD.Protocol.SeasonTaskInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.SeasonTaskInfo> Task {
      get { return task_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonTaskInfoSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonTaskInfoSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (SeasonId != other.SeasonId) return false;
      if(!task_.Equals(other.task_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      hash ^= task_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (SeasonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(SeasonId);
      }
      task_.WriteTo(output, _repeated_task_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      size += task_.CalculateSize(_repeated_task_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonTaskInfoSyncResponse other) {
      if (other == null) {
        return;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      task_.Add(other.task_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 18: {
            task_.AddEntriesFrom(input, _repeated_task_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 请求领取任务奖励
  /// </summary>
  public sealed partial class ReceiveSeasonTaskRewardRequest : pb::IMessage<ReceiveSeasonTaskRewardRequest> {
    private static readonly pb::MessageParser<ReceiveSeasonTaskRewardRequest> _parser = new pb::MessageParser<ReceiveSeasonTaskRewardRequest>(() => new ReceiveSeasonTaskRewardRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceiveSeasonTaskRewardRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveSeasonTaskRewardRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveSeasonTaskRewardRequest(ReceiveSeasonTaskRewardRequest other) : this() {
      seasonId_ = other.seasonId_;
      taskId_ = other.taskId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveSeasonTaskRewardRequest Clone() {
      return new ReceiveSeasonTaskRewardRequest(this);
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 1;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "task_id" field.</summary>
    public const int TaskIdFieldNumber = 2;
    private int taskId_;
    /// <summary>
    /// 任务配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int TaskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceiveSeasonTaskRewardRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceiveSeasonTaskRewardRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (SeasonId != other.SeasonId) return false;
      if (TaskId != other.TaskId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (TaskId != 0) hash ^= TaskId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (SeasonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(SeasonId);
      }
      if (TaskId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TaskId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (TaskId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TaskId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceiveSeasonTaskRewardRequest other) {
      if (other == null) {
        return;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      if (other.TaskId != 0) {
        TaskId = other.TaskId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 16: {
            TaskId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ReceivedSeasonTaskRewardResponse : pb::IMessage<ReceivedSeasonTaskRewardResponse> {
    private static readonly pb::MessageParser<ReceivedSeasonTaskRewardResponse> _parser = new pb::MessageParser<ReceivedSeasonTaskRewardResponse>(() => new ReceivedSeasonTaskRewardResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceivedSeasonTaskRewardResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedSeasonTaskRewardResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedSeasonTaskRewardResponse(ReceivedSeasonTaskRewardResponse other) : this() {
      seasonId_ = other.seasonId_;
      task_ = other.task_ != null ? other.task_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceivedSeasonTaskRewardResponse Clone() {
      return new ReceivedSeasonTaskRewardResponse(this);
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 1;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "task" field.</summary>
    public const int TaskFieldNumber = 2;
    private global::LD.Protocol.SeasonTaskInfo task_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.SeasonTaskInfo Task {
      get { return task_; }
      set {
        task_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceivedSeasonTaskRewardResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceivedSeasonTaskRewardResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (SeasonId != other.SeasonId) return false;
      if (!object.Equals(Task, other.Task)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (task_ != null) hash ^= Task.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (SeasonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(SeasonId);
      }
      if (task_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Task);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (task_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Task);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceivedSeasonTaskRewardResponse other) {
      if (other == null) {
        return;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      if (other.task_ != null) {
        if (task_ == null) {
          Task = new global::LD.Protocol.SeasonTaskInfo();
        }
        Task.MergeFrom(other.Task);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 18: {
            if (task_ == null) {
              Task = new global::LD.Protocol.SeasonTaskInfo();
            }
            input.ReadMessage(Task);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 赛季商店购买
  /// </summary>
  public sealed partial class SeasonShopBuyRequest : pb::IMessage<SeasonShopBuyRequest> {
    private static readonly pb::MessageParser<SeasonShopBuyRequest> _parser = new pb::MessageParser<SeasonShopBuyRequest>(() => new SeasonShopBuyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonShopBuyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopBuyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopBuyRequest(SeasonShopBuyRequest other) : this() {
      seasonId_ = other.seasonId_;
      shopId_ = other.shopId_;
      count_ = other.count_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopBuyRequest Clone() {
      return new SeasonShopBuyRequest(this);
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 1;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "shop_id" field.</summary>
    public const int ShopIdFieldNumber = 2;
    private int shopId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ShopId {
      get { return shopId_; }
      set {
        shopId_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int CountFieldNumber = 3;
    private int count_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonShopBuyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonShopBuyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (SeasonId != other.SeasonId) return false;
      if (ShopId != other.ShopId) return false;
      if (Count != other.Count) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (ShopId != 0) hash ^= ShopId.GetHashCode();
      if (Count != 0) hash ^= Count.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (SeasonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(SeasonId);
      }
      if (ShopId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ShopId);
      }
      if (Count != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (ShopId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ShopId);
      }
      if (Count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Count);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonShopBuyRequest other) {
      if (other == null) {
        return;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      if (other.ShopId != 0) {
        ShopId = other.ShopId;
      }
      if (other.Count != 0) {
        Count = other.Count;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 16: {
            ShopId = input.ReadInt32();
            break;
          }
          case 24: {
            Count = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class SeasonShopBuyResponse : pb::IMessage<SeasonShopBuyResponse> {
    private static readonly pb::MessageParser<SeasonShopBuyResponse> _parser = new pb::MessageParser<SeasonShopBuyResponse>(() => new SeasonShopBuyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SeasonShopBuyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopBuyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopBuyResponse(SeasonShopBuyResponse other) : this() {
      seasonId_ = other.seasonId_;
      shop_ = other.shop_ != null ? other.shop_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SeasonShopBuyResponse Clone() {
      return new SeasonShopBuyResponse(this);
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 1;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "shop" field.</summary>
    public const int ShopFieldNumber = 2;
    private global::LD.Protocol.SeasonShopInfo shop_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.SeasonShopInfo Shop {
      get { return shop_; }
      set {
        shop_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SeasonShopBuyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SeasonShopBuyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (SeasonId != other.SeasonId) return false;
      if (!object.Equals(Shop, other.Shop)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (shop_ != null) hash ^= Shop.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (SeasonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(SeasonId);
      }
      if (shop_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Shop);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (shop_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Shop);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SeasonShopBuyResponse other) {
      if (other == null) {
        return;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      if (other.shop_ != null) {
        if (shop_ == null) {
          Shop = new global::LD.Protocol.SeasonShopInfo();
        }
        Shop.MergeFrom(other.Shop);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 18: {
            if (shop_ == null) {
              Shop = new global::LD.Protocol.SeasonShopInfo();
            }
            input.ReadMessage(Shop);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///进入关卡
  /// </summary>
  public sealed partial class PlayerSeasonMissionStartRequest : pb::IMessage<PlayerSeasonMissionStartRequest> {
    private static readonly pb::MessageParser<PlayerSeasonMissionStartRequest> _parser = new pb::MessageParser<PlayerSeasonMissionStartRequest>(() => new PlayerSeasonMissionStartRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionStartRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionStartRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionStartRequest(PlayerSeasonMissionStartRequest other) : this() {
      missionId_ = other.missionId_;
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionStartRequest Clone() {
      return new PlayerSeasonMissionStartRequest(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionStartRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionStartRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionStartRequest other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class PlayerSeasonMissionStartResponse : pb::IMessage<PlayerSeasonMissionStartResponse> {
    private static readonly pb::MessageParser<PlayerSeasonMissionStartResponse> _parser = new pb::MessageParser<PlayerSeasonMissionStartResponse>(() => new PlayerSeasonMissionStartResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionStartResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionStartResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionStartResponse(PlayerSeasonMissionStartResponse other) : this() {
      missionId_ = other.missionId_;
      battleId_ = other.battleId_;
      seasonId_ = other.seasonId_;
      property_ = other.property_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionStartResponse Clone() {
      return new PlayerSeasonMissionStartResponse(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "battle_id" field.</summary>
    public const int BattleIdFieldNumber = 2;
    private long battleId_;
    /// <summary>
    ///战斗唯一id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long BattleId {
      get { return battleId_; }
      set {
        battleId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 3;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "property" field.</summary>
    public const int PropertyFieldNumber = 4;
    private static readonly pb::FieldCodec<global::LD.Protocol.PropertyInfo> _repeated_property_codec
        = pb::FieldCodec.ForMessage(34, global::LD.Protocol.PropertyInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.PropertyInfo> property_ = new pbc::RepeatedField<global::LD.Protocol.PropertyInfo>();
    /// <summary>
    /// 所有属性
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.PropertyInfo> Property {
      get { return property_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionStartResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionStartResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (BattleId != other.BattleId) return false;
      if (SeasonId != other.SeasonId) return false;
      if(!property_.Equals(other.property_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (BattleId != 0L) hash ^= BattleId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      hash ^= property_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (BattleId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(BattleId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(SeasonId);
      }
      property_.WriteTo(output, _repeated_property_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (BattleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(BattleId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      size += property_.CalculateSize(_repeated_property_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionStartResponse other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.BattleId != 0L) {
        BattleId = other.BattleId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      property_.Add(other.property_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            BattleId = input.ReadInt64();
            break;
          }
          case 24: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 34: {
            property_.AddEntriesFrom(input, _repeated_property_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///赛季扫荡
  /// </summary>
  public sealed partial class PlayerSeasonMissionSweepRequest : pb::IMessage<PlayerSeasonMissionSweepRequest> {
    private static readonly pb::MessageParser<PlayerSeasonMissionSweepRequest> _parser = new pb::MessageParser<PlayerSeasonMissionSweepRequest>(() => new PlayerSeasonMissionSweepRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionSweepRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionSweepRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionSweepRequest(PlayerSeasonMissionSweepRequest other) : this() {
      missionStartId_ = other.missionStartId_;
      missionEndId_ = other.missionEndId_;
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionSweepRequest Clone() {
      return new PlayerSeasonMissionSweepRequest(this);
    }

    /// <summary>Field number for the "missionStartId" field.</summary>
    public const int MissionStartIdFieldNumber = 1;
    private int missionStartId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionStartId {
      get { return missionStartId_; }
      set {
        missionStartId_ = value;
      }
    }

    /// <summary>Field number for the "missionEndId" field.</summary>
    public const int MissionEndIdFieldNumber = 2;
    private int missionEndId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionEndId {
      get { return missionEndId_; }
      set {
        missionEndId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 3;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionSweepRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionSweepRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionStartId != other.MissionStartId) return false;
      if (MissionEndId != other.MissionEndId) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionStartId != 0) hash ^= MissionStartId.GetHashCode();
      if (MissionEndId != 0) hash ^= MissionEndId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionStartId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionStartId);
      }
      if (MissionEndId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MissionEndId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionStartId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionStartId);
      }
      if (MissionEndId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionEndId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionSweepRequest other) {
      if (other == null) {
        return;
      }
      if (other.MissionStartId != 0) {
        MissionStartId = other.MissionStartId;
      }
      if (other.MissionEndId != 0) {
        MissionEndId = other.MissionEndId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionStartId = input.ReadInt32();
            break;
          }
          case 16: {
            MissionEndId = input.ReadInt32();
            break;
          }
          case 24: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class PlayerSeasonMissionSweepResponse : pb::IMessage<PlayerSeasonMissionSweepResponse> {
    private static readonly pb::MessageParser<PlayerSeasonMissionSweepResponse> _parser = new pb::MessageParser<PlayerSeasonMissionSweepResponse>(() => new PlayerSeasonMissionSweepResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionSweepResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionSweepResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionSweepResponse(PlayerSeasonMissionSweepResponse other) : this() {
      missionStartId_ = other.missionStartId_;
      missionEndId_ = other.missionEndId_;
      seasonId_ = other.seasonId_;
      reward_ = other.reward_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionSweepResponse Clone() {
      return new PlayerSeasonMissionSweepResponse(this);
    }

    /// <summary>Field number for the "missionStartId" field.</summary>
    public const int MissionStartIdFieldNumber = 1;
    private int missionStartId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionStartId {
      get { return missionStartId_; }
      set {
        missionStartId_ = value;
      }
    }

    /// <summary>Field number for the "missionEndId" field.</summary>
    public const int MissionEndIdFieldNumber = 2;
    private int missionEndId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionEndId {
      get { return missionEndId_; }
      set {
        missionEndId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 3;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "reward" field.</summary>
    public const int RewardFieldNumber = 4;
    private static readonly pb::FieldCodec<global::LD.Protocol.RewardResponse> _repeated_reward_codec
        = pb::FieldCodec.ForMessage(34, global::LD.Protocol.RewardResponse.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.RewardResponse> reward_ = new pbc::RepeatedField<global::LD.Protocol.RewardResponse>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.RewardResponse> Reward {
      get { return reward_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionSweepResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionSweepResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionStartId != other.MissionStartId) return false;
      if (MissionEndId != other.MissionEndId) return false;
      if (SeasonId != other.SeasonId) return false;
      if(!reward_.Equals(other.reward_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionStartId != 0) hash ^= MissionStartId.GetHashCode();
      if (MissionEndId != 0) hash ^= MissionEndId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      hash ^= reward_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionStartId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionStartId);
      }
      if (MissionEndId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MissionEndId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(SeasonId);
      }
      reward_.WriteTo(output, _repeated_reward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionStartId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionStartId);
      }
      if (MissionEndId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionEndId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      size += reward_.CalculateSize(_repeated_reward_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionSweepResponse other) {
      if (other == null) {
        return;
      }
      if (other.MissionStartId != 0) {
        MissionStartId = other.MissionStartId;
      }
      if (other.MissionEndId != 0) {
        MissionEndId = other.MissionEndId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      reward_.Add(other.reward_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionStartId = input.ReadInt32();
            break;
          }
          case 16: {
            MissionEndId = input.ReadInt32();
            break;
          }
          case 24: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 34: {
            reward_.AddEntriesFrom(input, _repeated_reward_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///关卡结束
  /// </summary>
  public sealed partial class PlayerSeasonMissionEndRequest : pb::IMessage<PlayerSeasonMissionEndRequest> {
    private static readonly pb::MessageParser<PlayerSeasonMissionEndRequest> _parser = new pb::MessageParser<PlayerSeasonMissionEndRequest>(() => new PlayerSeasonMissionEndRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionEndRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionEndRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionEndRequest(PlayerSeasonMissionEndRequest other) : this() {
      missionId_ = other.missionId_;
      battleTime_ = other.battleTime_;
      battleStatus_ = other.battleStatus_;
      progress_ = other.progress_;
      itemInfo_ = other.itemInfo_.Clone();
      killCount_ = other.killCount_;
      battleId_ = other.battleId_;
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionEndRequest Clone() {
      return new PlayerSeasonMissionEndRequest(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "battleTime" field.</summary>
    public const int BattleTimeFieldNumber = 2;
    private int battleTime_;
    /// <summary>
    ///战斗时长
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattleTime {
      get { return battleTime_; }
      set {
        battleTime_ = value;
      }
    }

    /// <summary>Field number for the "battleStatus" field.</summary>
    public const int BattleStatusFieldNumber = 3;
    private int battleStatus_;
    /// <summary>
    ///战斗结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattleStatus {
      get { return battleStatus_; }
      set {
        battleStatus_ = value;
      }
    }

    /// <summary>Field number for the "progress" field.</summary>
    public const int ProgressFieldNumber = 4;
    private int progress_;
    /// <summary>
    /// 进度 1 -100
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Progress {
      get { return progress_; }
      set {
        progress_ = value;
      }
    }

    /// <summary>Field number for the "itemInfo" field.</summary>
    public const int ItemInfoFieldNumber = 5;
    private static readonly pb::FieldCodec<global::LD.Protocol.ItemInfo> _repeated_itemInfo_codec
        = pb::FieldCodec.ForMessage(42, global::LD.Protocol.ItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ItemInfo> itemInfo_ = new pbc::RepeatedField<global::LD.Protocol.ItemInfo>();
    /// <summary>
    /// 战中掉落
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ItemInfo> ItemInfo {
      get { return itemInfo_; }
    }

    /// <summary>Field number for the "killCount" field.</summary>
    public const int KillCountFieldNumber = 6;
    private int killCount_;
    /// <summary>
    ///杀敌数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int KillCount {
      get { return killCount_; }
      set {
        killCount_ = value;
      }
    }

    /// <summary>Field number for the "battle_id" field.</summary>
    public const int BattleIdFieldNumber = 8;
    private long battleId_;
    /// <summary>
    ///战斗唯一id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long BattleId {
      get { return battleId_; }
      set {
        battleId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 9;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionEndRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionEndRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (BattleTime != other.BattleTime) return false;
      if (BattleStatus != other.BattleStatus) return false;
      if (Progress != other.Progress) return false;
      if(!itemInfo_.Equals(other.itemInfo_)) return false;
      if (KillCount != other.KillCount) return false;
      if (BattleId != other.BattleId) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (BattleTime != 0) hash ^= BattleTime.GetHashCode();
      if (BattleStatus != 0) hash ^= BattleStatus.GetHashCode();
      if (Progress != 0) hash ^= Progress.GetHashCode();
      hash ^= itemInfo_.GetHashCode();
      if (KillCount != 0) hash ^= KillCount.GetHashCode();
      if (BattleId != 0L) hash ^= BattleId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (BattleTime != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(BattleTime);
      }
      if (BattleStatus != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(BattleStatus);
      }
      if (Progress != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Progress);
      }
      itemInfo_.WriteTo(output, _repeated_itemInfo_codec);
      if (KillCount != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(KillCount);
      }
      if (BattleId != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(BattleId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (BattleTime != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattleTime);
      }
      if (BattleStatus != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattleStatus);
      }
      if (Progress != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Progress);
      }
      size += itemInfo_.CalculateSize(_repeated_itemInfo_codec);
      if (KillCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(KillCount);
      }
      if (BattleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(BattleId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionEndRequest other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.BattleTime != 0) {
        BattleTime = other.BattleTime;
      }
      if (other.BattleStatus != 0) {
        BattleStatus = other.BattleStatus;
      }
      if (other.Progress != 0) {
        Progress = other.Progress;
      }
      itemInfo_.Add(other.itemInfo_);
      if (other.KillCount != 0) {
        KillCount = other.KillCount;
      }
      if (other.BattleId != 0L) {
        BattleId = other.BattleId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            BattleTime = input.ReadInt32();
            break;
          }
          case 24: {
            BattleStatus = input.ReadInt32();
            break;
          }
          case 32: {
            Progress = input.ReadInt32();
            break;
          }
          case 42: {
            itemInfo_.AddEntriesFrom(input, _repeated_itemInfo_codec);
            break;
          }
          case 48: {
            KillCount = input.ReadInt32();
            break;
          }
          case 64: {
            BattleId = input.ReadInt64();
            break;
          }
          case 72: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class PlayerSeasonMissionEndResponse : pb::IMessage<PlayerSeasonMissionEndResponse> {
    private static readonly pb::MessageParser<PlayerSeasonMissionEndResponse> _parser = new pb::MessageParser<PlayerSeasonMissionEndResponse>(() => new PlayerSeasonMissionEndResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PlayerSeasonMissionEndResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionEndResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionEndResponse(PlayerSeasonMissionEndResponse other) : this() {
      missionId_ = other.missionId_;
      missionInfo_ = other.missionInfo_ != null ? other.missionInfo_.Clone() : null;
      itemInfo_ = other.itemInfo_.Clone();
      killCount_ = other.killCount_;
      battleId_ = other.battleId_;
      seasonId_ = other.seasonId_;
      ordinaryItemInfo_ = other.ordinaryItemInfo_.Clone();
      bossItemInfo_ = other.bossItemInfo_.Clone();
      battleTime_ = other.battleTime_;
      peakCompetitionItemInfo_ = other.peakCompetitionItemInfo_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PlayerSeasonMissionEndResponse Clone() {
      return new PlayerSeasonMissionEndResponse(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    /// <summary>
    /// 关卡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "missionInfo" field.</summary>
    public const int MissionInfoFieldNumber = 2;
    private global::LD.Protocol.SMissionInfo missionInfo_;
    /// <summary>
    /// 关卡纤细
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.SMissionInfo MissionInfo {
      get { return missionInfo_; }
      set {
        missionInfo_ = value;
      }
    }

    /// <summary>Field number for the "itemInfo" field.</summary>
    public const int ItemInfoFieldNumber = 3;
    private static readonly pb::FieldCodec<global::LD.Protocol.ItemInfo> _repeated_itemInfo_codec
        = pb::FieldCodec.ForMessage(26, global::LD.Protocol.ItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ItemInfo> itemInfo_ = new pbc::RepeatedField<global::LD.Protocol.ItemInfo>();
    /// <summary>
    /// 结算奖励汇总
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ItemInfo> ItemInfo {
      get { return itemInfo_; }
    }

    /// <summary>Field number for the "killCount" field.</summary>
    public const int KillCountFieldNumber = 4;
    private int killCount_;
    /// <summary>
    ///杀敌数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int KillCount {
      get { return killCount_; }
      set {
        killCount_ = value;
      }
    }

    /// <summary>Field number for the "battle_id" field.</summary>
    public const int BattleIdFieldNumber = 5;
    private long battleId_;
    /// <summary>
    ///战斗唯一id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long BattleId {
      get { return battleId_; }
      set {
        battleId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 6;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "ordinaryItemInfo" field.</summary>
    public const int OrdinaryItemInfoFieldNumber = 7;
    private static readonly pb::FieldCodec<global::LD.Protocol.ItemInfo> _repeated_ordinaryItemInfo_codec
        = pb::FieldCodec.ForMessage(58, global::LD.Protocol.ItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ItemInfo> ordinaryItemInfo_ = new pbc::RepeatedField<global::LD.Protocol.ItemInfo>();
    /// <summary>
    /// 普通奖励汇总
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ItemInfo> OrdinaryItemInfo {
      get { return ordinaryItemInfo_; }
    }

    /// <summary>Field number for the "bossItemInfo" field.</summary>
    public const int BossItemInfoFieldNumber = 8;
    private static readonly pb::FieldCodec<global::LD.Protocol.ItemInfo> _repeated_bossItemInfo_codec
        = pb::FieldCodec.ForMessage(66, global::LD.Protocol.ItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ItemInfo> bossItemInfo_ = new pbc::RepeatedField<global::LD.Protocol.ItemInfo>();
    /// <summary>
    /// 转盘奖励汇总
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ItemInfo> BossItemInfo {
      get { return bossItemInfo_; }
    }

    /// <summary>Field number for the "battleTime" field.</summary>
    public const int BattleTimeFieldNumber = 9;
    private int battleTime_;
    /// <summary>
    ///战斗时长
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattleTime {
      get { return battleTime_; }
      set {
        battleTime_ = value;
      }
    }

    /// <summary>Field number for the "peakCompetitionItemInfo" field.</summary>
    public const int PeakCompetitionItemInfoFieldNumber = 10;
    private static readonly pb::FieldCodec<global::LD.Protocol.ItemInfo> _repeated_peakCompetitionItemInfo_codec
        = pb::FieldCodec.ForMessage(82, global::LD.Protocol.ItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ItemInfo> peakCompetitionItemInfo_ = new pbc::RepeatedField<global::LD.Protocol.ItemInfo>();
    /// <summary>
    /// 巅峰赛首通奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ItemInfo> PeakCompetitionItemInfo {
      get { return peakCompetitionItemInfo_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PlayerSeasonMissionEndResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PlayerSeasonMissionEndResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (!object.Equals(MissionInfo, other.MissionInfo)) return false;
      if(!itemInfo_.Equals(other.itemInfo_)) return false;
      if (KillCount != other.KillCount) return false;
      if (BattleId != other.BattleId) return false;
      if (SeasonId != other.SeasonId) return false;
      if(!ordinaryItemInfo_.Equals(other.ordinaryItemInfo_)) return false;
      if(!bossItemInfo_.Equals(other.bossItemInfo_)) return false;
      if (BattleTime != other.BattleTime) return false;
      if(!peakCompetitionItemInfo_.Equals(other.peakCompetitionItemInfo_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (missionInfo_ != null) hash ^= MissionInfo.GetHashCode();
      hash ^= itemInfo_.GetHashCode();
      if (KillCount != 0) hash ^= KillCount.GetHashCode();
      if (BattleId != 0L) hash ^= BattleId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      hash ^= ordinaryItemInfo_.GetHashCode();
      hash ^= bossItemInfo_.GetHashCode();
      if (BattleTime != 0) hash ^= BattleTime.GetHashCode();
      hash ^= peakCompetitionItemInfo_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (missionInfo_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(MissionInfo);
      }
      itemInfo_.WriteTo(output, _repeated_itemInfo_codec);
      if (KillCount != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(KillCount);
      }
      if (BattleId != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(BattleId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(SeasonId);
      }
      ordinaryItemInfo_.WriteTo(output, _repeated_ordinaryItemInfo_codec);
      bossItemInfo_.WriteTo(output, _repeated_bossItemInfo_codec);
      if (BattleTime != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(BattleTime);
      }
      peakCompetitionItemInfo_.WriteTo(output, _repeated_peakCompetitionItemInfo_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (missionInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MissionInfo);
      }
      size += itemInfo_.CalculateSize(_repeated_itemInfo_codec);
      if (KillCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(KillCount);
      }
      if (BattleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(BattleId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      size += ordinaryItemInfo_.CalculateSize(_repeated_ordinaryItemInfo_codec);
      size += bossItemInfo_.CalculateSize(_repeated_bossItemInfo_codec);
      if (BattleTime != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattleTime);
      }
      size += peakCompetitionItemInfo_.CalculateSize(_repeated_peakCompetitionItemInfo_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PlayerSeasonMissionEndResponse other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.missionInfo_ != null) {
        if (missionInfo_ == null) {
          MissionInfo = new global::LD.Protocol.SMissionInfo();
        }
        MissionInfo.MergeFrom(other.MissionInfo);
      }
      itemInfo_.Add(other.itemInfo_);
      if (other.KillCount != 0) {
        KillCount = other.KillCount;
      }
      if (other.BattleId != 0L) {
        BattleId = other.BattleId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      ordinaryItemInfo_.Add(other.ordinaryItemInfo_);
      bossItemInfo_.Add(other.bossItemInfo_);
      if (other.BattleTime != 0) {
        BattleTime = other.BattleTime;
      }
      peakCompetitionItemInfo_.Add(other.peakCompetitionItemInfo_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 18: {
            if (missionInfo_ == null) {
              MissionInfo = new global::LD.Protocol.SMissionInfo();
            }
            input.ReadMessage(MissionInfo);
            break;
          }
          case 26: {
            itemInfo_.AddEntriesFrom(input, _repeated_itemInfo_codec);
            break;
          }
          case 32: {
            KillCount = input.ReadInt32();
            break;
          }
          case 40: {
            BattleId = input.ReadInt64();
            break;
          }
          case 48: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 58: {
            ordinaryItemInfo_.AddEntriesFrom(input, _repeated_ordinaryItemInfo_codec);
            break;
          }
          case 66: {
            bossItemInfo_.AddEntriesFrom(input, _repeated_bossItemInfo_codec);
            break;
          }
          case 72: {
            BattleTime = input.ReadInt32();
            break;
          }
          case 82: {
            peakCompetitionItemInfo_.AddEntriesFrom(input, _repeated_peakCompetitionItemInfo_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 领取首通奖励
  /// </summary>
  public sealed partial class RankSeasonMissionRewardReceiveRequest : pb::IMessage<RankSeasonMissionRewardReceiveRequest> {
    private static readonly pb::MessageParser<RankSeasonMissionRewardReceiveRequest> _parser = new pb::MessageParser<RankSeasonMissionRewardReceiveRequest>(() => new RankSeasonMissionRewardReceiveRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMissionRewardReceiveRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionRewardReceiveRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionRewardReceiveRequest(RankSeasonMissionRewardReceiveRequest other) : this() {
      receiveMaxMissionId_ = other.receiveMaxMissionId_;
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionRewardReceiveRequest Clone() {
      return new RankSeasonMissionRewardReceiveRequest(this);
    }

    /// <summary>Field number for the "receiveMaxMissionId" field.</summary>
    public const int ReceiveMaxMissionIdFieldNumber = 1;
    private int receiveMaxMissionId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ReceiveMaxMissionId {
      get { return receiveMaxMissionId_; }
      set {
        receiveMaxMissionId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMissionRewardReceiveRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMissionRewardReceiveRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReceiveMaxMissionId != other.ReceiveMaxMissionId) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ReceiveMaxMissionId != 0) hash ^= ReceiveMaxMissionId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ReceiveMaxMissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ReceiveMaxMissionId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ReceiveMaxMissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ReceiveMaxMissionId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMissionRewardReceiveRequest other) {
      if (other == null) {
        return;
      }
      if (other.ReceiveMaxMissionId != 0) {
        ReceiveMaxMissionId = other.ReceiveMaxMissionId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ReceiveMaxMissionId = input.ReadInt32();
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RankSeasonMissionRewardReceiveResponse : pb::IMessage<RankSeasonMissionRewardReceiveResponse> {
    private static readonly pb::MessageParser<RankSeasonMissionRewardReceiveResponse> _parser = new pb::MessageParser<RankSeasonMissionRewardReceiveResponse>(() => new RankSeasonMissionRewardReceiveResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMissionRewardReceiveResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionRewardReceiveResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionRewardReceiveResponse(RankSeasonMissionRewardReceiveResponse other) : this() {
      receiveMaxMissionId_ = other.receiveMaxMissionId_;
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionRewardReceiveResponse Clone() {
      return new RankSeasonMissionRewardReceiveResponse(this);
    }

    /// <summary>Field number for the "receiveMaxMissionId" field.</summary>
    public const int ReceiveMaxMissionIdFieldNumber = 1;
    private int receiveMaxMissionId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ReceiveMaxMissionId {
      get { return receiveMaxMissionId_; }
      set {
        receiveMaxMissionId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMissionRewardReceiveResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMissionRewardReceiveResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReceiveMaxMissionId != other.ReceiveMaxMissionId) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ReceiveMaxMissionId != 0) hash ^= ReceiveMaxMissionId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ReceiveMaxMissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ReceiveMaxMissionId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ReceiveMaxMissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ReceiveMaxMissionId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMissionRewardReceiveResponse other) {
      if (other == null) {
        return;
      }
      if (other.ReceiveMaxMissionId != 0) {
        ReceiveMaxMissionId = other.ReceiveMaxMissionId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ReceiveMaxMissionId = input.ReadInt32();
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 查看首通奖励列表
  /// </summary>
  public sealed partial class RankSeasonMissionFirstPassRewardRequest : pb::IMessage<RankSeasonMissionFirstPassRewardRequest> {
    private static readonly pb::MessageParser<RankSeasonMissionFirstPassRewardRequest> _parser = new pb::MessageParser<RankSeasonMissionFirstPassRewardRequest>(() => new RankSeasonMissionFirstPassRewardRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMissionFirstPassRewardRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionFirstPassRewardRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionFirstPassRewardRequest(RankSeasonMissionFirstPassRewardRequest other) : this() {
      missionIds_ = other.missionIds_.Clone();
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionFirstPassRewardRequest Clone() {
      return new RankSeasonMissionFirstPassRewardRequest(this);
    }

    /// <summary>Field number for the "missionIds" field.</summary>
    public const int MissionIdsFieldNumber = 1;
    private static readonly pb::FieldCodec<int> _repeated_missionIds_codec
        = pb::FieldCodec.ForInt32(10);
    private readonly pbc::RepeatedField<int> missionIds_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> MissionIds {
      get { return missionIds_; }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMissionFirstPassRewardRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMissionFirstPassRewardRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!missionIds_.Equals(other.missionIds_)) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= missionIds_.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      missionIds_.WriteTo(output, _repeated_missionIds_codec);
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += missionIds_.CalculateSize(_repeated_missionIds_codec);
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMissionFirstPassRewardRequest other) {
      if (other == null) {
        return;
      }
      missionIds_.Add(other.missionIds_);
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            missionIds_.AddEntriesFrom(input, _repeated_missionIds_codec);
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RankSeasonMissionFirstPassRewardResponse : pb::IMessage<RankSeasonMissionFirstPassRewardResponse> {
    private static readonly pb::MessageParser<RankSeasonMissionFirstPassRewardResponse> _parser = new pb::MessageParser<RankSeasonMissionFirstPassRewardResponse>(() => new RankSeasonMissionFirstPassRewardResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMissionFirstPassRewardResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionFirstPassRewardResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionFirstPassRewardResponse(RankSeasonMissionFirstPassRewardResponse other) : this() {
      records_ = other.records_.Clone();
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionFirstPassRewardResponse Clone() {
      return new RankSeasonMissionFirstPassRewardResponse(this);
    }

    /// <summary>Field number for the "records" field.</summary>
    public const int RecordsFieldNumber = 1;
    private static readonly pbc::MapField<int, global::LD.Protocol.RankRecord>.Codec _map_records_codec
        = new pbc::MapField<int, global::LD.Protocol.RankRecord>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::LD.Protocol.RankRecord.Parser), 10);
    private readonly pbc::MapField<int, global::LD.Protocol.RankRecord> records_ = new pbc::MapField<int, global::LD.Protocol.RankRecord>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, global::LD.Protocol.RankRecord> Records {
      get { return records_; }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMissionFirstPassRewardResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMissionFirstPassRewardResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Records.Equals(other.Records)) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= Records.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      records_.WriteTo(output, _map_records_codec);
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += records_.CalculateSize(_map_records_codec);
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMissionFirstPassRewardResponse other) {
      if (other == null) {
        return;
      }
      records_.Add(other.records_);
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            records_.AddEntriesFrom(input, _map_records_codec);
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 查看top5
  /// </summary>
  public sealed partial class RankSeasonMissionTopRoleRequest : pb::IMessage<RankSeasonMissionTopRoleRequest> {
    private static readonly pb::MessageParser<RankSeasonMissionTopRoleRequest> _parser = new pb::MessageParser<RankSeasonMissionTopRoleRequest>(() => new RankSeasonMissionTopRoleRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMissionTopRoleRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionTopRoleRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionTopRoleRequest(RankSeasonMissionTopRoleRequest other) : this() {
      missionId_ = other.missionId_;
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionTopRoleRequest Clone() {
      return new RankSeasonMissionTopRoleRequest(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMissionTopRoleRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMissionTopRoleRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMissionTopRoleRequest other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RankSeasonMissionTopRoleResponse : pb::IMessage<RankSeasonMissionTopRoleResponse> {
    private static readonly pb::MessageParser<RankSeasonMissionTopRoleResponse> _parser = new pb::MessageParser<RankSeasonMissionTopRoleResponse>(() => new RankSeasonMissionTopRoleResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMissionTopRoleResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionTopRoleResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionTopRoleResponse(RankSeasonMissionTopRoleResponse other) : this() {
      missionId_ = other.missionId_;
      seasonId_ = other.seasonId_;
      records_ = other.records_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMissionTopRoleResponse Clone() {
      return new RankSeasonMissionTopRoleResponse(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    /// <summary>Field number for the "records" field.</summary>
    public const int RecordsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::LD.Protocol.RankRecord> _repeated_records_codec
        = pb::FieldCodec.ForMessage(26, global::LD.Protocol.RankRecord.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.RankRecord> records_ = new pbc::RepeatedField<global::LD.Protocol.RankRecord>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.RankRecord> Records {
      get { return records_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMissionTopRoleResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMissionTopRoleResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (SeasonId != other.SeasonId) return false;
      if(!records_.Equals(other.records_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      hash ^= records_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      records_.WriteTo(output, _repeated_records_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      size += records_.CalculateSize(_repeated_records_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMissionTopRoleResponse other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      records_.Add(other.records_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
          case 26: {
            records_.AddEntriesFrom(input, _repeated_records_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class RankSeasonMaxMission : pb::IMessage<RankSeasonMaxMission> {
    private static readonly pb::MessageParser<RankSeasonMaxMission> _parser = new pb::MessageParser<RankSeasonMaxMission>(() => new RankSeasonMaxMission());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMaxMission> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[29]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMission() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMission(RankSeasonMaxMission other) : this() {
      missionId_ = other.missionId_;
      seasonId_ = other.seasonId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMission Clone() {
      return new RankSeasonMaxMission(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "season_id" field.</summary>
    public const int SeasonIdFieldNumber = 2;
    private int seasonId_;
    /// <summary>
    ///赛季id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SeasonId {
      get { return seasonId_; }
      set {
        seasonId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMaxMission);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMaxMission other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (SeasonId != other.SeasonId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (SeasonId != 0) hash ^= SeasonId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (SeasonId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(SeasonId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (SeasonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SeasonId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMaxMission other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.SeasonId != 0) {
        SeasonId = other.SeasonId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            SeasonId = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RankSeasonMaxMissionRequest : pb::IMessage<RankSeasonMaxMissionRequest> {
    private static readonly pb::MessageParser<RankSeasonMaxMissionRequest> _parser = new pb::MessageParser<RankSeasonMaxMissionRequest>(() => new RankSeasonMaxMissionRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMaxMissionRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[30]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMissionRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMissionRequest(RankSeasonMaxMissionRequest other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMissionRequest Clone() {
      return new RankSeasonMaxMissionRequest(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMaxMissionRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMaxMissionRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMaxMissionRequest other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  public sealed partial class RankSeasonMaxMissionResponse : pb::IMessage<RankSeasonMaxMissionResponse> {
    private static readonly pb::MessageParser<RankSeasonMaxMissionResponse> _parser = new pb::MessageParser<RankSeasonMaxMissionResponse>(() => new RankSeasonMaxMissionResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RankSeasonMaxMissionResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.SeasonMissonReflection.Descriptor.MessageTypes[31]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMissionResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMissionResponse(RankSeasonMaxMissionResponse other) : this() {
      info_ = other.info_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RankSeasonMaxMissionResponse Clone() {
      return new RankSeasonMaxMissionResponse(this);
    }

    /// <summary>Field number for the "info" field.</summary>
    public const int InfoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.RankSeasonMaxMission> _repeated_info_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.RankSeasonMaxMission.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.RankSeasonMaxMission> info_ = new pbc::RepeatedField<global::LD.Protocol.RankSeasonMaxMission>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.RankSeasonMaxMission> Info {
      get { return info_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RankSeasonMaxMissionResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RankSeasonMaxMissionResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!info_.Equals(other.info_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= info_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      info_.WriteTo(output, _repeated_info_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += info_.CalculateSize(_repeated_info_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RankSeasonMaxMissionResponse other) {
      if (other == null) {
        return;
      }
      info_.Add(other.info_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            info_.AddEntriesFrom(input, _repeated_info_codec);
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
