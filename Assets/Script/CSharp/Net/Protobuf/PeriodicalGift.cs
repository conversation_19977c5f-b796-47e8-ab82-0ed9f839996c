// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/PeriodicalGift.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/PeriodicalGift.proto</summary>
  public static partial class PeriodicalGiftReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/PeriodicalGift.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PeriodicalGiftReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch1DMlNQcm90by9QZXJpb2RpY2FsR2lmdC5wcm90byLxAQohUGVyaW9kaWNh",
            "bEdpZnRJbmZvQWxsU3luY1Jlc3BvbnNlElYKEnBlcmlvZGljYWxHaWZ0SW5m",
            "bxgBIAMoCzI6LlBlcmlvZGljYWxHaWZ0SW5mb0FsbFN5bmNSZXNwb25zZS5Q",
            "ZXJpb2RpY2FsR2lmdEluZm9FbnRyeRIQCghwcm9ncmVzcxgCIAEoBRIQCghy",
            "ZWNlaXZlZBgDIAMoBRIVCg1wcm9ncmVzc190aW1lGAQgASgDGjkKF1Blcmlv",
            "ZGljYWxHaWZ0SW5mb0VudHJ5EgsKA2tleRgBIAEoBRINCgV2YWx1ZRgCIAEo",
            "BToCOAEiKwocUmVjZWl2ZVByb2dyZXNzUmV3YXJkUmVxdWVzdBILCgNkYXkY",
            "ASABKAUiLAodUmVjZWl2ZVByb2dyZXNzUmV3YXJkUmVzcG9uc2USCwoDZGF5",
            "GAEgASgFIiQKFlJlY2VpdmVGcmVlR2lmdFJlcXVlc3QSCgoCaWQYASABKAUi",
            "JQoXUmVjZWl2ZUZyZWVHaWZ0UmVzcG9uc2USCgoCaWQYASABKAVCIwoTY29t",
            "LmdvbGRlbi5wcm90b2NvbKoCC0xELlByb3RvY29sYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PeriodicalGiftInfoAllSyncResponse), global::LD.Protocol.PeriodicalGiftInfoAllSyncResponse.Parser, new[]{ "PeriodicalGiftInfo", "Progress", "Received", "ProgressTime" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceiveProgressRewardRequest), global::LD.Protocol.ReceiveProgressRewardRequest.Parser, new[]{ "Day" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceiveProgressRewardResponse), global::LD.Protocol.ReceiveProgressRewardResponse.Parser, new[]{ "Day" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceiveFreeGiftRequest), global::LD.Protocol.ReceiveFreeGiftRequest.Parser, new[]{ "Id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ReceiveFreeGiftResponse), global::LD.Protocol.ReceiveFreeGiftResponse.Parser, new[]{ "Id" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class PeriodicalGiftInfoAllSyncResponse : pb::IMessage<PeriodicalGiftInfoAllSyncResponse> {
    private static readonly pb::MessageParser<PeriodicalGiftInfoAllSyncResponse> _parser = new pb::MessageParser<PeriodicalGiftInfoAllSyncResponse>(() => new PeriodicalGiftInfoAllSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PeriodicalGiftInfoAllSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.PeriodicalGiftReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PeriodicalGiftInfoAllSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PeriodicalGiftInfoAllSyncResponse(PeriodicalGiftInfoAllSyncResponse other) : this() {
      periodicalGiftInfo_ = other.periodicalGiftInfo_.Clone();
      progress_ = other.progress_;
      received_ = other.received_.Clone();
      progressTime_ = other.progressTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PeriodicalGiftInfoAllSyncResponse Clone() {
      return new PeriodicalGiftInfoAllSyncResponse(this);
    }

    /// <summary>Field number for the "periodicalGiftInfo" field.</summary>
    public const int PeriodicalGiftInfoFieldNumber = 1;
    private static readonly pbc::MapField<int, int>.Codec _map_periodicalGiftInfo_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 10);
    private readonly pbc::MapField<int, int> periodicalGiftInfo_ = new pbc::MapField<int, int>();
    /// <summary>
    ///礼包id 购买次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::MapField<int, int> PeriodicalGiftInfo {
      get { return periodicalGiftInfo_; }
    }

    /// <summary>Field number for the "progress" field.</summary>
    public const int ProgressFieldNumber = 2;
    private int progress_;
    /// <summary>
    /// 累充天数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Progress {
      get { return progress_; }
      set {
        progress_ = value;
      }
    }

    /// <summary>Field number for the "received" field.</summary>
    public const int ReceivedFieldNumber = 3;
    private static readonly pb::FieldCodec<int> _repeated_received_codec
        = pb::FieldCodec.ForInt32(26);
    private readonly pbc::RepeatedField<int> received_ = new pbc::RepeatedField<int>();
    /// <summary>
    ///领取奖励的id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> Received {
      get { return received_; }
    }

    /// <summary>Field number for the "progress_time" field.</summary>
    public const int ProgressTimeFieldNumber = 4;
    private long progressTime_;
    /// <summary>
    ///进度开启时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long ProgressTime {
      get { return progressTime_; }
      set {
        progressTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PeriodicalGiftInfoAllSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PeriodicalGiftInfoAllSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!PeriodicalGiftInfo.Equals(other.PeriodicalGiftInfo)) return false;
      if (Progress != other.Progress) return false;
      if(!received_.Equals(other.received_)) return false;
      if (ProgressTime != other.ProgressTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= PeriodicalGiftInfo.GetHashCode();
      if (Progress != 0) hash ^= Progress.GetHashCode();
      hash ^= received_.GetHashCode();
      if (ProgressTime != 0L) hash ^= ProgressTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      periodicalGiftInfo_.WriteTo(output, _map_periodicalGiftInfo_codec);
      if (Progress != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Progress);
      }
      received_.WriteTo(output, _repeated_received_codec);
      if (ProgressTime != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(ProgressTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += periodicalGiftInfo_.CalculateSize(_map_periodicalGiftInfo_codec);
      if (Progress != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Progress);
      }
      size += received_.CalculateSize(_repeated_received_codec);
      if (ProgressTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ProgressTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PeriodicalGiftInfoAllSyncResponse other) {
      if (other == null) {
        return;
      }
      periodicalGiftInfo_.Add(other.periodicalGiftInfo_);
      if (other.Progress != 0) {
        Progress = other.Progress;
      }
      received_.Add(other.received_);
      if (other.ProgressTime != 0L) {
        ProgressTime = other.ProgressTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            periodicalGiftInfo_.AddEntriesFrom(input, _map_periodicalGiftInfo_codec);
            break;
          }
          case 16: {
            Progress = input.ReadInt32();
            break;
          }
          case 26:
          case 24: {
            received_.AddEntriesFrom(input, _repeated_received_codec);
            break;
          }
          case 32: {
            ProgressTime = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///领取 累充 奖励
  /// </summary>
  public sealed partial class ReceiveProgressRewardRequest : pb::IMessage<ReceiveProgressRewardRequest> {
    private static readonly pb::MessageParser<ReceiveProgressRewardRequest> _parser = new pb::MessageParser<ReceiveProgressRewardRequest>(() => new ReceiveProgressRewardRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceiveProgressRewardRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.PeriodicalGiftReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveProgressRewardRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveProgressRewardRequest(ReceiveProgressRewardRequest other) : this() {
      day_ = other.day_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveProgressRewardRequest Clone() {
      return new ReceiveProgressRewardRequest(this);
    }

    /// <summary>Field number for the "day" field.</summary>
    public const int DayFieldNumber = 1;
    private int day_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Day {
      get { return day_; }
      set {
        day_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceiveProgressRewardRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceiveProgressRewardRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Day != other.Day) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Day != 0) hash ^= Day.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Day != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Day);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Day != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Day);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceiveProgressRewardRequest other) {
      if (other == null) {
        return;
      }
      if (other.Day != 0) {
        Day = other.Day;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Day = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ReceiveProgressRewardResponse : pb::IMessage<ReceiveProgressRewardResponse> {
    private static readonly pb::MessageParser<ReceiveProgressRewardResponse> _parser = new pb::MessageParser<ReceiveProgressRewardResponse>(() => new ReceiveProgressRewardResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceiveProgressRewardResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.PeriodicalGiftReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveProgressRewardResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveProgressRewardResponse(ReceiveProgressRewardResponse other) : this() {
      day_ = other.day_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveProgressRewardResponse Clone() {
      return new ReceiveProgressRewardResponse(this);
    }

    /// <summary>Field number for the "day" field.</summary>
    public const int DayFieldNumber = 1;
    private int day_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Day {
      get { return day_; }
      set {
        day_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceiveProgressRewardResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceiveProgressRewardResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Day != other.Day) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Day != 0) hash ^= Day.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Day != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Day);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Day != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Day);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceiveProgressRewardResponse other) {
      if (other == null) {
        return;
      }
      if (other.Day != 0) {
        Day = other.Day;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Day = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  ///领取免费礼包
  /// </summary>
  public sealed partial class ReceiveFreeGiftRequest : pb::IMessage<ReceiveFreeGiftRequest> {
    private static readonly pb::MessageParser<ReceiveFreeGiftRequest> _parser = new pb::MessageParser<ReceiveFreeGiftRequest>(() => new ReceiveFreeGiftRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceiveFreeGiftRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.PeriodicalGiftReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveFreeGiftRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveFreeGiftRequest(ReceiveFreeGiftRequest other) : this() {
      id_ = other.id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveFreeGiftRequest Clone() {
      return new ReceiveFreeGiftRequest(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceiveFreeGiftRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceiveFreeGiftRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceiveFreeGiftRequest other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ReceiveFreeGiftResponse : pb::IMessage<ReceiveFreeGiftResponse> {
    private static readonly pb::MessageParser<ReceiveFreeGiftResponse> _parser = new pb::MessageParser<ReceiveFreeGiftResponse>(() => new ReceiveFreeGiftResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ReceiveFreeGiftResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.PeriodicalGiftReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveFreeGiftResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveFreeGiftResponse(ReceiveFreeGiftResponse other) : this() {
      id_ = other.id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ReceiveFreeGiftResponse Clone() {
      return new ReceiveFreeGiftResponse(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ReceiveFreeGiftResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ReceiveFreeGiftResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ReceiveFreeGiftResponse other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
