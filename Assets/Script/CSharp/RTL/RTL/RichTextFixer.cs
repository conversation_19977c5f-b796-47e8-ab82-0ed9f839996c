using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RTLTMPro
{
    public static class RichTextFixer
    {
        public enum TagType
        {
            None,
            Opening,
            Closing,
            SelfContained,
        }
        public static int TAG_ID = 0;
        public struct Tag
        {
            public int TagID;
            public int Start;
            public int End;
            public int HashCode;
            public TagType Type;
            public FastStringBuilder Text;
            public Tag(int start, int end, TagType type, int hashCode)
            {
                Type = type;
                Start = start;
                End = end;
                HashCode = hashCode;
                TagID = TAG_ID++;
                Text = null;
            }
        }

        /// <summary>
        ///     Fixes rich text tags in input string and returns the result.
        /// </summary>
        public static void Fix(FastStringBuilder text)
        {
            for (int i = 0; i < text.Length; i++)
            {
                FindTag(text, i, out Tag tag);

                // If we couldn't find a tag, end the process
                if (tag.Type == TagType.None)
                {
                    break;
                }

                text.Reverse(tag.Start, tag.End - tag.Start + 1);

                i = tag.End;
            }
        }
        
        // <summary>
        ///     Fixes rich text tags in input string and returns the result.
        /// </summary>
        public static void FixWithExplicitDirectionalIsolates(FastStringBuilder text)
        { 
            for (int i = 0; i < text.Length; i++)
            {
                FindTag(text, i, out Tag tag);
                // If we couldn't find a tag, end the process
                if (tag.Type == TagType.None)
                {
                    break;
                }
                text.Reverse(tag.Start, tag.End - tag.Start + 1);
                text.Insert(tag.Start, 0x2066);
                text.Insert(tag.End+2, 0x2069);

                i = tag.End + 2;
            }
        }

        private static int TextLengthWithoutTag = 0;
        public static List<Tag> ListTagTexts = new List<Tag>(); // 富文本标签列表（不包含closing的标签）
        public static Dictionary<int, Tag> MapTags = new Dictionary<int, Tag>(); //与ListTagTexts中opening对应的closing标签
        public static Dictionary<int, int> MapTextLengths = new Dictionary<int, int>(); //标签内实际显示用文本长度
        private static List<Tag> _lastOpeningTagList = new List<Tag>();
        //是否有阿语特殊标记
        public static readonly HashSet<uint> VisualIgnoreSet = new HashSet<uint>(Enum.GetValues(typeof(SpecialCharacters)).Cast<uint>());
        public static void FixWithExplicitDirectionalIsolatesEx(FastStringBuilder text)
        {
            Bidi.TextTagIndexList.Clear();
            Bidi.TextTagFlagList.Clear();
            Bidi.TextLTRIndexList.Clear();
            TAG_ID = 0;
            ListTagTexts.Clear();
            MapTags.Clear();
            _lastOpeningTagList.Clear();
            MapTextLengths.Clear();
            // int removedLength = 0;
            for (int i = 0; i < text.Length; i++)
            {
                FindTag(text, i, out Tag tag);
                // If we couldn't find a tag, end the process
                if (tag.Type == TagType.None)
                {
                    break;
                }

                int tagStart = tag.Start;
                int tagEnd = tag.End;
                int length = tagEnd - tagStart + 1;
                text.Reverse(tagStart, length);
                tag.Text = new FastStringBuilder(length);
                tag.Text.Insert(0, text, tagStart, length);
                if (tag.Type == TagType.Closing && _lastOpeningTagList.Count != 0)
                {
                    Tag lastOpeningTag = _lastOpeningTagList.Last();
                    MapTags[lastOpeningTag.TagID] = tag;
                    int textLenght = tag.Start - lastOpeningTag.Start;
                    int startPos = tag.Start - textLenght - 1;
                    //长度减掉会被忽略的特殊字符
                    for (int j = tag.Start - 1; j > startPos; j--)
                    {
                        if (VisualIgnoreSet.Contains((uint)text.Get(j)))
                        {
                            textLenght --;
                        }
                    }
                    MapTextLengths[lastOpeningTag.TagID] = textLenght;
                    _lastOpeningTagList.Remove(lastOpeningTag);
                }
                // stringBuilder.Insert(0, 0x2066);
                // stringBuilder.Append(0x2069);
                text.Remove(tagStart, length);
                // removedLength += length;
                if (tag.Type != TagType.Closing)
                {
                    int tmpTagStart = tagStart;
                    //如果标签后面是会被忽略的特色字符 那么标签起始位置要往后移
                    for (int j = tagStart; j < text.Length -1; j++)
                    {
                        if (VisualIgnoreSet.Contains((char)text.Get(j)))
                        {
                            tmpTagStart++;
                            tag.Start++;
                        }
                        else
                        {
                            break;
                        }
                    }
                    ListTagTexts.Add(tag);
                    _lastOpeningTagList.Add(tag);
                    MapTextLengths[tag.TagID] = 0;
                    Bidi.TextTagIndexList.Add(tmpTagStart);
                    Bidi.TextTagFlagList.Add(false);
                    Bidi.TextLTRIndexList.Add(tmpTagStart);
                }
                i = tagStart - 1;
            }

            TextLengthWithoutTag = text.Length;
        }
        public static List<int> InsertIdxList = new List<int>();
        public static List<int> InsetLengthList = new List<int>();
        public static void InsertTags(FastStringBuilder text)
        {
            InsertIdxList.Clear();
            InsetLengthList.Clear();
            for (int j = ListTagTexts.Count - 1; j >= 0; j--)
            {
                Tag opengingTag = ListTagTexts[j];
                bool isRTL = Bidi.TextTagIndexList[j] == Bidi.TextLTRIndexList[j];
                FastStringBuilder tagString = opengingTag.Text;
                int tagIndex = Math.Min(Bidi.TextLTRIndexList[j], Bidi.TextTagIndexList[j] + MapTextLengths[opengingTag.TagID]);
                if (tagIndex >= TextLengthWithoutTag && MapTextLengths[opengingTag.TagID] == 0) //说明一开始就是在句尾的不包含文本独立标签 放到句首
                {
                    text.Insert(0, tagString);
                    InsertIdxList.Add(0);
                    InsetLengthList.Add(tagString.Length);
                    if (MapTags.TryGetValue(opengingTag.TagID, out Tag closingTag))
                    {
                        tagString = closingTag.Text;
                        text.Insert(0, tagString);
                        InsertIdxList.Add(0);
                        InsetLengthList.Add(tagString.Length);
                    }
                    continue;
                }
                else if (tagIndex - MapTextLengths[opengingTag.TagID] < (isRTL ? -1 : 0))
                {
                    tagIndex = MapTextLengths[opengingTag.TagID];
                }
                if (isRTL)
                {
                    tagIndex++;
                }
                int insertAddLength = 0;
                for (int i = 0; i < InsertIdxList.Count; i++)
                {
                    if (InsertIdxList[i] < tagIndex)
                    {
                        insertAddLength += InsetLengthList[i];
                    }
                }
                text.Insert(tagIndex + insertAddLength, tagString);
                int openingTagLength = tagString.Length;
                int openingTagIndex = tagIndex;
                //处理对应的 closingTag
                if (MapTags.TryGetValue(opengingTag.TagID, out Tag _closingTag))
                {
                    tagString = _closingTag.Text;
                    if (MapTextLengths[opengingTag.TagID] != 0)
                    {
                        tagIndex -= MapTextLengths[opengingTag.TagID];
                        insertAddLength = 0;
                        for (int i = 0; i < InsertIdxList.Count; i++)
                        {
                            if (InsertIdxList[i] <= tagIndex) //closing tag 和 opening相反
                            {
                                insertAddLength += InsetLengthList[i];
                            }
                        }
                    }
                    tagIndex = Math.Max(-insertAddLength, tagIndex);
                    text.Insert(tagIndex + insertAddLength, tagString);
                    InsertIdxList.Add(tagIndex);
                    InsetLengthList.Add(tagString.Length);
                }
                //再加入openingtag的位置 防止对closingtag的影响
                InsertIdxList.Add(openingTagIndex);
                InsetLengthList.Add(openingTagLength);

            }
        }

        public static List<int> TagStartIndexList = new List<int>();
        public static List<int> TagEndIndexList = new List<int>();
        public static void GetTagList(FastStringBuilder text)
        {
            for (int i = 0; i < text.Length; i++)
            {
                FindTag(text, i, out Tag tag);

                // If we couldn't find a tag, end the process
                if (tag.Type == TagType.None)
                {
                    break;
                }
                TagStartIndexList.Add(tag.Start);
                TagEndIndexList.Add(tag.End);
                i = tag.End - 1;
            }
        }

        public static int GetCurrentPreIndex(FastStringBuilder text, int index)
        {
            index -= 1;
            for (int i = TagEndIndexList.Count - 1; i >= 0; i--)
            {
                if (TagEndIndexList[i] == index)
                {
                    index = TagStartIndexList[i] - 1;
                }
            }
            return index;
        }

        public static int GetCurrentNextIndex(FastStringBuilder text, int index)
        {
            index += 1;
            for (int i = 0; i < TagStartIndexList.Count - 1; i++)
            {
                if (TagStartIndexList[i]== index)
                {
                    index = TagEndIndexList[i] + 1;
                }
            }
            return index;
        }
        
        public static void FindTag(
            FastStringBuilder str,
            int start,
            out Tag tag)
        {
            bool isHtmlTag = false;
            bool isSpeicalChar = false;
            for (int i = start; i < str.Length;)
            {
                isHtmlTag = str.Get(i) == '<';
                if (!isHtmlTag)
                {
                    isSpeicalChar = str.Get(i) == '&';
                    if (!isSpeicalChar)
                    {
                        i++;
                        continue;
                    }
                }

                bool calculateHashCode = true;
                tag.HashCode = 0;
                for (int j = i + 1; j < str.Length; j++)
                {
                    int jChar = str.Get(j);

                    if (calculateHashCode)
                    {
                        if (Char32Utils.IsLetter(jChar))
                        {
                            unchecked
                            {
                                if (tag.HashCode == 0)
                                {
                                    tag.HashCode = jChar.GetHashCode();
                                }
                                else
                                {
                                    tag.HashCode = (tag.HashCode * 397) ^ jChar.GetHashCode();
                                }
                            }
                        }
                        else if (tag.HashCode != 0)
                        {
                            // We have computed the hash code. Now we reached a non letter character. We need to stop
                            calculateHashCode = false;
                        }
                    }

                    // Rich text tag cannot contain < or start with space
                    if ((j == i + 1 && jChar == ' ') || jChar == '<')
                    {
                        break;
                    }

                    if (isHtmlTag && jChar == '>')
                    {
                        // Check if the tag is closing, opening or self contained

                        tag.Start = i;
                        tag.End = j;

                        if (str.Get(j - 1) == '/')
                        {
                            // This is self contained.
                            tag.Type = TagType.SelfContained;
                        }
                        else if (str.Get(i + 1) == '/')
                        {
                            // This is closing
                            tag.Type = TagType.Closing;
                        }
                        else
                        {
                            tag.Type = TagType.Opening;
                        }

                        tag.TagID = TAG_ID++;
                        tag.Text = null;
                        return;
                    }

                    if (isSpeicalChar && jChar == ';')
                    {
                        tag.Start = i;
                        tag.End = j;
                        tag.Type = TagType.SelfContained;
                        tag.TagID = TAG_ID++;
                        tag.Text = null;
                        return;
                    }
                }

                i++;
            }

            tag.Start = 0;
            tag.End = 0;
            tag.Type = TagType.None;
            tag.HashCode = 0;
            tag.TagID = 0;
            tag.Text = null;
        }
    }
}