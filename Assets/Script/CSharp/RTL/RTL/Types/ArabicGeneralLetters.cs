namespace RTLTMPro
{
    /// <summary>
    /// Letters contextual forms - Isolated
    /// </summary>
    public enum ArabicGeneralLetters
    {
        Hamza = 0x0621, // ?
        AlefMaddaAbove = 0x0622, // ?
        AlefHamzaAbove = 0x0623, // ?
        WawHamzaAbove = 0x0624,  // ?
        AlefHamzaBelow = 0x0625, // ?
        YehHamzaAbove = 0x0626,
        Alef = 0x0627, // ?
        Beh = 0x0628, // ?
        TehMarbuta = 0x0629, // ?
        Teh = 0x062A, // ?
        Theh = 0x062B, // ?
        Jeem = 0x062C, // ?
        Hah = 0x062D, // ?
        Khah = 0x062E, // ?
        Dal = 0x062F, // ?
        Thal = 0x0630, // ?
        Reh = 0x0631, // ?
        Zain = 0x0632, // ?
        Seen = 0x0633, // ?
        Sheen = 0x0634, // ?
        Sad = 0x0635, // ?
        Dad = 0x0636, // ?
        Tah = 0x0637, // ?
        Zah = 0x0638, // ?
        Ain = 0x0639, // ?
        Ghain = 0x063A, // ?
        Feh = 0x0641, // ?
        Qaf = 0x0642, // ?
        Kaf = 0x0643, // ?
        Lam = 0x0644, // ?
        Meem = 0x0645, // ?
        Noon = 0x0646, // ?
        Heh = 0x0647, // ?
        Waw = 0x0648, // ?
        AlefMaksura = 0x649, // ?
        Yeh = 0x064A, // ?
        HehGoal = 0x06C1, //?
        
        FarsiYeh = 0x6CC,
        Peh = 0x067E, // persian ?
        TCheh = 0x0686, // persian ?
        Jeh = 0x0698, // persian ?
        Keheh = 0x06A9, // persian ?
        Gaf = 0x06AF, // persian ?

        Tatweel = 0x640, // ?
    }
}