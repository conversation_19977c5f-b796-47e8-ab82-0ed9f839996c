using System.Collections.Generic;

namespace LD
{
    public class LDNetMechaAssistDTO
    {
        public Dictionary<int, LDNetMechaAssistSlotInfo> m_MechaAssitDict = new();

        public void SyncMechaAssistSlotInfo(int slotId, int mechaId, int level)
        {
            if (m_MechaAssitDict.TryGetValue(slotId, out LDNetMechaAssistSlotInfo value))
            {
                value.UpdateAssistSlotInfo(slotId, mechaId, level);
            }
            else
            {
                m_MechaAssitDict[slotId] = new LDNetMechaAssistSlotInfo(slotId, mechaId, level);
            }
        }

        public void SyncMechaAssistSlotMecha(int slotId, int mechaId)
        {
            if (m_MechaAssitDict.TryGetValue(slotId, out LDNetMechaAssistSlotInfo value))
            {
                value.UpdateAssistSlotMechaInfo(mechaId);
            }
        }

        public bool TryGetMechaAssistSlotInfo(int slotId, out LDNetMechaAssistSlotInfo value)
        {
            return m_MechaAssitDict.TryGetValue(slotId, out value);
        }
    }

    public class LDNetMechaAssistSlotInfo
    {
        public int SlotId;
        public int MechaId;
        public int Level;

        public LDNetMechaAssistSlotInfo(int slotId, int mechaId, int level)
        {
            UpdateAssistSlotInfo(slotId, mechaId, level);
        }

        public void UpdateAssistSlotInfo(int slotId, int mechaId, int level)
        {
            SlotId = slotId;
            MechaId = mechaId;
            Level = level;
        }

        public void UpdateAssistSlotMechaInfo(int mechaId)
        {
            MechaId = mechaId;
        }
    }

    public class LDNetRoleMechaAssistSlotInfo
    {
        public int SlotId;
        public int MechaId;
        public int Level;
        public int SkinId;
        public bool HideSkin ;

        public LDNetRoleMechaAssistSlotInfo(int slotId, int mechaId, int level, int skinid, bool hideSkin)
        {
            UpdateAssistSlotInfo(slotId, mechaId, level, skinid, hideSkin);
        }

        public void UpdateAssistSlotInfo(int slotId, int mechaId, int level, int skinid,bool hideSkin)
        {
            SlotId = slotId;
            MechaId = mechaId;
            Level = level;
            HideSkin = hideSkin;
            this.SkinId = skinid;
        }
    }
}