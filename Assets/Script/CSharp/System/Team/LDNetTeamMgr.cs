using System.Collections.Generic;
using LD.Protocol;

namespace LD
{
    public partial class LDNetTeamMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.Team, isShowLockTips);
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (sys == LDSystemEnum.Team)
            {
                var tasks = Global.gApp.gSystemMgr.gTaskMgr.CommonTaskData.GetTaskList(LDSystemEnum.Team);
                foreach (LDNetCTaskInfo task in tasks)
                {
                    if (task.Status == CTaskStatus.CFinish.GetHashCode())
                    {
                        return LDRedTipsState.RedPoint;
                    }
                }
            }

            if (sys == LDSystemEnum.Custom_Team_Battle)
            {
                var activityInfoCfg = ActivityInfo.Data.Get(5);
                var maxCount = 0;
                if (activityInfoCfg != null)
                {
                    var count = activityInfoCfg.ActivityNum;
                    maxCount = count;
                }

                var curCount = Global.gApp.gSystemMgr.gTeamMgr.Data.ChallengeTimes;
                var residueTime = maxCount - curCount;
                if (residueTime > 0)
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            return LDRedTipsState.None;
        }

        public void CheckOpenTeam()
        {
            if (Data.RoomInfo != null)
            {
                OpenTeamUI(Data.RoomInfo);
            }
            else
            {
                int missionId = Data.GetCurMissionId();
                SendGVERoomEnterRequest(missionId);
            }
        }

        public bool TryEnterOldRoom()
        {
            if (Data.RoomInfo != null)
            {
                LDPageJumpTools.TryJumpPage(LDSystemEnum.Team);
                return true;
            }

            return false;
        }

        public void OpenTeamUI(LDNetRoomInfo roomInfo)
        {
            if (Global.gApp.CurFightScene == null)
            {
                Global.gApp.gUiMgr.OpenUIAsync<TeamInstanceMainUI>(LDUICfg.TeamInstanceMainUI).SetLoadedCall(UI =>
                    UI.InitData(roomInfo)
                );
            }
        }

        public void TryShowTeamInviteWindow()
        {
            if (Global.gApp.gUiMgr.CheckOpenMainUI() || Global.gApp.gUiMgr.CheckOpenTeamUI())
            {
                if (Data.ShowInviteState)
                {
                    Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.TeamInviteUI);
                    Data.ShowInviteState = false;
                }
            }
        }

        public void TryCloseTeamInvateWindow()
        {
            var ui = Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.TeamInviteUI);
            if (ui != null)
            {
                ui.TouchClose();
            }
        }


        //是否已经被邀请
        public bool PlayerIsInvited(long playerId)
        {
            return Data.CheckIsBeInvited(playerId);
        }

        public void ChangeInvateState()
        {
            m_ShieldInviteState = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.TeamAssistInvite, 0);
        }

        public bool IsCanGuide()
        {
            ModuleOpenItem moduleOpenItem = ModuleOpen.Data.Get((int)LDSystemEnum.Team);
            if (moduleOpenItem != null)
            {
                string condition = moduleOpenItem.open_conditions;
                if (!Global.gApp.gSystemMgr.gFilterMgr.Filter(condition))
                {
                    return false;
                }

                string[] conParamStr = LDCommonTools.SplitStringList(condition);
                if (conParamStr.Length > 0)
                {
                    List<int> conParam = LDCommonTools.SplitToIntList(conParamStr[0]);
                    if (conParam.Count > 1 && conParam[0] == 1)
                    {
                        if (conParam[1] != Global.gApp.gSystemMgr.gMainPassMgr.GetLastFinishedPassId()) // 刚解锁
                        {
                            return false;
                        }
                    }
                }
            }

            return true;
        }
    }
}