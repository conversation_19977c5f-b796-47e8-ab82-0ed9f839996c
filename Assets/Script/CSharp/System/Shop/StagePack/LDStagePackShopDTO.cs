using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDStagePackShopDTO
    {
        public List<int> PurchasedList { get; private set; } = new List<int>();

        public void AddPurchasedId(int id)
        {
            if (!PurchasedList.Contains(id))
            {
                PurchasedList.Add(id);
            }
        }

    }
}