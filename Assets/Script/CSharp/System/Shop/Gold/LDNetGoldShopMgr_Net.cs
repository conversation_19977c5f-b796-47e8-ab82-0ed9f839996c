using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetGoldShopMgr
    {
        public LDNetGoldShopDTO Data { get; protected set; } = new LDNetGoldShopDTO();

        protected override void InitNetImp()
        {
            AddNetHandler(Protocol.opcode.GoldShopInfoSyncResponse);
            AddNetHandlerSymmetry(Protocol.opcode.GoldShopBuyRequest, Protocol.opcode.GoldShopBuyResponse);
        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.GoldShopInfoSyncResponse)
            {
                GoldShopInfoSyncResponse info = GoldShopInfoSyncResponse.Parser.ParseFrom(data);
                Data.GoldShopData.Clear();
                foreach (GoldShopInfo shopInfo in info.ShopInfo)
                {
                    Data.SetGoldShopData(LDNetGoldShopDTOItem.Convert(shopInfo));
                }

                return info;
            }
            else if (s2c_msgId == opcode.GoldShopBuyResponse)
            {
                GoldShopBuyResponse info = GoldShopBuyResponse.Parser.ParseFrom(data);
                if (info.ShopInfo != null)
                {
                    Data.SetGoldShopData(LDNetGoldShopDTOItem.Convert(info.ShopInfo));
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.ShopUI, ShopUI.GoldShop);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MessageBox_KuaiSuHuoQu, 1);
                Global.gApp.gMsgDispatcher.Broadcast(MsgIds.ItemChangeByBuy);
                return info;
            }

            return null;
        }


        public void SendPurchase(int id, BuyType buyType, int count = 1)
        {
            if (buyType == BuyType.Free && !GetFreeNum(id, out _, out _))
            {
                Global.gApp.gToastMgr.ShowGameTips(69171);
                return;
            }

            if (buyType == BuyType.Advertise && !GetADNum(id, out _, out _))
            {
                Global.gApp.gToastMgr.ShowGameTips(69171);
                return;
            }

            if (buyType == BuyType.Pay)
            {
                if (!GetBuyNum(id, out _, out _))
                {
                    Global.gApp.gToastMgr.ShowGameTips(69171);
                    return;
                }

                if (!GetCostInfo(id, out _, out _))
                {
                    Global.gApp.gToastMgr.ShowGameTips(95001);
                    return;
                }
            }

            GoldShopBuyRequest req = new GoldShopBuyRequest();
            req.ShopId = id;
            req.BuyType = (int)buyType;
            req.Count = count;
            Send(opcode.GoldShopBuyRequest, req);
        }


        protected override void ClearNetDataImp()
        {
            Data = new LDNetGoldShopDTO();
        }
    }
}