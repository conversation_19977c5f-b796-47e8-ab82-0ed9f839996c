using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetCircleActivityMgr
    {
        public MechaTreasureBaseItem GetMechaTreasureBaseItem(int activityId)
        {
            foreach (MechaTreasureBaseItem cfgItem in MechaTreasureBase.Data.items)
            {
                if (cfgItem.activityID != activityId) continue;

                return cfgItem;
            }
            return null;
        }

        public bool CheckMechaTreasureEnough(int activityId)
        {
            MechaTreasureBaseItem cfgItem = GetMechaTreasureBaseItem(activityId);

            LDCommonItem costItem = new LDCommonItem(cfgItem.consume);

            return costItem.CurNum >= costItem.Num;
        }

        /// <summary>
        /// 进度奖励是否领取
        /// </summary>
        /// <returns></returns>
        public bool CheckMechaTreasureProgressRewardReceived(int activityId, int id, int loopTimes)
        {
            if (GetCircleActivityInfo(activityId) is LDMechaTreasureActivityInfo activityInfo)
            {
                int roundReward = GetRoundRewardId(loopTimes, id);
                return activityInfo.floorsCountReward.Contains(roundReward);
            }

            return false;
        }

        public int GetMechaTreasureLoopTimesValue(int curRound, int activityId)
        {
            MechaTreasureBaseItem cfgMain = GetMechaTreasureBaseItem(activityId);

            if (cfgMain == null || curRound == 0)
            {
                return 1;
            }
            if (GetCircleActivityInfo(activityId) is LDMechaTreasureActivityInfo activityInfo)
            {
                int loopTimes = Mathf.CeilToInt((float)curRound / cfgMain.loopRound);

                if (curRound % cfgMain.loopRound == 0)
                {
                    bool isHave = GetIsHaveMechaTreasureProgressReward(activityId, activityInfo, loopTimes);
                    if (!isHave)
                    {
                        loopTimes += 1;  //当最后一个进度奖领取后 ，显示的比如20层 要直接变成0层，然后循环轮数1变成2
                    }
                }
                else
                {
                    loopTimes = Math.Min(loopTimes, cfgMain.loopTime + 1);
                }


                return loopTimes;
            }
            return 0;
        }

        public int GetMechaTreasureRewardShowLoopTimes(int curRound, int activityId)
        {
            int realLoopTimes = GetMechaTreasureLoopTimesValue(curRound, activityId);
            if (GetCircleActivityInfo(activityId) is LDMechaTreasureActivityInfo activityInfo)
            {
                for (int idx = 1; idx <= realLoopTimes; idx++)
                {
                    bool isHave = GetIsHaveMechaTreasureProgressReward(activityId, activityInfo, idx);
                    if (isHave)
                    {
                        return idx;
                    }
                }
            }
            return realLoopTimes;
        }

        /// <summary>
        /// 获取轮次
        /// </summary>
        /// <param name="curRound"></param>
        /// <param name="activityId"></param>
        /// <param name="loopTimes"></param>
        /// <returns></returns>
        public int GetMechaTreasureRoundValue(int curRound, int activityId, int loopTimes)
        {
            MechaTreasureBaseItem cfgMain = GetMechaTreasureBaseItem(activityId);

            if (curRound > (cfgMain.loopTime + 1) * cfgMain.loopRound)
            {
                return curRound - cfgMain.loopTime * cfgMain.loopRound;
            }

            return curRound - cfgMain.loopRound * (loopTimes - 1);
        }

        public bool GetIsHaveMechaTreasureProgressReward(int activityId, LDMechaTreasureActivityInfo activityInfo, int loopTimes)
        {
            MechaTreasureBaseItem cfgMain = GetMechaTreasureBaseItem(activityId);

            int countItem = 0;
            foreach (MechaTreasureRoundItem cfgRound in MechaTreasureRound.Data.items)
            {
                if (cfgRound.activityID != activityId) continue;

                if (countItem >= cfgMain.loopRound)
                {
                    break;
                }
                if (cfgRound.extraReward == "") continue;
                int m_CurRound = GetMechaTreasureRoundValue(activityInfo.floorsCount, activityId, loopTimes);
                bool isSelect = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckMechaTreasureProgressRewardReceived(activityId, cfgRound.id, loopTimes - 1);

                if (m_CurRound >= cfgRound.floorsNum && !isSelect && cfgRound.floorsNum <= cfgMain.loopRound)
                {
                    return true;
                }

                countItem++;
            }

            return false;
        }

        public bool GetIsHaveMechaTreasureProgressReward(int activityId, LDMechaTreasureActivityInfo activityInfo)
        {
            int realLoopTimes = GetMechaTreasureLoopTimesValue(activityInfo.floorsCount, activityId);
            for (int idx = 1; idx <= realLoopTimes; idx++)
            {
                bool isHave = GetIsHaveMechaTreasureProgressReward(activityId, activityInfo, idx);
                if (isHave)
                {
                    return true;
                }
            }

            return false;
        }

        #region 红点

        private LDRedTipsState GetAllRedStateMechaTreasure()
        {
            int activityId = GetSeasonActivityId(LDCircleActivitySubUIType.mechaTreasure);
            if (activityId == 0)
            {
                return LDRedTipsState.None;
            }

            List<string> openList = GetOpenChildUIList(activityId);
            if (openList.Count == 0)
            {
                return LDRedTipsState.None;
            }
            LDCircleActivityInfoBase activityInfo = GetCircleActivityInfo(activityId);

            if (activityInfo == null)
            {
                return LDRedTipsState.None;
            }

            LDRedTipsState mainActivityState = GetRedStateMechaTreasure(activityId);
            if (mainActivityState != LDRedTipsState.None)
            {
                return mainActivityState;
            }

            LDRedTipsState questState = GetRedStateQuest(activityId);
            if (questState != LDRedTipsState.None)
            {
                return questState;
            }

            LDRedTipsState rankState = GetRedStateRankList(activityId);
            if (rankState != LDRedTipsState.None)
            {
                return rankState;
            }

            LDRedTipsState PurchaseState = GetRedStatePurchase(activityId);
            if (PurchaseState != LDRedTipsState.None)
            {
                return PurchaseState;
            }

            return LDRedTipsState.None;
        }

        public LDRedTipsState GetRedStateMechaTreasure(int activityId)
        {
            if (IsActivityEnd(activityId))
            {
                return LDRedTipsState.None;
            }

            //战令红点
            LDRedTipsState BattlePassState = GetRedStateBattlePass(activityId);
            if (BattlePassState != LDRedTipsState.None)
            {
                return BattlePassState;
            }

            if (CheckMechaTreasureEnough(activityId))
            {
                return LDRedTipsState.RedPoint;
            }

            if (GetCircleActivityInfo(activityId) is LDMechaTreasureActivityInfo activityInfo)
            {
                bool isHaveProgressReward = GetIsHaveMechaTreasureProgressReward(activityId, activityInfo);
                if (isHaveProgressReward)
                {
                    return LDRedTipsState.RedPoint;
                }
            }
            return LDRedTipsState.None;
        }

        public LDRedTipsState GetRedStateMechaTreasureRunEnough(int activityId)
        {
            if (CheckMechaTreasureEnough(activityId))
            {
                return LDRedTipsState.RedPoint;
            }
            return LDRedTipsState.None;
        }

        #endregion
    }
}