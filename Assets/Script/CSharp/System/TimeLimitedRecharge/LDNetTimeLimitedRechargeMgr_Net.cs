using System.Collections.Generic;
using System.Linq;
using Google.Protobuf;
using LD.Protocol;

namespace LD
{
    public partial class LDNetTimeLimitedRechargeMgr
    {
        public LDNetTimeLimitedRechargeDTO Data { get; private set; } = new LDNetTimeLimitedRechargeDTO();

        protected override void InitNetImp()
        {
            AddNetHandler(opcode.LimitedRechargeInfoAllSyncResponse);
            AddNetHandlerSymmetry(opcode.ReceiveLimitedRechargeRewardRequest, opcode.ReceiveLimitedRechargeRewardResponse);
        }

        protected override void ClearNetDataImp()
        {
            Data = new();
        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.LimitedRechargeInfoAllSyncResponse)
            {
                LimitedRechargeInfoAllSyncResponse info = LimitedRechargeInfoAllSyncResponse.Parser.ParseFrom(data);

                if (info.ActivityInfo != null)
                {
                    if (Data.m_ActivityInfo != null)
                    {
                        if (Data.m_ActivityInfo.ActivityId != info.ActivityInfo.ActivityId)
                        {
                            Data.m_ActivityInfo = null;
                            Data.SyncRecharge(0f);
                            Data.m_RewardIds.Clear();
                            Data.m_LoginCheck = false;
                        }
                    }

                    Data.SyncActivityInfo(info.ActivityInfo);
                    if (!Data.m_LoginCheck)
                    {
                        Data.m_LoginCheck = true;
                        if (Data.m_ActivityInfo.IsOpen())
                        {
                            Global.gApp.gUiMgr.TryShowTimeLimitedRecharge(Data.m_ActivityInfo.ActivityId, Data.m_ActivityInfo.BeginTime); //拍脸图
                        }

                        if (Global.gApp.gSystemMgr.gExchangeShopMgr.GetShopItems(LDExchangeShopType.Activity).Count <= 0)
                        {
                            Global.gApp.gSystemMgr.gExchangeShopMgr.SendExchangeShopReqNotOpen(LDExchangeShopType.Activity, Data.m_ActivityInfo.ActivityId);
                        }
                    }
                }

                if (info.RewardIds != null)
                {
                    Data.AddRewardId(info.RewardIds.ToList());
                }

                if (info.Recharge > 0)
                {
                    Data.SyncRecharge(info.Recharge);
                }

                if (info.CommonInfo != null)
                {
                    Global.gApp.gSystemMgr.gActivityShopMgr.Data.SyncActivityCommonInfo(info.ActivityInfo.ActivityId ,info.CommonInfo);
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.TimeLimitedRechargeUI, 1);
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_TimeLimitedRecharge);

                return info;
            }
            else if (s2c_msgId == opcode.ReceiveLimitedRechargeRewardResponse)
            {
                ReceiveLimitedRechargeRewardResponse info = ReceiveLimitedRechargeRewardResponse.Parser.ParseFrom(data);
                if (info.ActivityId == Data.m_ActivityInfo.ActivityId)
                {
                    Data.AddRewardId(info.Id.ToList());
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.TimeLimitedRechargeUI, info.Id.Count > 0 ? info.Id[0] : 0);
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_TimeLimitedRecharge);
                return info;
            }

            return null;
        }

        public void SendReceiveLimitedRechargeRewardRequest(int activityId, int id)
        {
            ReceiveLimitedRechargeRewardRequest requestData = new ReceiveLimitedRechargeRewardRequest();
            // float allRecharge = Data.m_Recharge;
            List<int> temps = new List<int>();
            // TimeLimitedRechargeRewardItem[] all = TimeLimitedRechargeReward.Data.items;
            // for (int i = 0; i < all.Length; i++)
            // {
            //     TimeLimitedRechargeRewardItem item = all[i];
            //     if (item.activityID == activityId)
            //     {
            //         if (allRecharge >= item.rechargeLevel01 && !Data.m_RewardIds.Contains(item.id))
            //             temps.Add(item.id);
            //     }
            // }

            temps.Add(id);
            requestData.Id.AddRange(temps);
            requestData.ActivityId = activityId;
            Send(opcode.ReceiveLimitedRechargeRewardRequest, requestData);
        }
    }
}