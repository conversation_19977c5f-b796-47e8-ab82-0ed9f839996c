using System.Collections;
using System.Collections.Generic;
using LD.Protocol;
using LitJson;
using UnityEngine;

namespace LD
{
    public partial class LDNetGuildMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.Guild, isShowLockTips);
        }

        #region 常量

        // 创建公会所需消耗
        public LDCommonItem GetCreateCost()
        {
            int num = GlobalCfg.Data.Get(80069).valueInt;
            LDCommonItem commonItem = new LDCommonItem(LDCommonType.Item, LDSpecialItemId.Diamond, num); // 固定钻石
            return commonItem;
        }

        // 公会改会徽的CD（小时）
        public int GetChangeBadgeCD()
        {
            return GlobalCfg.Data.Get(80070).valueInt;
        }

        // 离开公会再加入的CD (小时)
        public float GetLeaveCD()
        {
            int min = GlobalCfg.Data.Get(80074).valueInt;
            float hour = min / 60f;
            return hour;
        }

        // 前几次离开公会无冷却（次数）
        public int GetLeaveCDCount()
        {
            return GlobalCfg.Data.Get(80075).valueInt;
        }

        // 公会改名的CD（小时）
        public int GetChangeNameCD()
        {
            return GlobalCfg.Data.Get(80103).valueInt;
        }

        // 公会改名价格
        public int GetChangeNamePrice()
        {
            return GlobalCfg.Data.Get(80102).valueInt;
        }

        #endregion

        // 获取公会ID
        public long GetGuildId()
        {
            return Data.GuildId;
        }

        // 获取公会信息
        public LDGuildInfo GetGuildInfo()
        {
            return Data.MyGuildInfo;
        }

        // 获取我的成员信息
        public LDGuildMemberInfo GetMyMemberInfo()
        {
            long myPlayerId = Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId();
            return GetMemberInfo(myPlayerId);
        }

        // 获取成员信息
        public LDGuildMemberInfo GetMemberInfo(long playerId)
        {
            return GetGuildInfo().Members.GetValueOrDefault(playerId);
        }

        // 解析公会日志
        public string ParseLogs(LDGuildLogInfo log)
        {
            // 与服务器协定  日志只有一个人名的时候 读SourcePlayerName    有操作者和被操作者的时候 分别读 SourcePlayerName 和 TargetPlayerName
            GuildLogItem logCfg = GuildLog.Data.Get(log.Action);
            switch (logCfg.id)
            {
                case 1:
                case 2:
                case 4:
                case 5:
                {
                    return UiTools.Localize(logCfg.guildLog, log.SourcePlayerName);
                }

                case 3:
                {
                    if (log.Params.Count > 0)
                    {
                        return UiTools.Localize(logCfg.guildLog, log.Params[0]);
                    }
                    else
                    {
                        Global.LogError($"日志数据错误");
                        break;
                    }
                }

                default:
                    Global.LogError($"not support action:{log.Action}");
                    break;
            }

            return string.Empty;
        }

        // 获取踢人提醒设置  是否不提醒
        public bool GetKickoutNoTips()
        {
            long val = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, LDLocalDataKeys.Guild_Kickout_Tips);
            if (val <= 0)
            {
                return false;
            }

            bool isClose = DateTimeUtil.SameDay(val, DateTimeUtil.GetServerTime());
            return isClose;
        }

        // 设置踢人提醒
        public void SetKickoutTips(bool close)
        {
            long val = close ? DateTimeUtil.GetServerTime() : 0;
            Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, LDLocalDataKeys.Guild_Kickout_Tips, val);
        }
        
        // 检测是否弹出升级提示
        public void CheckLevelUpTips()
        {
            if (!HasGuild()) return;

            if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.GuildMainUI) == null)
            {
                return;
            }

            int lv = GetGuildInfo().BriefInfo.Level;
            if (lv <= 1)
            {
                return;
            }

            string content = Global.gApp.gSystemMgr.gLocalDataMgr.GetVal(true, LDLocalDataKeys.Guild_LvUp_Panel);
            bool check = string.IsNullOrEmpty(content);
            if (!string.IsNullOrEmpty(content))
            {
                List<long> vals = JsonMapper.ToObject<List<long>>(content);
                if (vals.Count > 1 && vals[0] == Data.GuildId && vals[1] != lv)
                {
                    check = true;
                }
            }

            if (check)
            {
                Global.gApp.gUiMgr.OpenUIAsync<GuildUpgradeUI>(LDUICfg.GuildUpgradeUI).SetLoadedCall(ui =>
                {
                    ui?.RefreshUI(lv);
                });
            }
            else
            {
                SaveLevelUpTips();
            }
        }

        // 保存检测是否弹出升级提示
        public void SaveLevelUpTips()
        {
            if (!HasGuild()) return;

            int lv = GetGuildInfo().BriefInfo.Level;
            List<long> vals = new List<long>() { Data.GuildId, lv };
            Global.gApp.gSystemMgr.gLocalDataMgr.SetVal(true, LDLocalDataKeys.Guild_LvUp_Panel, JsonMapper.ToJson(vals));
        }
        
        // 获取公会资金
        public long GetGuildFunds()
        {
            if (!HasGuild()) return 0;
            return Data.MyGuildInfo.GuildCoin;
        }


        // 是否有公会
        public bool HasGuild()
        {
            return GetGuildId() > 0;
        }

        // 是否满足条件
        public bool IsMatchCondition(List<LDGuildJoinCondition> conditions, LDGuildMemberInfo memberInfo, bool isShowTips = false)
        {
            foreach (LDGuildJoinCondition cond in conditions)
            {
                switch ((GuildJoinConditionType)cond.CondType)
                {
                    case GuildJoinConditionType.ConditionFight:
                        if (Global.gApp.gSystemMgr.gRoleMgr.GetPowerNum() < cond.Value)
                        {
                            if (isShowTips) Global.gApp.gToastMgr.ShowGameTips(UiTools.Localize(67054, cond.Value));
                            return false;
                        }

                        break;
                    default:
                        Global.LogError($"info not support condition type:{cond.CondType}");
                        return false;
                }
            }

            return true;
        }

        // 公会是否满级
        public bool IsGuildLevelMax()
        {
            if (!HasGuild())
            {
                return false;
            }

            GuildLevelItem[] lvCfgs = GuildLevel.Data.items;
            return GetGuildInfo().BriefInfo.Level >= lvCfgs[lvCfgs.Length - 1].id;
        }

        // 公会是否可以升级
        public bool CanGuildLevelUp()
        {
            if (!HasGuild())
            {
                return false;
            }

            if (IsGuildLevelMax())
            {
                return false;
            }

            LDGuildMemberInfo myMemberInfo = GetMyMemberInfo();
            if (myMemberInfo == null || myMemberInfo.JobInfo.Job <= 0)
            {
                return false;
            }

            LDGuildInfo guildInfo = GetGuildInfo();
            int level = guildInfo.BriefInfo.Level;
            GuildLevelItem cfg = GuildLevel.Data.Get(level);
            if (cfg != null)
            {
                return GetGuildFunds() >= cfg.guildUpPrice;
            }

            return false;
        }

        #region 红点

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (!IsUnlock())
            {
                return LDRedTipsState.None;
            }

            if (sys == LDSystemEnum.Guild)
            {
                if (!HasGuild())
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            if (sys == LDSystemEnum.Custom_Guild_LvUp)
            {
                if (CanGuildLevelUp())
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            if (sys == LDSystemEnum.GuildDonation)
            {
                LDNetGuildDonationMgr donationMgr = Global.gApp.gSystemMgr.gGuildDonationMgr;
                if (donationMgr.IsHaveCount())
                {
                    return LDRedTipsState.RedPoint;
                }

                if (donationMgr.IsHaveProgressReward())
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            return LDRedTipsState.None;
        }

        #endregion

        protected override void DestroyImp()
        {
        }
    }
}