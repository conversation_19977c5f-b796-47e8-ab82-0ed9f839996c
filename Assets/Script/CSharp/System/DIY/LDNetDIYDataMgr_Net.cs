using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetDIYDataMgr
    {
        public LDNetDIYDTO Data { private set; get; } = new LDNetDIYDTO();
        protected override void InitNetImp()
        {
            AddNetHandler(Protocol.opcode.DiyModelDataSyncResponse);
            AddNetHandlerSymmetry(Protocol.opcode.DiyModelUpdateRequest, Protocol.opcode.DiyModelUpdateResponse);
        }
        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.DiyModelUpdateResponse)
            {
                CoverDIYData(m_CoveData);
                DiyModelUpdateResponse diyModelUpdateResponse = DiyModelUpdateResponse.Parser.ParseFrom(data);
                Global.gApp.gToastMgr.ShowGameTips(95109);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MechaDiyUI, 0);
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.MechaPart);
                return diyModelUpdateResponse;
            }
            // ���� ��
            else if (s2c_msgId == opcode.DiyModelDataSyncResponse)
            {
                DiyModelDataSyncResponse diyModeDataData = DiyModelDataSyncResponse.Parser.ParseFrom(data);
                OnLoadDIYData(diyModeDataData);
                return diyModeDataData;
            }
            return null;
        }
        public List<int> GetExclusiveSkill(LDDIYData diyData, int passType)
        {
            List<int> exclusiveSkill = new List<int>();
            foreach (LDDIYPartData partData in diyData.Data)
            {
                DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(partData.PId);
                if (partCfgItem.ExclusiveEntry.Length > 1)
                {
                    int bodyID = partCfgItem.ExclusiveEntry[0];
                    if (bodyID == diyData.BodyId || bodyID < 0)
                    {
                        for (int i = 0; i < partCfgItem.ExclusiveEntryGameplay.Length; i++)
                        {
                            if (partCfgItem.ExclusiveEntryGameplay[i] == (int)LDPassType.All || partCfgItem.ExclusiveEntryGameplay[i] == passType)
                            {
                                for (int j = 1; j < partCfgItem.ExclusiveEntry.Length; j++)
                                {
                                    exclusiveSkill.Add(partCfgItem.ExclusiveEntry[j]);
                                }
                                break;
                            }
                        }

                    }
                }
            }
            return exclusiveSkill;
        }
        private void OnLoadDIYData(DiyModelDataSyncResponse diyModel)
        {
            if (diyModel != null)
            {
                Data.DIYData.Clear();
                foreach (DiyModel dyyMode in diyModel.Model)
                {
                    Data.DIYData.Add(LDDIYData.Convert(dyyMode));
                }
            }
        }
        public void SaveTempDiyData(LDDIYData diyData)
        {
            CoverDIYData(diyData);
        }
        public void SendSaveDiyData(LDDIYData diyData)
        {
            m_CoveData = diyData;
            DiyModelUpdateRequest modelUpdateRequest = new DiyModelUpdateRequest();
            DiyModel Model = diyData.Convert();
            modelUpdateRequest.Model = Model;
            Send(opcode.DiyModelUpdateRequest, modelUpdateRequest);
        }

        public LDDIYData GetTempDiyData()
        {
            MechaDiyUI mechaDiyUI = Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.MechaDiyUI) as MechaDiyUI;
            if (mechaDiyUI != null)
            {
                LDDIYData DIYData = mechaDiyUI.GetCreature().GetCurDiyData();
                return DIYData;
            }
            else
            {

                LDDIYData DIYData = GetDIYData();
                return DIYData;
            }
        }
        public bool CanEquipWeapon(int partId)
        {
            if (!Global.gApp.gSystemMgr.gMechaPartDataMgr.DIYPartUnLocked(partId))
            {
                return false;
            }
            if (Global.gApp.gSystemMgr.gMechaPartDataMgr.DIYPartEquiped(partId))
            {
                return false;
            }
            return CanEquipWeapon();
        }

        private bool CanEquipWeapon()
        {
            LDDIYData DIYData = GetTempDiyData();
            int equipmentCount = DIYData.GetBlockCountByType(LDDIYPartItemType.PartWeapon);
            MechaItem mechaItem = Mecha.Data.Get(DIYData.BodyId);
            if (equipmentCount >= mechaItem.weapon)
            {
                return false;
            }
            else
            {
                int unlockPartCount = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetUnlckPartCountByType(LDDIYPartItemType.PartWeapon);
                if (unlockPartCount > equipmentCount)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }


        public bool CanEquipEquip()
        {
            LDDIYData DIYData = GetTempDiyData();
            int equipmentCount = DIYData.GetBlockCountByType(LDDIYPartItemType.PartEquipment);
            MechaItem mechaItem = Mecha.Data.Get(DIYData.BodyId);
            if (equipmentCount >= mechaItem.equip)
            {
                return false;
            }
            else
            {
                int unlockPartCount = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetUnlckPartCountByType(LDDIYPartItemType.PartEquipment);
                if (unlockPartCount > equipmentCount)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }
        public LDDIYData GetMirrorDIYData(int mechaId,List<int> partIds)
        {
            LDDIYData diyData = new LDDIYData();
            diyData.BodyId = mechaId;
            
            List<ArenaPartPositionItem> posItems = new List<ArenaPartPositionItem>();
            foreach (ArenaPartPositionItem posItem in ArenaPartPosition.Data.items)
            {
                if(posItem.mechaId == mechaId)
                {
                    posItems.Add(posItem);
                }
            }
            int index = -1;
            foreach (int pId in partIds)
            {
                index++;
                LDDIYPartData partData = new LDDIYPartData();
                partData.PId = pId;
                if(posItems.Count > 0)
                {
                    ArenaPartPositionItem arenaPartPositionItem = posItems[index % posItems.Count];
                    partData.PN = arenaPartPositionItem.bone;
                    MyVector3 LP = new MyVector3();
                    LP.x = arenaPartPositionItem.position[0];
                    LP.y = arenaPartPositionItem.position[1];
                    LP.z = arenaPartPositionItem.position[2];
                    partData.LP = LP;

                    MyVector3 LR = new MyVector3();
                    LR.x = arenaPartPositionItem.rotation[0];
                    LR.y = arenaPartPositionItem.rotation[1];
                    LR.z = arenaPartPositionItem.rotation[2];
                    partData.LR = LR;
                    partData.SF = arenaPartPositionItem.scale;
                    partData.RF = 0;
                }
                diyData.Data.Add(partData);
            }
            return diyData;
        }

        public bool HasOptionalNotEquip(int pos)
        {
            LDDIYData DIYData = GetTempDiyData();
            MechaItem mechaCfg = Mecha.Data.Get(DIYData.BodyId);
            foreach (int partId in mechaCfg.optionalWeapon)
            {
                bool isUnlock = Global.gApp.gSystemMgr.gMechaPartDataMgr.DIYPartUnLocked(partId);
                if (!isUnlock)
                {
                    continue;
                }

                DIYPartCfgItem partCfg  = DIYPartCfg.Data.Get(partId);
                if (partCfg.pos == pos && !DIYData.ContainsPart(partId)) // 拥有且未装备
                {
                    return true;
                }
            }
            return false;
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (sys == LDSystemEnum.DIY)
            {
                if (CanEquipWeapon())
                {
                    return LDRedTipsState.RedPoint;
                }
                //else if(CanEquipEquip())
                //{
                //    return LDRedTipsState.RedPoint;
                //}
                else if (Global.gApp.gSystemMgr.gMechaPartDataMgr.HasNewPart(-1))
                {
                    return LDRedTipsState.RedPoint;
                }
                else
                {
                    return LDRedTipsState.None;
                }

            }
            else if (sys == LDSystemEnum.Custom_DIY_WeaponTab)
            {
                if (CanEquipWeapon())
                {
                    return LDRedTipsState.RedPoint;
                }
                else if (Global.gApp.gSystemMgr.gMechaPartDataMgr.HasNewPart(LDDIYPartItemType.PartWeapon))
                {
                    return LDRedTipsState.RedPoint;
                }
                else if (HasOptionalNotEquip(LDDIYPartItemType.PartWeapon))
                {
                    return LDRedTipsState.RedPoint;
                }
                else
                {
                    return LDRedTipsState.None;
                }
            }
            else if (sys == LDSystemEnum.Custom_DIY_EquipTab)
            {
                //if (CanEquipEquip())
                //{
                //    return LDRedTipsState.RedPoint;
                //}
                //else
                if (Global.gApp.gSystemMgr.gMechaPartDataMgr.HasNewPart(LDDIYPartItemType.PartEquipment))
                {
                    return LDRedTipsState.RedPoint;
                }
                else if (HasOptionalNotEquip(LDDIYPartItemType.PartEquipment))
                {
                    return LDRedTipsState.RedPoint;
                }
                else
                {
                    return LDRedTipsState.None;
                }
            }
            else if (sys == LDSystemEnum.Custom_MechaPart_Equipment_New)
            {
                if (Global.gApp.gSystemMgr.gMechaPartDataMgr.HasNewPart(LDDIYPartItemType.PartEquipment))
                {
                    return LDRedTipsState.RedPoint;
                }
            }
            else if (sys == LDSystemEnum.Custom_MechaPart_Weapon_New)
            {
                if (Global.gApp.gSystemMgr.gMechaPartDataMgr.HasNewPart(LDDIYPartItemType.PartWeapon))
                {
                    return LDRedTipsState.RedPoint;
                }
            }
            return LDRedTipsState.None;
        }

        protected override void ClearNetDataImp()
        {
            m_CoveData = null;
            Data = new LDNetDIYDTO();
        }
    }
}
