using Google.Protobuf;
using LD.Protocol;

namespace LD
{
    public partial class LDNetPrivateChatMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        protected override void InitNetImp()
        {
        }

        protected override void ClearNetDataImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            return null;
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.PrivateChat, isShowLockTips);
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (Global.gApp.gSystemMgr.gChatMgr.GetRedNum() > 0)
            {
                return LDRedTipsState.RedPoint;
            }

            if (Global.gApp.gSystemMgr.gChatMgr.CheckPrivateGift() == true)
            {
                return LDRedTipsState.RedPoint;
            }

            return LDRedTipsState.None;
        }
    }
}