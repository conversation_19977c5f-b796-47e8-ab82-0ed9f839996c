using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDNetMechaSpinDTO
    {
        public long OpenTime { get; private set; }
        public long EndTime { get; private set; }
        public Dictionary<int, int> LeftCount { get; private set; } = new Dictionary<int, int>();
        public int SpinCount { get; private set; }
        public Dictionary<int, int> ExchangeCount { get; private set; } = new Dictionary<int, int>();
        public int SpinRound { get; private set; }
        public List<LDCircleMarqueeRecord> MarqueeRecordList { get; private set; } = new List<LDCircleMarqueeRecord>();

        public void SetOpenTime(long time)
        {
            OpenTime = time;
        }

        public void SetEndTime(long time)
        {
            EndTime = time;
        }

        public void SetLeftCount(int id, int count)
        {
            LeftCount[id] = count;
        }

        public void SetSpinCount(int count)
        {
            SpinCount = count;
        }

        public void SetExchangeInfo(int id, int count)
        {
            ExchangeCount[id] = count;
        }

        public void SetSpineRound(int round)
        {
            SpinRound = round;
        }
        
        public void SetMarqueeRecordList(List<LDCircleMarqueeRecord> list)
        {
            MarqueeRecordList = list;
        }
    }
}