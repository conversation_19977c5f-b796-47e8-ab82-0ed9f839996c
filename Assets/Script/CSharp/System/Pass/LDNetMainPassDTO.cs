using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDNetMainPassRewardStatus
    {
        public const int Unfinished = 0;
        public const int Finished = 1;
        public const int Rewarded = 2;
    }



    public class LDNetMainPassDTOItem
    {
        public int PassId { get; set; }
        public bool Finished { get; set; }
        public int Progress { get; set; }
        public Dictionary<int, int> RewardInfo { get; set; } = new Dictionary<int, int>();

        public static implicit operator LDNetMainPassDTOItem(MissionInfo info)
        {
            LDNetMainPassDTOItem item = new LDNetMainPassDTOItem();
            item.PassId = info.MissionId;
            item.Finished = info.Win;
            item.Progress = info.Progress;
            if (info.RewardMap != null)
            {
                item.RewardInfo.Clear();
                foreach (KeyValuePair<int, int> reward in info.RewardMap)
                {
                    item.RewardInfo.Add(reward.Key, reward.Value);
                }
            }
            return item;
        }
    }

    public class LDNetMainPassDTO
    {
        public Dictionary<int, LDNetMainPassDTOItem> MainPassDatas { get; private set; } = new Dictionary<int, LDNetMainPassDTOItem>();

        public void SetMainPassData(MissionInfo data)
        {
            if (MainPassDatas.ContainsKey(data.MissionId))
            {
                MainPassDatas[data.MissionId] = data;
            }
            else
            {
                MainPassDatas.Add(data.MissionId, data);
            }
        }

        public void ClearData()
        {
            MainPassDatas.Clear();
        }
    }
}