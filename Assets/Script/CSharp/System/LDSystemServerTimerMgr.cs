using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDSystemServerTimerMgr
    {
        private Dictionary<string, int> m_SysTimerMap = new Dictionary<string, int>();
        private LDServerTimerMgr m_ServerTimerMgr;

        public LDSystemServerTimerMgr()
        {
            m_ServerTimerMgr = new LDServerTimerMgr();
        }

        public void OnUpdate(float dt)
        {
            m_ServerTimerMgr.OnDUpdate(dt);
        }

        public void AddSysServerTimer(string key, long exprieTime, Action callback)
        {
            TryRemoveTimer(key);
            int timerGuid = m_ServerTimerMgr.AddTimer(exprieTime, callback);
            m_SysTimerMap[key] = timerGuid;
        }

        private void TryRemoveTimer(string key)
        {
            if (m_SysTimerMap.TryGetValue(key, out int guid))
            {
                m_ServerTimerMgr.RemoveTimer(guid);
            }
        }
    }
}