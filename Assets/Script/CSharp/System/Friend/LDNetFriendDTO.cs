using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDFriendEnergyInfo
    {
        public Dictionary<long, bool> ReceivePlayers = new Dictionary<long, bool>();
        public List<long> SendPlayers = new List<long>();

        public LDFriendEnergyInfo() { }
        public LDFriendEnergyInfo(FriendEnergyInfo info)
        {
            foreach (KeyValuePair<long, bool> item in info.ReceivePlayers)
            {
                ReceivePlayers.Add(item.Key, item.Value);
            }

            foreach (long item in info.SendPlayers)
            {
                SendPlayers.Add(item);
            }
        }
    }

    public class LDFriendRecommendInfo
    {
        public long PlayerId;
        public string Name;
        public long Level;
        public LDRoleHeadInfo RoleHeadInfo;
        public long Power;

        public LDFriendRecommendInfo(FriendRecommendInfo info)
        {
            PlayerId = info.PlayerId;
            Name = info.Name;
            Level = info.Level;
            RoleHeadInfo = new LDRoleHeadInfo
            {
                Head = info.Head,
                HeadBox = info.HeadBox,
                PlayerId = PlayerId,
                MechaId = info.CurMechaId,
                MechaSkinId = info.MechaSkin
            };
            Power = info.Fight;
        }

        public string GetPowerStr()
        {
            return UiTools.FormateMoney(Power);
        }

        public LDFriendRecommendInfo(RoleInfo info)
        {
            PlayerId = info.PlayerId;
            Name = info.Name;
            Level = info.Level;
            RoleHeadInfo = new LDRoleHeadInfo
            {
                Head = info.Head,
                HeadBox = info.HeadBox,
                PlayerId = PlayerId
            };
            Power = info.Fight;
        }
    }


    public class LDNetFriendDTO
    {
        public Dictionary<long, LDRoleInfoDTO> Friends = new Dictionary<long, LDRoleInfoDTO>();
        public Dictionary<long, LDRoleInfoDTO> ApplyRoles = new Dictionary<long, LDRoleInfoDTO>();
        public Dictionary<long, LDRoleInfoDTO> BlackRoles = new Dictionary<long, LDRoleInfoDTO>();
        public LDFriendEnergyInfo EnergyInfo = new LDFriendEnergyInfo();
        public Dictionary<long, LDFriendRecommendInfo> RecommendRecord = new Dictionary<long, LDFriendRecommendInfo>();
        public Dictionary<long, LDFriendRecommendInfo> CurRecommendRecord = new Dictionary<long, LDFriendRecommendInfo>();
        public List<long> AppliedList = new List<long>();


        public void ClearRecord()
        {
            RecommendRecord.Clear();
            CurRecommendRecord.Clear();
            AppliedList.Clear();
        }

        public List<LDRoleInfoDTO> GetSortFriendList()
        {
            List<LDRoleInfoDTO> list = new List<LDRoleInfoDTO>(Friends.Values);
            list.Sort(SortFriends);
            return list;
        }
        public int SortFriends(LDRoleInfoDTO a, LDRoleInfoDTO b)
        {
            if (a.Online == b.Online)
            {
                if (a.Online)// 都在线
                {
                    return b.Level - a.Level;
                }
                else// 都不在线
                {
                    return (int)(b.OfflineTime / 1000) - (int)(a.OfflineTime / 1000);
                }
            }
            else // 有在线有不在线
            {
                int sortA = a.Online ? 1 : 2;
                int sortB = b.Online ? 1 : 2;
                return sortA - sortB;
            }
        }

        public List<LDRoleInfoDTO> GetSortApplyRoles()
        {
            List<LDRoleInfoDTO> list = new List<LDRoleInfoDTO>(ApplyRoles.Values);
            list.Sort(SortApplyRoles);
            return list;
        }
        public int SortApplyRoles(LDRoleInfoDTO a, LDRoleInfoDTO b)
        {
            return b.Power.CompareTo(a.Power);
        }

        public List<LDRoleInfoDTO> GetSortBlackRoles()
        {
            List<LDRoleInfoDTO> list = new List<LDRoleInfoDTO>(BlackRoles.Values);
            list.Sort(SortBlackRoles);
            return list;
        }
        public int SortBlackRoles(LDRoleInfoDTO a, LDRoleInfoDTO b)
        {
            return b.Power.CompareTo(a.Power);
        }
    }
}