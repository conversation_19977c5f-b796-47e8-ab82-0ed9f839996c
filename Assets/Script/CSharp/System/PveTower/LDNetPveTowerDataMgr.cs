using System.Collections.Generic;
using System.Data;
using UnityEngine;

namespace LD
{
    public partial class LDNetPveTowerDataMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        // 获取当前关卡
        public int GetCurPassId()
        {
            TowerMissionItem[] towerMissionItems = TowerMission.Data.items;

            if (IsPassMax())
            {
                return towerMissionItems[towerMissionItems.Length - 1].id;
            }

            if (Data.LastFinishedPassId > 0)
            {
                TowerMissionItem tmItem = TowerMission.Data.Get(Data.LastFinishedPassId);
                if(IsChapterLastPass(Data.LastFinishedPassId) && tmItem.order == Data.CurChapterId)
                {
                    return Data.LastFinishedPassId; // 需要手动解锁下一章
                }

                for (int i = 0; i < towerMissionItems.Length; i++)
                {
                    if (towerMissionItems[i].id == Data.LastFinishedPassId)
                    {
                        return towerMissionItems[i + 1].id;
                    }
                }
            }
            else
            {
                return towerMissionItems[0].id;
            }

            Global.LogError($"PveTower GetCurPassId Error");
            return 1001;
        }

        // 获取当前章节
        public int GetCurChapterId()
        {
            return Data.CurChapterId;
        }

        // 获取当前排名 0 = 未上榜
        public int GetCurRank()
        {
            return Data.Rank;
        }

        // 获取最大可以上榜的最大排名
        public int GetRankMaxNum(int chapterId)
        {
            List<TowerRankItem> cfgs = GetChapterRankCfgList(chapterId);
            int maxNum = 1;
            foreach (TowerRankItem cfg in cfgs)
            {
                if (maxNum < cfg.MinRank)
                {
                    maxNum = cfg.MinRank;
                }
            }
            return maxNum;
        }

        // 获取剩余上榜人数
        public int GetSurplusRankNum(int chapterId)
        {
            int maxNum = GetRankMaxNum(chapterId);
            int surplusNum = Mathf.Max(0, maxNum - Data.RankPersonNum);
            return surplusNum;
        }

        // 获取章节 关卡列表
        public List<TowerMissionItem> GetChapterPassCfgList(int chapterId)
        {
            List<TowerMissionItem> passItems = new List<TowerMissionItem>();
            TowerMissionItem[] towerItems = TowerMission.Data.items;
            foreach (TowerMissionItem item in towerItems)
            {
                if(item.order == chapterId)
                {
                    passItems.Add(item);
                }
            }
            return passItems;
        }

        // 获取章节 排名数据
        public List<TowerRankItem> GetChapterRankCfgList(int chapterId)
        {
            List<TowerRankItem> rankCfgs = new List<TowerRankItem>();
            TowerRankItem[] rankItems = TowerRank.Data.items;
            foreach (TowerRankItem item in rankItems)
            {
                if (item.orderID == chapterId)
                {
                    rankCfgs.Add(item);
                }
            }
            return rankCfgs;
        }

        // 获取最后一关ID
        public int GetMaxPassId()
        {
            TowerMissionItem[] towerMissionItems = TowerMission.Data.items;
            return towerMissionItems[towerMissionItems.Length - 1].id;
        }

        // 获取关闭战力提醒设置
        public bool GetCloseTodayPowerTips()
        {
            long val = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, LDLocalDataKeys.PveTower_BattleTips);
            if(val <= 0)
            {
                return false;
            }

            bool isClose = DateTimeUtil.SameDay(val, DateTimeUtil.GetServerTime());
            return isClose;
        }

        // 设置关闭战力提醒
        public void SetCloseTodayPowerTips(bool close)
        {
            long val = close ? DateTimeUtil.GetServerTime() : 0;
            Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, LDLocalDataKeys.PveTower_BattleTips, val);
        }
        
        // 获取最大通关关卡
        public int GetMaxFinishedPassId()
        {
            return Data.LastFinishedPassId;
        }

        // 解锁
        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.PveTower, isShowLockTips);
        }

        // 是否已经通关最后一关
        public bool IsPassMax()
        {
            bool isMax = Data.LastFinishedPassId == GetMaxPassId();
            return isMax;
        }

        // 是否是章节最后一关
        public bool IsChapterLastPass(int passId)
        {
            TowerMissionItem tmItem = TowerMission.Data.Get(passId);
            List<TowerMissionItem> cfgs = GetChapterPassCfgList(tmItem.order);
            if(cfgs.Count > 0)
            {
                return passId == cfgs[cfgs.Count - 1].id;
            }
            return false;
        }
        
        // 是否需要弹出章节解锁
        public bool IsShowChapterUnlockUI()
        {
            if(Data.LastFinishedPassId <= 0)
            {
                return false;
            }

            int storageMaxPassId = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.PveTower_MaxPass, 0);
            if(storageMaxPassId == Data.LastFinishedPassId && IsPassMax()) // 最后一关  已经弹过了
            {
                return false;
            }

            if (IsChapterLastPass(Data.LastFinishedPassId))
            {
                TowerMissionItem tmItem = TowerMission.Data.Get(Data.LastFinishedPassId);
                if(tmItem.order == Data.CurChapterId)
                {
                    return true;
                }
            }
            return false;
        }


        protected override void DestroyImp()
        {
        }
    }
}