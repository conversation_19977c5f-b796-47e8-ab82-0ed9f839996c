using Google.Protobuf;
using LD.Protocol;

namespace LD
{
    public partial class LDNetPowerCompareMgr
    {
        public LDNetPowerCompareDTO Data { get; protected set; }

        protected override void InitNetImp()
        {
            AddNetHandlerSymmetry(opcode.PlayerCombatPowerRequest, opcode.PlayerCombatPowerResponse);
        }

        protected override void ClearNetDataImp()
        {
            Data = new LDNetPowerCompareDTO();
        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.PlayerCombatPowerResponse)
            {
                PlayerCombatPowerResponse info = PlayerCombatPowerResponse.Parser.ParseFrom(data);
                Data.SetCombatPowerInfo(info);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.PowerCompareUI);
                return info;
            }
            return null;
        }

        public void SendPlayerCombatPowerRequest(long playerId)
        {
            PlayerCombatPowerRequest request = new PlayerCombatPowerRequest
            {
                PlayerId = playerId,
            };
            Send(opcode.PlayerCombatPowerRequest, request);
        }
    }
}