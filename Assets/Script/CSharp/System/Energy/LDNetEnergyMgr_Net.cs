using Google.Protobuf;
using LD.Protocol;

namespace LD
{
    public partial class LDNetEnergyMgr
    {
        public LDNetEnergyDTO Data { get; protected set; } = new LDNetEnergyDTO();
        protected override void InitNetImp()
        {
            AddNetHandler(Protocol.opcode.EnergyInfoResponse);
            AddNetHandlerSymmetry(Protocol.opcode.ItemEnergyBuyRequest, Protocol.opcode.ItemEnergyBuyResponse);
            AddNetHandlerSymmetry(Protocol.opcode.ItemEnergyStorgeBuyRequest, Protocol.opcode.ItemEnergyStorgeBuyResponse);
        }
        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.EnergyInfoResponse)
            {
                EnergyInfoResponse info = EnergyInfoResponse.Parser.ParseFrom(data);
                if (info.EnergyInfo != null)
                {
                    Data.RecoverStartTime = info.EnergyInfo.RecoverStartTime + 1;
                    Data.TodayADTimes = info.EnergyInfo.TodayWatchAdTimes;
                    Data.TodayBuyTimes = info.EnergyInfo.TodayBuyTimes;
                    Data.StorageTimes = info.EnergyInfo.RemainStorageTimes;
                }
                m_IsSyncInited = true;
                return info;
            }
            else if (s2c_msgId == opcode.ItemEnergyBuyResponse)
            {
                ItemEnergyBuyResponse info = ItemEnergyBuyResponse.Parser.ParseFrom(data);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.BuyEnergyUI);
                return info;
            }
            else if (s2c_msgId == opcode.ItemEnergyStorgeBuyResponse)
            {
                ItemEnergyStorgeBuyResponse info = ItemEnergyStorgeBuyResponse.Parser.ParseFrom(data);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.BuyEnergyUI);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.BuyLeftEnergyUI);
                return info;
            }
            return null;
        }

        public void SendUpdateEnergy()
        {
            EnergyInfoRequest req = new EnergyInfoRequest();
            Send(opcode.EnergyInfoRequest, req);
        }

        // 买今天体力
        public void SendBuyTodayEnergy()
        {
            int times = 1;
            if (GetTodayLastBuyTimes() < times)
            {
                Global.gApp.gSystemMgr.gCommonHandleMgr.ShowErrorTips(36);
                return;
            }

            if (!GetBuyEnergyCost(out _, out _))
            {
                Global.gApp.gSystemMgr.gCommonHandleMgr.ShowErrorTips(3);
                return;
            }

            ItemEnergyBuyRequest req = new ItemEnergyBuyRequest();
            req.Times = 1;
            Send(opcode.ItemEnergyBuyRequest, req);
        }

        // 买存储体力
        public void SendBuyStorgeEnergy(int times)
        {
            if (Data.StorageTimes < times)
            {
                Global.gApp.gSystemMgr.gCommonHandleMgr.ShowErrorTips(37);
                return;
            }

            if (!GetBuyEnergyCost(out _, out _))
            {
                Global.gApp.gSystemMgr.gCommonHandleMgr.ShowErrorTips(3);
                return;
            }

            ItemEnergyStorgeBuyRequest req = new ItemEnergyStorgeBuyRequest();
            req.Times = times;
            Send(opcode.ItemEnergyStorgeBuyRequest, req);
        }

        protected override void ClearNetDataImp()
        {
            m_IsSyncInited = false;
            Data.ClearData();
        }

    }
}
