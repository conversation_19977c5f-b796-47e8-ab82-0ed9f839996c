using System.Collections.Generic;
using System.Linq;
using Google.Protobuf;
using LD.Protocol;

namespace LD
{
    public class LDNetTaskUIData : LDUIDataBase
    {
        public int ViewType;
        public int TaskId;
        public int Score;
    }

    public partial class LDNetTaskMgr
    {
        public LDNetTaskDTO Data { get; private set; } = new LDNetTaskDTO();
        public LDNetCommonTaskDTO CommonTaskData { get; private set; } = new LDNetCommonTaskDTO();

        protected override void InitNetImp()
        {
            AddNetHandler(opcode.TaskInfoAllSyncResponse);
            AddNetHandler(opcode.TaskInfoSyncResponse);
            AddNetHandlerSymmetry(opcode.TaskInfoRequest, opcode.TaskInfoResponse);
            AddNetHandlerSymmetry(opcode.ReceiveTaskRewardRequest, opcode.ReceivedTaskRewardResponse);
            AddNetHandlerSymmetry(opcode.ReceivePointRewardRequest, opcode.ReceivedPointRewardResponse);
        }

        protected override void ClearNetDataImp()
        {
            Data = new LDNetTaskDTO();
            CommonTaskData = new LDNetCommonTaskDTO();
        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.TaskInfoAllSyncResponse)
            {
                //登录发送所有数据
                TaskInfoAllSyncResponse taskInfoAllSyncResponse = TaskInfoAllSyncResponse.Parser.ParseFrom(data);
                foreach (TaskInfoAll taskInfoAll in taskInfoAllSyncResponse.Tasks)
                {
                    Data.InitTaskData(taskInfoAll.Task, taskInfoAll.Point, taskInfoAll.PointRewards,
                        taskInfoAll.TaskType);
                }

                RefreshRedTips();
                return taskInfoAllSyncResponse;
            }
            else if (s2c_msgId == opcode.TaskInfoSyncResponse)
            {
                //任务信息同步
                TaskInfoSyncResponse taskInfoSyncResponse = TaskInfoSyncResponse.Parser.ParseFrom(data);
                if (taskInfoSyncResponse.Task != null)
                {
                    int type = 0;
                    foreach (TaskInfo info in taskInfoSyncResponse.Task)
                    {
                        Data.SyncTaskInfo(info);
                        type = info.Type;
                    }

                    Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.QuestUI, type);
                }

                RefreshRedTips();
                return taskInfoSyncResponse;
            }
            else if (s2c_msgId == opcode.TaskInfoResponse)
            {
                // 登录就同步了所有的数据。这个地方应该不用了

                //任务数据回复
                // TaskInfoResponse taskInfoResponse = TaskInfoResponse.Parser.ParseFrom(data);
                // Data.InitTaskData(taskInfoResponse.Task, taskInfoResponse.Point, taskInfoResponse.PointRewards);
                // Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.QuestUI);
                // return taskInfoResponse;

                return null;
            }
            else if (s2c_msgId == opcode.ReceivedTaskRewardResponse)
            {
                //领取任务奖励
                ReceivedTaskRewardResponse receivedTaskRewardResponse =
                    ReceivedTaskRewardResponse.Parser.ParseFrom(data);
                int score = 0;
                foreach (TaskInfo info in receivedTaskRewardResponse.Task)
                {
                    var task = Data.SyncTaskInfo(info);
                    if (task != null)
                    {
                        score += task.CfgInfo.Score;
                    }
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.QuestUI,
                    new LDNetTaskUIData()
                    {
                        TaskId = receivedTaskRewardResponse.Task[0].Id,
                        ViewType = receivedTaskRewardResponse.Task[0].Type,
                        Score = score
                    });
                RefreshRedTips();
                return receivedTaskRewardResponse;
            }
            else if (s2c_msgId == opcode.ReceivedPointRewardResponse)
            {
                //领取任务积分奖励
                ReceivedPointRewardResponse receivedPointRewardResponse =
                    ReceivedPointRewardResponse.Parser.ParseFrom(data);
                Data.SyncTaskPointReward(receivedPointRewardResponse.TaskType,
                    receivedPointRewardResponse.PointRewards);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.QuestUI);
                RefreshRedTips();
                return receivedPointRewardResponse;
            }

            return null;
        }

        #region 发送消息

        //请求任务数据
        public void SendTaskInfoRequest(int taskType)
        {
            //登录的时候就同步了所有的数据，这里应该不需要请求了

            // TaskInfoRequest request = new TaskInfoRequest();
            // request.TaskType = taskType;
            // Send(opcode.TaskInfoRequest, request);
        }

        //请求领取任务奖励
        public void SendReceiveTaskRewardRequest(int taskType)
        {
            ReceiveTaskRewardRequest request = new ReceiveTaskRewardRequest();


            foreach (KeyValuePair<TaskType, LDNetTaskData> taskData in Data.AllTaskData)
            {
                if (taskData.Key == (TaskType)taskType)
                {
                    foreach (KeyValuePair<int, LDNetTaskInfo> taskInfo in taskData.Value.AllTask)
                    {
                        if (taskInfo.Value.Status == TaskStatus.Finish.GetHashCode())
                        {
                            request.Reward.Add(new ReceiveTaskReward() { TaskId = taskInfo.Key, TaskType = taskType });
                        }
                    }
                }
            }

            if (request.Reward.Count > 0)
            {
                Send(opcode.ReceiveTaskRewardRequest, request);
            }
        }

        //请求领取积分奖励
        public void SendReceivePointRewardRequest(int taskType)
        {
            LDNetTaskData taskData = Data.GetTaskDataByType(taskType);
            if (taskData != null)
            {
                List<int> temp = new List<int>();
                for (int i = 0; i < taskData.PointCfgInfos.Count; i++)
                {
                    if (taskData.Point >= taskData.PointCfgInfos[i].RewardScore &&
                        !taskData.PointRewards.Contains(i + 1))
                    {
                        temp.Add(i + 1);
                    }
                }

                if (temp.Count > 0)
                {
                    SendReceivePointRewardRequest(taskType, temp);
                }
            }
        }

        public void SendReceivePointRewardRequest(int taskType, List<int> pointRewards)
        {
            ReceivePointRewardRequest request = new ReceivePointRewardRequest();
            request.TaskType = taskType;
            foreach (int pointReward in pointRewards)
            {
                request.PointRewards.Add(pointReward);
            }

            Send(opcode.ReceivePointRewardRequest, request);
        }

        #endregion
    }
}