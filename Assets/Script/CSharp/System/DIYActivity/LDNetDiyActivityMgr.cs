using System.Collections.Generic;
using LD.Protocol;
using UnityEngine;

namespace LD
{
    public partial class LDNetDiyActivityMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            if (!CheckModuleOpen(LDSystemEnum.DiyActivity, isShowLockTips))
                return false;

            var cfgs = OpenServerActivity.Data.items;
            bool isOpen = false; //IsActivityOpen(1) || IsActivityOpen(6);
            foreach (var cfg in cfgs)
            {
                if (cfg.activityType == LDNetActivityType.DIYActivityType)
                {
                    if (IsActivityOpen(cfg.id))
                    {
                        isOpen = true;
                        break;
                    }
                }
            }

            var cfgs2 = OpenTimeActivity.Data.items;
            foreach (var cfg in cfgs2)
            {
                if (cfg.activityType == LDNetActivityType.DIYActivityType)
                {
                    if (IsActivityOpen(cfg.id))
                    {
                        isOpen = true;
                        break;
                    }
                }
            }

            if (!isOpen && isShowLockTips)
            {
                Global.gApp.gToastMgr.ShowGameTips(95002);
            }

            return isOpen;
        }

        public bool IsActivityOpen(int activityId)
        {
            bool isOpen = false;
            var activityInfo = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.ActivityInfo(activityId);
            if (activityInfo != null && activityInfo.IsOpen())
            {
                isOpen = true;
            }

            return isOpen;
        }

        public bool IsActivityFuncOpen(int activityId)
        {
            var cfg = Global.gApp.gSystemMgr.gDiyActivityMgr.GetDiyActivityCfg(activityId);
            if (cfg != null)
            {
                switch (cfg.ActivityType)
                {
                    case LDNetActivityType.DIYActivityType:
                        if (!Global.gApp.gSystemMgr.CheckModuleOpen(LDSystemEnum.DiyActivity))
                        {
                            return false;
                        }

                        break;
                    case LDNetActivityType.UAVActivityType:
                        if (!Global.gApp.gSystemMgr.CheckModuleOpen(LDSystemEnum.UAVActivity))
                        {
                            return false;
                        }

                        break;
                }
            }

            return true;
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (!Global.gApp.gSystemMgr.gDiyActivityMgr.IsUnlock(false))
            {
                return LDRedTipsState.None;
            }

            var activityInfo = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetActivityInfo(LDNetActivityType.DIYActivityType);
            if (activityInfo == null)
                return LDRedTipsState.None;
            if (!activityInfo.IsOpen())
            {
                return LDRedTipsState.None;
            }

            bool isShowGainEntrance = Global.gApp.gSystemMgr.gActivityPreviewMgr.GetIsShowGainEntrance(activityInfo.ActivityId, (int)ActivityPreviewType.OpenServerActivity);
            if (isShowGainEntrance)
            {
                return LDRedTipsState.RedPoint;
            }

            LDRedTipsState taskRedPoint = CheckTaskRed(activityInfo.ActivityId);
            if (taskRedPoint != LDRedTipsState.None)
                return taskRedPoint;

            LDRedTipsState shopRed = CheckShopRed(activityInfo.ActivityId);
            if (shopRed != LDRedTipsState.None)
                return shopRed;

            return LDRedTipsState.None;
        }

        public LDRedTipsState CheckTaskRed(int activityID)
        {
            // var tasks = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.m_DiyActivityTasks;
            (int, int, List<DiyActivityTasksItem>) result = GetTaskList(activityID);
            var taskList = result.Item3;
            foreach (var cfg in taskList)
            {
                var info = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetDiyActivityTaskInfo(cfg.id);
                if (info != null)
                {
                    if (info.Status == DiyActivityTaskStatus.Finish.GetHashCode())
                        return LDRedTipsState.RedPoint;
                }
            }

            return LDRedTipsState.None;
        }

        public LDRedTipsState CheckShopRed(int activityID)
        {
            DiyActivityPackItem[] items = DiyActivityPack.Data.items;

            for (int i = 0; i < items.Length; i++)
            {
                DiyActivityPackItem cfg = items[i];
                if (cfg.activityID != activityID)
                    continue;
                if (cfg.ADid > 0)
                {
                    var adCfg = AD.Data.Get(cfg.ADid);
                    if (adCfg != null)
                    {
                        var adInfo = Global.gApp.gSystemMgr.gAdvertiseMgr.Data.GetAdvertiseInfo(adCfg.adPlacementld);
                        var adcfg = AD.Data.Get(cfg.ADid);
                        if (adInfo == null || adInfo.AdvertiseCount < adcfg.adCount)
                        {
                            return LDRedTipsState.RedPoint;
                        }
                    }
                }
            }

            return LDRedTipsState.None;
        }

        public (int, int, List<DiyActivityTasksItem>) GetTaskList(int activityId)
        {
            DiyActivityTasksItem[] items = DiyActivityTasks.Data.items;

            var activityData = Global.gApp.gSystemMgr.gDiyActivityMgr.GetDiyActivityCfg(activityId);
            int loop = 400;
            if (activityData != null)
            {
                loop = activityData.LoopRound;
                if (loop == 0)
                {
                    loop = 1;
                }
            }

            Dictionary<int, List<DiyActivityTasksItem>> tempDict = new();
            int maxLun = 0;
            foreach (DiyActivityTasksItem item in items)
            {
                if (item.activityID != activityId)
                    continue;

                var lines = LDCommonTools.Split(item.type);
                int progress = LDParseTools.IntParse(lines[1]);

                int lun = Mathf.CeilToInt((float)progress / loop) - 1;
                if (lun > maxLun)
                {
                    maxLun = lun;
                }

                if (!tempDict.ContainsKey(lun))
                {
                    tempDict.Add(lun, new List<DiyActivityTasksItem>() { item });
                }
                else
                {
                    tempDict[lun].Add(item);
                }
            }

            for (int i = 0; i <= maxLun; i++)
            {
                if (tempDict.TryGetValue(i, out var list))
                {
                    foreach (DiyActivityTasksItem item in list)
                    {
                        var info = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetDiyActivityTaskInfo(item.id);
                        if (info != null && info.Status != DiyActivityTaskStatus.Reward.GetHashCode())
                        {
                            return (i + 1, tempDict.Count, list);
                        }
                    }
                }
            }

            if (maxLun <= 0)
            {
                return (maxLun + 1, tempDict.Count, new List<DiyActivityTasksItem>());
            }

            return (maxLun + 1, tempDict.Count, tempDict[maxLun]);
        }

        public DiyActivityCfg GetDiyActivityCfg(int activityId)
        {
            if (activityId <= 1000)
            {
                var openServerActivityCfg = OpenServerActivity.Data.Get(activityId);
                DiyActivityCfg cfg = new DiyActivityCfg(activityId, openServerActivityCfg.activityType, openServerActivityCfg.openDay, openServerActivityCfg.activityTime,
                    openServerActivityCfg.promotionalPoster, openServerActivityCfg.displayRewards, openServerActivityCfg.loopRound, openServerActivityCfg.severOpenDayBegin,
                    openServerActivityCfg.severOpenDayEnd, openServerActivityCfg.shopDelay);
                return cfg;
            }
            else
            {
                var openTimeActivityCfg = OpenTimeActivity.Data.Get(activityId);
                DiyActivityCfg cfg = new DiyActivityCfg(activityId, openTimeActivityCfg.activityType, 0, openTimeActivityCfg.activityTime,
                    openTimeActivityCfg.promotionalPoster, openTimeActivityCfg.displayRewards, openTimeActivityCfg.loopRound, "",
                    "", openTimeActivityCfg.shopDelay);
                return cfg;
            }
        }
    }

    public class DiyActivityCfg
    {
        public int ActivityId;
        public string ActivityType;
        public int OpenDay;
        public int ActivityTime;
        public string PromotionalPoster;
        public string[] DisplayRewards;
        public int LoopRound;
        public string SeverOpenDayBegin;
        public string SeverOpenDayEnd;
        public int ShopDelay;

        public DiyActivityCfg(int activityId, string activityType, int openDay, int activityTime, string promotionalPoster, string[] displayRewards, int loopRound, string severOpenDayBegin,
            string severOpenDayEnd, int shopDelay)
        {
            ActivityId = activityId;
            ActivityType = activityType;
            OpenDay = openDay;
            ActivityTime = activityTime;
            PromotionalPoster = promotionalPoster;
            DisplayRewards = displayRewards;
            LoopRound = loopRound;
            SeverOpenDayBegin = severOpenDayBegin;
            SeverOpenDayEnd = severOpenDayEnd;
            ShopDelay = shopDelay;
        }
    }
}