using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDMechaComboUIData
    {
        public string Icon { get; private set; }
        public int Name { get; private set; }
        public int Desc { get; private set; }
        public int Pos { get; private set; }

        public LDMechaComboUIData(string icon, int name, int desc, int pos)
        {
            Icon = icon;
            Name = name;
            Desc = desc;
            Pos = pos;
        }
    }

    public partial class LDNetMechaMgr : LDNetDataMgr
    {
        private int m_ClickLvUpBtnTimes;

        protected override void InitImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.Mecha, isShowLockTips);
        }

        // 获取当前出战机甲
        public LDNetMechaDTOItem GetCurBattleMecha()
        {
            foreach (LDNetMechaDTOItem _data in Data.MechaDatas)
            {
                if (_data.IsFightState())
                {
                    return _data;
                }
            }

            Global.LogError($"未找到上阵状态的机甲");
            return null;
        }

        public List<LDNetMechaDTOItem> GetAllMecha()
        {
            return Data.MechaDatas;
        }

        // 获取默认机甲ID
        public int GetDefaultMechaId()
        {
            return GlobalCfg.Data.Get(10002).valueInt;
        }

        // 获取机甲等级
        public int GetCurMechaLevel()
        {
            LDNetMechaDTOItem mecha = GetCurBattleMecha();
            return GetCurMechaLevel(mecha);
        }

        // 获取机甲等级
        public int GetCurMechaLevel(LDNetMechaDTOItem mecha)
        {
            int level = 1;

            if (mecha == null)
            {
                return level;
            }

            MechaQualityItem quaCfg = MechaQuality.Data.Get(mecha.Quality);
            if (quaCfg == null)
            {
                return level;
            }

            level = Mathf.Min(quaCfg.lvLimit, Data.MechaGlobalInfo.Level);
            return level;
        }

        // 获取机甲数据  by id
        public LDNetMechaDTOItem GetMechaData(int mechaId, bool canNull = false)
        {
            foreach (LDNetMechaDTOItem _data in Data.MechaDatas)
            {
                if (_data.MechaId == mechaId)
                {
                    return _data;
                }
            }

            if (!canNull)
            {
                Global.LogError($"机甲数据未找到   mechaId = {mechaId}");
            }

            return null;
        }

        // 获取机甲解锁相关数据
        public bool GetUnlockCost(int mechaId, out long ownedNum, out long num)
        {
            MechaItem mechaCfg = Mecha.Data.Get(mechaId);

            LDCommonItem costItem = new LDCommonItem(mechaCfg.mechacost);
            ownedNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(costItem.Id);
            num = costItem.Num;

            bool isEnough = ownedNum >= num;
            return isEnough;
        }

        // 获取机甲升级消耗相关数据
        public bool GetLevelUpCost(out long ownedNum, out long num)
        {
            MechaLevelItem lvCfg = MechaLevel.Data.Get(GetCurMechaLevel());

            LDCommonItem costItem = new LDCommonItem(lvCfg.cost);
            ownedNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(costItem.Id);
            num = costItem.Num;

            bool isEnough = ownedNum >= num;
            return isEnough;
        }

        // 获取机甲升阶消耗相关数据
        public bool GetQuaUpCost(LDNetMechaDTOItem mecha, out long ownedNum, out long num)
        {
            MechaItem mechaCfg = Mecha.Data.Get(mecha.MechaId);
            MechaQualityItem quaCfg = MechaQuality.Data.Get(mecha.Quality);

            int costId = mechaCfg.CorrespondingProps;
            num = quaCfg.itemCost1;
            ownedNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(costId);

            bool isEnough = ownedNum >= num;
            return isEnough;
        }

        // 获取机甲品质对应的属性加成数据
        public List<LDAttrAddition> GetQualityAttrAdditions(int mechaId, MechaQualityItem quaCfg)
        {
            MechaItem cfg = Mecha.Data.Get(mechaId);
            List<LDAttrAddition> additions = new List<LDAttrAddition>();
            int coef = 10000;
            for (int i = 0; i < quaCfg.Attr.Length; i++)
            {
                if (cfg.AscensionCoefficient.Length > i)
                {
                    coef = cfg.AscensionCoefficient[i];
                }
                else
                {
                    Global.LogError($"MechaQuality 属性系数 与 属性加成 表参数数量不一致");
                }

                LDAttrAddition lDAttrAddition = new LDAttrAddition(quaCfg.Attr[i], coef);
                additions.Add(lDAttrAddition);
            }

            return additions;
        }

        // 获取机甲升阶技能配置信息
        public Dictionary<int, MechaQualitySkillItem> GetMechaQuaSkillCfg(int mechaId)
        {
            Dictionary<int, MechaQualitySkillItem> skillDic = new Dictionary<int, MechaQualitySkillItem>();
            MechaItem mechaCfg = Mecha.Data.Get(mechaId);
            foreach (int ueID in mechaCfg.UpgradeEffect)
            {
                if (MechaQualitySkill.Data.TryGet(ueID, out MechaQualitySkillItem mqsCfg, false))
                {
                    if (!skillDic.ContainsKey(mqsCfg.Requirement))
                    {
                        skillDic.Add(mqsCfg.Requirement, mqsCfg);
                    }
                    else
                    {
                        Global.LogError($"MechaQualitySkill表品质配置重复 ID = {ueID}  机甲ID = {mechaId} 品质 = {mqsCfg.Requirement}");
                    }
                }
            }

            return skillDic;
        }

        // 获取机甲道具品质
        public int GetMechaItemQuality(int mechaId, bool ignoreSelfQuality = false)
        {
            if (ignoreSelfQuality)
            {
                MechaItem mechaCfg = Mecha.Data.Get(mechaId);
                return mechaCfg.quality;
            }

            if (IsMechaUnlocked(mechaId))
            {
                LDNetMechaDTOItem mechaData = GetMechaData(mechaId);
                return mechaData.Quality;
            }
            else
            {
                MechaItem mechaCfg = Mecha.Data.Get(mechaId);
                return mechaCfg.quality;
            }
        }

        // 获取解锁碎片数量  或者 升阶碎片数量
        public long GetQuaChangePiceNeedCount(int mechaId)
        {
            if (!IsMechaUnlocked(mechaId))
            {
                GetUnlockCost(mechaId, out _, out long needNum);
                return needNum;
            }
            else
            {
                GetQuaUpCost(GetMechaData(mechaId), out _, out long needNum);
                return needNum;
            }
        }

        // 通过碎片id获取机甲数据
        public MechaItem GetMechaCfgItemByPice(int piceId)
        {
            foreach (MechaItem item in Mecha.Data.items)
            {
                if (item.CorrespondingProps == piceId)
                {
                    return item;
                }
            }

            Debug.LogError("Wrong pice mecha Id " + piceId);
            return null;
        }

        // 获取机甲品质最大上限
        public int GetMechaQuaMax()
        {
            return MechaQuality.Data.items.Length;
        }

        // 连携词条
        public List<LDMechaComboUIData> GetComboCitiaos(int mechaId)
        {
            List<LDMechaComboUIData> m_ComboDatas = new List<LDMechaComboUIData>();
            MechaItem mechaCfg = Mecha.Data.Get(mechaId);

            if (mechaCfg.EntryPhrases > 0)
            {
                int citiaoId = GetWeaponComboCitiaoInGroup(mechaCfg.EntryPhrases);
                if (citiaoId > 0)
                {
                    // 配置在机甲上的连携
                    CitiaoItem citiaoCfg = Citiao.Data.Get(citiaoId);
                    if (citiaoCfg != null && citiaoCfg.initialValid != 2)
                    {
                        int partId = citiaoCfg.optionalCondition[1];
                        DIYPartCfgItem partCfg = DIYPartCfg.Data.Get(partId);
                        string iconPath = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartLocalBigIcon(partCfg.id);
                        LDMechaComboUIData comboData = new LDMechaComboUIData(iconPath, partCfg.name, citiaoCfg.citiaoDesc, partCfg.pos);
                        m_ComboDatas.Add(comboData);
                    }
                }
            }

            List<int> partCitiaoIds = new List<int>();
            foreach (int partId in mechaCfg.optionalWeapon)
            {
                partCitiaoIds.Clear();
                DIYPartCfgItem partCfg = DIYPartCfg.Data.Get(partId);
                if (partCfg.pos == 1 && partCfg.EntryPhrases > 0)
                {
                    int citiaoId = GetWeaponComboCitiaoInGroup(partCfg.EntryPhrases);
                    if (citiaoId > 0)
                    {
                        partCitiaoIds.Add(citiaoId);
                    }
                }
                else if (partCfg.pos == 2 && partCfg.ExclusiveEntry.Length > 1)
                {
                    if (partCfg.ExclusiveEntry[0] == mechaCfg.id)
                    {
                        for (int i = 1; i < partCfg.ExclusiveEntry.Length; i++)
                        {
                            partCitiaoIds.Add(partCfg.ExclusiveEntry[i]);
                        }
                    }
                }

                foreach (int citiaoId in partCitiaoIds)
                {
                    CitiaoItem citiaoCfg = Citiao.Data.Get(citiaoId);
                    if (citiaoCfg != null && citiaoCfg.optionalCondition.Length > 1 && citiaoCfg.optionalCondition[1] == mechaCfg.id && citiaoCfg.initialValid != 2)
                    {
                        string iconPath = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartLocalBigIcon(partCfg.id);
                        LDMechaComboUIData comboData = new LDMechaComboUIData(iconPath, partCfg.name, citiaoCfg.citiaoDesc, partCfg.pos);
                        m_ComboDatas.Add(comboData);
                    }
                }
            }

            return m_ComboDatas;
        }

        private int GetWeaponComboCitiaoInGroup(int groupId)
        {
            CitiaoGroupItem groupCfg = CitiaoGroup.Data.Get(groupId);
            foreach (int id in groupCfg.citiaoitems)
            {
                CitiaoItem citiaoCfg = Citiao.Data.Get(id);
                if (citiaoCfg.optionalCondition.Length > 0)
                {
                    return id; // 武器词条取第一个
                }
            }

            return 0;
        }

        // 检测 单击升级 长按提示
        public void CheckLvUpLongPressTips()
        {
            m_ClickLvUpBtnTimes++;
            if (m_ClickLvUpBtnTimes == 5) // 第5次检测一下就行了 不重要
            {
                long time = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, LDLocalDataKeys.Mecha_LvBtnLongPressTips, 0);
                if (DateTimeUtil.SameDay(time, DateTimeUtil.GetServerTime()))
                {
                    return;
                }

                int val = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.Mecha_LvBtnLongPressTips_NoTips, 0);
                if (val <= 0)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<LongPressTipsUI>(LDUICfg.LongPressTipsUI).SetLoadedCall(ui => { ui?.RefreshUI(LongPressTipsUI.Type_MechaLvUp); });
                    Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, LDLocalDataKeys.Mecha_LvBtnLongPressTips, DateTimeUtil.GetServerTime());
                }
            }
        }

        // 存储单机升级长按提示
        public void SaveLvUpBtnLongPressState(bool enable)
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.Mecha_LvBtnLongPressTips_NoTips, enable ? 1 : 0);
        }

        // 获取机甲-皮肤数据
        public Dictionary<int, int> GetAllMechaSkinData()
        {
            Dictionary<int, int> dic = new Dictionary<int, int>();
            foreach (LDNetMechaDTOItem mecha in GetAllMecha())
            {
                dic.Add(mecha.MechaId, mecha.Skin);
            }

            return dic;
        }

        // 获取机甲模型路径
        public string GetMechaPrefabPath(int mechaId, int skinId, int HidBodySkinId)
        {
            if (skinId > 0 && HidBodySkinId <= 0)
            {
                if (Skin.Data.TryGet(skinId, out SkinItem skinCfg, false))
                {
                    return skinCfg.skinPrefab;
                }
                else
                {
                    Global.LogError($"皮肤ID不存在 : {skinId}");
                }
            }

            if (Mecha.Data.TryGet(mechaId, out MechaItem mechaCfg))
            {
                return mechaCfg.Model;
            }

            return string.Empty;
        }

        public string GetMechaPrefabPath(int mechaId)
        {
            int skinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(mechaId);
            int HideSkine = Global.gApp.gSystemMgr.gMechaSkinDataMgr.ISMechaSkineHide(skinId) ? 1 : 0;
            return GetMechaPrefabPath(mechaId, skinId, HideSkine);
        }

        // 获取机甲图标（211*215）
        public string GetMechaIcon(int mechaId, int defaultSkinId = -1, bool IgnoreSkin = false)
        {
            int skinId = defaultSkinId > 0 ? defaultSkinId : Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(mechaId);
            if (skinId > 0 && !IgnoreSkin)
            {
                if (Skin.Data.TryGet(skinId, out SkinItem skinCfg, false))
                {
                    return skinCfg.icon;
                }
                else
                {
                    Global.LogError($"皮肤ID不存在 : {skinId}");
                }
            }

            if (Mecha.Data.TryGet(mechaId, out MechaItem mechaCfg))
            {
                return mechaCfg.icon;
            }

            return string.Empty;
        }

        // 获取助战/头像图（135*135）
        public string GetMechaAssistIcon(int mechaId, bool hideSkin)
        {
            int skinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(mechaId);
            return GetMechaAssistIcon(mechaId, skinId, hideSkin);
        }

        public string GetMechaAssistIcon(int mechaId, int skinId, bool hideSkin)
        {
            if (skinId > 0 && !hideSkin)
            {
                if (Skin.Data.TryGet(skinId, out SkinItem skinCfg, false))
                {
                    return skinCfg.assistSkinIcon;
                }
                else
                {
                    Global.LogError($"皮肤ID不存在 : {skinId}");
                }
            }

            if (Mecha.Data.TryGet(mechaId, out MechaItem mechaCfg))
            {
                return mechaCfg.assistMechaIcon;
            }

            return string.Empty;
        }

        // 获取列表图（318*224）
        public string GetMechaBG(int mechaId)
        {
            int skinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(mechaId);
            bool hidSkin = Global.gApp.gSystemMgr.gMechaSkinDataMgr.ISMechaSkineHide(skinId);
            if (skinId > 0 && !hidSkin)
            {
                if (Skin.Data.TryGet(skinId, out SkinItem skinCfg, false))
                {
                    return skinCfg.mechaBG;
                }
                else
                {
                    Global.LogError($"皮肤ID不存在 : {skinId}");
                }
            }

            if (Mecha.Data.TryGet(mechaId, out MechaItem mechaCfg))
            {
                return mechaCfg.mechaBG;
            }

            return string.Empty;
        }

        // 获取机甲词条图（888*888）
        public string GetMechaBigIcon(int mechaId)
        {
            int skinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(mechaId);
            bool hideSkine = Global.gApp.gSystemMgr.gMechaSkinDataMgr.ISMechaSkineHide(skinId);
            if (skinId > 0 && !hideSkine)
            {
                if (Skin.Data.TryGet(skinId, out SkinItem skinCfg, false))
                {
                    return skinCfg.bigIcon;
                }
                else
                {
                    Global.LogError($"皮肤ID不存在 : {skinId}");
                }
            }

            if (Mecha.Data.TryGet(mechaId, out MechaItem mechaCfg))
            {
                return mechaCfg.bigIcon;
            }

            return string.Empty;
        }

        public string GetMechaBigIcon(int mechaId, int skinId, bool hideSkin)
        {
            if (skinId > 0 && !hideSkin)
            {
                if (Skin.Data.TryGet(skinId, out SkinItem skinCfg, false))
                {
                    return skinCfg.bigIcon;
                }
                else
                {
                    Global.LogError($"皮肤ID不存在 : {skinId}");
                }
            }

            if (Mecha.Data.TryGet(mechaId, out MechaItem mechaCfg))
            {
                return mechaCfg.bigIcon;
            }

            return string.Empty;
        }

        // 清除机甲new状态
        public bool ClearMechaNewState(int mechaId)
        {
            if (IsNewMecha(mechaId))
            {
                Global.gApp.gSystemMgr.gNetClientDataMgr.ChangeGenerateClientData(GeneralClientDataKey.NewMecha + mechaId.ToString(), "1");
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_MechaMain_List);
                return true;
            }
            else
            {
                return false;
            }
        }

        // 获取升阶红点状态
        public LDRedTipsState GetUpgradeRedState(int mechaId)
        {
            if (CanUpgrade(mechaId))
            {
                return LDRedTipsState.RedPoint;
            }

            return LDRedTipsState.None;
        }

        // 获取新机甲红点状态
        public LDRedTipsState GetNewMechaState(int mechaId)
        {
            if (IsNewMecha(mechaId))
            {
                return LDRedTipsState.RedPoint;
            }

            return LDRedTipsState.None;
        }

        // 红点
        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (sys == LDSystemEnum.Custom_MechaMain_List)
            {
                if (HasMechaCanBeUnlocked())
                {
                    return LDRedTipsState.RedPoint;
                }

                if (HasNewMecha())
                {
                    return LDRedTipsState.RedPoint;
                }

                if (HasMechaCanUpgrade() && !CanUpgrade(GetCurBattleMecha().MechaId))
                {
                    return LDRedTipsState.RedPoint;
                }

                foreach (LDNetMechaDTOItem mecha in GetAllMecha())
                {
                    LDRedTipsState state = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetRedStateForMechaSkin(mecha.MechaId);
                    if (state != LDRedTipsState.None)
                    {
                        return state;
                    }
                }
            }

            if (sys == LDSystemEnum.Custom_MechaMain_LevelUp)
            {
                if (!IsMaxLevel() && GetLevelUpCost(out _, out _))
                {
                    return LDRedTipsState.Arrow;
                }
            }

            if (sys == LDSystemEnum.Custom_MechaMain_Upgrade)
            {
                if (CanUpgrade(GetCurBattleMecha().MechaId))
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            if (sys == LDSystemEnum.Custom_Mecha_Upgrade_Pack)
            {
                string newTag = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.MechaUpgradePackRed);
                if (!string.IsNullOrEmpty(newTag))
                {
                    return LDRedTipsState.None; // 这个红点  只生效一次
                }

                LDNetMechaDTOItem mecha = GetCurBattleMecha();
                if (mecha.Quality >= LDQualityType.Max)
                {
                    return LDRedTipsState.None;
                }

                if (CanUpgradeByPack(mecha))
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            return LDRedTipsState.None;
        }


        // 判断机甲是否满级
        public bool IsMaxLevel()
        {
            LDNetMechaDTOItem mecha = GetCurBattleMecha();
            MechaQualityItem quaCfg = MechaQuality.Data.Get(mecha.Quality);
            return GetCurMechaLevel() >= quaCfg.lvLimit;
        }

        // 判断机甲是否解锁
        public bool IsMechaUnlocked(int mechaId)
        {
            return GetMechaData(mechaId, true) != null;
        }

        // 判断是否有机甲可以解锁
        public bool HasMechaCanBeUnlocked()
        {
            MechaItem[] mechaItems = Mecha.Data.items;
            foreach (MechaItem item in mechaItems)
            {
                if (!IsMechaUnlocked(item.id) && GetUnlockCost(item.id, out _, out _))
                {
                    return true;
                }
            }

            return false;
        }

        // 判断是否可以升阶
        public bool CanUpgrade(int mechaId)
        {
            LDNetMechaDTOItem mecha = GetMechaData(mechaId, true);
            if (mecha == null)
            {
                return false;
            }

            return mecha.Quality < LDQualityType.Max && GetQuaUpCost(mecha, out _, out _);
        }

        // 判断是否有机甲可以升阶
        public bool HasMechaCanUpgrade()
        {
            foreach (LDNetMechaDTOItem _mecha in Data.MechaDatas)
            {
                if (_mecha.Quality < LDQualityType.Max && GetQuaUpCost(_mecha, out _, out _))
                {
                    return true;
                }
            }

            return false;
        }

        // 是否可以用礼包升阶
        public bool CanUpgradeByPack(LDNetMechaDTOItem mecha, bool isOpenUI = false)
        {
            // if (mecha.Quality >= LDQualityType.Max)
            // {
            //     return false;
            // }

            long lackCount = GetUpgradeLackCount(mecha);
            if (lackCount <= 0)
            {
                return false;
            }

            MechaItem mechaCfg = Mecha.Data.Get(mecha.MechaId);

            foreach (int itemId in mechaCfg.upgradePack)
            {
                long itemCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(itemId);
                if (itemCount >= lackCount)
                {
                    if (isOpenUI)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<ChooseBox_Use>(LDUICfg.ChooseBox_Use).SetLoadedCall(ui =>
                        {
                            LDCommonItem commonItem = new LDCommonItem(LDCommonType.Item, itemId, itemCount);
                            ui?.InitData(commonItem);
                            ui?.SetSelectMechaInfo(mechaCfg.CorrespondingProps, lackCount);
                        });
                    }

                    return true;
                }
            }

            return false;
        }

        // 升阶缺少碎片数量
        public long GetUpgradeLackCount(LDNetMechaDTOItem mecha)
        {
            GetQuaUpCost(mecha, out long ownedNum, out long needNum);
            return needNum - ownedNum;
        }

        // 升阶需要的礼包数量
        public LDCommonItem GetPackCount(LDNetMechaDTOItem mecha)
        {
            int itemId = 0;
            long packCount = 0;

            MechaItem mechaCfg = Mecha.Data.Get(mecha.MechaId);
            if (mechaCfg != null)
            {
                foreach (int id in mechaCfg.upgradePack)
                {
                    long itemCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(id);
                    if (itemId == 0 || itemCount > packCount)
                    {
                        itemId = id;
                        packCount = itemCount;
                    }
                }
            }

            if (itemId == 0)
            {
                return null;
            }

            return new LDCommonItem(LDCommonType.Item, itemId, packCount);
        }


        // 是否是新机甲
        public bool IsNewMecha(int mechaId)
        {
            if (mechaId == GetDefaultMechaId()) // 默认机甲不算新的
            {
                return false;
            }

            if (IsMechaUnlocked(mechaId))
            {
                string newTag = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.NewMecha + mechaId.ToString());
                return string.IsNullOrEmpty(newTag);
            }
            else
            {
                return false;
            }
        }

        // 是否有新机甲
        public bool HasNewMecha()
        {
            foreach (LDNetMechaDTOItem _mecha in Data.MechaDatas)
            {
                if (IsNewMecha(_mecha.MechaId))
                {
                    return true;
                }
            }

            return false;
        }

        protected override void DestroyImp()
        {
        }
    }
}