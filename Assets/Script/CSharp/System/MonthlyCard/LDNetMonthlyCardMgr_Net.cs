using Google.Protobuf;
using LD.Protocol;

namespace LD
{
    public partial class LDNetMonthlyCardMgr
    {
        public LDNetMonthlyCardDTO Data { get; private set; } = new();

        protected override void InitNetImp()
        {
            AddNetHandler(opcode.MonthlyCardDailyInfoSyncResponse);
            AddNetHandlerSymmetry(opcode.MonthlyCardBuyRequest, opcode.MonthlyCardBuyResponse);
        }

        protected override void ClearNetDataImp()
        {
            Data = new LDNetMonthlyCardDTO();
        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.MonthlyCardDailyInfoSyncResponse)
            {
                MonthlyCardDailyInfoSyncResponse dailyInfoSyncResponse = MonthlyCardDailyInfoSyncResponse.Parser.ParseFrom(data);
                Data.Ids.Clear();
                foreach (int id in dailyInfoSyncResponse.Ids)
                {
                    Data.Ids.Add(id);
                }
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MonthlyCardUI);
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.MonthlyCard);
                return dailyInfoSyncResponse;
            }
            else if (s2c_msgId == opcode.MonthlyCardBuyResponse)
            {
                MonthlyCardBuyResponse cardBuyResponse = MonthlyCardBuyResponse.Parser.ParseFrom(data);
                Data.Ids.Add(cardBuyResponse.Id);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MonthlyCardUI);
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.MonthlyCard);
                return cardBuyResponse;
            }


            return null;
        }

        public void SendMonthlyCardBuyRequest(int id)
        {
            MonthlyCardBuyRequest request = new MonthlyCardBuyRequest();
            request.Id = id;
            Send(opcode.MonthlyCardBuyRequest, request);
        }
    }
}