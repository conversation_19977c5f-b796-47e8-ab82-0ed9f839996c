using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDDiyPartSkinInfo
    {
        public int SkinId;
        public int Star;
        public int Count;
        public bool Hide;

        public LDDiyPartSkinInfo()
        {
        }

        public LDDiyPartSkinInfo(DiyPartSkinInfo info)
        {
            if (Skin.Data.TryGet(info.SkinId, out _, false)) // 兼容旧数据
            {
                SkinId = info.SkinId;
            }
            Star = info.Star;
            Count = info.Count;
            Hide = info.Hide;
        }
    }


    public class LDNetMechaPartSkinDTO
    {
        public Dictionary<int, LDDiyPartSkinInfo> SkinDatas = new Dictionary<int, LDDiyPartSkinInfo>();

        public void SetPartSkinData(LDDiyPartSkinInfo skinInfo)
        {
            if (skinInfo == null)
            {
                return;
            }

            SkinDatas[skinInfo.SkinId] = skinInfo;
        }

        public LDDiyPartSkinInfo GetPartSkinData(int skinId)
        {
            if (!SkinDatas.ContainsKey(skinId))
            {
                LDDiyPartSkinInfo skinInfo = new LDDiyPartSkinInfo();
                skinInfo.SkinId = skinId;
                SetPartSkinData(skinInfo);
            }

            return SkinDatas[skinId];
        } 
        public void FreshPartSkinData(int skinId, bool hide)
        {
            if (SkinDatas.ContainsKey(skinId))
            {
                SkinDatas[skinId].Hide = hide;
            }
        }
        public bool ISPartSkineHide(int skinId)
        {
            if (SkinDatas.ContainsKey(skinId))
            {
                return SkinDatas[skinId].Hide;
            }
            else
            {
                return false;
            }
        }

    }
}