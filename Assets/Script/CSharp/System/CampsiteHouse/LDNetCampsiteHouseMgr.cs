namespace LD
{
    public partial class LDNetCampsiteHouseMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.CampHouse, isShowLockTips);
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            var campsiteMgr = Global.gApp.gSystemMgr.gCampsiteMgr;
            LDRedTipsState redState = LDRedTipsState.None;
            if (sys == LDSystemEnum.Custom_Camp_Survivor)
            {
                return campsiteMgr.GetSurvivorRedTipsState();
            }

            if (sys == LDSystemEnum.Custom_Camp_Driver)
            {
                return campsiteMgr.GetDriverRedTipsState();
            }


            // if (sys == LDSystemEnum.Custom_Camp_Formation)
            // {
            //     return campsiteMgr.GetDriverFormationRedTipsState();
            // }

            return LDRedTipsState.None;
        }
    }
}