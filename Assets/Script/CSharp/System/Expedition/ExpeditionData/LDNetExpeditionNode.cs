using System.Collections.Generic;
using Google.Protobuf.Collections;
using LD.Protocol;

namespace LD
{
    public class LDNetExpeditionNode
    {
        public int NodeIndex;
        public int NodeId; //节点Id
        public LDNetExpeditionEvent Event; //事件数据
        public int State; //当前状态  ExpeditionNodeState
        public int LotteryCount; // 当前抽奖了几次
        public int SelectMissionId; // 选择的boss难度关卡id

        public NodePlayStage ClientTriggerStage = NodePlayStage.None; //节点触发状态，触发前，触发，触发后

        public List<LDNetExpeditionEffect> m_TempEffect = new();
        public List<LDNetExpeditionBox> m_TempBox = new();

        public Queue<LDNetExpeditionEffect> m_PlayEffect = new();
        public Dictionary<int, List<ExpeditionEffectTrigger>> m_EffectTrigger = new();

        /// <summary>
        /// 加倍的效果
        /// </summary>
        public Dictionary<int, List<LDNetExpeditionEffect>> m_TempDoubleEffects = new();

        public Queue<LDNetExpeditionEffectTriggerRtnData> m_PlayExtraData;

        public long m_TempHp = 0;

        public static LDNetExpeditionNode Convert(LD.Protocol.ExpeditionNode expeditionNode)
        {
            LDNetExpeditionNode node = new LDNetExpeditionNode();
            node.NodeId = expeditionNode.NodeId;
            node.Event = LDNetExpeditionEvent.Convert(expeditionNode.Event);
            node.State = expeditionNode.State;
            node.LotteryCount = expeditionNode.LotteryCount;
            node.SelectMissionId = expeditionNode.SelectMissionId;
            return node;
        }

        public void SyncNode(LDNetExpeditionNode node)
        {
            if (NodeId != node.NodeId)
                return;
            Event = node.Event;
            this.State = node.State;
            this.LotteryCount = node.LotteryCount;
            this.SelectMissionId = node.SelectMissionId;
            this.ClientTriggerStage = node.ClientTriggerStage;
            this.m_TempEffect.Clear();
            this.m_TempBox.Clear();
            this.m_TempEffect.AddRange(node.m_TempEffect);
            this.m_TempBox.AddRange(node.m_TempBox);
            
            this.m_PlayEffect.Clear();
            if (node.m_PlayEffect != null)
            {
                this.m_PlayEffect = new Queue<LDNetExpeditionEffect>(node.m_PlayEffect);
            }

            this.m_EffectTrigger.Clear();
            if (node.m_EffectTrigger != null)
            {
                foreach (KeyValuePair<int, List<ExpeditionEffectTrigger>> pair in node.m_EffectTrigger)
                {
                    if (!m_EffectTrigger.ContainsKey(pair.Key))
                    {
                        m_EffectTrigger.Add(pair.Key, new List<ExpeditionEffectTrigger>());
                    }

                    m_EffectTrigger[pair.Key].AddRange(pair.Value);
                }
            }

            this.m_TempDoubleEffects.Clear();
            if (node.m_TempDoubleEffects != null)
            {
                foreach (KeyValuePair<int, List<LDNetExpeditionEffect>> pair in node.m_TempDoubleEffects)
                {
                    if (!m_TempDoubleEffects.ContainsKey(pair.Key))
                    {
                        m_TempDoubleEffects.Add(pair.Key, new List<LDNetExpeditionEffect>());
                    }

                    m_TempDoubleEffects[pair.Key].AddRange(pair.Value);
                }
            }

            if (node.m_PlayExtraData != null)
            {
                this.m_PlayExtraData = new Queue<LDNetExpeditionEffectTriggerRtnData>(node.m_PlayExtraData);
            }

            this.m_TempHp = node.m_TempHp;
        }

        /// <summary>
        /// 检查是不是地图最后一个节点
        /// </summary>
        /// <returns></returns>
        public bool CheckIsMapLastNode()
        {
            int mapId = Global.gApp.gSystemMgr.gExpeditionMgr.Data.ExpeditionMap.MapId;
            var mapCfg = ExpeditionMap.Data.Get(mapId);
            if (mapCfg != null)
            {
                if (NodeId == mapCfg.nodes[^1])
                {
                    return true;
                }
            }

            return false;
        }


        /// <summary>
        /// 当前stage播放完，进入下一个阶段
        /// </summary>
        public void ChangeNextStep()
        {
            ClientTriggerStage += 1;
        }

        public void LastNodeFinish()
        {
            ClientTriggerStage = NodePlayStage.TriggerAfter;
            Global.gApp.gSystemMgr.gExpeditionMgr.SendExpeditionNodePassRequest(NodeId);
        }

        /// <summary>
        /// 进入事件点的话，改变状态到下一个
        /// </summary>
        public void ChangePlayStageToNext()
        {
            Global.Log($"expedition node event play,step:{ClientTriggerStage}");
            switch (ClientTriggerStage)
            {
                case NodePlayStage.None:
                    Global.gApp.gSystemMgr.gExpeditionMgr.SendExpeditionNodeStartRequest(NodeId);
                    break;
                case NodePlayStage.TriggerBefore:
                    if (!this.PlayBefore())
                    {
                        this.PlayNextStep();
                    }

                    break;
                case NodePlayStage.Trigger:
                    this.Play();
                    break;
                case NodePlayStage.TriggerAfter:
                    if (!this.PlayAfter())
                    {
                        this.PlayNextStep();
                    }

                    break;
                case NodePlayStage.Max:
                    State = ExpeditionNodeState.Passed.GetHashCode();
                    this.AddEffect2Player();
                    this.AddBox2Player();
                    this.CalculateTriggerTimes();
                    this.SetTempHp2Player();
                    Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.ExpeditionMainUI,
                        ExpeditionMainFreshType.NodeFinish.GetHashCode());
                    this.FinishNode();
                    break;
            }
        }

        /// <summary>
        /// 重置事件播放状态
        /// </summary>
        public void ResetPlayStage()
        {
            ClientTriggerStage = NodePlayStage.None;
            ClearTempBox();
            ClearTempEffect();
            ClearTempDoubleEffect();
            ClearEffectTrigger();
            m_TempHp = 0;
        }

        /// <summary>
        /// 节点完成
        /// </summary>
        public void FinishNode()
        {
            ClientTriggerStage = NodePlayStage.Max;
            State = ExpeditionNodeState.Passed.GetHashCode();
            bool mapIsFinish = Global.gApp.gSystemMgr.gExpeditionMgr.Data.ExpeditionMap.CheckMapIsFinish();
            if (mapIsFinish)
            {
                //最后一个节点完成
                Global.gApp.gSystemMgr.gExpeditionMgr.Check2RequestNextLayerMap();
            }
        }

        /// <summary>
        /// 节点触发效果，先存在节点上，节点完毕再添加上角色上
        /// </summary>
        public void AddTempEffect(int index, RepeatedField<ExpeditionEffect> effects)
        {
            foreach (ExpeditionEffect effect in effects)
            {
                var ldeffect = LDNetExpeditionEffect.Convert(effect);
                m_TempEffect.Add(ldeffect);
            }
        }

        /// <summary>
        /// 填加临时Effect到player上
        /// </summary>
        public void AddEffect2Player()
        {
            Global.gApp.gSystemMgr.gExpeditionMgr.Data.EffectAdd(m_TempEffect);
            ClearTempEffect();
        }

        public void ClearTempEffect()
        {
            m_TempEffect.Clear();
        }

        /// <summary>
        /// 宝箱先加到格子上，等节点完成，再加到身上
        /// </summary>
        /// <param name="box"></param>
        public void AddTempBox(RepeatedField<Protocol.ExpeditionBox> boxs)
        {
            ClearTempBox();
            foreach (Protocol.ExpeditionBox box in boxs)
            {
                AddTempBox(box);
            }
        }

        public void AddTempBox(Protocol.ExpeditionBox box)
        {
            ClearTempBox();
            AddTempBox(LDNetExpeditionBox.Convert(box));
        }

        public void AddTempBox(LDNetExpeditionBox box)
        {
            m_TempBox.Add(box);
        }

        public void AddBox2Player()
        {
            Global.gApp.gSystemMgr.gExpeditionMgr.Data.BoxAdd(m_TempBox);
            m_TempBox.Clear();
        }

        public void ClearTempBox()
        {
            m_TempBox.Clear();
        }

        /// <summary>
        /// 节点播放之前和之后   节点事件完毕后也会立即触发效果
        /// </summary>
        public bool CheckEventEffect()
        {
            m_PlayEffect.Clear();
            List<LDNetExpeditionEffect> allEffect = Global.gApp.gSystemMgr.gExpeditionMgr.GetAllEffects();
            for (int i = 0; i < allEffect.Count; i++)
            {
                LDNetExpeditionEffect effect = allEffect[i];
                if (effect.EffectCondition.IsSatisfy(this, effect))
                {
                    m_PlayEffect.Enqueue(effect);
                }
            }

            //身上刚加的效果  也要立即触发
            for (int i = 0; i < m_TempEffect.Count; i++)
            {
                LDNetExpeditionEffect effect = m_TempEffect[i];
                //刚加到身上的条件是3类型的，不在这个节点上触发
                if (effect.EffectCfgItem.triggerCond[0] == 3)
                    continue;
                if (effect.EffectCondition.IsSatisfy(this, effect))
                {
                    m_PlayEffect.Enqueue(effect);
                }
            }

            return m_PlayEffect.Count > 0;
        }

        public void PlayEventEffect()
        {
            LDNetExpeditionEffect effect = this.GetEffect();
            if (effect != null)
            {
                Global.Log($"play event effect uid:{effect.Id}  cfgId:{effect.EffectCfgItem.id} " +
                           $"cond:{string.Join(", ", effect.EffectCfgItem.triggerCond)}  res:{string.Join(", ", effect.EffectCfgItem.result)}");
                bool finish = effect.EffectImp.ImpEffect(this);
                if (finish)
                {
                    PlayEventEffect();
                }
            }
            else
            {
                this.Check2SendEffectTrigger();
            }
        }

        public LDNetExpeditionEffect GetEffect()
        {
            if (m_PlayEffect.Count > 0)
            {
                LDNetExpeditionEffect effect = m_PlayEffect.Dequeue();
                return effect;
            }

            return null;
        }

        /// <summary>
        /// 节点前后执行的效果
        /// </summary>
        /// <param name="trigger"></param>
        public void EffectTrigger(ExpeditionEffectTrigger trigger)
        {
            if (!m_EffectTrigger.TryGetValue(ClientTriggerStage.GetHashCode(),
                    out List<ExpeditionEffectTrigger> triggers))
            {
                triggers = new List<ExpeditionEffectTrigger>();
                m_EffectTrigger.Add(ClientTriggerStage.GetHashCode(), triggers);
            }

            triggers.Add(trigger);
            if (trigger.Type == LDExpeditionEffectType.Treat_HP.GetHashCode())
            {
                SetTempHp(trigger.TreatHpData.RecoverPer);
            }
        }

        /// <summary>
        /// 效果执行完毕，检查并发送给服务器
        /// </summary>
        public void Check2SendEffectTrigger()
        {
            if (m_EffectTrigger.TryGetValue(ClientTriggerStage.GetHashCode(),
                    out List<ExpeditionEffectTrigger> triggers))
            {
                Global.gApp.gSystemMgr.gExpeditionMgr.SendExpeditionEffectTriggerRequest(this.NodeId,
                    ClientTriggerStage.GetHashCode(), triggers);
            }
            else
            {
                //没有触发的事件, 继续下一个step
                this.PlayNextStep();
            }
        }

        /// <summary>
        /// 最后一关选中打boss后，设置抽奖次数和misiion id 
        /// </summary>
        /// <param name="missionId"></param>
        /// <param name="lotteryCount"></param>
        public void SetLastMissionLottery(int missionId, int lotteryCount)
        {
            SelectMissionId = missionId;
        }

        /// <summary>
        /// 检查临时池里面有没有宝物
        /// </summary>
        /// <param name="boxId"></param>
        /// <returns></returns>
        public bool CheckHaveBoxTemp(int boxId)
        {
            foreach (LDNetExpeditionBox box in m_TempBox)
            {
                if (box.CfgId == boxId)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 增加效果的翻倍的，先加到身上临时，然后加休息点事件的时候再加触发
        /// </summary>
        /// <param name="effect"></param>
        public void AddDoubleEffect(LDNetExpeditionEffect effect)
        {
            if (!m_TempDoubleEffects.TryGetValue(effect.EffectType.GetHashCode(),
                    out List<LDNetExpeditionEffect> effects))
            {
                effects = new();
                m_TempDoubleEffects.Add(effect.EffectType.GetHashCode(), effects);
            }

            effects.Add(effect);
        }

        public List<LDNetExpeditionEffect> GetDoubleEffect(LDExpeditionEffectType type)
        {
            if (m_TempDoubleEffects.TryGetValue(type.GetHashCode(), out var effects))
            {
                return effects;
            }

            return null;
        }

        public void UseDoubleEffect(LDExpeditionEffectType type)
        {
            m_TempDoubleEffects.Remove(type.GetHashCode());
        }

        public void ClearTempDoubleEffect()
        {
            m_TempDoubleEffects.Clear();
        }


        /// <summary>
        /// 计算效果触发 次数，词条存上什么的
        /// </summary>
        public void CalculateTriggerTimes()
        {
            foreach (KeyValuePair<int, List<ExpeditionEffectTrigger>> pair in m_EffectTrigger)
            {
                foreach (ExpeditionEffectTrigger effectTrigger in pair.Value)
                {
                    Global.gApp.gSystemMgr.gExpeditionMgr.Data.AddTriggerTimes(effectTrigger.EffectId);
                    if (effectTrigger.Type == LDExpeditionEffectType.Select_Lexical.GetHashCode())
                    {
                        foreach (ExpeditionLexicalGroup group in effectTrigger.LexicalGroups)
                        {
                            //增加词条
                            Global.gApp.gSystemMgr.gExpeditionMgr.Data.SyncTriggerSelectLexical(group);
                        }
                    }
                    else if (effectTrigger.Type == LDExpeditionEffectType.Exchange_Lexical.GetHashCode())
                    {
                        //交换词条
                        Global.gApp.gSystemMgr.gExpeditionMgr.Data.ExchangeLexical(effectTrigger.ExchangeData);
                    }
                    else if (effectTrigger.Type == LDExpeditionEffectType.AddLvLimit.GetHashCode())
                    {
                        //词条加等级
                        foreach (ExpeditionLexicalGroup group in effectTrigger.LexicalGroupLevel)
                        {
                            Global.gApp.gSystemMgr.gExpeditionMgr.Data.AddLexicalExtraLv(group.GroupId, group.ExtraMaxLevel);
                        }
                    }
                }
            }

            ClearEffectTrigger();
        }

        /// <summary>
        /// 加临时效果
        /// </summary>
        /// <param name="hp"></param>
        public void SetTempHp(long hp)
        {
            m_TempHp = hp;
        }

        /// <summary>
        /// 同步血量到身上
        /// </summary>
        public void SetTempHp2Player()
        {
            if (m_TempHp > 0)
                Global.gApp.gSystemMgr.gExpeditionMgr.Data.SetHp(m_TempHp);
        }

        /// <summary>
        /// 获取血量
        /// </summary>
        /// <returns></returns>
        public long GetHp()
        {
            if (m_TempHp > 0)
            {
                return m_TempHp;
            }
            else
            {
                return Global.gApp.gSystemMgr.gExpeditionMgr.Data.Hp;
            }
        }

        /// <summary>
        /// 清除触发效果
        /// </summary>
        private void ClearEffectTrigger()
        {
            m_EffectTrigger.Clear();
        }

        /// <summary>
        /// 获取已经选择的词条，不能直接读取dto里面的，因为，节点步骤1 23 选择的还没有放到身上，等节点完成才会放到身上，这里重新组织一下数据
        /// </summary>
        /// <returns></returns>
        public List<LDNetExpeditionLexicalGroup> GetSelectedLexicals()
        {
            List<LDNetExpeditionLexicalGroup> tempLexicalGroups = new();
            List<LDNetExpeditionLexicalGroup> dtoSelects = Global.gApp.gSystemMgr.gExpeditionMgr.Data.LexicalItems;
            foreach (LDNetExpeditionLexicalGroup select in dtoSelects)
            {
                LDNetExpeditionLexicalGroup temp = new LDNetExpeditionLexicalGroup();
                temp.LexicalId = select.LexicalId;
                temp.GroupId = select.GroupId;
                temp.PartId = select.PartId;
                temp.MechaId = select.MechaId;
                temp.ExtendMaxLevel = select.ExtendMaxLevel;
                tempLexicalGroups.Add(temp);
            }

            foreach (KeyValuePair<int, List<ExpeditionEffectTrigger>> pair in m_EffectTrigger)
            {
                foreach (ExpeditionEffectTrigger trigger in pair.Value)
                {
                    if (trigger.Type == LDExpeditionEffectType.Select_Lexical.GetHashCode())
                    {
                        foreach (ExpeditionLexicalGroup triggerItemGroup in trigger.LexicalGroups)
                        {
                            bool isHave = false;
                            foreach (LDNetExpeditionLexicalGroup select in tempLexicalGroups)
                            {
                                if (select.GroupId == triggerItemGroup.GroupId)
                                {
                                    isHave = true;
                                    select.LexicalId.AddRange(triggerItemGroup.LexicalIds);
                                    break;
                                }
                            }

                            if (!isHave)
                            {
                                LDNetExpeditionLexicalGroup temp = new LDNetExpeditionLexicalGroup();
                                temp.LexicalId = new List<int>();
                                temp.LexicalId.AddRange(triggerItemGroup.LexicalIds);
                                temp.GroupId = triggerItemGroup.GroupId;
                                temp.PartId = triggerItemGroup.PartId;
                                temp.MechaId = triggerItemGroup.MechaId;
                                tempLexicalGroups.Add(temp);
                            }
                        }
                    }
                    else if (trigger.Type == LDExpeditionEffectType.Exchange_Lexical.GetHashCode())
                    {
                        foreach (LDNetExpeditionLexicalGroup item in tempLexicalGroups)
                        {
                            foreach (ExpeditionLexicalItem giveUpLexical in trigger.ExchangeData.GiveUpLexicals)
                            {
                                if (item.GroupId == giveUpLexical.GroupId)
                                {
                                    if (item.LexicalId.Contains(giveUpLexical.LexicalId))
                                    {
                                        item.LexicalId.Remove(giveUpLexical.LexicalId);
                                    }
                                }
                            }

                            foreach (ExpeditionLexicalItem getLexical in trigger.ExchangeData.GetLexicals)
                            {
                                if (item.GroupId == getLexical.GroupId)
                                {
                                    item.LexicalId.Add(getLexical.LexicalId);
                                }
                            }
                        }
                    }
                    else if (trigger.Type == LDExpeditionEffectType.AddLvLimit.GetHashCode())
                    {
                    }
                }
            }


            return tempLexicalGroups;
        }

        /// <summary>
        /// 获取词条增加等级
        /// </summary>
        /// <param name="group"></param>
        /// <returns></returns>
        public int GetCitiaoExtraLv(int group)
        {
            int addLv = 0;
            foreach (KeyValuePair<int, List<ExpeditionEffectTrigger>> pair in m_EffectTrigger)
            {
                foreach (ExpeditionEffectTrigger trigger in pair.Value)
                {
                    if (trigger.Type == LDExpeditionEffectType.AddLvLimit.GetHashCode())
                    {
                        foreach (ExpeditionLexicalGroup lexicalGroup in trigger.LexicalGroupLevel)
                        {
                            if (lexicalGroup.GroupId == group)
                            {
                                addLv += lexicalGroup.ExtraMaxLevel;
                            }
                        }
                    }
                }
            }

            return addLv;
        }

        /// <summary>
        /// 检查是不是还有词条可以选择
        /// </summary>
        /// <returns></returns>
        public bool CheckCanSelectLexical(ExpeditionEventEffectItem effectCfg)
        {
            LDDIYData DIYData = Global.gApp.gSystemMgr.gExpeditionMgr.Data.Model;
            List<LDExpeditionCiTiaoDataItemNet> expeditionCiTiaoDataItemNets = Global.gApp.gSystemMgr.gExpeditionMgr.Data.GetCiTiTiaoData(this);
            LDExpeditionCiTiaoData expeditionCiTiaoData = new LDExpeditionCiTiaoData(DIYData, expeditionCiTiaoDataItemNets, effectCfg);

            List<LDCanSelectCiTiaoData> citiaoList = expeditionCiTiaoData.GetSelectCiTiaoData();
            return citiaoList.Count > 0;
        }

        /// <summary>
        /// 获取战斗中要生效的效果
        /// </summary>
        /// <returns></returns>
        public List<LDNetExpeditionEffect> GetFightEffect()
        {
            List<LDNetExpeditionEffect> temp = new();
            List<LDNetExpeditionEffect> allEffect = Global.gApp.gSystemMgr.gExpeditionMgr.GetAllEffects();
            //trigger的 比如，节点步骤前的，触发了，要带到战斗里面去的
            List<ExpeditionEffectTrigger> list;
            if (m_EffectTrigger.ContainsKey(NodePlayStage.TriggerBefore.GetHashCode()))
            {
                list = m_EffectTrigger[NodePlayStage.TriggerBefore.GetHashCode()];
            }
            else
            {
                list = new List<ExpeditionEffectTrigger>();
            }

            foreach (LDNetExpeditionEffect effect in allEffect)
            {
                bool isAdd = false;
                //前一步刚触发的就直接丢给战斗
                foreach (ExpeditionEffectTrigger trigger in list)
                {
                    if (trigger.EffectId == effect.Id)
                    {
                        temp.Add(effect);
                        isAdd = true;
                        break;
                    }
                }

                if (!isAdd)
                {
                    if (effect.EffectCondition.IsSatisfyInFight(this, effect))
                    {
                        temp.Add(effect);
                    }
                }
            }

            foreach (LDNetExpeditionEffect effect in m_TempEffect)
            {
                if (effect.EffectCondition.IsSatisfyInFight(this, effect))
                {
                    temp.Add(effect);
                }
            }


            return temp;
        }

        /// <summary>
        /// 战斗外能触发的效果
        /// </summary>
        /// <returns></returns>
        public List<LDNetExpeditionEffect> GetFightOutEffect()
        {
            List<LDNetExpeditionEffect> temp = new();
            List<LDNetExpeditionEffect> allEffect = Global.gApp.gSystemMgr.gExpeditionMgr.GetAllEffects();
            foreach (LDNetExpeditionEffect effect in allEffect)
            {
                if (effect.EffectCondition.IsSatisfy(this, effect))
                {
                    temp.Add(effect);
                }
            }

            foreach (LDNetExpeditionEffect effect in m_TempEffect)
            {
                if (effect.EffectCondition.IsSatisfy(this, effect))
                {
                    temp.Add(effect);
                }
            }

            return temp;
        }

        /// <summary>
        /// 获取Effect触发了的次数
        /// </summary>
        /// <param name="effectId"></param>
        /// <returns></returns>
        public int GetEffectTriggerTimes(long effectId)
        {
            int times = 0;
            foreach (KeyValuePair<int, List<ExpeditionEffectTrigger>> valuePair in m_EffectTrigger)
            {
                foreach (ExpeditionEffectTrigger trigger in valuePair.Value)
                {
                    if (trigger.EffectId == effectId)
                    {
                        times++;
                    }
                }
            }

            return times;
        }

        /// <summary>
        /// 获取词条刷新的Effect count
        /// </summary>
        /// <returns></returns>
        public int GetLexicalRefreshCount()
        {
            List<LDNetExpeditionEffect> effects = GetFightOutEffect();
            int count = 0;
            foreach (LDNetExpeditionEffect effect in effects)
            {
                if (effect.EffectCfgItem.result[0] == LDExpeditionEffectType.Lexical_Change.GetHashCode())
                {
                    count++;
                }
            }

            return count;
        }

        /// <summary>
        /// 播放事件触发的特殊效果
        /// </summary>
        /// <param name="extraData"></param>
        public void PlayExtraData(Queue<LDNetExpeditionEffectTriggerRtnData> extraData)
        {
            m_PlayExtraData = extraData;
            this.CheckToPlayExtraData();
        }
    }
}