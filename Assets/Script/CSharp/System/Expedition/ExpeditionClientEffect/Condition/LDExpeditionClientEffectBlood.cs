namespace LD
{
    public class LDExpeditionClientEffectBlood : LDExpeditionClientEffectBaseCondition, ILDExpeditionClientEffectCondition
    {
        public LDExpeditionClientEffectBlood(ExpeditionEventEffectItem cfg) : base(cfg)
        {
        }

        public bool IsSatisfy(LDNetExpeditionNode node, LDNetExpeditionEffect effect)
        {
            return false;
        }

        public bool IsSatisfyInFight(LDNetExpeditionNode node, LDNetExpeditionEffect effect)
        {
            m_Node = node;
            if (effect.EffectCfgItem.times > 0)
            {
                int triggerTimes = GetTriggerTimes(m_Node, effect);
                if (triggerTimes >= effect.EffectCfgItem.times)
                    return false;
            }

            int bloodType = effect.EffectCfgItem.triggerCond[1];
            int bloodNum = effect.EffectCfgItem.triggerCond[2];
            if (bloodType == 1)
            {
                //大于等于生效
                if (Global.gApp.gSystemMgr.gExpeditionMgr.Data.Hp < bloodNum)
                    return false;
            }
            else
            {
                //小于等于生效
                if (Global.gApp.gSystemMgr.gExpeditionMgr.Data.Hp > bloodNum)
                    return false;
            }


            return true;
        }
    }
}