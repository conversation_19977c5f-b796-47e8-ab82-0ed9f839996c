using LD.Protocol;

namespace LD
{
    /// <summary>
    /// 词条系数
    /// </summary>
    public class LDExpeditionClientEffectLexicalCoefficient : LDExpeditionClientEffectBaseImp,
        ILDExpeditionClientEffectEffectImp
    {
        public bool ImpEffect(LDNetExpeditionNode node)
        {
            m_Node = node;
            node.AddDoubleEffect(m_Effect);

            //刷新次数，刷新了就找到掉一下
            ExpeditionEffectTrigger trigger = new ExpeditionEffectTrigger();
            trigger.Type = ExpeditionEffectActionType.LexicalChange.GetHashCode(); // 刷新次数
            trigger.EffectId = m_Effect.Id;
            node.EffectTrigger(trigger);
            Global.Log($"Expedition Effect Imp，effectId:{m_Effect.Id},type:{m_Effect.EffectType},cfgId:{m_Effect.CfgId}," +
                       $"triggerCond:{string.Join(", ", m_Effect.EffectCfgItem.triggerCond)}、result:{string.Join(", ", m_Effect.EffectCfgItem.result)}");

            return true;
        }

        public LDExpeditionClientEffectLexicalCoefficient(LDNetExpeditionEffect effect) : base(effect)
        {
        }
    }
}