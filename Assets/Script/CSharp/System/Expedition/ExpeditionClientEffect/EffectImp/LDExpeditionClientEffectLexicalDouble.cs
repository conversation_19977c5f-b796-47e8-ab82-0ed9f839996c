using LD.Protocol;

namespace LD
{
    public class LDExpeditionClientEffectLexicalDouble : LDExpeditionClientEffectBaseImp,
        ILDExpeditionClientEffectEffectImp
    {
        public LDExpeditionClientEffectLexicalDouble(LDNetExpeditionEffect effect) : base(effect)
        {
        }

        public bool ImpEffect(LDNetExpeditionNode node)
        {
            m_Node = node;
            node.AddDoubleEffect(m_Effect);

            ExpeditionEffectTrigger doubleTrigger = new ExpeditionEffectTrigger();
            doubleTrigger.Type = ExpeditionEffectActionType.LexicalDouble.GetHashCode(); //选词条次数 加倍
            doubleTrigger.EffectId = m_Effect.Id;
            node.EffectTrigger(doubleTrigger);
            Global.Log($"Expedition Effect Imp，effectId:{m_Effect.Id},type:{m_Effect.EffectType},cfgId:{m_Effect.CfgId}," +
                       $"triggerCond:{string.Join(", ", m_Effect.EffectCfgItem.triggerCond)}、result:{string.Join(", ", m_Effect.EffectCfgItem.result)}");
            return true;
        }
    }
}