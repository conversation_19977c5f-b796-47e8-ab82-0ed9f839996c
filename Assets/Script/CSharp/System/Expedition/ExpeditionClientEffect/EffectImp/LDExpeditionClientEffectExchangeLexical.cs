using System.Collections.Generic;
using LD.Protocol;

namespace LD
{
    /// <summary>
    /// 交换词条
    /// </summary>
    public class LDExpeditionClientEffectExchangeLexical : LDExpeditionClientEffectBaseImp,
        ILDExpeditionClientEffectEffectImp
    {
        private ExpeditionEffectTrigger m_Trigger;
        private int m_GiveUpTimes;
        private int m_SelectTimes;

        public bool ImpEffect(LDNetExpeditionNode node)
        {
            m_Node = node;
            m_Trigger = new ExpeditionEffectTrigger();
            m_Trigger.Type = ExpeditionEffectActionType.ExchangeLexical.GetHashCode(); //交换词条
            m_Trigger.EffectId = m_Effect.Id;
            m_Trigger.ExchangeData = new ExpeditionExchangeData();

            ExpeditionEventEffectItem expeditionEventEffectItem = m_Effect.EffectCfgItem;
            m_GiveUpTimes = expeditionEventEffectItem.result[1];
            m_SelectTimes = expeditionEventEffectItem.result[2];

            CheckToShowNextCiTiao();
            return false;
        }

        public override void CheckToShowNextCiTiao()
        {
            if (m_GiveUpTimes > 0)
            {
                m_GiveUpTimes--;
                ShowGiveUp();
                return;
            }

            if (m_SelectTimes > 0)
            {
                m_SelectTimes--;
                ShowSelect();
                return;
            }

            Global.Log($"Expedition Effect Imp，effectId:{m_Effect.Id},type:{m_Effect.EffectType},cfgId:{m_Effect.CfgId}," +
                       $"triggerCond:{string.Join(", ", m_Effect.EffectCfgItem.triggerCond)}、result:{string.Join(", ", m_Effect.EffectCfgItem.result)}");
            m_Node.EffectTrigger(m_Trigger);

            this.CheckPlayNextEffect();
        }

        private void ShowGiveUp()
        {
            LDDIYData DIYData = Global.gApp.gSystemMgr.gExpeditionMgr.Data.Model;
            List<LDExpeditionCiTiaoDataItemNet> expeditionCiTiaoDataItemNets = Global.gApp.gSystemMgr.gExpeditionMgr.Data.GetCiTiTiaoData(m_Node);
            ExpeditionEventEffectItem expeditionEventEffectItem = m_Effect.EffectCfgItem;
            LDExpeditionCiTiaoData expeditionCiTiaoData = new LDExpeditionCiTiaoData(DIYData, expeditionCiTiaoDataItemNets, expeditionEventEffectItem);

            bool isShow = expeditionCiTiaoData.TryOpenCiTiaoGiveUpView(this);
            if (!isShow)
            {
                CheckToShowNextCiTiao();
            }
        }

        private void ShowSelect()
        {
            LDDIYData DIYData = Global.gApp.gSystemMgr.gExpeditionMgr.Data.Model;
            List<LDExpeditionCiTiaoDataItemNet> expeditionCiTiaoDataItemNets = Global.gApp.gSystemMgr.gExpeditionMgr.Data.GetCiTiTiaoData(m_Node);

            foreach (ExpeditionLexicalItem giveUpLexical in m_Trigger.ExchangeData.GiveUpLexicals)
            {
                foreach (LDExpeditionCiTiaoDataItemNet itemNet in expeditionCiTiaoDataItemNets)
                {
                    if (itemNet.CitiaoGroupId == giveUpLexical.GroupId)
                    {
                        itemNet.SelectCiTiaoIds.Remove(giveUpLexical.LexicalId);
                        itemNet.CiTiaoIds.Remove(giveUpLexical.LexicalId);
                        break;
                    }
                }
            }

            ExpeditionEventEffectItem expeditionEventEffectItem = m_Effect.EffectCfgItem;
            LDExpeditionCiTiaoData expeditionCiTiaoData = new LDExpeditionCiTiaoData(DIYData, expeditionCiTiaoDataItemNets, expeditionEventEffectItem);

            bool isShow = expeditionCiTiaoData.TryOpenCiTiaoView(this, m_Node);
            if (!isShow)
            {
                CheckToShowNextCiTiao();
            }
        }

        public LDExpeditionClientEffectExchangeLexical(LDNetExpeditionEffect effect) : base(effect)
        {
        }

        public override void GiveUpLexical(LDCanSelectCiTiaoData lexicalData)
        {
            Global.Log($"Expedition Effect Imp ----  Give up CiTiao，effectId:{m_Effect.Id},type:{m_Effect.EffectType},cfgId:{m_Effect.CfgId}," +
                       $"triggerCond:{string.Join(", ", m_Effect.EffectCfgItem.triggerCond)}、result:{string.Join(", ", m_Effect.EffectCfgItem.result)}" +
                       $"---------    citiao groupId:{lexicalData.CiTiaoGroupId}   citiaoID:{lexicalData.CiTiaoId}");

            m_Trigger.ExchangeData.GiveUpLexicals.Add(new ExpeditionLexicalItem()
            {
                GroupId = lexicalData.CiTiaoGroupId,
                LexicalId = lexicalData.CiTiaoId
            });
        }

        public override void SelectLexical(LDCanSelectCiTiaoData lexicalData)
        {
            Global.Log($"Expedition Effect Imp ----  Select CiTiao，effectId:{m_Effect.Id},type:{m_Effect.EffectType},cfgId:{m_Effect.CfgId}," +
                       $"triggerCond:{string.Join(", ", m_Effect.EffectCfgItem.triggerCond)}、result:{string.Join(", ", m_Effect.EffectCfgItem.result)}" +
                       $"---------    citiao groupId:{lexicalData.CiTiaoGroupId}   citiaoID:{lexicalData.CiTiaoId}");

            m_Trigger.ExchangeData.GetLexicals.Add(new ExpeditionLexicalItem()
            {
                GroupId = lexicalData.CiTiaoGroupId,
                LexicalId = lexicalData.CiTiaoId
            });
            CheckToShowNextCiTiao();
        }
    }
}