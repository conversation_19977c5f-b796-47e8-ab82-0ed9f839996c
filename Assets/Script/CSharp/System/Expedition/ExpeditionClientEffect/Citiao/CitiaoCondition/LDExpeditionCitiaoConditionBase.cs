namespace LD
{
    public class LDExpeditionCitiaoConditionBase
    {
        protected float[] m_ConditionParam;
        protected int m_TriggerType;

        protected int m_TriggerCitiaoId;

        public virtual void Init(float[] conditionPara, int triggerType)
        {
            m_ConditionParam = conditionPara;
            m_TriggerType = triggerType;
        }

        public void SetTriggerCitiaoId(int citiaoId)
        {
            m_TriggerCitiaoId = citiaoId;
        }

        public virtual bool MatchCondition(LDNetExpeditionNode node)
        {
            return false;
        }
    }
}