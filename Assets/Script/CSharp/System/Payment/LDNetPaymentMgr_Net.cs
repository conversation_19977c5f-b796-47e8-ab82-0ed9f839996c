using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetPaymentMgr
    {
        public LDNetPaymentDTO Data { private set; get; } = new LDNetPaymentDTO();
        private float m_UpdateTime = 5f;
        private float m_CurTime = 0f;
        private bool m_IsWaiting = false;
        public bool ShowUseMoneyTips = true;

        protected override void InitNetImp()
        {
            AddNetHandler(opcode.RechargeShopInfoSyncResponse);
            AddNetHandler(opcode.PlayerDiyPackInfoSyncResponse);
            AddNetHandler(opcode.BattlePassInfoSyncResponse);
            AddNetHandler(opcode.SeasonBattlePassInfoSyncResponse);
            AddNetHandler(opcode.MonthlyCardInfoSyncResponse);
            AddNetHandler(opcode.FirstRechargeInfoSyncResponse);
            AddNetHandler(opcode.MechaSpinMallInfoSyncResponse);
            AddNetHandler(opcode.DiyActivityInfoSyncResponse);
            AddNetHandler(opcode.MechaLevelPackMallInfoSyncResponse);
            AddNetHandler(opcode.CombatActivityInfoSyncResponse);
            AddNetHandlerSymmetry(opcode.IaPGenGameOrderRequest, opcode.IaPGenGameOrderResponse);

            AddNetHandler(opcode.MoneyInfoSyncResponse);
            AddNetHandlerSymmetry(opcode.ThirdPayBuyItemRequest, opcode.ThirdPayBuyItemResponse);
        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.RechargeShopInfoSyncResponse) // 钻石充值
            {
                RechargeShopInfoSyncResponse info = RechargeShopInfoSyncResponse.Parser.ParseFrom(data);
                foreach (RechargeItemInfo item in info.MallItems)
                {
                    Data.SetRechargeMallData(new LDRechargeItemInfo(item));
                }

                return info;
            }
            else if (s2c_msgId == opcode.BattlePassInfoSyncResponse) // 战令购买
            {
                BattlePassInfoSyncResponse info = BattlePassInfoSyncResponse.Parser.ParseFrom(data);
                foreach (BattlePassInfo item in info.BattleInfo)
                {
                    Data.SetBattlePassMallData(new LDBattlePassInfo(item));
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.BattlePassBuy);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.BattlePassBuyPublicize);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.BattlePassMain, info.BattleInfo[0].Id);
                return info;
            }
            else if (s2c_msgId == opcode.SeasonBattlePassInfoSyncResponse)
            {
                //赛季战令
                SeasonBattlePassInfoSyncResponse info = SeasonBattlePassInfoSyncResponse.Parser.ParseFrom(data);
                foreach (BattlePassInfo item in info.BattleInfo)
                {
                    Data.SetSeasonBattlePassMallData(new LDBattlePassInfo(item));
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.SeasonBattlePassBuyUI);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.SeasonUI, SeasonFunctionType.BattlePass.GetHashCode());
                return info;
            }
            else if (s2c_msgId == opcode.IaPGenGameOrderResponse)
            {
                IAPGenGameOrderResponse info = IAPGenGameOrderResponse.Parser.ParseFrom(data);
                OnGetOrder(info);
                return info;
            }
            else if (s2c_msgId == opcode.MonthlyCardInfoSyncResponse)
            {
                // 月卡
                MonthlyCardInfoSyncResponse info = MonthlyCardInfoSyncResponse.Parser.ParseFrom(data);
                foreach (MonthlyCardInfo cardInfo in info.Card)
                {
                    bool isHave = Data.GetMonthlyCardInfo(cardInfo.Id) != null;
                    Data.SyncMonthlyCardInfo(new LDMonthlyCardInfo(cardInfo));
                    if (!isHave)
                    {
                        LDSDKEvent.SendCustomMonthlyCarEvent(cardInfo.Id);
                    }
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MonthlyCardUI);
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.MonthlyCard);
                return info;
            }
            else if (s2c_msgId == opcode.FirstRechargeInfoSyncResponse)
            {
                //首冲
                FirstRechargeInfoSyncResponse info = FirstRechargeInfoSyncResponse.Parser.ParseFrom(data);
                foreach (int id in info.Id)
                {
                    Data.SyncFirstRecharge(id);
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.FirstRechargeUI, 1);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MainUI, MainUIFunc.LeftBar);
                return info;
            }
            else if (s2c_msgId == opcode.PlayerDiyPackInfoSyncResponse)
            {
                PlayerDiyPackInfoSyncResponse info = PlayerDiyPackInfoSyncResponse.Parser.ParseFrom(data);
                foreach (DiyPackInfo packInfo in info.Diy)
                {
                    Data.SyncDIYPartInfo(packInfo);
                }

                return info;
            }
            else if (s2c_msgId == opcode.MechaSpinMallInfoSyncResponse)
            {
                // 机甲转盘
                MechaSpinMallInfoSyncResponse info = MechaSpinMallInfoSyncResponse.Parser.ParseFrom(data);
                foreach (MechaSpinMallInfo item in info.MechaSpinMallInfo)
                {
                    Data.SetMechaSpinData(new LDMechaSpinMallInfo(item));
                }

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MechaSpinBuyItem);
                return info;
            }
            else if (s2c_msgId == opcode.DiyActivityInfoSyncResponse)
            {
                //部件累抽
                DiyActivityInfoSyncResponse info = DiyActivityInfoSyncResponse.Parser.ParseFrom(data);
                Data.SetDiyActivityInfos(info.DiyActivityInfo);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.DiyActivityMain, DiyActivityMainFun.DiyActivityShop);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.UAVActivityMain, DiyActivityMainFun.DiyActivityShop);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.CombatTarget_Main, CombatTargetMainFun.Shop);
                return info;
            }
            else if (s2c_msgId == opcode.MechaLevelPackMallInfoSyncResponse)
            {
                //进阶礼包
                MechaLevelPackMallInfoSyncResponse info = MechaLevelPackMallInfoSyncResponse.Parser.ParseFrom(data);
                bool isBuy = Data.SetMechaLevelPackMall(info.MechaLevelPackMallInfo);
                if (PlayerAssembleFinishResponse)
                {
                    Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MainUI, MainUIFunc.LeftBar);
                    if (isBuy)
                    {
                        Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MechaLvPackMain, 1);
                    }
                    else
                    {
                        if (Global.gApp.gSystemMgr.CheckModuleOpen(LDSystemEnum.MechaLvPack))
                        {
                            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.MechaLvPackMain);
                        }
                    }
                }

                return info;
            }
            else if (s2c_msgId == opcode.CombatActivityInfoSyncResponse)
            {
                //探险家
                CombatActivityInfoSyncResponse info = CombatActivityInfoSyncResponse.Parser.ParseFrom(data);
                Data.SetNewCombatPowerActivityInfos(info.CombatActivityInfo);
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.CombatPower_MainUI, 2);
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_NewCombatPower_Shop);

                return info;
            }
            else if (s2c_msgId == opcode.MoneyInfoSyncResponse)
            {
                //三方支付数据同步
                MoneyInfoSyncResponse info = MoneyInfoSyncResponse.Parser.ParseFrom(data);
                long preTimes = Data.ThirdPayMoney;
                Data.SyncThirdPayMoney(info.Money);
                Global.gApp.gMsgDispatcher.Broadcast(MsgIds.ItemChange, LDSpecialItemId.Money, 0L, preTimes, Data.ThirdPayMoney);

                return info;
            }
            else if (s2c_msgId == opcode.ThirdPayBuyItemResponse)
            {
                //三方支付购买商品
                ThirdPayBuyItemResponse info = ThirdPayBuyItemResponse.Parser.ParseFrom(data);
                return info;
            }

            return null;
        }

        // 购买商品  goodsId = MallGoods表的Id
        public void SendMallBuyItemRequest(int goodsId, int count = 1)
        {
            if (m_IsWaiting)
                return;
            m_IsWaiting = true;

            MallGoodsItem mallGoodsItem = MallGoods.Data.Get(goodsId);
            iapItem item = iap.Data.Get(mallGoodsItem.iap);
            string iapId = Global.gApp.gSdkMgr.gPaymentMgr.GetProductImp(item);
            LDSDKEvent.SendPurseGetOrderEvent(goodsId);
            if (RuntimeSettings.UseSDK && !string.IsNullOrEmpty(iapId))
            {
                if (!Global.gApp.gSdkMgr.gPaymentMgr.CanCharge)
                {
                    Global.gApp.gToastMgr.ShowGameTips(95013);
                    return;
                }

                // if (!Global.gApp.gSdkMgr.gPaymentMgr.ProductInfo.ContainsKey(iapId))
                // {
                //     Global.gApp.gToastMgr.ShowGameTips(95104);
                //     return;
                // }

                //string price = Global.gApp.gSdkMgr.gPaymentMgr.GetUsPrice(goodsId);
                //string currency = Global.gApp.gSdkMgr.gPaymentMgr.GetUsCurrencySymble(goodsId);

                IAPGenGameOrderRequest gameOrderRequest = new IAPGenGameOrderRequest();
                gameOrderRequest.GoodId = goodsId;
                Send(opcode.IaPGenGameOrderRequest, gameOrderRequest);
            }
            else
            {
                MallBuyItemRequest mallBuyItemRequest = new MallBuyItemRequest();
                mallBuyItemRequest.Amount = count;
                mallBuyItemRequest.GoodsId = goodsId;
                Send(opcode.MallBuyItemRequest, mallBuyItemRequest);
            }
        }

        public void SendThirdPayBuyItemRequest(int goodsId, int count = 1)
        {
            ThirdPayBuyItemRequest request = new ThirdPayBuyItemRequest();
            request.GoodsId = goodsId;
            request.Amount = count;
            Send(opcode.ThirdPayBuyItemRequest, request);
        }

        private void OnGetOrder(IAPGenGameOrderResponse orderData)
        {
            Global.gApp.gSdkMgr.gPaymentMgr.purchase(orderData);
        }

        protected override void ClearNetDataImp()
        {
            Data = new LDNetPaymentDTO();
            ShowUseMoneyTips = true;
        }

        public override void OnUpdate(float dt)
        {
            if (m_IsWaiting)
            {
                m_CurTime += dt;
                if (m_CurTime >= m_UpdateTime)
                {
                    m_CurTime = 0f;
                    m_IsWaiting = false;
                }
            }
        }
    }
}