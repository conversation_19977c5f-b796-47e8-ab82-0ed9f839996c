using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDServerOpenDaysFilter : LDBaseFilter
    {
        public override bool Filter(List<int> condition)
        {
            long openServerTimeOfZero = DateTimeUtil.OpenServerTimeOfZero;
            long todayZero = DateTimeUtil.GetTimeOfZero(DateTimeUtil.GetServerTime());
            int openServerDay = (int)((todayZero - openServerTimeOfZero) / DateTimeUtil.Day_Mills) + 1;
            return openServerDay >= condition[1];
        }

        public override string GetUnFinishTips(List<int> condition)
        {
            if (condition.Count < 2)
            {
                return string.Empty;
            }

            int cfgId = condition[0];
            ConditionItem cfg = Condition.Data.Get(cfgId);
            long needMills = (condition[1] - 1) * DateTimeUtil.Day_Mills;
            long openZero = DateTimeUtil.GetTimeOfZero(DateTimeUtil.OpenServerTimeMills);
            // long curMills = Math.Max(0, DateTimeUtil.GetServerTime() - openZero);
            return UiTools.Localize(cfg.desc, Global.gApp.gGameData.GetExpireTimeTips5(openZero + needMills));
        }

        public override float GetProgressInfo(List<int> condition, out long current, out long target)
        {
            current = 0;
            target = 0;
            return 0f;
        }
    }
}