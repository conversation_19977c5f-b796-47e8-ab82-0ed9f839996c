using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetCommonMgr
    {
        protected override void InitNetImp()
        {
            AddNetHandler(Protocol.opcode.HeartbeatResponse);
            AddNetHandler(Protocol.opcode.ErrorResponse);
            AddNetHandler(Protocol.opcode.RewardResponse);
        }
        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.HeartbeatResponse)
            {
                HeartbeatResponse heartbeatResponse = HeartbeatResponse.Parser.ParseFrom(data);
                DateTimeUtil.FreshServerTime(heartbeatResponse.ServerTime);
                Debug.Log("BeatHearRespond ");
            }
            else if (s2c_msgId == opcode.ErrorResponse)
            {
                return OnHandleError(s2c_msgId, data);
            }
            else if (s2c_msgId == opcode.RewardResponse)
            {
                return OnHandleReward(s2c_msgId, data);
            }
            return null;
        }

        public void ShowErrorTips(int errorCodeId)
        {
            ErrorCodeItem ecCfg = ErrorCode.Data.Get(errorCodeId);
            if (ecCfg == null || ecCfg.localizationid == 0) { 
                Global.LogError($"错误码{errorCodeId}  没有配置多语言"); 
                Global.gApp.gToastMgr.ShowGameTips(0);
                return; 
            }
            Global.gApp.gToastMgr.ShowGameTips(ecCfg.localizationid);
        }
        private IMessage OnHandleReward(opcode s2c_msgId, byte[] data)
        {
            RewardResponse rewardResponse = RewardResponse.Parser.ParseFrom(data);

            List<LDCommonItem> showRewards = new List<LDCommonItem>();

            foreach (ItemInfo item in rewardResponse.ItemInfos)
            {
                showRewards.Add(item);
            }
            Global.gApp.gUiMgr.TryShowGetRewardUI(showRewards);
            return rewardResponse;
        }

        private IMessage OnHandleError(opcode s2c_msgId, byte[] data)
        {
            Protocol.ErrorResponse errorRespond = Protocol.ErrorResponse.Parser.ParseFrom(data);
            // 错误码过来要清楚 标记
            Global.gApp.gNetMgr.TryClearAllRespond((Protocol.opcode)errorRespond.PacketId);
            if (!Global.gApp.gNetMgr.OnHandleErrorNetMsg(errorRespond))
            {
                /// 处理 msg 逻辑 一般是一个tips
                ShowErrorTips(errorRespond.ErrorCode);
            }
            return errorRespond;
        }

        protected override void ClearNetDataImp()
        {

        }
    }
}
