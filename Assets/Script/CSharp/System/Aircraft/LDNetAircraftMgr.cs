using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetAircraftMgr : LDNetDataMgr
    {
        private int m_ClickLvUpBtnTimes;
        private int m_ClickUpgradeBtnTimes;

        protected override void InitImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.Aircraft, isShowLockTips);
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (sys == LDSystemEnum.Custom_Aircraft_Slot1Base)
            {
                int aircraftId = Data.AircraftSlotInfo.GetAircraftIdBySlotIndex(1);
                if (aircraftId <= 0)
                {
                    if (Data.AircraftInfos.Count >= 1)
                    {
                        return LDRedTipsState.RedPoint;
                    }

                    return LDRedTipsState.None;
                }

                var lvUpRed = GetLevelUpRed(aircraftId);
                if (lvUpRed != LDRedTipsState.None)
                {
                    return lvUpRed;
                }

                var upgradeRed = GetUpgradeRed(aircraftId);
                if (upgradeRed != LDRedTipsState.None)
                {
                    return upgradeRed;
                }
            }

            if (sys == LDSystemEnum.Custom_Aircraft_Slot2Base)
            {
                int aircraftId = Data.AircraftSlotInfo.GetAircraftIdBySlotIndex(2);
                if (aircraftId <= 0)
                {
                    if (Data.AircraftInfos.Count >= 2)
                    {
                        return LDRedTipsState.RedPoint;
                    }

                    return LDRedTipsState.None;
                }

                var lvUpRed = GetLevelUpRed(aircraftId);
                if (lvUpRed != LDRedTipsState.None)
                {
                    return lvUpRed;
                }

                var upgradeRed = GetUpgradeRed(aircraftId);
                if (upgradeRed != LDRedTipsState.None)
                {
                    return upgradeRed;
                }
            }

            if (sys == LDSystemEnum.Custom_Aircraft_List)
            {
                var listRed = GetListRed();
                if (listRed != LDRedTipsState.None)
                {
                    return listRed;
                }
            }

            if (sys == LDSystemEnum.Custom_Aircraft_Xilian)
            {
                var xilianRed = GetXilianRed();
                if (xilianRed != LDRedTipsState.None)
                {
                    return xilianRed;
                }
            }

            return LDRedTipsState.None;
        }

        /// <summary>
        /// 获取slot上的平台信息
        /// </summary>
        /// <param name="slotIndex"></param>
        /// <returns></returns>
        public LDNetAircraftInfo GetAircraftByIndex(int slotIndex)
        {
            int aircraftId = Data.AircraftSlotInfo.GetAircraftIdBySlotIndex(slotIndex);
            if (aircraftId < 0)
            {
                return null;
            }

            return Data.GetAircraftInfo(aircraftId);
        }

        public int GetAircraftCount(int aircraftId)
        {
            return Data.IsAircraftGet(aircraftId) ? 1 : 0;
        }

        /// <summary>
        /// 是否解锁
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <returns></returns>
        public bool IsAircraftUnlocked(int aircraftId)
        {
            return Data.IsAircraftGet(aircraftId);
        }

        /// <summary>
        /// 是否上阵
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <returns></returns>
        public bool IsAircraftInUse(int aircraftId)
        {
            return Data.AircraftSlotInfo.IsAircraftInUse(aircraftId);
        }

        public bool IsAircraftInUseBySlotIndex(int slotIndex)
        {
            if (Data.AircraftSlotInfo.SlotInfo.ContainsKey(slotIndex))
            {
                return Data.AircraftSlotInfo.SlotInfo[slotIndex] > 0;
            }

            return false;
        }

        /// <summary>
        /// 品质
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <returns></returns>
        public int GetAircraftQuality(int aircraftId)
        {
            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
            if (aircraftInfo != null)
            {
                return aircraftInfo.Quality;
            }

            return 0;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="mechaId"></param>
        /// <returns></returns>
        public string GetAircraftBG(int aircraftId)
        {
            int skinId = 0; //Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(mechaId);
            bool hidSkin = false; //Global.gApp.gSystemMgr.gMechaSkinDataMgr.ISMechaSkineHide(skinId);
            if (skinId > 0 && !hidSkin)
            {
                // if (Skin.Data.TryGet(skinId, out SkinItem skinCfg, false))
                // {
                //     return skinCfg.mechaBG;
                // }
                // else
                {
                    Global.LogError($"皮肤ID不存在 : {skinId}");
                }
            }

            if (AircraftCfg.Data.TryGet(aircraftId, out var aircraftCfgItem))
            {
                return aircraftCfgItem.AircraftBG;
            }

            return string.Empty;
        }

        public bool GetAircraftUnlockCost(int aircraftId, out bool canPiece, out long own, out long need)
        {
            canPiece = false;
            own = 0;
            need = 0;

            if (AircraftCfg.Data.TryGet(aircraftId, out var aircraftCfgItem))
            {
                if (aircraftCfgItem.installType == 1)
                {
                    canPiece = true;
                    LDCommonItem costItem = new LDCommonItem(aircraftCfgItem.Aircraftcost);
                    own = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(costItem.Id);
                    need = costItem.Num;

                    bool isEnough = own >= need;
                    return isEnough;
                }
                else if (aircraftCfgItem.installType == 2)
                {
                    //ss
                    canPiece = false;
                    string[] costAircraft = aircraftCfgItem.UnlockSS;
                    need = costAircraft.Length;
                    bool isEnough = true;
                    int enoughCount = costAircraft.Length;
                    foreach (string aircraft in costAircraft)
                    {
                        string[] lines = LDCommonTools.Split(aircraft);
                        if (lines.Length > 2)
                        {
                            if (lines[0] == LDCommonType.Aircraft)
                            {
                                int id = LDParseTools.IntParse(lines[1]);
                                LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(id);
                                if (aircraftInfo != null)
                                {
                                    int quality = LDParseTools.IntParse(lines[3]);
                                    if (aircraftInfo.Quality < quality)
                                    {
                                        enoughCount--;
                                        isEnough = false;
                                    }
                                }
                                else
                                {
                                    enoughCount--;
                                    isEnough = false;
                                }
                            }
                        }
                    }

                    own = enoughCount;
                    return isEnough;
                }
            }

            return false;
        }

        /// <summary>
        /// 升阶消耗
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <param name="own"></param>
        /// <param name="need"></param>
        /// <returns></returns>
        public bool GetAircraftQuaUpCost(int aircraftId, out long own, out long need)
        {
            own = 0;
            need = 0;
            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
            if (aircraftInfo == null)
            {
                return false;
            }

            AircraftCfgItem aircraftCfg = AircraftCfg.Data.Get(aircraftId);
            AircraftQualityItem quaCfg = AircraftQuality.Data.Get(aircraftInfo.Quality);

            int costId = aircraftCfg.pieceID;
            need = quaCfg.itemCost2;
            own = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(costId);

            bool isEnough = own >= need;
            return isEnough;
        }

        /// <summary>
        /// SS平台升阶消耗
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <param name="own"></param>
        /// <param name="need"></param>
        /// <returns></returns>
        public bool GetAircraftSSQuaUpCost(int aircraftId, out long own, out long need)
        {
            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
            AircraftQualitySSItem ssItem = GetQualitySsItem(aircraftId, aircraftInfo.Quality);
            string ssCost = ssItem.qualityItem;
            LDCommonItem ssCostItem = new LDCommonItem(ssCost);
            need = ssCostItem.Num;
            own = ssCostItem.CurNum;

            bool isEnough = own >= need;
            return isEnough;
        }

        /// <summary>
        /// 机甲平台是否达到某品质
        /// </summary>
        /// <param name="aircraftStr"></param>
        /// <returns></returns>
        public bool IsAircraftReachQuality(string aircraftStr)
        {
            string[] lines = LDCommonTools.Split(aircraftStr);
            if (lines[0] != LDCommonType.Aircraft || lines.Length != 4)
            {
                Global.LogError($"数据格式错误 : {aircraftStr}");
                return false;
            }

            int id = LDParseTools.IntParse(lines[1]);
            int quality = LDParseTools.IntParse(lines[3]);
            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(id);
            if (aircraftInfo != null)
            {
                if (aircraftInfo.Quality >= quality)
                {
                    return true;
                }
            }

            return false;
        }

        public bool IsCanUpgrade(int aircraftId)
        {
            if (aircraftId <= 0)
            {
                return false;
            }

            AircraftCfg.Data.TryGet(aircraftId, out AircraftCfgItem aircraftCfg, false);
            if (aircraftCfg == null)
            {
                return false;
            }

            bool isSS = aircraftCfg.installType == 2;
            if (isSS)
            {
                LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
                if (aircraftInfo == null)
                {
                    return false;
                }

                int quality = aircraftInfo.Quality;
                AircraftQualitySSItem ssItem = GetQualitySsItem(aircraftId, quality);
                foreach (string itemStr in ssItem.qualityUp)
                {
                    bool isPreUnlock = IsAircraftReachQuality(itemStr);
                    if (!isPreUnlock)
                    {
                        return false;
                    }
                }

                return GetAircraftSSQuaUpCost(aircraftId, out long _, out long _);
            }
            else
            {
                LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
                if (aircraftInfo == null)
                {
                    return false;
                }

                if (aircraftInfo.Quality >= LDQualityType.Max)
                {
                    return false;
                }

                return GetAircraftQuaUpCost(aircraftId, out long _, out long _);
            }
        }

        // 获取升级消耗相关数据
        public bool GetAircraftLevelUpCost(int aircraftId, out long ownedNum, out long num)
        {
            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
            AircraftLevelItem lvCfg = AircraftLevel.Data.Get(aircraftInfo.Level);

            LDCommonItem costItem = new LDCommonItem(lvCfg.cost);
            ownedNum = costItem.CurNum;
            num = costItem.Num;

            bool isEnough = ownedNum >= num;
            return isEnough;
        }

        // 判断是否满级
        public bool IsAircraftMaxLevel(int aircraftId)
        {
            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
            if (aircraftInfo == null)
            {
                return false;
            }

            AircraftQualityItem quaCfg = AircraftQuality.Data.Get(aircraftInfo.Quality);
            return aircraftInfo.Level >= quaCfg.lvLimit;
        }

        // 是否可以用礼包升阶
        public bool CanUpgradeByPack(int aircraftId, bool isOpenUI = false)
        {
            long lackCount = GetUpgradeLackCount(aircraftId);
            if (lackCount <= 0)
            {
                return false;
            }

            AircraftCfgItem aircraftCfg = AircraftCfg.Data.Get(aircraftId);
            foreach (int itemId in aircraftCfg.upgradePack)
            {
                long itemCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(itemId);
                if (itemCount >= lackCount)
                {
                    if (isOpenUI)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<ChooseBox_Use>(LDUICfg.ChooseBox_Use).SetLoadedCall(ui =>
                        {
                            LDCommonItem commonItem = new LDCommonItem(LDCommonType.Item, itemId, itemCount);
                            ui?.InitData(commonItem);
                            ui?.SetSelectAircraftInfo(aircraftCfg.pieceID, lackCount);
                        });
                    }

                    return true;
                }
            }

            return false;
        }

        // 升阶缺少碎片数量
        public long GetUpgradeLackCount(int aircraftId)
        {
            GetAircraftQuaUpCost(aircraftId, out long ownedNum, out long needNum);
            return needNum - ownedNum;
        }

        // 升阶需要的礼包数量
        public LDCommonItem GetPackCount(int aircraftId)
        {
            int itemId = 0;
            long packCount = 0;

            AircraftCfgItem aircraftCfg = AircraftCfg.Data.Get(aircraftId);
            if (aircraftCfg != null)
            {
                foreach (int id in aircraftCfg.upgradePack)
                {
                    long itemCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(id);
                    if (itemId == 0 || itemCount > packCount)
                    {
                        itemId = id;
                        packCount = itemCount;
                    }
                }
            }

            if (itemId == 0)
            {
                return null;
            }

            return new LDCommonItem(LDCommonType.Item, itemId, packCount);
        }

        // 获取机甲平台升阶技能配置信息
        public Dictionary<int, AircraftQualitySkillItem> GetAircraftQuaSkillCfg(int aircraftId)
        {
            Dictionary<int, AircraftQualitySkillItem> skillDic = new Dictionary<int, AircraftQualitySkillItem>();
            AircraftCfgItem cfgItem = AircraftCfg.Data.Get(aircraftId);
            foreach (int ueID in cfgItem.UpgradeEffect)
            {
                if (AircraftQualitySkill.Data.TryGet(ueID, out AircraftQualitySkillItem mqsCfg, false))
                {
                    if (!skillDic.ContainsKey(mqsCfg.Requirement))
                    {
                        skillDic.Add(mqsCfg.Requirement, mqsCfg);
                    }
                    else
                    {
                        Global.LogError($"AircraftQualitySkill表品质配置重复 ID = {ueID}  机甲平台ID = {aircraftId} 品质 = {mqsCfg.Requirement}");
                    }
                }
            }

            return skillDic;
        }


        // 获取机甲平台品质对应的属性加成数据
        public List<LDAttrAddition> GetQualityAttrAdditions(int aircraftId, AircraftQualityItem quaCfg)
        {
            AircraftCfgItem cfg = AircraftCfg.Data.Get(aircraftId);
            List<LDAttrAddition> additions = new List<LDAttrAddition>();
            int coef = 10000;
            for (int i = 0; i < quaCfg.Attr.Length; i++)
            {
                if (cfg.AscensionCoefficient.Length > i)
                {
                    coef = cfg.AscensionCoefficient[i];
                }
                else
                {
                    Global.LogError($"MechaQuality 属性系数 与 属性加成 表参数数量不一致");
                }

                LDAttrAddition lDAttrAddition = new LDAttrAddition(quaCfg.Attr[i], coef);
                additions.Add(lDAttrAddition);
            }

            return additions;
        }


        // 获取默认机甲ID
        public int GetDefaultAircraftId()
        {
            return GlobalCfg.Data.Get(10003).valueInt;
        }

        public AircraftQualitySSItem GetQualitySsItem(int aircraftId, int quality)
        {
            int nextQuality = quality + 1;
            foreach (AircraftQualitySSItem ssItem in AircraftQualitySS.Data.items)
            {
                if (ssItem.aircraftId == aircraftId && ssItem.quality == nextQuality)
                {
                    return ssItem;
                }
            }

            return null;
        }

        // 是否是新机甲
        public bool IsNewAircraft(int aircraftId)
        {
            if (aircraftId == GetDefaultAircraftId()) // 默认机甲不算新的
            {
                return false;
            }

            if (IsAircraftUnlocked(aircraftId))
            {
                string newTag = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.NewAircraft + aircraftId.ToString());
                return string.IsNullOrEmpty(newTag);
            }
            else
            {
                return false;
            }
        }

        // 清除新机甲红点状态
        public bool ClearAircraftNewState(int aircraftId)
        {
            if (IsNewAircraft(aircraftId))
            {
                Global.gApp.gSystemMgr.gNetClientDataMgr.ChangeGenerateClientData(GeneralClientDataKey.NewAircraft + aircraftId.ToString(), "1");
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_MechaMain_List);
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 重置道具
        /// </summary>
        /// <param name="partId"></param>
        /// <returns></returns>
        public Dictionary<int, LDCommonItem> GetResetCostItem(int aircraftId)
        {
            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
            // int lv = Mathf.Max(1, aircraftInfo.Level);

            Dictionary<int, LDCommonItem> resetCost = new Dictionary<int, LDCommonItem>();

            for (int i = 1; i < aircraftInfo.Level; i++)
            {
                var levelItem = AircraftLevel.Data.Get(i);
                LDCommonItem commonItem = new LDCommonItem(levelItem.cost);
                if (resetCost.ContainsKey(commonItem.Id))
                {
                    resetCost[commonItem.Id].AddItemNum(commonItem.Num);
                }
                else
                {
                    resetCost[commonItem.Id] = commonItem;
                }
            }

            return resetCost;
        }


        /// <summary>
        /// 机甲平台属性
        /// </summary>
        /// <returns></returns>
        public Dictionary<int, LDAttrAddition> GetAircraftAttr(int aircraftId)
        {
            Dictionary<int, LDAttrAddition> attrDic = new Dictionary<int, LDAttrAddition>();

            LDNetAircraftInfo aircraftInfo = Data.GetAircraftInfo(aircraftId);
            if (aircraftInfo == null)
            {
                Global.LogError($"获取机甲属性失败，机甲ID不存在: {aircraftId}");
                return attrDic;
            }

            AircraftLevelItem levelItem = AircraftLevel.Data.Get(aircraftInfo.Level);
            if (levelItem == null)
            {
                Global.LogError($"获取机甲属性失败，机甲等级配置不存在: {aircraftInfo.Level}");
                return attrDic;
            }

            AircraftCfgItem aircraftCfgItem = AircraftCfg.Data.Get(aircraftId);

            for (int i = 0; i < levelItem.Attr.Length; i++)
            {
                string attrStr = levelItem.Attr[i];
                LDAttrAddition attrAddition = new LDAttrAddition(attrStr, aircraftCfgItem.UpgradeCoefficient[i]);
                attrDic[attrAddition.Id] = attrAddition;
            }

            return attrDic;
        }

        public List<int> GetAircraftSkills(int aircraftId, int qua)
        {
            List<int> skills = new List<int>();
            AircraftCfgItem aircraftItem = AircraftCfg.Data.Get(aircraftId);
            if (aircraftItem != null)
            {
                foreach (int id in aircraftItem.UpgradeEffect)
                {
                    AircraftQualitySkillItem quaSkillItem = AircraftQualitySkill.Data.Get(id);
                    if (quaSkillItem != null)
                    {
                        if (quaSkillItem.Requirement <= qua)
                        {
                            foreach (int delSkillId in quaSkillItem.deleteSkill)
                            {
                                if (skills.Contains(delSkillId))
                                {
                                    skills.Remove(delSkillId);
                                }
                            }

                            foreach (int addSkillId in quaSkillItem.addSkill)
                            {
                                if (!skills.Contains(addSkillId))
                                {
                                    skills.Add(addSkillId);
                                }
                            }
                        }
                    }
                }
            }

            return skills;
        }

        // 检测 单击升级 长按提示
        public void CheckLvUpLongPressTips()
        {
            m_ClickLvUpBtnTimes++;
            if (m_ClickLvUpBtnTimes == 5) // 第5次检测一下就行了 不重要
            {
                long time = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, LDLocalDataKeys.Aircraft_LvBtnLongPressTips, 0);
                if (DateTimeUtil.SameDay(time, DateTimeUtil.GetServerTime()))
                {
                    return;
                }

                int val = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.Aircraft_LvBtnLongPressTips_NoTips, 0);
                if (val <= 0)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<LongPressTipsUI>(LDUICfg.LongPressTipsUI).SetLoadedCall(ui => { ui?.RefreshUI(LongPressTipsUI.Type_AircraftLvUp); });
                    Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, LDLocalDataKeys.Aircraft_LvBtnLongPressTips, DateTimeUtil.GetServerTime());
                }
            }
        }

        // 存储单机升级长按提示
        public void SaveLvUpBtnLongPressState(bool enable)
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.Aircraft_LvBtnLongPressTips_NoTips, enable ? 1 : 0);
        }

        // 检测 单击升级 长按提示
        public void CheckUpgradeLongPressTips()
        {
            m_ClickUpgradeBtnTimes++;
            if (m_ClickUpgradeBtnTimes == 5) // 第5次检测一下就行了 不重要
            {
                long time = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, LDLocalDataKeys.Aircraft_UpgradeBtnLongPressTips, 0);
                if (DateTimeUtil.SameDay(time, DateTimeUtil.GetServerTime()))
                {
                    return;
                }

                int val = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.Aircraft_UpgradeBtnLongPressTips_NoTips, 0);
                if (val <= 0)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<LongPressTipsUI>(LDUICfg.LongPressTipsUI).SetLoadedCall(ui => { ui?.RefreshUI(LongPressTipsUI.Type_AircraftUpgrade); });
                    Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, LDLocalDataKeys.Aircraft_UpgradeBtnLongPressTips, DateTimeUtil.GetServerTime());
                }
            }
        }

        // 存储单机升级长按提示
        public void SaveUpgradeBtnLongPressState(bool enable)
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.Aircraft_UpgradeBtnLongPressTips_NoTips, enable ? 1 : 0);
        }

        #region 红点

        // 判断机甲是否解锁
        public bool IsMechaUnlocked(int aircraftId)
        {
            return Data.GetAircraftInfo(aircraftId) != null;
        }

        // 判断是否有机甲平台可以解锁
        public bool HasMechaCanBeUnlocked()
        {
            AircraftCfgItem[] aircraftItems = AircraftCfg.Data.items;
            foreach (AircraftCfgItem item in aircraftItems)
            {
                if (!IsMechaUnlocked(item.id) && GetAircraftUnlockCost(item.id, out _, out _, out _))
                {
                    return true;
                }
            }

            return false;
        }

        // 是否有新机甲平台b
        public bool HasNewMecha()
        {
            foreach (LDNetAircraftInfo aircraft in Data.AircraftInfos.Values)
            {
                if (IsNewAircraft(aircraft.AircraftId))
                {
                    return true;
                }
            }

            return false;
        }

        public Dictionary<int, LDNetAircraftInfo> GetAllAircraft()
        {
            return Data.AircraftInfos;
        }

        /// <summary>
        /// 升级红点
        /// </summary>
        /// <returns></returns>
        public LDRedTipsState GetLevelUpRed(int aircraftId)
        {
            if (aircraftId <= 0)
                return LDRedTipsState.None;
            if (!IsAircraftMaxLevel(aircraftId) && GetAircraftLevelUpCost(aircraftId, out _, out _))
            {
                return LDRedTipsState.Arrow;
            }

            return LDRedTipsState.None;
        }

        /// <summary>
        /// 升阶红点
        /// </summary>
        /// <returns></returns>
        public LDRedTipsState GetUpgradeRed(int aircraftId)
        {
            if (IsCanUpgrade(aircraftId))
            {
                return LDRedTipsState.RedPoint;
            }

            return LDRedTipsState.None;
        }

        public LDRedTipsState GetXilianRed()
        {
            var count = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(LDSpecialItemId.AircraftXilian);
            if (count >= 100)
            {
                return LDRedTipsState.Arrow;
            }

            return LDRedTipsState.None;
        }

        /// <summary>
        /// 列表红点
        /// </summary>
        /// <returns></returns>
        public LDRedTipsState GetListRed()
        {
            if (HasMechaCanBeUnlocked())
            {
                return LDRedTipsState.RedPoint;
            }

            if (HasNewMecha())
            {
                return LDRedTipsState.RedPoint;
            }


            // foreach (LDNetAircraftInfo aircraft in Data.AircraftInfos.Values)
            // {
            //     LDRedTipsState state = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetRedStateForMechaSkin(aircraft.MechaId);
            //     if (state != LDRedTipsState.None)
            //     {
            //         return state;
            //     }
            // }

            return LDRedTipsState.None;
        }


        /// <summary>
        /// 洗练红点
        /// </summary>
        /// <returns></returns>
        public LDRedTipsState GetXiLianRed(int aircraftId)
        {
            return LDRedTipsState.None;
        }

        // 获取新机甲红点状态
        public LDRedTipsState GetNewMechaState(int aircraftId)
        {
            if (IsNewAircraft(aircraftId))
            {
                return LDRedTipsState.RedPoint;
            }

            return LDRedTipsState.None;
        }

        #endregion

        public bool IsCanGuide()
        {
            foreach (LDNetAircraftInfo info in Data.AircraftInfos.Values)
            {
                if (info.AircraftRefinementInfo.Effects.Count > 0)
                {
                    return false;
                }
            }

            return true;
        }
    }
}