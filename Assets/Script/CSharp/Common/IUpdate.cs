namespace LD 
{
    public interface ILifeDCycle : <PERSON>ife<PERSON>ycle,ID<PERSON><PERSON><PERSON>roy
    {
    }

    public interface ILifeCycle : IUpdate
    {
        void OnEnter();
        void OnExit();
    }
    public interface IDYDestroy:IUpdate
    {
        void OnDestroySelf();
    }    
    public interface IDMObj: IDYDestroy
    {
        void SetTimeScale(float timescale);
    }
    public interface IDMSObj : IDMObj
    {
        void SetRenderTimeScale(float timescale);
    }

    public interface INormalMode:IUpdate
    {
        void InitImp();
        void UpdateImp(float dt);
        void DestroyImp();
    }
    public interface IUpdate
    {
        void OnDUpdate(float dt);
	}
}