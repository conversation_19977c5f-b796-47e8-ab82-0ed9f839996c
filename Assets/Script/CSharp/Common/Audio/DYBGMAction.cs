using System;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace LD
{
    public class DYBGMFade
    {
        private AudioSource m_BGMAudioSource;
        int m_TimerId = -1;
        private string m_NextBGM;
        private float m_FadeInTime;
        private float m_Timer = 0.016f;
        private float m_ScaleTimer;
        private Action<string, float> m_PlayAction;
        public DYBGMFade(AudioSource audioSource)
        {
            m_BGMAudioSource = audioSource;
        }
        public void ZMWave_FadeOut(float fadeOutTime)
        {
            m_ScaleTimer = fadeOutTime;
            Global.gApp.gTimerMgr.RemoveTimer(m_TimerId);
            int timerCount = Mathf.CeilToInt(fadeOutTime / m_Timer);
            m_TimerId = Global.gApp.gTimerMgr.AddTimer(m_Timer, timerCount, ZMWave_FadeOutCallBack);
        }
        public void ZMWave_FadeIn(float fadeInTime)
        {
            m_ScaleTimer = fadeInTime;
            Global.gApp.gTimerMgr.RemoveTimer(m_TimerId);
            int timerCount = Mathf.CeilToInt(fadeInTime / m_Timer);
            m_TimerId = Global.gApp.gTimerMgr.AddTimer(m_Timer, timerCount, ZMWave_FadeInCallBack);
        }
        private void ZMWave_FadeOutCallBack(float dt, bool end)
        {
            if (!end)
            {
                m_BGMAudioSource.volume -= (m_Timer / m_ScaleTimer);
            }
            else
            {
                if (m_FadeInTime > 0.001f)
                {
                    m_PlayAction?.Invoke(m_NextBGM, 0);
                    ZMWave_FadeIn(m_FadeInTime);
                }
                m_ScaleTimer = 0f;
            }
        }
        private void ZMWave_FadeInCallBack(float dt, bool end)
        {
            if (end)
            {
                m_ScaleTimer = 0f;
                m_FadeInTime = 0f;
            }
            else
            {
                m_BGMAudioSource.volume += (m_Timer / m_ScaleTimer);
            }
        }
        //支持淡出，也支持淡出淡入
        public void ZMWave_FadeOutIn(float fadeOutTime, float fadeInTime = 0f, string bgmName = "", Action<string, float> action = null)
        {
            m_NextBGM = bgmName;
            m_FadeInTime = fadeInTime;
            m_PlayAction = action;
            ZMWave_FadeOut(fadeOutTime);
        }
    }
    public class LHBGMAction
    {
        AudioSource m_BGMAudioSource;
        AudioSourceController m_AudioSourceController;
        DYBGMFade m_BGMFade;

        public LHBGMAction(AudioSourceController ctroller, AudioSource audioSource)
        {
            m_AudioSourceController = ctroller;
            m_BGMAudioSource = audioSource;
            m_BGMFade = new DYBGMFade(audioSource);
        }
        public void PlayBGM(string bgmName, float fadeOutTime, float fadeInTime, float audioScale = 1)
        {
            if (!string.IsNullOrEmpty(bgmName))
            {
                //m_BGMFade.Stop();
                if (m_AudioSourceController.BGMVolum <= 0.001f)
                {
                    ZMWave_PlayBGMImp(bgmName, audioScale);
                }
                else
                {
                    m_BGMFade.ZMWave_FadeOutIn(fadeOutTime, fadeInTime, bgmName, ZMWave_PlayBGMImp);
                }
            }
        }
        public void PlayBGM(string bgmName, float fadeInTime, float audioScale)
        {
            if (!string.IsNullOrEmpty(bgmName))
            {
                //m_BGMFade.Stop();
                if (fadeInTime < 0.001f || m_AudioSourceController.BGMVolum <= 0.001f)
                {
                    ZMWave_PlayBGMImp(bgmName, audioScale);
                }
                else
                {
                    ZMWave_PlayBGMImp(bgmName, 0);

                    m_BGMFade.ZMWave_FadeIn(fadeInTime);

                }
            }
        }
        public void StopBGM(float fadeOutTime)
        {
            //m_BGMFade.Stop();
            if (fadeOutTime <= 0.001f || m_AudioSourceController.BGMVolum < 0.001f || !m_BGMAudioSource.isPlaying)
            {
                ZMWave_StopBGMImp();
            }
            else
            {
                m_BGMFade.ZMWave_FadeOutIn(fadeOutTime);
            }
        }
        private void ZMWave_PlayBGMImp(string bgnName, float audioScale)
        {
            m_BGMAudioSource.clip = Global.gApp.gResMgr.LoadAudioClip(bgnName, false);
            m_BGMAudioSource.loop = true;
            m_BGMAudioSource.volume = m_AudioSourceController.BGMVolum * audioScale;
            m_BGMAudioSource.Play();
        }
        private void ZMWave_StopBGMImp()
        {
            if (m_BGMAudioSource.clip != null)
            {
                Addressables.Release(m_BGMAudioSource.clip);
            }
            m_BGMAudioSource.clip = null;
            m_BGMAudioSource.Stop();
        }
    }
}
