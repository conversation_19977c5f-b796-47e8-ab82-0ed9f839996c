using System;
using System.Collections.Generic;
namespace LD
{
    public class LDSafeListMObj<TVal> : LDSafeListD<TVal>, IDMObj where TVal : IDMObj
    {
        public void SetTimeScale(float timescale)
        {
            if (m_IsDirty)
            {
                UnityEngine.Debug.LogError(" DYSafeListD not Clean");
                return;
            }
            foreach (TVal kv in m_Datas)
            {
                if (!m_RemoveCacheMap.Contains(kv))
                {
                    kv.SetTimeScale(timescale);
                }
            }
            foreach (TVal kv in m_AddCache)
            {
                if (!m_RemoveCacheMap.Contains(kv))
                {
                    kv.SetTimeScale(timescale);
                }
            }
        }
    }

    public class LDSafeListD<TVal> : LDSafeList<TVal>, IDYDestroy where TVal : IDYDestroy
    {
        // 一般就是 战斗结束 替代 foreach 用的 别的地方别用。
        public void OnDestroySelf()
        {
            //m_IsDirty = false; 如果 add 有数据 那么有可能会 少destroy
            if (m_IsDirty)
            {
                UnityEngine.Debug.LogError(" DYSafeListD not Clean");
                return;
            }
            foreach (TVal kv in m_Datas)
            {
                if (!m_RemoveCacheMap.Contains(kv))
                {
                    kv.OnDestroySelf();
                }
            }
            foreach (TVal kv in m_AddCache)
            {
                if (!m_RemoveCacheMap.Contains(kv))
                {
                    kv.OnDestroySelf();
                }
            }
            Clear();
        }
    }
    public class LDSafeList<TVal> : IUpdate where TVal : IUpdate
    {
        public int Count
        {
            get { return m_Datas.Count - m_RemoveCache.Count; }
        }
        public int RCount
        {
            get { return m_Datas.Count - m_RemoveCache.Count + m_AddCache.Count; }
        }

        public int ValCount
        {
            get { return m_Datas.Count; }
        }
        protected List<TVal> m_Datas;
        protected List<TVal> m_AddCache;
        protected List<TVal> m_RemoveCache;
        protected HashSet<TVal> m_RemoveCacheMap = new HashSet<TVal>();
        protected int m_TraversalCount = 0;
        protected bool m_IsDirty = false;
        protected bool m_IsClearData = false;

        protected Action<TVal, float> m_UpdateAct;
        protected Action<TVal, float> m_DirtyUpdateAct;
        protected Action<TVal, float> m_CleanUpdateAct;
        public LDSafeList()
        {
            m_DirtyUpdateAct = DirtyUpdate;
            m_CleanUpdateAct = CleanUpdate;
            m_UpdateAct = CleanUpdate;
            Init();
        }
        private void Init()
        {
            m_Datas = new List<TVal>();
            m_AddCache = new List<TVal>();
            m_RemoveCache = new List<TVal>();
            m_TraversalCount = 0;
            m_IsDirty = false;
        }

        public List<TVal> GetAll()
        {
            return m_Datas;
        }
        public List<TVal> GetAllForDel()
        {
            if (m_AddCache.Count > 0)
            {
                foreach (TVal val in m_AddCache)
                {
                    m_Datas.Add(val);
                }
            }
            return m_Datas;
        }
        public TVal Get(int index)
        {
            if (m_Datas.Count > 0)
            {
                return m_Datas[index];
            }
            else
            {
                return default(TVal);
            }
        }
        public void Add(TVal val)
        {
            if (m_TraversalCount > 0)
            {
                m_AddCache.Add(val);
                m_RemoveCache.Remove(val);
                m_RemoveCacheMap.Remove(val);
                m_IsDirty = true;
                m_UpdateAct = m_DirtyUpdateAct;
            }
            else
            {
                m_Datas.Add(val);
            }
        }
        public void SwitchData(int startIndex, int endIndex)
        {
            // 自己保证变量 安全性
            if (m_Datas.Count > 1 && m_Datas.Count > startIndex && m_Datas.Count > endIndex)
            {
                TVal startVal = m_Datas[startIndex];
                TVal endVal = m_Datas[endIndex];
                m_Datas[startIndex] = endVal;
                m_Datas[endIndex] = startVal;
            }
        }

        public void Foreach(Action<TVal> callBack)
        {
//#if !UNITY_EDITOR
//            try
//#endif
            {
                if (m_IsClearData)
                {
                    Clear();
                    m_IsClearData = false;
                }
                //m_IsDirty = false;
                m_TraversalCount = m_TraversalCount + 1;
                foreach (TVal kv in m_Datas)
                {
                    if (!m_RemoveCacheMap.Contains(kv))
                    {
                        callBack(kv);
                    }
                }
            }
// #if !UNITY_EDITOR

//            catch (Exception e)
//            {
//                UnityEngine.Debug.LogError("msg" + e.Message);
//            }
//            finally
//#endif
            {
                ClearCacheData();
                if (m_IsClearData)
                {
                    Clear();
                    m_IsClearData = false;
                }
            }
        }
        public bool Contains(TVal val)
        {
            return m_Datas.Contains(val);
        }
        public void Remove(TVal val)
        {
            if (m_TraversalCount > 0)
            {
                m_RemoveCache.Add(val);
                if (!m_RemoveCacheMap.Contains(val))
                {
                    m_RemoveCacheMap.Add(val);
                }
                m_AddCache.Remove(val);
                m_IsDirty = true;
                m_UpdateAct = m_DirtyUpdateAct;
            }
            else
            {
                m_Datas.Remove(val);
            }
        }

        public void OnDUpdate(float dt)
        {
            // 防止在遍历的过程中 报错。导致 遍历失败
//#if !UNITY_EDITOR
//            try
//#endif
        {
                if (m_IsClearData)
                {
                    Clear();
                    m_IsClearData = false;
                }
                m_IsDirty = false;
                m_UpdateAct = m_CleanUpdateAct;
                m_TraversalCount = m_TraversalCount + 1;
                foreach (TVal kv in m_Datas)
                {
                    m_UpdateAct(kv, dt);
                }
            }
//#if !UNITY_EDITOR

//            catch (Exception e)
//            {
//                UnityEngine.Debug.LogError("msg" + e.Message);
//            }
//            finally
//#endif
            {
                ClearCacheData();
                if (m_IsClearData)
                {
                    Clear();
                    m_IsClearData = false;
                }
            }

        }

        private void DirtyUpdate(TVal val, float dt)
        {
            if (!m_RemoveCacheMap.Contains(val))
            {
                val?.OnDUpdate(dt);
            }
        }
        private void CleanUpdate(TVal val, float dt)
        {
            val?.OnDUpdate(dt);
        }
        public void ClearCacheData()
        {
            m_TraversalCount = m_TraversalCount - 1;
            if (m_IsDirty && m_TraversalCount == 0)
            {
                m_IsDirty = false;
                foreach (TVal kv in m_AddCache)
                {
                    if (!m_Datas.Contains(kv))
                    {
                        m_Datas.Add(kv);
                    }
                }
                m_AddCache.Clear();

                foreach (TVal key in m_RemoveCache)
                {
                    m_Datas.Remove(key);
                }
                m_RemoveCacheMap.Clear();
                m_RemoveCache.Clear();
            }
        }
        public void OnDestroy()
        {
            if (m_TraversalCount == 0)
            {
                Clear();
                m_IsClearData = false;
            }
            else
            {
                m_IsClearData = true;
            }
        }

        public void Clear()
        {
            if (m_TraversalCount == 0)
            {
                m_Datas.Clear();
                m_AddCache.Clear();
                m_RemoveCacheMap.Clear();
                m_RemoveCache.Clear();
                m_TraversalCount = 0;
                m_IsDirty = false;
                m_IsClearData = false;
            }
            else
            {
                m_IsClearData = true;
            }
        }


        /// <summary>
        /// 确保使用安全 并且
        /// </summary>
        /// <returns></returns>
        public List<TVal> GetOriSafeData()
        {
            if (!m_IsDirty)
            {
                return m_Datas;
            }
            else
            {
                return null;
            }
        }
        public List<TVal> GetSafeData()
        {
            List<TVal> ts = new List<TVal>();
            GetSafeData(ts);
            return ts;
        }
        public void GetSafeData(List<TVal> ts)
        {
            ts.Clear();
            if (!m_IsDirty)
            {
                ts.AddRange(m_Datas);

            }
            else
            {
                Foreach(
                    (TVal player) =>
                    {
                        ts.Add(player);
                    }
                );
            }
        }

        public int GetCount()
        {
            return m_Datas.Count;
        }

        public TVal this[int index]
        {
            get
            {
                return Get(index);
            }
        }
        public static implicit operator LDSafeList<TVal>(List<TVal> payment)
        {
            LDSafeList<TVal> safeList = new LDSafeList<TVal>();
            safeList.m_Datas = payment;
            return safeList;
        }
        public static implicit operator LDSafeList<TVal>(TVal[] payment)
        {
            LDSafeList<TVal> safeList = new LDSafeList<TVal>();
            safeList.m_Datas = new List<TVal>(payment);
            return safeList;
        }
    }
}
