using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDServerTimerMgr : IUpdate
    {
        private int m_Guid = 0;
        private LDSafeMap<int, LDServerTimer> m_TimerMap;

        public LDServerTimerMgr()
        {
            m_TimerMap = new LDSafeMap<int, LDServerTimer>();
        }

        public void OnDUpdate(float dt)
        {
            if (dt > 0)
            {
                m_TimerMap.OnDUpdate(dt);
            }
        }

        public int AddTimer(long expireTime, Action callback)
        {
            int guid = GetNewGuid();
            LDServerTimer timer = new LDServerTimer(this, guid, expireTime, callback);
            m_TimerMap.Add(guid, timer);
            return guid;
        }

        public void RemoveTimer(int key)
        {
            m_TimerMap.Remove(key);
        }
        private int GetNewGuid()
        {
            m_Guid++;
            return m_Guid;
        }

        public void ClearTimer()
        {
            m_TimerMap.OnDestroy();
        }
    }
}
