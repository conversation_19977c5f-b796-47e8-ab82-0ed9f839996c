using System.Collections.Generic;

namespace LD
{
    public partial class GameCtrl
    {
        public void ChangeToCSArenaFightScene(LDCSArenaFighter attacker, LDCSArenaFighter defender, int passId, long uniqueId, bool recordFight = false)
        {
            RecordFight = recordFight;
            PassItem passItem = Pass.Data.Get(passId);
            ChangeToLoadingScene(passItem.scene, passItem.loading, () => { ChangeToCSArenaFightSceneFormLoading(attacker, defender, passId, uniqueId); });
        }
        
        public void ChangeToCSArenaFightSceneFormLoading(LDCSArenaFighter attacker, LDCSArenaFighter defender, int passId, long uniqueId)
        {
            LDFightData fightData = new LDFightData();
            fightData.UniqueId = uniqueId;
            fightData.PassId = passId;

            LDRoleData roleData = CreateCSArenaRoleData(attacker);
            roleData.TeamID = 1;
            roleData.MaxReviveTimes = 0;
            roleData.RivalRoleGuid = defender.Fighter.BriefRole.PlayerId;
            fightData.RoleDatas.Add(roleData);

            roleData = CreateCSArenaRoleData(defender);
            roleData.TeamID = 2;
            roleData.MaxReviveTimes = 0;
            roleData.RivalRoleGuid = attacker.Fighter.BriefRole.PlayerId;
            fightData.RoleDatas.Add(roleData);

            LDPassInfo battleInfo = new LDPassInfo(fightData);
            battleInfo.CreateCSArenaPassInfo();
            LDCSArenaFightScene scene = new LDCSArenaFightScene(battleInfo);
            LDFightFrameCtrl frameCtrl = new LDFightFrameCtrl(scene);
            ChangeCtrl(frameCtrl);
            System.GC.Collect();
        }

        public LDRoleData CreateCSArenaRoleData(LDCSArenaFighter arenaFighter)
        {
            LDRoleData roleData = new LDRoleData();

            roleData.RoleGuid = arenaFighter.Fighter.BriefRole.PlayerId;
            roleData.Name = arenaFighter.Fighter.BriefRole.Name;

            foreach (LdNetRoleUAVInfo uavInfo in arenaFighter.Fighter.UAVInfos)
            {
                LDUAVInfo UAVInfo = new LDUAVInfo();
                UAVInfo.UAVId = uavInfo.CfgId;
                UAVInfo.UAVQua = uavInfo.Quality;
                roleData.UAVInfo.Add(UAVInfo);
            }

            foreach (LDRoleAirCraftSlotInfo aircraftSlot in arenaFighter.Formation.AirCraftFormation.Slots.Values)
            {
                if (aircraftSlot.Models.Count <= 0)
                {
                    continue;
                }
                
                LDAircraftInfo aircraftInfo = new LDAircraftInfo();
                aircraftInfo.CfgId = aircraftSlot.AirCraftCfgId;
                aircraftInfo.Slot = aircraftSlot.Slot;
                aircraftInfo.Mechas.Clear();

                foreach (KeyValuePair<int, LDDIYData> modelInfo in aircraftSlot.Models)
                {
                    aircraftInfo.Mechas.Add(modelInfo.Value.BodyId);
                    
                    LDHeroInfo heroInfo = new LDHeroInfo();
                    heroInfo.MechaId = modelInfo.Value.BodyId;
                    heroInfo.Slot = modelInfo.Key;
                    heroInfo.DIYData = modelInfo.Value;
                    heroInfo.AricraftId = aircraftSlot.AirCraftCfgId;
                    foreach (LDDIYPartData item in heroInfo.DIYData.Data)
                    {
                        LDPartInfo partInfo = new LDPartInfo();
                        heroInfo.PartInfo[item.PId] = partInfo;
                        heroInfo.AllPart.Add(item.PId);
                    }
                    heroInfo.DriveInfo = Global.gApp.gSystemMgr.gCampsiteMgr.GetDriverBattleInfo(arenaFighter.Fighter.DriverInfos, heroInfo);
                    roleData.HeroInfos.Add(heroInfo);    
                }
                
                foreach (LDNetAircraftInfo aircraftData in arenaFighter.Fighter.AircraftInfos)
                {
                    if (aircraftData.AircraftId == aircraftInfo.CfgId)
                    {
                        aircraftInfo.Qua = aircraftData.Quality;
                        break;
                    }
                }
                
                roleData.AircraftInfos.Add(aircraftInfo);
            }
            
            foreach (LDNetRoleDiyPartInfo netPartInfo in arenaFighter.Fighter.PartInfos)
            {
                foreach (LDHeroInfo heroInfo in roleData.HeroInfos)
                {
                    if (heroInfo.PartInfo.ContainsKey(netPartInfo.CfgId))
                    {
                        foreach (LDDIYPartData item in heroInfo.DIYData.Data)
                        {
                            if (item.PId == netPartInfo.CfgId)
                            {
                                item.SId = netPartInfo.SkinId;
                                item.HSId = netPartInfo.HideSkin;
                            }
                        }
                        LDPartInfo partInfo = heroInfo.PartInfo[netPartInfo.CfgId];
                        partInfo.SkinId = netPartInfo.SkinId;
                        partInfo.Lv = netPartInfo.Level;
                        partInfo.Qua = netPartInfo.Quality;
                        partInfo.SkinStar = netPartInfo.SkinStar;
                        heroInfo.Skill_id_Extern.AddRange(Global.gApp.gSystemMgr.gMechaPartSkinePart.GetPartSkinCitiaosBySkinID(netPartInfo.SkinId, partInfo.SkinStar));
                    }
                }
            }

            foreach (LDNetRoleMechaInfo mechaInfo in arenaFighter.Fighter.MechaInfos)
            {
                foreach (LDHeroInfo heroInfo in roleData.HeroInfos)
                {
                    if (heroInfo.DIYData.BodyId == mechaInfo.CfgId)
                    {
                        heroInfo.DIYData.BodySkinId = mechaInfo.SkinId;
                        heroInfo.DIYData.HidBodySkinId = mechaInfo.HideSkin ? 1 : 0;
                        heroInfo.MechaStar = mechaInfo.SkinStar;
                        heroInfo.CurArrs = mechaInfo.GetAttrItem();
                        
                        int enhancementUnlockId = mechaInfo.EnhanceLv;
                        heroInfo.Skill_id = Global.gApp.gSystemMgr.gEnhancementMgr.GetCitiaoInfo(enhancementUnlockId);

                        int quality = mechaInfo.Quality;
                        Dictionary<int, int> partQua = new Dictionary<int, int>();
                        foreach (LDDIYPartData item in heroInfo.DIYData.Data)
                        {
                            if (heroInfo.PartInfo.ContainsKey(item.PId))
                            {
                                LDPartInfo partInfo = heroInfo.PartInfo[item.PId];
                                partQua[item.PId] = partInfo.Qua;
                            }
                        }
                        heroInfo.CitiaoGroup_Info = heroInfo.DIYData.GetDIYCiTiaoGroup(quality, partQua, heroInfo.PartInfo, heroInfo.MechaStar);
                        heroInfo.Skill_id_Extern.AddRange(Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetMechaSkinCitiaos(heroInfo.DIYData.BodyId, heroInfo.DIYData.BodySkinId, heroInfo.MechaStar));
                        break;
                    }
                }
            }
            
            roleData.AttrEffect = arenaFighter.Fighter.GetAllExtraEffect();
            roleData.AssistBattleInfo = new List<AssistBattleInfo>();
            roleData.MaxCiTiaoFreshTimes = 0;
            roleData.CiTiaoFreshTimes = 0;
            roleData.MonthCardNormal = false;
            
            return roleData;
        }

    }
}