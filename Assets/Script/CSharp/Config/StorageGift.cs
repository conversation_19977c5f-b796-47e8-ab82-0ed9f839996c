//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class StorageGift : ScriptableObject {

		public static string CfgName = "StorageGift";

		public static StorageGift Data{ get { return Global.gApp.gGameData.GetData<StorageGift>(CfgName); } }
		[SerializeField, HideInInspector]
		private StorageGiftItem[] _Items;
		public StorageGiftItem[] items { get { return _Items; } }

		public StorageGiftItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				StorageGiftItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("StorageGift表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out StorageGiftItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("StorageGift表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class StorageGiftItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _EarlyItemNum01;
		/// <summary>
		/// 道具预购数量（低档）
		/// </summary>
		public string earlyItemNum01 { get { return _EarlyItemNum01; } }

		[SerializeField, HideInInspector]
		private string _ReturnItemNum01;
		/// <summary>
		/// VIP档位和道具返还的数量（低档）
		/// </summary>
		public string returnItemNum01 { get { return _ReturnItemNum01; } }

		[SerializeField, HideInInspector]
		private string _DropId01;
		/// <summary>
		/// 实际返还内容（低档）
		/// </summary>
		public string dropId01 { get { return _DropId01; } }

		[SerializeField, HideInInspector]
		private string _EarlyItemNum02;
		/// <summary>
		/// 道具预购数量（高档）
		/// </summary>
		public string earlyItemNum02 { get { return _EarlyItemNum02; } }

		[SerializeField, HideInInspector]
		private string _ReturnItemNum02;
		/// <summary>
		/// VIP档位和道具返还的数量（高档）
		/// </summary>
		public string returnItemNum02 { get { return _ReturnItemNum02; } }

		[SerializeField, HideInInspector]
		private string _DropId02;
		/// <summary>
		/// 实际返还内容（高档）
		/// </summary>
		public string dropId02 { get { return _DropId02; } }

		[SerializeField, HideInInspector]
		private int _MailID;
		/// <summary>
		/// 活动过期未领取的邮件ID
		/// </summary>
		public int mailID { get { return _MailID; } }

		[SerializeField, HideInInspector]
		private string _ReturnMailItem01;
		/// <summary>
		/// 奖励自动补发（低档）
		/// </summary>
		public string returnMailItem01 { get { return _ReturnMailItem01; } }

		[SerializeField, HideInInspector]
		private string _ReturnMailItem02;
		/// <summary>
		/// 奖励自动补发（高档）
		/// </summary>
		public string returnMailItem02 { get { return _ReturnMailItem02; } }

		public override string ToString() {
			return string.Format("[StorageGiftItem]{{id:{0}, earlyItemNum01:{1}, returnItemNum01:{2}, dropId01:{3}, earlyItemNum02:{4}, returnItemNum02:{5}, dropId02:{6}, mailID:{7}, returnMailItem01:{8}, returnMailItem02:{9}}}",
				id, earlyItemNum01, returnItemNum01, dropId01, earlyItemNum02, returnItemNum02, dropId02, mailID, returnMailItem01, returnMailItem02);
		}

		public static implicit operator bool(StorageGiftItem item) {
			return item != null;
		}

	}

}
