//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class CitiaoGroup : ScriptableObject {

		public static string CfgName = "CitiaoGroup";

		public static CitiaoGroup Data{ get { return Global.gApp.gGameData.GetData<CitiaoGroup>(CfgName); } }
		[SerializeField, HideInInspector]
		private CitiaoGroupItem[] _Items;
		public CitiaoGroupItem[] items { get { return _Items; } }

		public CitiaoGroupItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				CitiaoGroupItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("CitiaoGroup表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out CitiaoGroupItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("CitiaoGroup表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class CitiaoGroupItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int[] _Citiaoitems;
		/// <summary>
		/// 备注
		/// </summary>
		public int[] citiaoitems { get { return _Citiaoitems; } }

		[SerializeField, HideInInspector]
		private int _MaxLv;
		/// <summary>
		/// 词条ID
		/// </summary>
		public int maxLv { get { return _MaxLv; } }

		[SerializeField, HideInInspector]
		private int _CitiaoNAME;
		/// <summary>
		/// 等级上限
		/// </summary>
		public int citiaoNAME { get { return _CitiaoNAME; } }

		[SerializeField, HideInInspector]
		private int _CitiaoDescribe;
		/// <summary>
		/// 词条组名称
		/// </summary>
		public int citiaoDescribe { get { return _CitiaoDescribe; } }

		[SerializeField, HideInInspector]
		private string _Icon;
		/// <summary>
		/// 词条描述
		/// </summary>
		public string icon { get { return _Icon; } }

		[SerializeField, HideInInspector]
		private int[] _RelateIcon;
		/// <summary>
		/// 图标（特殊显示）
		/// </summary>
		public int[] relateIcon { get { return _RelateIcon; } }

		public override string ToString() {
			return string.Format("[CitiaoGroupItem]{{id:{0}, citiaoitems:{1}, maxLv:{2}, citiaoNAME:{3}, citiaoDescribe:{4}, icon:{5}, relateIcon:{6}}}",
				id, array2string(citiaoitems), maxLv, citiaoNAME, citiaoDescribe, icon, array2string(relateIcon));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(CitiaoGroupItem item) {
			return item != null;
		}

	}

}
