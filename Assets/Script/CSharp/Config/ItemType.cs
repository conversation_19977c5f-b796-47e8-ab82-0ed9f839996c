//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class ItemType : ScriptableObject {

		public static string CfgName = "ItemType";

		public static ItemType Data{ get { return Global.gApp.gGameData.GetData<ItemType>(CfgName); } }
		[SerializeField, HideInInspector]
		private ItemTypeItem[] _Items;
		public ItemTypeItem[] items { get { return _Items; } }

		public ItemTypeItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				ItemTypeItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("ItemType表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out ItemTypeItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("ItemType表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class ItemTypeItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _EnumName;
		/// <summary>
		/// 类型D
		/// </summary>
		public string enumName { get { return _EnumName; } }

		public override string ToString() {
			return string.Format("[ItemTypeItem]{{id:{0}, enumName:{1}}}",
				id, enumName);
		}

		public static implicit operator bool(ItemTypeItem item) {
			return item != null;
		}

	}

}
