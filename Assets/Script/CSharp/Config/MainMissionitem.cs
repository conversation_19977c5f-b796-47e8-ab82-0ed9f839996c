//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class MainMissionitem : ScriptableObject {

		public static string CfgName = "MainMissionitem";

		public static MainMissionitem Data{ get { return Global.gApp.gGameData.GetData<MainMissionitem>(CfgName); } }
		[SerializeField, HideInInspector]
		private MainMissionitemItem[] _Items;
		public MainMissionitemItem[] items { get { return _Items; } }

		public MainMissionitemItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				MainMissionitemItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("MainMissionitem表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out MainMissionitemItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("MainMissionitem表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class MainMissionitemItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 奖励ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Order;
		/// <summary>
		/// 顺序
		/// </summary>
		public int order { get { return _Order; } }

		[SerializeField, HideInInspector]
		private int _MainMissionPackId;
		/// <summary>
		/// 礼包ID
		/// </summary>
		public int MainMissionPackId { get { return _MainMissionPackId; } }

		[SerializeField, HideInInspector]
		private int _IapId;
		/// <summary>
		/// 商品ID
		/// </summary>
		public int iapId { get { return _IapId; } }

		[SerializeField, HideInInspector]
		private string[] _PackItem;
		/// <summary>
		/// 礼包奖励
		/// </summary>
		public string[] packItem { get { return _PackItem; } }

		public override string ToString() {
			return string.Format("[MainMissionitemItem]{{id:{0}, order:{1}, MainMissionPackId:{2}, iapId:{3}, packItem:{4}}}",
				id, order, MainMissionPackId, iapId, array2string(packItem));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(MainMissionitemItem item) {
			return item != null;
		}

	}

}
