//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class iap : ScriptableObject {

		public static string CfgName = "iap";

		public static iap Data{ get { return Global.gApp.gGameData.GetData<iap>(CfgName); } }
		[SerializeField, HideInInspector]
		private iapItem[] _Items;
		public iapItem[] items { get { return _Items; } }

		public iapItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				iapItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("iap表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out iapItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("iap表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class iapItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _Google_play_goods_id;
		/// <summary>
		/// 备注
		/// </summary>
		public string google_play_goods_id { get { return _Google_play_goods_id; } }

		[SerializeField, HideInInspector]
		private string _Appstore_goods_id;
		/// <summary>
		/// GooglePlay 产品ID
		/// </summary>
		public string appstore_goods_id { get { return _Appstore_goods_id; } }

		[SerializeField, HideInInspector]
		private float _Price;
		/// <summary>
		/// Appstore 产品ID
		/// </summary>
		public float price { get { return _Price; } }

		[SerializeField, HideInInspector]
		private int _Sourceprice;
		/// <summary>
		/// 价格（美元）
		/// </summary>
		public int sourceprice { get { return _Sourceprice; } }

		[SerializeField, HideInInspector]
		private string _Item_cost;
		/// <summary>
		/// 价格（美元）
		/// </summary>
		public string item_cost { get { return _Item_cost; } }

		public override string ToString() {
			return string.Format("[iapItem]{{id:{0}, google_play_goods_id:{1}, appstore_goods_id:{2}, price:{3}, sourceprice:{4}, item_cost:{5}}}",
				id, google_play_goods_id, appstore_goods_id, price, sourceprice, item_cost);
		}

		public static implicit operator bool(iapItem item) {
			return item != null;
		}

	}

}
