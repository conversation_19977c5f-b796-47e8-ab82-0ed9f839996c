//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class Wave : ScriptableObject {

		public static string CfgName = "Wave";

		public static Wave Data{ get { return Global.gApp.gGameData.GetData<Wave>(CfgName); } }
		[SerializeField, HideInInspector]
		private WaveItem[] _Items;
		public WaveItem[] items { get { return _Items; } }

		public WaveItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				WaveItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("Wave表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out WaveItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("Wave表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class WaveItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 波次ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _BornMode;
		/// <summary>
		/// 
		/// </summary>
		public int bornMode { get { return _BornMode; } }

		[SerializeField, HideInInspector]
		private string[] _NodeID;
		/// <summary>
		/// 怪物出生方式
		/// </summary>
		public string[] nodeID { get { return _NodeID; } }

		[SerializeField, HideInInspector]
		private int[] _TrigMode;
		/// <summary>
		/// 出生点
		/// </summary>
		public int[] trigMode { get { return _TrigMode; } }

		[SerializeField, HideInInspector]
		private float _TrigDelay;
		/// <summary>
		/// 触发方式
		/// </summary>
		public float trigDelay { get { return _TrigDelay; } }

		[SerializeField, HideInInspector]
		private int[] _EnemyID;
		/// <summary>
		/// 触发延迟
		/// </summary>
		public int[] enemyID { get { return _EnemyID; } }

		[SerializeField, HideInInspector]
		private float _Delay;
		/// <summary>
		/// 怪物ID
		/// </summary>
		public float delay { get { return _Delay; } }

		[SerializeField, HideInInspector]
		private int[] _EnemyNum;
		/// <summary>
		/// 延迟出现
		/// </summary>
		public int[] enemyNum { get { return _EnemyNum; } }

		[SerializeField, HideInInspector]
		private float _DtTime;
		/// <summary>
		/// 刷新数量
		/// </summary>
		public float dtTime { get { return _DtTime; } }

		[SerializeField, HideInInspector]
		private int _NumTime;
		/// <summary>
		/// 刷新间隔
		/// </summary>
		public int numTime { get { return _NumTime; } }

		[SerializeField, HideInInspector]
		private int _MaxNum;
		/// <summary>
		/// 每次数量
		/// </summary>
		public int maxNum { get { return _MaxNum; } }

		[SerializeField, HideInInspector]
		private int _Duration;
		/// <summary>
		/// 最大存在数量
		/// </summary>
		public int duration { get { return _Duration; } }

		[SerializeField, HideInInspector]
		private int _Warning;
		/// <summary>
		/// 持续时间
		/// </summary>
		public int warning { get { return _Warning; } }

		[SerializeField, HideInInspector]
		private double _HpParam;
		/// <summary>
		/// 怪群提示
		/// </summary>
		public double hpParam { get { return _HpParam; } }

		[SerializeField, HideInInspector]
		private double _AtkParam;
		/// <summary>
		/// 血量系数
		/// </summary>
		public double atkParam { get { return _AtkParam; } }

		[SerializeField, HideInInspector]
		private double _DefParam;
		/// <summary>
		/// 攻击系数
		/// </summary>
		public double defParam { get { return _DefParam; } }

		[SerializeField, HideInInspector]
		private float _MsParam;
		/// <summary>
		/// 防御系数
		/// </summary>
		public float msParam { get { return _MsParam; } }

		public override string ToString() {
			return string.Format("[WaveItem]{{id:{0}, bornMode:{1}, nodeID:{2}, trigMode:{3}, trigDelay:{4}, enemyID:{5}, delay:{6}, enemyNum:{7}, dtTime:{8}, numTime:{9}, maxNum:{10}, duration:{11}, warning:{12}, hpParam:{13}, atkParam:{14}, defParam:{15}, msParam:{16}}}",
				id, bornMode, array2string(nodeID), array2string(trigMode), trigDelay, array2string(enemyID), delay, array2string(enemyNum), dtTime, numTime, maxNum, duration, warning, hpParam, atkParam, defParam, msParam);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(WaveItem item) {
			return item != null;
		}

	}

}
