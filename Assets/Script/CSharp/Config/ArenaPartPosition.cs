//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class ArenaPartPosition : ScriptableObject {

		public static string CfgName = "ArenaPartPosition";

		public static ArenaPartPosition Data{ get { return Global.gApp.gGameData.GetData<ArenaPartPosition>(CfgName); } }
		[SerializeField, HideInInspector]
		private ArenaPartPositionItem[] _Items;
		public ArenaPartPositionItem[] items { get { return _Items; } }

		public ArenaPartPositionItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				ArenaPartPositionItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("ArenaPartPosition表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out ArenaPartPositionItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("ArenaPartPosition表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class ArenaPartPositionItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _MechaId;
		/// <summary>
		/// 备注1
		/// </summary>
		public int mechaId { get { return _MechaId; } }

		[SerializeField, HideInInspector]
		private string _Bone;
		/// <summary>
		/// 备注2
		/// </summary>
		public string bone { get { return _Bone; } }

		[SerializeField, HideInInspector]
		private int[] _Position;
		/// <summary>
		/// 机甲id
		/// </summary>
		public int[] position { get { return _Position; } }

		[SerializeField, HideInInspector]
		private int[] _Rotation;
		/// <summary>
		/// 装配部件绑定点
		/// </summary>
		public int[] rotation { get { return _Rotation; } }

		[SerializeField, HideInInspector]
		private int _Scale;
		/// <summary>
		/// 装配部件位置
		/// </summary>
		public int scale { get { return _Scale; } }

		public override string ToString() {
			return string.Format("[ArenaPartPositionItem]{{id:{0}, mechaId:{1}, bone:{2}, position:{3}, rotation:{4}, scale:{5}}}",
				id, mechaId, bone, array2string(position), array2string(rotation), scale);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(ArenaPartPositionItem item) {
			return item != null;
		}

	}

}
