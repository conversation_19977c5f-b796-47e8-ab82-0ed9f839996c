//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class UAVQuality : ScriptableObject {

		public static string CfgName = "UAVQuality";

		public static UAVQuality Data{ get { return Global.gApp.gGameData.GetData<UAVQuality>(CfgName); } }
		[SerializeField, HideInInspector]
		private UAVQualityItem[] _Items;
		public UAVQualityItem[] items { get { return _Items; } }

		public UAVQualityItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				UAVQualityItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("UAVQuality表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out UAVQualityItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("UAVQuality表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class UAVQualityItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 编号
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Uavid;
		/// <summary>
		/// 无人机ID
		/// </summary>
		public int uavid { get { return _Uavid; } }

		[SerializeField, HideInInspector]
		private int _Quality;
		/// <summary>
		/// 品质
		/// </summary>
		public int quality { get { return _Quality; } }

		[SerializeField, HideInInspector]
		private int _UnlockTtpe;
		/// <summary>
		/// 进阶方式
		/// </summary>
		public int unlockTtpe { get { return _UnlockTtpe; } }

		[SerializeField, HideInInspector]
		private int _QuaCondition;
		/// <summary>
		/// 消耗品质
		/// </summary>
		public int QuaCondition { get { return _QuaCondition; } }

		[SerializeField, HideInInspector]
		private int _UavCount;
		/// <summary>
		/// 消耗数量
		/// </summary>
		public int uavCount { get { return _UavCount; } }

		[SerializeField, HideInInspector]
		private string _UniversalProps;
		/// <summary>
		/// 升阶可使用万能道具
		/// </summary>
		public string universalProps { get { return _UniversalProps; } }

		[SerializeField, HideInInspector]
		private string[] _RollbackReward;
		/// <summary>
		/// 降级奖励
		/// </summary>
		public string[] rollbackReward { get { return _RollbackReward; } }

		[SerializeField, HideInInspector]
		private string[] _Attr;
		/// <summary>
		/// 属性
		/// </summary>
		public string[] attr { get { return _Attr; } }

		[SerializeField, HideInInspector]
		private int _UavAttr;
		/// <summary>
		/// 继承属性
		/// </summary>
		public int uavAttr { get { return _UavAttr; } }

		[SerializeField, HideInInspector]
		private int _UavSkill;
		/// <summary>
		/// 技能
		/// </summary>
		public int uavSkill { get { return _UavSkill; } }

		public override string ToString() {
			return string.Format("[UAVQualityItem]{{id:{0}, uavid:{1}, quality:{2}, unlockTtpe:{3}, QuaCondition:{4}, uavCount:{5}, universalProps:{6}, rollbackReward:{7}, attr:{8}, uavAttr:{9}, uavSkill:{10}}}",
				id, uavid, quality, unlockTtpe, QuaCondition, uavCount, universalProps, array2string(rollbackReward), array2string(attr), uavAttr, uavSkill);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(UAVQualityItem item) {
			return item != null;
		}

	}

}
