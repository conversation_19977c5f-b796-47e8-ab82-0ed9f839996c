//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class DailyRechargeEnd : ScriptableObject {

		public static string CfgName = "DailyRechargeEnd";

		public static DailyRechargeEnd Data{ get { return Global.gApp.gGameData.GetData<DailyRechargeEnd>(CfgName); } }
		[SerializeField, HideInInspector]
		private DailyRechargeEndItem[] _Items;
		public DailyRechargeEndItem[] items { get { return _Items; } }

		public DailyRechargeEndItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				DailyRechargeEndItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("DailyRechargeEnd表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out DailyRechargeEndItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("DailyRechargeEnd表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class DailyRechargeEndItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityID;
		/// <summary>
		/// 备注
		/// </summary>
		public int activityID { get { return _ActivityID; } }

		[SerializeField, HideInInspector]
		private string[] _Reward01;
		/// <summary>
		/// 活动Id
		/// </summary>
		public string[] Reward01 { get { return _Reward01; } }

		[SerializeField, HideInInspector]
		private string[] _Reward02;
		/// <summary>
		/// 奖励（第一档）
		/// </summary>
		public string[] Reward02 { get { return _Reward02; } }

		public override string ToString() {
			return string.Format("[DailyRechargeEndItem]{{id:{0}, activityID:{1}, Reward01:{2}, Reward02:{3}}}",
				id, activityID, array2string(Reward01), array2string(Reward02));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(DailyRechargeEndItem item) {
			return item != null;
		}

	}

}
