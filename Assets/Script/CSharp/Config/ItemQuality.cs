//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class ItemQuality : ScriptableObject {

		public static string CfgName = "ItemQuality";

		public static ItemQuality Data{ get { return Global.gApp.gGameData.GetData<ItemQuality>(CfgName); } }
		[SerializeField, HideInInspector]
		private ItemQualityItem[] _Items;
		public ItemQualityItem[] items { get { return _Items; } }

		public ItemQualityItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				ItemQualityItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("ItemQuality表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out ItemQualityItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("ItemQuality表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class ItemQualityItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Name;
		/// <summary>
		/// 备注
		/// </summary>
		public int name { get { return _Name; } }

		[SerializeField, HideInInspector]
		private int _Itemname;
		/// <summary>
		/// 品阶名
		/// </summary>
		public int itemname { get { return _Itemname; } }

		[SerializeField, HideInInspector]
		private int _ItemQuantityNum;
		/// <summary>
		/// 道具名称+X
		/// </summary>
		public int itemQuantityNum { get { return _ItemQuantityNum; } }

		[SerializeField, HideInInspector]
		private string _IconQuantityItem;
		/// <summary>
		/// 品质+N对应的角标数量
		/// </summary>
		public string iconQuantityItem { get { return _IconQuantityItem; } }

		[SerializeField, HideInInspector]
		private string _ItemQuality;
		/// <summary>
		/// 品质+N对应的角标资源路径
		/// </summary>
		public string ItemQuality { get { return _ItemQuality; } }

		[SerializeField, HideInInspector]
		private string _Border;
		/// <summary>
		/// 品阶图标底框
		/// </summary>
		public string border { get { return _Border; } }

		[SerializeField, HideInInspector]
		private string _Name_quality_image;
		/// <summary>
		/// 品阶小图标（奖章）
		/// </summary>
		public string name_quality_image { get { return _Name_quality_image; } }

		[SerializeField, HideInInspector]
		private string _BorderLock;
		/// <summary>
		/// 名字品质图
		/// </summary>
		public string borderLock { get { return _BorderLock; } }

		[SerializeField, HideInInspector]
		private string _MechalistBg;
		/// <summary>
		/// 品阶小图标（锁）
		/// </summary>
		public string mechalistBg { get { return _MechalistBg; } }

		[SerializeField, HideInInspector]
		private string[] _RGBA;
		/// <summary>
		/// 机甲列表展示页用
		/// </summary>
		public string[] RGBA { get { return _RGBA; } }

		[SerializeField, HideInInspector]
		private string _FX;
		/// <summary>
		/// 品阶对应文本颜色
		/// </summary>
		public string FX { get { return _FX; } }

		[SerializeField, HideInInspector]
		private string _NameFX;
		/// <summary>
		/// 对应升阶特效
		/// </summary>
		public string NameFX { get { return _NameFX; } }

		[SerializeField, HideInInspector]
		private string _BgFX;
		/// <summary>
		/// 品质名字特效
		/// </summary>
		public string BgFX { get { return _BgFX; } }

		[SerializeField, HideInInspector]
		private string _UAVFX;
		/// <summary>
		/// 背景特效
		/// </summary>
		public string UAVFX { get { return _UAVFX; } }

		[SerializeField, HideInInspector]
		private string _PartFX;
		/// <summary>
		/// 无人机品质特效
		/// </summary>
		public string PartFX { get { return _PartFX; } }

		[SerializeField, HideInInspector]
		private string _PartDetailFX;
		/// <summary>
		/// 部件品质特效
		/// </summary>
		public string PartDetailFX { get { return _PartDetailFX; } }

		[SerializeField, HideInInspector]
		private string _GetEffect;
		/// <summary>
		/// 部件详情特效
		/// </summary>
		public string GetEffect { get { return _GetEffect; } }

		[SerializeField, HideInInspector]
		private string _StreamerFX;
		/// <summary>
		/// 获得特效
		/// </summary>
		public string StreamerFX { get { return _StreamerFX; } }

		[SerializeField, HideInInspector]
		private string _CardFX;
		/// <summary>
		/// 流光特效
		/// </summary>
		public string CardFX { get { return _CardFX; } }

		[SerializeField, HideInInspector]
		private string _NextMecha;
		/// <summary>
		/// 流光特效
		/// </summary>
		public string NextMecha { get { return _NextMecha; } }

		[SerializeField, HideInInspector]
		private string _DriverQuality;
		/// <summary>
		/// 机甲预告底图
		/// </summary>
		public string DriverQuality { get { return _DriverQuality; } }

		[SerializeField, HideInInspector]
		private string _UavUpgrade;
		/// <summary>
		/// 驾驶员品质相关
		/// </summary>
		public string uavUpgrade { get { return _UavUpgrade; } }

		[SerializeField, HideInInspector]
		private string _EquipmentLvBG;
		/// <summary>
		/// 无人机升阶特效
		/// </summary>
		public string equipmentLvBG { get { return _EquipmentLvBG; } }

		[SerializeField, HideInInspector]
		private string _EquipmentInfoTopBG;
		/// <summary>
		/// 装备制造炉子概率底图
		/// </summary>
		public string equipmentInfoTopBG { get { return _EquipmentInfoTopBG; } }

		[SerializeField, HideInInspector]
		private string _EquipmentItemBG;
		/// <summary>
		/// 装备信息弹窗顶图
		/// </summary>
		public string equipmentItemBG { get { return _EquipmentItemBG; } }

		[SerializeField, HideInInspector]
		private string _EquipmentCiTiaoBG;
		/// <summary>
		/// 装备信息图标-长方形
		/// </summary>
		public string equipmentCiTiaoBG { get { return _EquipmentCiTiaoBG; } }

		[SerializeField, HideInInspector]
		private string _AssistFX;
		/// <summary>
		/// 装备词条底图
		/// </summary>
		public string assistFX { get { return _AssistFX; } }

		[SerializeField, HideInInspector]
		private string _AircraftSSRankUpBG;
		/// <summary>
		/// 助战品质特效
		/// </summary>
		public string AircraftSSRankUpBG { get { return _AircraftSSRankUpBG; } }

		[SerializeField, HideInInspector]
		private string _AircraftSSRankUpQualityBG;
		/// <summary>
		/// 机甲平台SS品质升阶背景
		/// </summary>
		public string AircraftSSRankUpQualityBG { get { return _AircraftSSRankUpQualityBG; } }

		[SerializeField, HideInInspector]
		private string _AircraftSSRankUpQualityLight;
		/// <summary>
		/// 机甲平台SS品质升阶品质底图
		/// </summary>
		public string AircraftSSRankUpQualityLight { get { return _AircraftSSRankUpQualityLight; } }

		[SerializeField, HideInInspector]
		private string _AircraftFX;
		/// <summary>
		/// 机甲平台SS品质升阶品质高亮框
		/// </summary>
		public string AircraftFX { get { return _AircraftFX; } }

		[SerializeField, HideInInspector]
		private string _TacticMechaQualityBG;
		/// <summary>
		/// 机甲平台首页品质特效
		/// </summary>
		public string TacticMechaQualityBG { get { return _TacticMechaQualityBG; } }

		public override string ToString() {
			return string.Format("[ItemQualityItem]{{id:{0}, name:{1}, itemname:{2}, itemQuantityNum:{3}, iconQuantityItem:{4}, ItemQuality:{5}, border:{6}, name_quality_image:{7}, borderLock:{8}, mechalistBg:{9}, RGBA:{10}, FX:{11}, NameFX:{12}, BgFX:{13}, UAVFX:{14}, PartFX:{15}, PartDetailFX:{16}, GetEffect:{17}, StreamerFX:{18}, CardFX:{19}, NextMecha:{20}, DriverQuality:{21}, uavUpgrade:{22}, equipmentLvBG:{23}, equipmentInfoTopBG:{24}, equipmentItemBG:{25}, equipmentCiTiaoBG:{26}, assistFX:{27}, AircraftSSRankUpBG:{28}, AircraftSSRankUpQualityBG:{29}, AircraftSSRankUpQualityLight:{30}, AircraftFX:{31}, TacticMechaQualityBG:{32}}}",
				id, name, itemname, itemQuantityNum, iconQuantityItem, ItemQuality, border, name_quality_image, borderLock, mechalistBg, array2string(RGBA), FX, NameFX, BgFX, UAVFX, PartFX, PartDetailFX, GetEffect, StreamerFX, CardFX, NextMecha, DriverQuality, uavUpgrade, equipmentLvBG, equipmentInfoTopBG, equipmentItemBG, equipmentCiTiaoBG, assistFX, AircraftSSRankUpBG, AircraftSSRankUpQualityBG, AircraftSSRankUpQualityLight, AircraftFX, TacticMechaQualityBG);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(ItemQualityItem item) {
			return item != null;
		}

	}

}
