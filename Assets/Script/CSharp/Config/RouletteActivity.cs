//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class RouletteActivity : ScriptableObject {

		public static string CfgName = "RouletteActivity";

		public static RouletteActivity Data{ get { return Global.gApp.gGameData.GetData<RouletteActivity>(CfgName); } }
		[SerializeField, HideInInspector]
		private RouletteActivityItem[] _Items;
		public RouletteActivityItem[] items { get { return _Items; } }

		public RouletteActivityItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				RouletteActivityItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("RouletteActivity表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out RouletteActivityItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("RouletteActivity表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class RouletteActivityItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// #唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityId;
		/// <summary>
		/// 活动ID
		/// </summary>
		public int activityId { get { return _ActivityId; } }

		[SerializeField, HideInInspector]
		private string _Consume;
		/// <summary>
		/// 单抽道具
		/// </summary>
		public string consume { get { return _Consume; } }

		[SerializeField, HideInInspector]
		private int _LoopTime;
		/// <summary>
		/// 循环次数
		/// </summary>
		public int loopTime { get { return _LoopTime; } }

		[SerializeField, HideInInspector]
		private int[] _NormalPityCount;
		/// <summary>
		/// 小奖池保底触发次数
		/// </summary>
		public int[] normalPityCount { get { return _NormalPityCount; } }

		[SerializeField, HideInInspector]
		private string _DelItem;
		/// <summary>
		/// 活动到期删除道具列表
		/// </summary>
		public string delItem { get { return _DelItem; } }

		[SerializeField, HideInInspector]
		private string _PortalModel;
		/// <summary>
		/// 测试展示模型
		/// </summary>
		public string portalModel { get { return _PortalModel; } }

		[SerializeField, HideInInspector]
		private string[] _DisplayModel;
		/// <summary>
		/// 展示模型
		/// </summary>
		public string[] displayModel { get { return _DisplayModel; } }

		public override string ToString() {
			return string.Format("[RouletteActivityItem]{{id:{0}, activityId:{1}, consume:{2}, loopTime:{3}, normalPityCount:{4}, delItem:{5}, portalModel:{6}, displayModel:{7}}}",
				id, activityId, consume, loopTime, array2string(normalPityCount), delItem, portalModel, array2string(displayModel));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(RouletteActivityItem item) {
			return item != null;
		}

	}

}
