//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class DiyActivityTasks : ScriptableObject {

		public static string CfgName = "DiyActivityTasks";

		public static DiyActivityTasks Data{ get { return Global.gApp.gGameData.GetData<DiyActivityTasks>(CfgName); } }
		[SerializeField, HideInInspector]
		private DiyActivityTasksItem[] _Items;
		public DiyActivityTasksItem[] items { get { return _Items; } }

		public DiyActivityTasksItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				DiyActivityTasksItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("DiyActivityTasks表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out DiyActivityTasksItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("DiyActivityTasks表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class DiyActivityTasksItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityID;
		/// <summary>
		/// 备注
		/// </summary>
		public int activityID { get { return _ActivityID; } }

		[SerializeField, HideInInspector]
		private int _Info;
		/// <summary>
		/// 活动ID
		/// </summary>
		public int info { get { return _Info; } }

		[SerializeField, HideInInspector]
		private string _Type;
		/// <summary>
		/// 任务描述
		/// </summary>
		public string type { get { return _Type; } }

		[SerializeField, HideInInspector]
		private string[] _Reward;
		/// <summary>
		/// 类型D
		/// </summary>
		public string[] Reward { get { return _Reward; } }

		[SerializeField, HideInInspector]
		private int _Jump;
		/// <summary>
		/// 任务奖励
		/// </summary>
		public int jump { get { return _Jump; } }

		[SerializeField, HideInInspector]
		private int _InitPro;
		/// <summary>
		/// 跳转着陆
		/// </summary>
		public int initPro { get { return _InitPro; } }

		public override string ToString() {
			return string.Format("[DiyActivityTasksItem]{{id:{0}, activityID:{1}, info:{2}, type:{3}, Reward:{4}, jump:{5}, initPro:{6}}}",
				id, activityID, info, type, array2string(Reward), jump, initPro);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(DiyActivityTasksItem item) {
			return item != null;
		}

	}

}
