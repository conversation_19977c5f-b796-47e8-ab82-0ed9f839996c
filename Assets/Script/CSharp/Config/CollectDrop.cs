//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class CollectDrop : ScriptableObject {

		public static string CfgName = "CollectDrop";

		public static CollectDrop Data{ get { return Global.gApp.gGameData.GetData<CollectDrop>(CfgName); } }
		[SerializeField, HideInInspector]
		private CollectDropItem[] _Items;
		public CollectDropItem[] items { get { return _Items; } }

		public CollectDropItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				CollectDropItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("CollectDrop表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out CollectDropItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("CollectDrop表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class CollectDropItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityId;
		/// <summary>
		/// #策划备注
		/// </summary>
		public int activityId { get { return _ActivityId; } }

		[SerializeField, HideInInspector]
		private string[] _DropId;
		/// <summary>
		/// 活动ID
		/// </summary>
		public string[] dropId { get { return _DropId; } }

		public override string ToString() {
			return string.Format("[CollectDropItem]{{id:{0}, activityId:{1}, dropId:{2}}}",
				id, activityId, array2string(dropId));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(CollectDropItem item) {
			return item != null;
		}

	}

}
