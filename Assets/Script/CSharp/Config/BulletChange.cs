//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class BulletChange : ScriptableObject {

		public static string CfgName = "BulletChange";

		public static BulletChange Data{ get { return Global.gApp.gGameData.GetData<BulletChange>(CfgName); } }
		[SerializeField, HideInInspector]
		private BulletChangeItem[] _Items;
		public BulletChangeItem[] items { get { return _Items; } }

		public BulletChangeItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				BulletChangeItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("BulletChange表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out BulletChangeItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("BulletChange表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class BulletChangeItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// BuffID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int[] _BulletID;
		/// <summary>
		/// 备注
		/// </summary>
		public int[] bulletID { get { return _BulletID; } }

		[SerializeField, HideInInspector]
		private string _Param;
		/// <summary>
		/// 子弹ID
		/// </summary>
		public string param { get { return _Param; } }

		[SerializeField, HideInInspector]
		private int _Rank;
		/// <summary>
		/// 修改字段
		/// </summary>
		public int rank { get { return _Rank; } }

		[SerializeField, HideInInspector]
		private int _Type;
		/// <summary>
		/// 优先级
		/// </summary>
		public int type { get { return _Type; } }

		[SerializeField, HideInInspector]
		private string[] _Value;
		/// <summary>
		/// 修改方式
		/// </summary>
		public string[] value { get { return _Value; } }

		public override string ToString() {
			return string.Format("[BulletChangeItem]{{id:{0}, bulletID:{1}, param:{2}, rank:{3}, type:{4}, value:{5}}}",
				id, array2string(bulletID), param, rank, type, array2string(value));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(BulletChangeItem item) {
			return item != null;
		}

	}

}
