//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class MatchConfig : ScriptableObject {

		public static string CfgName = "MatchConfig";

		public static MatchConfig Data{ get { return Global.gApp.gGameData.GetData<MatchConfig>(CfgName); } }
		[SerializeField, HideInInspector]
		private MatchConfigItem[] _Items;
		public MatchConfigItem[] items { get { return _Items; } }

		public MatchConfigItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				MatchConfigItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("MatchConfig表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out MatchConfigItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("MatchConfig表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class MatchConfigItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 属性类型
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Name;
		/// <summary>
		/// 备注
		/// </summary>
		public int name { get { return _Name; } }

		[SerializeField, HideInInspector]
		private int _Add;
		/// <summary>
		/// 系名称
		/// </summary>
		public int add { get { return _Add; } }

		[SerializeField, HideInInspector]
		private int _Reduce;
		/// <summary>
		/// 系增伤
		/// </summary>
		public int reduce { get { return _Reduce; } }

		[SerializeField, HideInInspector]
		private int _AddOne;
		/// <summary>
		/// 系免伤
		/// </summary>
		public int addOne { get { return _AddOne; } }

		[SerializeField, HideInInspector]
		private string _Icon;
		/// <summary>
		/// 系增伤（每携带1个）
		/// </summary>
		public string icon { get { return _Icon; } }

		public override string ToString() {
			return string.Format("[MatchConfigItem]{{id:{0}, name:{1}, add:{2}, reduce:{3}, addOne:{4}, icon:{5}}}",
				id, name, add, reduce, addOne, icon);
		}

		public static implicit operator bool(MatchConfigItem item) {
			return item != null;
		}

	}

}
