//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class GamePlayBattlePassReward : ScriptableObject {

		public static string CfgName = "GamePlayBattlePassReward";

		public static GamePlayBattlePassReward Data{ get { return Global.gApp.gGameData.GetData<GamePlayBattlePassReward>(CfgName); } }
		[SerializeField, HideInInspector]
		private GamePlayBattlePassRewardItem[] _Items;
		public GamePlayBattlePassRewardItem[] items { get { return _Items; } }

		public GamePlayBattlePassRewardItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				GamePlayBattlePassRewardItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("GamePlayBattlePassReward表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out GamePlayBattlePassRewardItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("GamePlayBattlePassReward表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class GamePlayBattlePassRewardItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Battlepass_id;
		/// <summary>
		/// #
		/// </summary>
		public int battlepass_id { get { return _Battlepass_id; } }

		[SerializeField, HideInInspector]
		private string _TaskConditions;
		/// <summary>
		/// 战令ID
		/// </summary>
		public string taskConditions { get { return _TaskConditions; } }

		[SerializeField, HideInInspector]
		private string _Normal_reward;
		/// <summary>
		/// 任务类型
		/// </summary>
		public string normal_reward { get { return _Normal_reward; } }

		[SerializeField, HideInInspector]
		private string _Advance_reward;
		/// <summary>
		/// 普通奖励
		/// </summary>
		public string advance_reward { get { return _Advance_reward; } }

		[SerializeField, HideInInspector]
		private string _QuickPurchase;
		/// <summary>
		/// 高级战令奖励
		/// </summary>
		public string quickPurchase { get { return _QuickPurchase; } }

		public override string ToString() {
			return string.Format("[GamePlayBattlePassRewardItem]{{id:{0}, battlepass_id:{1}, taskConditions:{2}, normal_reward:{3}, advance_reward:{4}, quickPurchase:{5}}}",
				id, battlepass_id, taskConditions, normal_reward, advance_reward, quickPurchase);
		}

		public static implicit operator bool(GamePlayBattlePassRewardItem item) {
			return item != null;
		}

	}

}
