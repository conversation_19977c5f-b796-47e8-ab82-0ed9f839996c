//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class SlotActivity : ScriptableObject {

		public static string CfgName = "SlotActivity";

		public static SlotActivity Data{ get { return Global.gApp.gGameData.GetData<SlotActivity>(CfgName); } }
		[SerializeField, HideInInspector]
		private SlotActivityItem[] _Items;
		public SlotActivityItem[] items { get { return _Items; } }

		public SlotActivityItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				SlotActivityItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("SlotActivity表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out SlotActivityItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("SlotActivity表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class SlotActivityItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// #唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityId;
		/// <summary>
		/// 活动ID
		/// </summary>
		public int activityId { get { return _ActivityId; } }

		[SerializeField, HideInInspector]
		private int _SmallGuaranteeTrigger;
		/// <summary>
		/// 小保底触发次数
		/// </summary>
		public int smallGuaranteeTrigger { get { return _SmallGuaranteeTrigger; } }

		[SerializeField, HideInInspector]
		private int _BigGuaranteeTrigger;
		/// <summary>
		/// 大保底触发次数
		/// </summary>
		public int bigGuaranteeTrigger { get { return _BigGuaranteeTrigger; } }

		[SerializeField, HideInInspector]
		private string _Consume;
		/// <summary>
		/// 单抽道具
		/// </summary>
		public string consume { get { return _Consume; } }

		[SerializeField, HideInInspector]
		private string _ConsumeTen;
		/// <summary>
		/// 十抽道具
		/// </summary>
		public string consumeTen { get { return _ConsumeTen; } }

		[SerializeField, HideInInspector]
		private string _Draw_once_consume;
		/// <summary>
		/// 单次抽奖消耗
		/// </summary>
		public string draw_once_consume { get { return _Draw_once_consume; } }

		[SerializeField, HideInInspector]
		private string _Multi_draw_consume;
		/// <summary>
		/// 十连抽消耗
		/// </summary>
		public string multi_draw_consume { get { return _Multi_draw_consume; } }

		[SerializeField, HideInInspector]
		private string _GrandPrizeBG;
		/// <summary>
		/// 终极奖底框
		/// </summary>
		public string grandPrizeBG { get { return _GrandPrizeBG; } }

		[SerializeField, HideInInspector]
		private string _GrandPrizeImage;
		/// <summary>
		/// 终极奖图片
		/// </summary>
		public string grandPrizeImage { get { return _GrandPrizeImage; } }

		[SerializeField, HideInInspector]
		private string _CenterImage;
		/// <summary>
		/// 活动主页奖励图片
		/// </summary>
		public string centerImage { get { return _CenterImage; } }

		[SerializeField, HideInInspector]
		private string _RoleImage;
		/// <summary>
		/// 中心大奖展示图
		/// </summary>
		public string roleImage { get { return _RoleImage; } }

		[SerializeField, HideInInspector]
		private string _LimitedrewardsBG;
		/// <summary>
		/// 限定奖概率底图
		/// </summary>
		public string LimitedrewardsBG { get { return _LimitedrewardsBG; } }

		[SerializeField, HideInInspector]
		private int _LimitedrewardsText;
		/// <summary>
		/// 限定奖文本
		/// </summary>
		public int LimitedrewardsText { get { return _LimitedrewardsText; } }

		[SerializeField, HideInInspector]
		private string _OrdinaryRewardBG;
		/// <summary>
		/// 普通将概率底图
		/// </summary>
		public string OrdinaryRewardBG { get { return _OrdinaryRewardBG; } }

		[SerializeField, HideInInspector]
		private int _OrdinaryRewardsText;
		/// <summary>
		/// 普通奖文本
		/// </summary>
		public int OrdinaryRewardsText { get { return _OrdinaryRewardsText; } }

		[SerializeField, HideInInspector]
		private int _LoopRound;
		/// <summary>
		/// 循环层数
		/// </summary>
		public int loopRound { get { return _LoopRound; } }

		[SerializeField, HideInInspector]
		private int _LoopTime;
		/// <summary>
		/// 循环次数
		/// </summary>
		public int loopTime { get { return _LoopTime; } }

		public override string ToString() {
			return string.Format("[SlotActivityItem]{{id:{0}, activityId:{1}, smallGuaranteeTrigger:{2}, bigGuaranteeTrigger:{3}, consume:{4}, consumeTen:{5}, draw_once_consume:{6}, multi_draw_consume:{7}, grandPrizeBG:{8}, grandPrizeImage:{9}, centerImage:{10}, roleImage:{11}, LimitedrewardsBG:{12}, LimitedrewardsText:{13}, OrdinaryRewardBG:{14}, OrdinaryRewardsText:{15}, loopRound:{16}, loopTime:{17}}}",
				id, activityId, smallGuaranteeTrigger, bigGuaranteeTrigger, consume, consumeTen, draw_once_consume, multi_draw_consume, grandPrizeBG, grandPrizeImage, centerImage, roleImage, LimitedrewardsBG, LimitedrewardsText, OrdinaryRewardBG, OrdinaryRewardsText, loopRound, loopTime);
		}

		public static implicit operator bool(SlotActivityItem item) {
			return item != null;
		}

	}

}
