//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class Item : ScriptableObject {

		public static string CfgName = "Item";

		public static Item Data{ get { return Global.gApp.gGameData.GetData<Item>(CfgName); } }
		[SerializeField, HideInInspector]
		private ItemItem[] _Items;
		public ItemItem[] items { get { return _Items; } }

		public ItemItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				ItemItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("Item表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out ItemItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("Item表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class ItemItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _Type;
		/// <summary>
		/// 类型D
		/// </summary>
		public string type { get { return _Type; } }

		[SerializeField, HideInInspector]
		private float _DiamondPriceB;
		/// <summary>
		/// 策划备注
		/// </summary>
		public float diamondPriceB { get { return _DiamondPriceB; } }

		[SerializeField, HideInInspector]
		private int _Name;
		/// <summary>
		/// 道具价值
		/// </summary>
		public int name { get { return _Name; } }

		[SerializeField, HideInInspector]
		private int _Desc;
		/// <summary>
		/// 名称
		/// </summary>
		public int desc { get { return _Desc; } }

		[SerializeField, HideInInspector]
		private string _Icon;
		/// <summary>
		/// 描述
		/// </summary>
		public string icon { get { return _Icon; } }

		[SerializeField, HideInInspector]
		private string _Icon_JiaoBiao;
		/// <summary>
		/// 图标资源：道具图标
		/// </summary>
		public string icon_JiaoBiao { get { return _Icon_JiaoBiao; } }

		[SerializeField, HideInInspector]
		private string _Icon_currency;
		/// <summary>
		/// 图标资源：道具角标
		/// </summary>
		public string icon_currency { get { return _Icon_currency; } }

		[SerializeField, HideInInspector]
		private int _Quality;
		/// <summary>
		/// 小图标资源：货币图标
		/// </summary>
		public int quality { get { return _Quality; } }

		[SerializeField, HideInInspector]
		private int _Rarity;
		/// <summary>
		/// 品质
		/// </summary>
		public int rarity { get { return _Rarity; } }

		[SerializeField, HideInInspector]
		private int _Diamond_price;
		/// <summary>
		/// 稀有度
		/// </summary>
		public int diamond_price { get { return _Diamond_price; } }

		[SerializeField, HideInInspector]
		private int _Order;
		/// <summary>
		/// 钻石价格
		/// </summary>
		public int order { get { return _Order; } }

		[SerializeField, HideInInspector]
		private string _Validity;
		/// <summary>
		/// 排序
		/// </summary>
		public string validity { get { return _Validity; } }

		[SerializeField, HideInInspector]
		private string _RegainItems;
		/// <summary>
		/// 物品有效期
		/// </summary>
		public string regainItems { get { return _RegainItems; } }

		[SerializeField, HideInInspector]
		private int _RegainMail;
		/// <summary>
		/// 到期返还道具ID
		/// </summary>
		public int regainMail { get { return _RegainMail; } }

		[SerializeField, HideInInspector]
		private string _Open_conditions;
		/// <summary>
		/// 到期返还邮件ID信息
		/// </summary>
		public string open_conditions { get { return _Open_conditions; } }

		[SerializeField, HideInInspector]
		private string[] _Inner_link;
		/// <summary>
		/// 宝箱条件
		/// </summary>
		public string[] inner_link { get { return _Inner_link; } }

		[SerializeField, HideInInspector]
		private string _Chat_link;
		/// <summary>
		/// 物品获取途径
		/// </summary>
		public string chat_link { get { return _Chat_link; } }

		[SerializeField, HideInInspector]
		private int _Red;
		/// <summary>
		/// 物品超级链接
		/// </summary>
		public int Red { get { return _Red; } }

		[SerializeField, HideInInspector]
		private int _UseCountLimit;
		/// <summary>
		/// 红点显示规则
		/// </summary>
		public int useCountLimit { get { return _UseCountLimit; } }

		[SerializeField, HideInInspector]
		private string _UseParams;
		/// <summary>
		/// 批量使用数量
		/// </summary>
		public string useParams { get { return _UseParams; } }

		[SerializeField, HideInInspector]
		private string _UseDisplay;
		/// <summary>
		/// 手动使用
		/// </summary>
		public string useDisplay { get { return _UseDisplay; } }

		[SerializeField, HideInInspector]
		private int _AutoUse;
		/// <summary>
		/// 客户端显示角标概率
		/// </summary>
		public int autoUse { get { return _AutoUse; } }

		[SerializeField, HideInInspector]
		private int _ComposeCost;
		/// <summary>
		/// 自动使用
		/// </summary>
		public int composeCost { get { return _ComposeCost; } }

		[SerializeField, HideInInspector]
		private string _Compose;
		/// <summary>
		/// 合成消耗数量
		/// </summary>
		public string compose { get { return _Compose; } }

		[SerializeField, HideInInspector]
		private int _Plus_link;
		/// <summary>
		/// 合成获得
		/// </summary>
		public int plus_link { get { return _Plus_link; } }

		public override string ToString() {
			return string.Format("[ItemItem]{{id:{0}, type:{1}, diamondPriceB:{2}, name:{3}, desc:{4}, icon:{5}, icon_JiaoBiao:{6}, icon_currency:{7}, quality:{8}, rarity:{9}, diamond_price:{10}, order:{11}, validity:{12}, regainItems:{13}, regainMail:{14}, open_conditions:{15}, inner_link:{16}, chat_link:{17}, Red:{18}, useCountLimit:{19}, useParams:{20}, useDisplay:{21}, autoUse:{22}, composeCost:{23}, compose:{24}, plus_link:{25}}}",
				id, type, diamondPriceB, name, desc, icon, icon_JiaoBiao, icon_currency, quality, rarity, diamond_price, order, validity, regainItems, regainMail, open_conditions, array2string(inner_link), chat_link, Red, useCountLimit, useParams, useDisplay, autoUse, composeCost, compose, plus_link);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(ItemItem item) {
			return item != null;
		}

	}

}
