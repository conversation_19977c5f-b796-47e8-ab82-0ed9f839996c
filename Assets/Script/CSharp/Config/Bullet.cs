//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class Bullet : ScriptableObject {

		public static string CfgName = "Bullet";

		public static Bullet Data{ get { return Global.gApp.gGameData.GetData<Bullet>(CfgName); } }
		[SerializeField, HideInInspector]
		private BulletItem[] _Items;
		public BulletItem[] items { get { return _Items; } }

		public BulletItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				BulletItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("Bullet表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out BulletItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("Bullet表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class BulletItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 子弹ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _Path;
		/// <summary>
		/// 备注
		/// </summary>
		public string path { get { return _Path; } }

		[SerializeField, HideInInspector]
		private float _Scale;
		/// <summary>
		/// 子弹路径
		/// </summary>
		public float scale { get { return _Scale; } }

		[SerializeField, HideInInspector]
		private double _DamageParam;
		/// <summary>
		/// 子弹缩放
		/// </summary>
		public double damageParam { get { return _DamageParam; } }

		[SerializeField, HideInInspector]
		private int _DamageType;
		/// <summary>
		/// 弹药伤害系数
		/// </summary>
		public int damageType { get { return _DamageType; } }

		[SerializeField, HideInInspector]
		private float _Speed;
		/// <summary>
		/// 伤害类型
		/// </summary>
		public float speed { get { return _Speed; } }

		[SerializeField, HideInInspector]
		private float _Livetime;
		/// <summary>
		/// 速度
		/// </summary>
		public float livetime { get { return _Livetime; } }

		[SerializeField, HideInInspector]
		private int _PierceVal;
		/// <summary>
		/// 存在时间
		/// </summary>
		public int pierceVal { get { return _PierceVal; } }

		[SerializeField, HideInInspector]
		private float _HitbackVal;
		/// <summary>
		/// 穿透值
		/// </summary>
		public float hitbackVal { get { return _HitbackVal; } }

		[SerializeField, HideInInspector]
		private float[] _StunParam;
		/// <summary>
		/// 击退值
		/// </summary>
		public float[] stunParam { get { return _StunParam; } }

		[SerializeField, HideInInspector]
		private int _BounceVal;
		/// <summary>
		/// 击晕
		/// </summary>
		public int bounceVal { get { return _BounceVal; } }

		[SerializeField, HideInInspector]
		private int _BindFirePoint;
		/// <summary>
		/// 弹射值
		/// </summary>
		public int BindFirePoint { get { return _BindFirePoint; } }

		[SerializeField, HideInInspector]
		private int _BulletEffect;
		/// <summary>
		/// 绑定开火点
		/// </summary>
		public int bulletEffect { get { return _BulletEffect; } }

		[SerializeField, HideInInspector]
		private int _EffectType;
		/// <summary>
		/// 子弹特效
		/// </summary>
		public int effectType { get { return _EffectType; } }

		[SerializeField, HideInInspector]
		private int _HitEffect;
		/// <summary>
		/// 特效方式
		/// </summary>
		public int hitEffect { get { return _HitEffect; } }

		[SerializeField, HideInInspector]
		private int _HitGEffect;
		/// <summary>
		/// 命中特效
		/// </summary>
		public int hitGEffect { get { return _HitGEffect; } }

		[SerializeField, HideInInspector]
		private float[] _HitBuff;
		/// <summary>
		/// 命中地面
		/// </summary>
		public float[] hitBuff { get { return _HitBuff; } }

		[SerializeField, HideInInspector]
		private float[] _HitHeroBuff;
		/// <summary>
		/// 命中BUFF（对敌人）
		/// </summary>
		public float[] hitHeroBuff { get { return _HitHeroBuff; } }

		[SerializeField, HideInInspector]
		private int _DisappearEffect;
		/// <summary>
		/// 命中BUFF（对自己）
		/// </summary>
		public int disappearEffect { get { return _DisappearEffect; } }

		[SerializeField, HideInInspector]
		private int[] _ExternBullet;
		/// <summary>
		/// 子弹消失特效
		/// </summary>
		public int[] externBullet { get { return _ExternBullet; } }

		[SerializeField, HideInInspector]
		private int[] _HitBullet;
		/// <summary>
		/// 消失产生子弹
		/// </summary>
		public int[] hitBullet { get { return _HitBullet; } }

		[SerializeField, HideInInspector]
		private float[] _ExternTimeBullet;
		/// <summary>
		/// 命中产生子弹
		/// </summary>
		public float[] externTimeBullet { get { return _ExternTimeBullet; } }

		[SerializeField, HideInInspector]
		private float _DamageCD;
		/// <summary>
		/// 间隔产生子弹
		/// </summary>
		public float damageCD { get { return _DamageCD; } }

		[SerializeField, HideInInspector]
		private string _DYObject;
		/// <summary>
		/// 产生伤害间隔
		/// </summary>
		public string DYObject { get { return _DYObject; } }

		[SerializeField, HideInInspector]
		private int _DYObjectParam;
		/// <summary>
		/// 子弹类型
		/// </summary>
		public int DYObjectParam { get { return _DYObjectParam; } }

		[SerializeField, HideInInspector]
		private string _DYTriggerType;
		/// <summary>
		/// 子弹类型参数
		/// </summary>
		public string DYTriggerType { get { return _DYTriggerType; } }

		[SerializeField, HideInInspector]
		private string _MoveType;
		/// <summary>
		/// 触发类型
		/// </summary>
		public string MoveType { get { return _MoveType; } }

		[SerializeField, HideInInspector]
		private float[] _MoveParam;
		/// <summary>
		/// 移动类型
		/// </summary>
		public float[] moveParam { get { return _MoveParam; } }

		[SerializeField, HideInInspector]
		private float[] _TimeDamgeBoost;
		/// <summary>
		/// 移动参数
		/// </summary>
		public float[] timeDamgeBoost { get { return _TimeDamgeBoost; } }

		[SerializeField, HideInInspector]
		private float[] _BounceDamgeBoost;
		/// <summary>
		/// 随时间增伤
		/// </summary>
		public float[] bounceDamgeBoost { get { return _BounceDamgeBoost; } }

		[SerializeField, HideInInspector]
		private int _MaxCount;
		/// <summary>
		/// 每次弹射增伤
		/// </summary>
		public int maxCount { get { return _MaxCount; } }

		[SerializeField, HideInInspector]
		private float _ForwardOffset;
		/// <summary>
		/// 子弹最大数量
		/// </summary>
		public float forwardOffset { get { return _ForwardOffset; } }

		public override string ToString() {
			return string.Format("[BulletItem]{{id:{0}, path:{1}, scale:{2}, damageParam:{3}, damageType:{4}, speed:{5}, livetime:{6}, pierceVal:{7}, hitbackVal:{8}, stunParam:{9}, bounceVal:{10}, BindFirePoint:{11}, bulletEffect:{12}, effectType:{13}, hitEffect:{14}, hitGEffect:{15}, hitBuff:{16}, hitHeroBuff:{17}, disappearEffect:{18}, externBullet:{19}, hitBullet:{20}, externTimeBullet:{21}, damageCD:{22}, DYObject:{23}, DYObjectParam:{24}, DYTriggerType:{25}, MoveType:{26}, moveParam:{27}, timeDamgeBoost:{28}, bounceDamgeBoost:{29}, maxCount:{30}, forwardOffset:{31}}}",
				id, path, scale, damageParam, damageType, speed, livetime, pierceVal, hitbackVal, array2string(stunParam), bounceVal, BindFirePoint, bulletEffect, effectType, hitEffect, hitGEffect, array2string(hitBuff), array2string(hitHeroBuff), disappearEffect, array2string(externBullet), array2string(hitBullet), array2string(externTimeBullet), damageCD, DYObject, DYObjectParam, DYTriggerType, MoveType, array2string(moveParam), array2string(timeDamgeBoost), array2string(bounceDamgeBoost), maxCount, forwardOffset);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(BulletItem item) {
			return item != null;
		}

	}

}
