//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class NewCombatPowerRank : ScriptableObject {

		public static string CfgName = "NewCombatPowerRank";

		public static NewCombatPowerRank Data{ get { return Global.gApp.gGameData.GetData<NewCombatPowerRank>(CfgName); } }
		[SerializeField, HideInInspector]
		private NewCombatPowerRankItem[] _Items;
		public NewCombatPowerRankItem[] items { get { return _Items; } }

		public NewCombatPowerRankItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				NewCombatPowerRankItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("NewCombatPowerRank表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out NewCombatPowerRankItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("NewCombatPowerRank表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class NewCombatPowerRankItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 奖励ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityID;
		/// <summary>
		/// 策划备注
		/// </summary>
		public int activityID { get { return _ActivityID; } }

		[SerializeField, HideInInspector]
		private int _MaxRank;
		/// <summary>
		/// 活动Id
		/// </summary>
		public int MaxRank { get { return _MaxRank; } }

		[SerializeField, HideInInspector]
		private int _MinRank;
		/// <summary>
		/// 最高排名（≥）
		/// </summary>
		public int MinRank { get { return _MinRank; } }

		[SerializeField, HideInInspector]
		private string[] _RankItem;
		/// <summary>
		/// 最低排名（≤）
		/// </summary>
		public string[] RankItem { get { return _RankItem; } }

		public override string ToString() {
			return string.Format("[NewCombatPowerRankItem]{{id:{0}, activityID:{1}, MaxRank:{2}, MinRank:{3}, RankItem:{4}}}",
				id, activityID, MaxRank, MinRank, array2string(RankItem));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(NewCombatPowerRankItem item) {
			return item != null;
		}

	}

}
