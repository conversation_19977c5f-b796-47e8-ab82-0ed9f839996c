//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class MechaTreasureRound : ScriptableObject {

		public static string CfgName = "MechaTreasureRound";

		public static MechaTreasureRound Data{ get { return Global.gApp.gGameData.GetData<MechaTreasureRound>(CfgName); } }
		[SerializeField, HideInInspector]
		private MechaTreasureRoundItem[] _Items;
		public MechaTreasureRoundItem[] items { get { return _Items; } }

		public MechaTreasureRoundItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				MechaTreasureRoundItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("MechaTreasureRound表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out MechaTreasureRoundItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("MechaTreasureRound表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class MechaTreasureRoundItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityID;
		/// <summary>
		/// 策划备注
		/// </summary>
		public int activityID { get { return _ActivityID; } }

		[SerializeField, HideInInspector]
		private int _FloorsNum;
		/// <summary>
		/// 活动ID
		/// </summary>
		public int floorsNum { get { return _FloorsNum; } }

		[SerializeField, HideInInspector]
		private string _ExtraReward;
		/// <summary>
		/// 登顶次数
		/// </summary>
		public string extraReward { get { return _ExtraReward; } }

		[SerializeField, HideInInspector]
		private string _LoopExtraReward;
		/// <summary>
		/// 首次层数奖励
		/// </summary>
		public string loopExtraReward { get { return _LoopExtraReward; } }

		public override string ToString() {
			return string.Format("[MechaTreasureRoundItem]{{id:{0}, activityID:{1}, floorsNum:{2}, extraReward:{3}, loopExtraReward:{4}}}",
				id, activityID, floorsNum, extraReward, loopExtraReward);
		}

		public static implicit operator bool(MechaTreasureRoundItem item) {
			return item != null;
		}

	}

}
