//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class AircraftSkillParam : ScriptableObject {

		public static string CfgName = "AircraftSkillParam";

		public static AircraftSkillParam Data{ get { return Global.gApp.gGameData.GetData<AircraftSkillParam>(CfgName); } }
		[SerializeField, HideInInspector]
		private AircraftSkillParamItem[] _Items;
		public AircraftSkillParamItem[] items { get { return _Items; } }

		public AircraftSkillParamItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				AircraftSkillParamItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("AircraftSkillParam表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out AircraftSkillParamItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("AircraftSkillParam表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class AircraftSkillParamItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 编号
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _SkillType;
		/// <summary>
		/// 技能备注
		/// </summary>
		public string skillType { get { return _SkillType; } }

		[SerializeField, HideInInspector]
		private float[] _SkillCD;
		/// <summary>
		/// 技能类型
		/// </summary>
		public float[] skillCD { get { return _SkillCD; } }

		[SerializeField, HideInInspector]
		private int _TriggerMaxCount;
		/// <summary>
		/// 技能冷却
		/// </summary>
		public int triggerMaxCount { get { return _TriggerMaxCount; } }

		[SerializeField, HideInInspector]
		private float[] _TriggerCondition;
		/// <summary>
		/// 最大生效次数
		/// </summary>
		public float[] triggerCondition { get { return _TriggerCondition; } }

		[SerializeField, HideInInspector]
		private int _ConditionTarget;
		/// <summary>
		/// 触发条件
		/// </summary>
		public int conditionTarget { get { return _ConditionTarget; } }

		[SerializeField, HideInInspector]
		private float[] _SkillParam;
		/// <summary>
		/// 条件目标
		/// </summary>
		public float[] skillParam { get { return _SkillParam; } }

		[SerializeField, HideInInspector]
		private int _TargetType;
		/// <summary>
		/// 技能参数
		/// </summary>
		public int targetType { get { return _TargetType; } }

		[SerializeField, HideInInspector]
		private string _FirePoint;
		/// <summary>
		/// 技能目标
		/// </summary>
		public string firePoint { get { return _FirePoint; } }

		[SerializeField, HideInInspector]
		private string[] _SkillAnim;
		/// <summary>
		/// 开火点
		/// </summary>
		public string[] skillAnim { get { return _SkillAnim; } }

		[SerializeField, HideInInspector]
		private float[] _SkillTime;
		/// <summary>
		/// 技能动画
		/// </summary>
		public float[] skillTime { get { return _SkillTime; } }

		[SerializeField, HideInInspector]
		private int[] _SkillEffect;
		/// <summary>
		/// 技能时间
		/// </summary>
		public int[] skillEffect { get { return _SkillEffect; } }

		public override string ToString() {
			return string.Format("[AircraftSkillParamItem]{{id:{0}, skillType:{1}, skillCD:{2}, triggerMaxCount:{3}, triggerCondition:{4}, conditionTarget:{5}, skillParam:{6}, targetType:{7}, firePoint:{8}, skillAnim:{9}, skillTime:{10}, skillEffect:{11}}}",
				id, skillType, array2string(skillCD), triggerMaxCount, array2string(triggerCondition), conditionTarget, array2string(skillParam), targetType, firePoint, array2string(skillAnim), array2string(skillTime), array2string(skillEffect));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(AircraftSkillParamItem item) {
			return item != null;
		}

	}

}
