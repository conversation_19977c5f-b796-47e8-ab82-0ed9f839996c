//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class AssistSlot : ScriptableObject {

		public static string CfgName = "AssistSlot";

		public static AssistSlot Data{ get { return Global.gApp.gGameData.GetData<AssistSlot>(CfgName); } }
		[SerializeField, HideInInspector]
		private AssistSlotItem[] _Items;
		public AssistSlotItem[] items { get { return _Items; } }

		public AssistSlotItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				AssistSlotItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("AssistSlot表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out AssistSlotItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("AssistSlot表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class AssistSlotItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Slot;
		/// <summary>
		/// 槽位ID
		/// </summary>
		public int slot { get { return _Slot; } }

		[SerializeField, HideInInspector]
		private int _Name;
		/// <summary>
		/// 槽位名称
		/// </summary>
		public int name { get { return _Name; } }

		[SerializeField, HideInInspector]
		private string _Unlock;
		/// <summary>
		/// 解锁条件
		/// </summary>
		public string unlock { get { return _Unlock; } }

		public override string ToString() {
			return string.Format("[AssistSlotItem]{{id:{0}, slot:{1}, name:{2}, unlock:{3}}}",
				id, slot, name, unlock);
		}

		public static implicit operator bool(AssistSlotItem item) {
			return item != null;
		}

	}

}
