//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class DailyRechargeReward : ScriptableObject {

		public static string CfgName = "DailyRechargeReward";

		public static DailyRechargeReward Data{ get { return Global.gApp.gGameData.GetData<DailyRechargeReward>(CfgName); } }
		[SerializeField, HideInInspector]
		private DailyRechargeRewardItem[] _Items;
		public DailyRechargeRewardItem[] items { get { return _Items; } }

		public DailyRechargeRewardItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				DailyRechargeRewardItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("DailyRechargeReward表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out DailyRechargeRewardItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("DailyRechargeReward表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class DailyRechargeRewardItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ActivityID;
		/// <summary>
		/// 备注
		/// </summary>
		public int activityID { get { return _ActivityID; } }

		[SerializeField, HideInInspector]
		private int _Day;
		/// <summary>
		/// 活动Id
		/// </summary>
		public int day { get { return _Day; } }

		[SerializeField, HideInInspector]
		private float _RechargeLevel01;
		/// <summary>
		/// 天数
		/// </summary>
		public float rechargeLevel01 { get { return _RechargeLevel01; } }

		[SerializeField, HideInInspector]
		private string[] _Reward01;
		/// <summary>
		/// 充值金额（第一档）
		/// </summary>
		public string[] Reward01 { get { return _Reward01; } }

		[SerializeField, HideInInspector]
		private float _RechargeLevel02;
		/// <summary>
		/// 奖励（第一档）
		/// </summary>
		public float rechargeLevel02 { get { return _RechargeLevel02; } }

		[SerializeField, HideInInspector]
		private string[] _Reward02;
		/// <summary>
		/// 充值金额（第二档）
		/// </summary>
		public string[] Reward02 { get { return _Reward02; } }

		public override string ToString() {
			return string.Format("[DailyRechargeRewardItem]{{id:{0}, activityID:{1}, day:{2}, rechargeLevel01:{3}, Reward01:{4}, rechargeLevel02:{5}, Reward02:{6}}}",
				id, activityID, day, rechargeLevel01, array2string(Reward01), rechargeLevel02, array2string(Reward02));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(DailyRechargeRewardItem item) {
			return item != null;
		}

	}

}
