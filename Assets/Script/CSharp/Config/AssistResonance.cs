//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class AssistResonance : ScriptableObject {

		public static string CfgName = "AssistResonance";

		public static AssistResonance Data{ get { return Global.gApp.gGameData.GetData<AssistResonance>(CfgName); } }
		[SerializeField, HideInInspector]
		private AssistResonanceItem[] _Items;
		public AssistResonanceItem[] items { get { return _Items; } }

		public AssistResonanceItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				AssistResonanceItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("AssistResonance表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out AssistResonanceItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("AssistResonance表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class AssistResonanceItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ResonanceLv;
		/// <summary>
		/// 共鸣等级
		/// </summary>
		public int resonanceLv { get { return _ResonanceLv; } }

		[SerializeField, HideInInspector]
		private string _Condition;
		/// <summary>
		/// 条件
		/// </summary>
		public string condition { get { return _Condition; } }

		[SerializeField, HideInInspector]
		private string[] _ResonanceAttr;
		/// <summary>
		/// 属性奖励
		/// </summary>
		public string[] resonanceAttr { get { return _ResonanceAttr; } }

		[SerializeField, HideInInspector]
		private int _Info;
		/// <summary>
		/// 描述
		/// </summary>
		public int info { get { return _Info; } }

		public override string ToString() {
			return string.Format("[AssistResonanceItem]{{id:{0}, resonanceLv:{1}, condition:{2}, resonanceAttr:{3}, info:{4}}}",
				id, resonanceLv, condition, array2string(resonanceAttr), info);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(AssistResonanceItem item) {
			return item != null;
		}

	}

}
