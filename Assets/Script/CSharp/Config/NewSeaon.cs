//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class NewSeaon : ScriptableObject {

		public static string CfgName = "NewSeaon";

		public static NewSeaon Data{ get { return Global.gApp.gGameData.GetData<NewSeaon>(CfgName); } }
		[SerializeField, HideInInspector]
		private NewSeaonItem[] _Items;
		public NewSeaonItem[] items { get { return _Items; } }

		public NewSeaonItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				NewSeaonItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("NewSeaon表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out NewSeaonItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("NewSeaon表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class NewSeaonItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Lasttime;
		/// <summary>
		/// #
		/// </summary>
		public int lasttime { get { return _Lasttime; } }

		[SerializeField, HideInInspector]
		private string _SeverOpenDayBegin;
		/// <summary>
		/// 持续时间（天）
		/// </summary>
		public string severOpenDayBegin { get { return _SeverOpenDayBegin; } }

		[SerializeField, HideInInspector]
		private string _SeverOpenDayEnd;
		/// <summary>
		/// 开服日期（起）
		/// </summary>
		public string severOpenDayEnd { get { return _SeverOpenDayEnd; } }

		public override string ToString() {
			return string.Format("[NewSeaonItem]{{id:{0}, lasttime:{1}, severOpenDayBegin:{2}, severOpenDayEnd:{3}}}",
				id, lasttime, severOpenDayBegin, severOpenDayEnd);
		}

		public static implicit operator bool(NewSeaonItem item) {
			return item != null;
		}

	}

}
