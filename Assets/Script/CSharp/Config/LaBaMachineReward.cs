//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class LaBaMachineReward : ScriptableObject {

		public static string CfgName = "LaBaMachineReward";

		public static LaBaMachineReward Data{ get { return Global.gApp.gGameData.GetData<LaBaMachineReward>(CfgName); } }
		[SerializeField, HideInInspector]
		private LaBaMachineRewardItem[] _Items;
		public LaBaMachineRewardItem[] items { get { return _Items; } }

		public LaBaMachineRewardItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				LaBaMachineRewardItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("LaBaMachineReward表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out LaBaMachineRewardItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("LaBaMachineReward表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class LaBaMachineRewardItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Type;
		/// <summary>
		/// #备注
		/// </summary>
		public int type { get { return _Type; } }

		[SerializeField, HideInInspector]
		private string[] _PseudoRandom;
		/// <summary>
		/// 类型
		/// </summary>
		public string[] pseudoRandom { get { return _PseudoRandom; } }

		[SerializeField, HideInInspector]
		private string _Reward;
		/// <summary>
		/// 随机概率
		/// </summary>
		public string reward { get { return _Reward; } }

		[SerializeField, HideInInspector]
		private int[] _Type2Jackpot;
		/// <summary>
		/// 奖励
		/// </summary>
		public int[] type2Jackpot { get { return _Type2Jackpot; } }

		[SerializeField, HideInInspector]
		private string _ShowIcon;
		/// <summary>
		/// 情况2特殊处理
		/// </summary>
		public string showIcon { get { return _ShowIcon; } }

		public override string ToString() {
			return string.Format("[LaBaMachineRewardItem]{{id:{0}, type:{1}, pseudoRandom:{2}, reward:{3}, type2Jackpot:{4}, showIcon:{5}}}",
				id, type, array2string(pseudoRandom), reward, array2string(type2Jackpot), showIcon);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(LaBaMachineRewardItem item) {
			return item != null;
		}

	}

}
