//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class LaBaMachine : ScriptableObject {

		public static string CfgName = "LaBaMachine";

		public static LaBaMachine Data{ get { return Global.gApp.gGameData.GetData<LaBaMachine>(CfgName); } }
		[SerializeField, HideInInspector]
		private LaBaMachineItem[] _Items;
		public LaBaMachineItem[] items { get { return _Items; } }

		public LaBaMachineItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				LaBaMachineItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("LaBaMachine表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out LaBaMachineItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("LaBaMachine表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class LaBaMachineItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Type;
		/// <summary>
		/// #备注
		/// </summary>
		public int type { get { return _Type; } }

		[SerializeField, HideInInspector]
		private int _RewardMultiple;
		/// <summary>
		/// 类型
		/// </summary>
		public int rewardMultiple { get { return _RewardMultiple; } }

		[SerializeField, HideInInspector]
		private string[] _PseudoRandom;
		/// <summary>
		/// 奖励倍数
		/// </summary>
		public string[] pseudoRandom { get { return _PseudoRandom; } }

		public override string ToString() {
			return string.Format("[LaBaMachineItem]{{id:{0}, type:{1}, rewardMultiple:{2}, pseudoRandom:{3}}}",
				id, type, rewardMultiple, array2string(pseudoRandom));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(LaBaMachineItem item) {
			return item != null;
		}

	}

}
