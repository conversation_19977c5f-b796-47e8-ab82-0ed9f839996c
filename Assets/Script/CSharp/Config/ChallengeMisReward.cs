//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class ChallengeMisReward : ScriptableObject {

		public static string CfgName = "ChallengeMisReward";

		public static ChallengeMisReward Data{ get { return Global.gApp.gGameData.GetData<ChallengeMisReward>(CfgName); } }
		[SerializeField, HideInInspector]
		private ChallengeMisRewardItem[] _Items;
		public ChallengeMisRewardItem[] items { get { return _Items; } }

		public ChallengeMisRewardItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				ChallengeMisRewardItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("ChallengeMisReward表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out ChallengeMisRewardItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("ChallengeMisReward表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class ChallengeMisRewardItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _RewardScore;
		/// <summary>
		/// 策划备注
		/// </summary>
		public int rewardScore { get { return _RewardScore; } }

		[SerializeField, HideInInspector]
		private int _GiftIcon;
		/// <summary>
		/// 领奖所需分值档位
		/// </summary>
		public int giftIcon { get { return _GiftIcon; } }

		[SerializeField, HideInInspector]
		private string[] _RegainItems;
		/// <summary>
		/// 是否显示小礼盒图标
		/// </summary>
		public string[] regainItems { get { return _RegainItems; } }

		public override string ToString() {
			return string.Format("[ChallengeMisRewardItem]{{id:{0}, rewardScore:{1}, giftIcon:{2}, regainItems:{3}}}",
				id, rewardScore, giftIcon, array2string(regainItems));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(ChallengeMisRewardItem item) {
			return item != null;
		}

	}

}
