//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class MRGuide : ScriptableObject {

		public static string CfgName = "MRGuide";

		public static MRGuide Data{ get { return Global.gApp.gGameData.GetData<MRGuide>(CfgName); } }
		[SerializeField, HideInInspector]
		private MRGuideItem[] _Items;
		public MRGuideItem[] items { get { return _Items; } }

		public MRGuideItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				MRGuideItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("MRGuide表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out MRGuideItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("MRGuide表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class MRGuideItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Group;
		/// <summary>
		/// 组
		/// </summary>
		public int group { get { return _Group; } }

		[SerializeField, HideInInspector]
		private int _CompleteStep;
		/// <summary>
		/// 备注
		/// </summary>
		public int completeStep { get { return _CompleteStep; } }

		[SerializeField, HideInInspector]
		private int _Open01;
		/// <summary>
		/// 引导步数
		/// </summary>
		public int open01 { get { return _Open01; } }

		[SerializeField, HideInInspector]
		private string[] _Open02;
		/// <summary>
		/// 触发条件1（基础条件）
		/// </summary>
		public string[] open02 { get { return _Open02; } }

		[SerializeField, HideInInspector]
		private string[] _GuideUi;
		/// <summary>
		/// 触发条件2（特殊触发）
		/// </summary>
		public string[] guideUi { get { return _GuideUi; } }

		[SerializeField, HideInInspector]
		private int _GuideType;
		/// <summary>
		/// 触发条件3（对应UI名字）
		/// </summary>
		public int guideType { get { return _GuideType; } }

		[SerializeField, HideInInspector]
		private int _SkipTime;
		/// <summary>
		/// 引导类型
		/// </summary>
		public int skipTime { get { return _SkipTime; } }

		[SerializeField, HideInInspector]
		private int _Place;
		/// <summary>
		/// 弱引导跳过时间
		/// </summary>
		public int place { get { return _Place; } }

		[SerializeField, HideInInspector]
		private float[] _PlaceOffset;
		/// <summary>
		/// 指针类型
		/// </summary>
		public float[] placeOffset { get { return _PlaceOffset; } }

		[SerializeField, HideInInspector]
		private string _GuideMecha;
		/// <summary>
		/// 指针位置偏移
		/// </summary>
		public string guideMecha { get { return _GuideMecha; } }

		[SerializeField, HideInInspector]
		private int _NPCOffset;
		/// <summary>
		/// 出现引导NPC的资源路径
		/// </summary>
		public int NPCOffset { get { return _NPCOffset; } }

		[SerializeField, HideInInspector]
		private int _Size;
		/// <summary>
		/// 引导NPC出现在引导框的位置
		/// </summary>
		public int size { get { return _Size; } }

		[SerializeField, HideInInspector]
		private float[] _BoxOffset;
		/// <summary>
		/// 引导框尺寸
		/// </summary>
		public float[] boxOffset { get { return _BoxOffset; } }

		[SerializeField, HideInInspector]
		private int _UiAni;
		/// <summary>
		/// 小引导框位置偏移
		/// </summary>
		public int uiAni { get { return _UiAni; } }

		[SerializeField, HideInInspector]
		private int _BoxTxt;
		/// <summary>
		/// 播放动画
		/// </summary>
		public int boxTxt { get { return _BoxTxt; } }

		[SerializeField, HideInInspector]
		private float _MaskVal;
		/// <summary>
		/// 引导文案
		/// </summary>
		public float maskVal { get { return _MaskVal; } }

		[SerializeField, HideInInspector]
		private string _BI;
		/// <summary>
		/// 遮罩透明
		/// </summary>
		public string BI { get { return _BI; } }

		public override string ToString() {
			return string.Format("[MRGuideItem]{{id:{0}, group:{1}, completeStep:{2}, open01:{3}, open02:{4}, guideUi:{5}, guideType:{6}, skipTime:{7}, place:{8}, placeOffset:{9}, guideMecha:{10}, NPCOffset:{11}, size:{12}, boxOffset:{13}, uiAni:{14}, boxTxt:{15}, maskVal:{16}, BI:{17}}}",
				id, group, completeStep, open01, array2string(open02), array2string(guideUi), guideType, skipTime, place, array2string(placeOffset), guideMecha, NPCOffset, size, array2string(boxOffset), uiAni, boxTxt, maskVal, BI);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(MRGuideItem item) {
			return item != null;
		}

	}

}
