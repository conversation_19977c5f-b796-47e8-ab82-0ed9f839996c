using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildManagementUI_Apply : LDBaseResUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_Choose_Button;
		public RectTransform_Button_Image_Container Choose_Button { get { return m_Choose_Button; } }

		[SerializeField]
		private RectTransform_Image_Container m_Choose_Select;
		public RectTransform_Image_Container Choose_Select { get { return m_Choose_Select; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_PowerChoose_Button;
		public RectTransform_Button_Image_Container PowerChoose_Button { get { return m_PowerChoose_Button; } }

		[SerializeField]
		private RectTransform_Image_Container m_PowerChoose_Select;
		public RectTransform_Image_Container PowerChoose_Select { get { return m_PowerChoose_Select; } }

		[SerializeField]
		private RectTransform_InputField_Image_Container m_InputSearch;
		public RectTransform_InputField_Image_Container InputSearch { get { return m_InputSearch; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_SetConditionBtn;
		public RectTransform_Button_Image_Container SetConditionBtn { get { return m_SetConditionBtn; } }

	}

}
