using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ShopUI_BuyStagePack : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_List;
		public RectTransform_Image_Container List { get { return m_List; } }

		[SerializeField]
		private RectTransform_Container m_StageTouchCheck;
		public RectTransform_Container StageTouchCheck { get { return m_StageTouchCheck; } }

		[SerializeField]
		private RectTransform_ShopUI_BuyStagePack_Item_Container m_Item;
		public RectTransform_ShopUI_BuyStagePack_Item_Container Item { get { return m_Item; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_left_btn;
		public RectTransform_Button_Image_Container left_btn { get { return m_left_btn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_right_btn;
		public RectTransform_Button_Image_Container right_btn { get { return m_right_btn; } }

		[System.Serializable]
		public class RectTransform_ShopUI_BuyStagePack_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ShopUI_BuyStagePack_Item m_Item;
			public ShopUI_BuyStagePack_Item Item { get { return m_Item; } }

			[System.NonSerialized] public List<ShopUI_BuyStagePack_Item> mCachedList = new List<ShopUI_BuyStagePack_Item>();
			private Queue<ShopUI_BuyStagePack_Item> mCachedInstances;
			public ShopUI_BuyStagePack_Item GetInstance(bool ignoreSibling = false) {
				ShopUI_BuyStagePack_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ShopUI_BuyStagePack_Item>(m_Item);
					instance.ItemInit();
				}
				Transform t0 = m_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ShopUI_BuyStagePack_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ShopUI_BuyStagePack_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ShopUI_BuyStagePack_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
