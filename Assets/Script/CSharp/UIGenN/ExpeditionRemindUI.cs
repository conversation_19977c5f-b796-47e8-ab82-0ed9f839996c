using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ExpeditionRemindUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_CommonModelUI;
		public RectTransform_Container CommonModelUI { get { return m_CommonModelUI; } }

		[SerializeField]
		private Transform_Container m_ModelNodeRoot;
		public Transform_Container ModelNodeRoot { get { return m_ModelNodeRoot; } }

		[SerializeField]
		private Transform_Container m_WorldNode;
		public Transform_Container WorldNode { get { return m_WorldNode; } }

		[SerializeField]
		private Transform_Container m_ModelNode;
		public Transform_Container ModelNode { get { return m_ModelNode; } }

		[SerializeField]
		private Transform_Container m_CamerRootNode;
		public Transform_Container CamerRootNode { get { return m_CamerRootNode; } }

		[SerializeField]
		private Transform_Container m_RenderCameraNode;
		public Transform_Container RenderCameraNode { get { return m_RenderCameraNode; } }

		[SerializeField]
		private Transform_Container m_diybeijing;
		public Transform_Container diybeijing { get { return m_diybeijing; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_ModelRawImage;
		public RectTransform_RawImage_Container ModelRawImage { get { return m_ModelRawImage; } }

		[SerializeField]
		private RectTransform_Container m_Mech;
		public RectTransform_Container Mech { get { return m_Mech; } }

		[SerializeField]
		private RectTransform_Container m_Part_Node;
		public RectTransform_Container Part_Node { get { return m_Part_Node; } }

		[SerializeField]
		private RectTransform_Container m_part01;
		public RectTransform_Container part01 { get { return m_part01; } }

		[SerializeField]
		private RectTransform_Container m_partIcon01;
		public RectTransform_Container partIcon01 { get { return m_partIcon01; } }

		[SerializeField]
		private RectTransform_Container m_empty01;
		public RectTransform_Container empty01 { get { return m_empty01; } }

		[SerializeField]
		private RectTransform_Container m_part02;
		public RectTransform_Container part02 { get { return m_part02; } }

		[SerializeField]
		private RectTransform_Container m_partIcon02;
		public RectTransform_Container partIcon02 { get { return m_partIcon02; } }

		[SerializeField]
		private RectTransform_Container m_empty02;
		public RectTransform_Container empty02 { get { return m_empty02; } }

		[SerializeField]
		private RectTransform_Container m_part03;
		public RectTransform_Container part03 { get { return m_part03; } }

		[SerializeField]
		private RectTransform_Container m_partIcon03;
		public RectTransform_Container partIcon03 { get { return m_partIcon03; } }

		[SerializeField]
		private RectTransform_Container m_empty03;
		public RectTransform_Container empty03 { get { return m_empty03; } }

		[SerializeField]
		private RectTransform_Container m_part04;
		public RectTransform_Container part04 { get { return m_part04; } }

		[SerializeField]
		private RectTransform_Container m_partIcon04;
		public RectTransform_Container partIcon04 { get { return m_partIcon04; } }

		[SerializeField]
		private RectTransform_Container m_empty04;
		public RectTransform_Container empty04 { get { return m_empty04; } }

		[SerializeField]
		private RectTransform_Container m_Button_Confirm;
		public RectTransform_Container Button_Confirm { get { return m_Button_Confirm; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Battle_Button;
		public RectTransform_Button_Image_Container Battle_Button { get { return m_Battle_Button; } }

		[SerializeField]
		private RectTransform_Container m_Button_Cancel;
		public RectTransform_Container Button_Cancel { get { return m_Button_Cancel; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Cancel_Button;
		public RectTransform_Button_Image_Container Cancel_Button { get { return m_Cancel_Button; } }

		[SerializeField]
		private RectTransform_Toggle_Image_Container m_Week_Reminder_Button;
		public RectTransform_Toggle_Image_Container Week_Reminder_Button { get { return m_Week_Reminder_Button; } }

		[SerializeField]
		private RectTransform_Image_Container m_Week_Reminder_Select;
		public RectTransform_Image_Container Week_Reminder_Select { get { return m_Week_Reminder_Select; } }

		[SerializeField]
		private RectTransform_Container m_Model;
		public RectTransform_Container Model { get { return m_Model; } }

	}

}
