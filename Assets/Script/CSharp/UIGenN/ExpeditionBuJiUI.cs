using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ExpeditionBuJiUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_ShiJian_BG;
		public RectTransform_Image_Container ShiJian_BG { get { return m_ShiJian_BG; } }

		[SerializeField]
		private RectTransform_Text_Container m_Question_Txt;
		public RectTransform_Text_Container Question_Txt { get { return m_Question_Txt; } }

		[SerializeField]
		private RectTransform_ExpeditionBuJiUI_Option_Container m_Option;
		public RectTransform_ExpeditionBuJiUI_Option_Container Option { get { return m_Option; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_confirm_BG;
		public RectTransform_Button_Image_Container confirm_BG { get { return m_confirm_BG; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Grey_btn_S;
		public RectTransform_Button_Image_Container Grey_btn_S { get { return m_Grey_btn_S; } }

		[System.Serializable]
		public class RectTransform_ExpeditionBuJiUI_Option_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ExpeditionBuJiUI_Option m_Option;
			public ExpeditionBuJiUI_Option Option { get { return m_Option; } }

			[System.NonSerialized] public List<ExpeditionBuJiUI_Option> mCachedList = new List<ExpeditionBuJiUI_Option>();
			private Queue<ExpeditionBuJiUI_Option> mCachedInstances;
			public ExpeditionBuJiUI_Option GetInstance(bool ignoreSibling = false) {
				ExpeditionBuJiUI_Option instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ExpeditionBuJiUI_Option>(m_Option);
					instance.ItemInit();
				}
				Transform t0 = m_Option.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ExpeditionBuJiUI_Option instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ExpeditionBuJiUI_Option>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ExpeditionBuJiUI_Option> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
