using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class AssistMainUI_skill : LDBaseResUI {

		[SerializeField]
		private RectTransform_Animator_Container m_ani;
		public RectTransform_Animator_Container ani { get { return m_ani; } }

		[SerializeField]
		private RectTransform_Container m_root;
		public RectTransform_Container root { get { return m_root; } }

		[SerializeField]
		private RectTransform_Image_Container m_lockbg;
		public RectTransform_Image_Container lockbg { get { return m_lockbg; } }

		[SerializeField]
		private RectTransform_Text_Container m_LvText;
		public RectTransform_Text_Container LvText { get { return m_LvText; } }

		[SerializeField]
		private RectTransform_Text_Container m_skill_info;
		public RectTransform_Text_Container skill_info { get { return m_skill_info; } }

		[SerializeField]
		private RectTransform_Image_Container m_lockState;
		public RectTransform_Image_Container lockState { get { return m_lockState; } }

		[SerializeField]
		private RectTransform_Container m_fx_saoguang;
		public RectTransform_Container fx_saoguang { get { return m_fx_saoguang; } }

	}

}
