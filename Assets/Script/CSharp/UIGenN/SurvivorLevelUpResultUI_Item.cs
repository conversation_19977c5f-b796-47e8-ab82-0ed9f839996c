using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SurvivorLevelUpResultUI_Item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_itemScale;
		public RectTransform_Container itemScale { get { return m_itemScale; } }

		[SerializeField]
		private RectTransform_Image_Container m_item_Quality;
		public RectTransform_Image_Container item_Quality { get { return m_item_Quality; } }

		[SerializeField]
		private RectTransform_Image_Container m_item_Icon;
		public RectTransform_Image_Container item_Icon { get { return m_item_Icon; } }

		[SerializeField]
		private RectTransform_Text_Container m_levell1;
		public RectTransform_Text_Container levell1 { get { return m_levell1; } }

		[SerializeField]
		private RectTransform_Text_Container m_levell2;
		public RectTransform_Text_Container levell2 { get { return m_levell2; } }

	}

}
