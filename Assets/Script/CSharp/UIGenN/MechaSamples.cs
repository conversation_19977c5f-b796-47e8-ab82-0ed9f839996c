using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class MechaSamples : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_NameNode;
		public RectTransform_Container NameNode { get { return m_NameNode; } }

		[SerializeField]
		private RectTransform_Container m_img_quaTitle1;
		public RectTransform_Container img_quaTitle1 { get { return m_img_quaTitle1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rank1;
		public RectTransform_Image_Container img_rank1 { get { return m_img_rank1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_nameBG;
		public RectTransform_Image_Container img_nameBG { get { return m_img_nameBG; } }

		[SerializeField]
		private Transform_Container m_fx;
		public Transform_Container fx { get { return m_fx; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_name_upgrade;
		public RectTransform_Text_Container txt_name_upgrade { get { return m_txt_name_upgrade; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_sortIcon1;
		public RectTransform_Image_Container img_sortIcon1 { get { return m_img_sortIcon1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_cancel;
		public RectTransform_Button_Image_Container cancel { get { return m_cancel; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_use;
		public RectTransform_Button_Image_Container use { get { return m_use; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

	}

}
