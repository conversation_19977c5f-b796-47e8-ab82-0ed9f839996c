using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ServerChooseUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Container m_AllServers;
		public RectTransform_Container AllServers { get { return m_AllServers; } }

		[SerializeField]
		private RectTransform_ServerChooseUI_ServerList_Item_Container m_ServerList_Item;
		public RectTransform_ServerChooseUI_ServerList_Item_Container ServerList_Item { get { return m_ServerList_Item; } }

		[SerializeField]
		private RectTransform_ServerChooseUI_ServerZone_Item_Container m_ServerZone_Item;
		public RectTransform_ServerChooseUI_ServerZone_Item_Container ServerZone_Item { get { return m_ServerZone_Item; } }

		[SerializeField]
		private RectTransform_Container m_Recommend;
		public RectTransform_Container Recommend { get { return m_Recommend; } }

		[SerializeField]
		private RectTransform_ServerChooseUI_RecommendList_Item_Container m_RecommendList_Item;
		public RectTransform_ServerChooseUI_RecommendList_Item_Container RecommendList_Item { get { return m_RecommendList_Item; } }

		[SerializeField]
		private RectTransform_Container m_tabNode;
		public RectTransform_Container tabNode { get { return m_tabNode; } }

		[SerializeField]
		private RectTransform_Button_Container m_btn_AllServersTab;
		public RectTransform_Button_Container btn_AllServersTab { get { return m_btn_AllServersTab; } }

		[SerializeField]
		private RectTransform_Image_Container m_AllServers_Checked_BG;
		public RectTransform_Image_Container AllServers_Checked_BG { get { return m_AllServers_Checked_BG; } }

		[SerializeField]
		private RectTransform_Image_Container m_AllServers_Unchecked_BG;
		public RectTransform_Image_Container AllServers_Unchecked_BG { get { return m_AllServers_Unchecked_BG; } }

		[SerializeField]
		private RectTransform_Button_Container m_btn_RecommendTab;
		public RectTransform_Button_Container btn_RecommendTab { get { return m_btn_RecommendTab; } }

		[SerializeField]
		private RectTransform_Image_Container m_Recommend_Checked_BG;
		public RectTransform_Image_Container Recommend_Checked_BG { get { return m_Recommend_Checked_BG; } }

		[SerializeField]
		private RectTransform_Image_Container m_Recommend_Unchecked_BG;
		public RectTransform_Image_Container Recommend_Unchecked_BG { get { return m_Recommend_Unchecked_BG; } }

		[System.Serializable]
		public class RectTransform_ServerChooseUI_RecommendList_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ServerChooseUI_RecommendList_Item m_RecommendList_Item;
			public ServerChooseUI_RecommendList_Item RecommendList_Item { get { return m_RecommendList_Item; } }

			[System.NonSerialized] public List<ServerChooseUI_RecommendList_Item> mCachedList = new List<ServerChooseUI_RecommendList_Item>();
			private Queue<ServerChooseUI_RecommendList_Item> mCachedInstances;
			public ServerChooseUI_RecommendList_Item GetInstance(bool ignoreSibling = false) {
				ServerChooseUI_RecommendList_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ServerChooseUI_RecommendList_Item>(m_RecommendList_Item);
					instance.ItemInit();
				}
				Transform t0 = m_RecommendList_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ServerChooseUI_RecommendList_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ServerChooseUI_RecommendList_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ServerChooseUI_RecommendList_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_ServerChooseUI_ServerList_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ServerChooseUI_ServerList_Item m_ServerList_Item;
			public ServerChooseUI_ServerList_Item ServerList_Item { get { return m_ServerList_Item; } }

			[System.NonSerialized] public List<ServerChooseUI_ServerList_Item> mCachedList = new List<ServerChooseUI_ServerList_Item>();
			private Queue<ServerChooseUI_ServerList_Item> mCachedInstances;
			public ServerChooseUI_ServerList_Item GetInstance(bool ignoreSibling = false) {
				ServerChooseUI_ServerList_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ServerChooseUI_ServerList_Item>(m_ServerList_Item);
					instance.ItemInit();
				}
				Transform t0 = m_ServerList_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ServerChooseUI_ServerList_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ServerChooseUI_ServerList_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ServerChooseUI_ServerList_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_ServerChooseUI_ServerZone_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ServerChooseUI_ServerZone_Item m_ServerZone_Item;
			public ServerChooseUI_ServerZone_Item ServerZone_Item { get { return m_ServerZone_Item; } }

			[System.NonSerialized] public List<ServerChooseUI_ServerZone_Item> mCachedList = new List<ServerChooseUI_ServerZone_Item>();
			private Queue<ServerChooseUI_ServerZone_Item> mCachedInstances;
			public ServerChooseUI_ServerZone_Item GetInstance(bool ignoreSibling = false) {
				ServerChooseUI_ServerZone_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ServerChooseUI_ServerZone_Item>(m_ServerZone_Item);
					instance.ItemInit();
				}
				Transform t0 = m_ServerZone_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ServerChooseUI_ServerZone_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ServerChooseUI_ServerZone_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ServerChooseUI_ServerZone_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
