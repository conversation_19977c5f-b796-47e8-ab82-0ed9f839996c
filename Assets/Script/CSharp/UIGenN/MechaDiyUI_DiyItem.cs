using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class MechaDiyUI_DiyItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_BtnNode;
		public RectTransform_Container BtnNode { get { return m_BtnNode; } }

		[SerializeField]
		private RectTransform_Container m_scaleNode;
		public RectTransform_Container scaleNode { get { return m_scaleNode; } }

		[SerializeField]
		private RectTransform_Container m_comboSkillNode;
		public RectTransform_Container comboSkillNode { get { return m_comboSkillNode; } }

		[SerializeField]
		private RectTransform_Container m_comboEffectNode;
		public RectTransform_Container comboEffectNode { get { return m_comboEffectNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_mechaIcon;
		public RectTransform_Image_Container img_mechaIcon { get { return m_img_mechaIcon; } }

		[SerializeField]
		private RectTransform_Image_Container m_partLevel_bg;
		public RectTransform_Image_Container partLevel_bg { get { return m_partLevel_bg; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_partLevel;
		public RectTransform_Text_Container txt_partLevel { get { return m_txt_partLevel; } }

		[SerializeField]
		private RectTransform_Container m_RedTips;
		public RectTransform_Container RedTips { get { return m_RedTips; } }

		[SerializeField]
		private RectTransform_Image_Container m_Arrow;
		public RectTransform_Image_Container Arrow { get { return m_Arrow; } }

		[SerializeField]
		private RectTransform_Image_Container m_RedNode;
		public RectTransform_Image_Container RedNode { get { return m_RedNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_NumNode;
		public RectTransform_Image_Container NumNode { get { return m_NumNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

	}

}
