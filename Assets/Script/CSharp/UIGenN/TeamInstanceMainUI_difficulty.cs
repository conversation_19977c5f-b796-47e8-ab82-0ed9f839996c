using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class TeamInstanceMainUI_difficulty : LDBaseResUI {

		[SerializeField]
		private RectTransform_Button_Container m_defaultBg;
		public RectTransform_Button_Container defaultBg { get { return m_defaultBg; } }

		[SerializeField]
		private RectTransform_Image_Container m_mission_defaultBG1;
		public RectTransform_Image_Container mission_defaultBG1 { get { return m_mission_defaultBG1; } }

		[SerializeField]
		private RectTransform_Text_Container m_default_name;
		public RectTransform_Text_Container default_name { get { return m_default_name; } }

		[SerializeField]
		private RectTransform_Container m_selectedBg;
		public RectTransform_Container selectedBg { get { return m_selectedBg; } }

		[SerializeField]
		private RectTransform_Image_Container m_mission_BG2;
		public RectTransform_Image_Container mission_BG2 { get { return m_mission_BG2; } }

		[SerializeField]
		private RectTransform_Text_Container m_mission_name;
		public RectTransform_Text_Container mission_name { get { return m_mission_name; } }

		[SerializeField]
		private RectTransform_Button_Container m_difficultylock;
		public RectTransform_Button_Container difficultylock { get { return m_difficultylock; } }

		[SerializeField]
		private RectTransform_Image_Container m_mission_lockBG1;
		public RectTransform_Image_Container mission_lockBG1 { get { return m_mission_lockBG1; } }

		[SerializeField]
		private RectTransform_Text_Container m_lock_name;
		public RectTransform_Text_Container lock_name { get { return m_lock_name; } }

	}

}
