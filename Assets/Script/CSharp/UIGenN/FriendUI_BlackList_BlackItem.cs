using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class FriendUI_BlackList_BlackItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_List_BG_Online;
		public RectTransform_Image_Container List_BG_Online { get { return m_List_BG_Online; } }

		[SerializeField]
		private RectTransform_Image_Container m_List_BG_Offline;
		public RectTransform_Image_Container List_BG_Offline { get { return m_List_BG_Offline; } }

		[SerializeField]
		private RectTransform_Container m_RoleHead;
		public RectTransform_Container RoleHead { get { return m_RoleHead; } }

		[SerializeField]
		private RectTransform_Text_Container m_Level_Txt;
		public RectTransform_Text_Container Level_Txt { get { return m_Level_Txt; } }

		[SerializeField]
		private RectTransform_Text_Container m_List_PlayerName_Txt;
		public RectTransform_Text_Container List_PlayerName_Txt { get { return m_List_PlayerName_Txt; } }

		[SerializeField]
		private RectTransform_Container m_PowerNode;
		public RectTransform_Container PowerNode { get { return m_PowerNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power;
		public RectTransform_Text_Container Power { get { return m_Power; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_Delete;
		public RectTransform_Button_Image_Container Orange_btn_Delete { get { return m_Orange_btn_Delete; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Grey_btn_Delete;
		public RectTransform_Button_Image_Container Grey_btn_Delete { get { return m_Grey_btn_Delete; } }

	}

}
