using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SeasonRewardsPreviewUI : LDBaseActivityChildUI {

		[SerializeField]
		private RectTransform_Text_Container m_time;
		public RectTransform_Text_Container time { get { return m_time; } }

		[SerializeField]
		private RectTransform_Container m_ScrollView;
		public RectTransform_Container ScrollView { get { return m_ScrollView; } }

		[SerializeField]
		private RectTransform_Container m_RankRewardContent;
		public RectTransform_Container RankRewardContent { get { return m_RankRewardContent; } }

		[SerializeField]
		private RectTransform_SeasonRewardsPreviewUI_RankRewardItem_Container m_RankRewardItem;
		public RectTransform_SeasonRewardsPreviewUI_RankRewardItem_Container RankRewardItem { get { return m_RankRewardItem; } }

		[SerializeField]
		private RectTransform_Container m_MySelf_Rank_rewards;
		public RectTransform_Container MySelf_Rank_rewards { get { return m_MySelf_Rank_rewards; } }

		[SerializeField]
		private RectTransform_Image_Container m_exist;
		public RectTransform_Image_Container exist { get { return m_exist; } }

		[SerializeField]
		private RectTransform_Text_Container m_num;
		public RectTransform_Text_Container num { get { return m_num; } }

		[SerializeField]
		private RectTransform_Image_Container m_no_exist;
		public RectTransform_Image_Container no_exist { get { return m_no_exist; } }

		[SerializeField]
		private RectTransform_Container m_SelfItems;
		public RectTransform_Container SelfItems { get { return m_SelfItems; } }

		[SerializeField]
		private RectTransform_SeasonRewardsPreviewUI_item_Container m_item;
		public RectTransform_SeasonRewardsPreviewUI_item_Container item { get { return m_item; } }

		[System.Serializable]
		public class RectTransform_SeasonRewardsPreviewUI_item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonRewardsPreviewUI_item m_item;
			public SeasonRewardsPreviewUI_item item { get { return m_item; } }

			[System.NonSerialized] public List<SeasonRewardsPreviewUI_item> mCachedList = new List<SeasonRewardsPreviewUI_item>();
			private Queue<SeasonRewardsPreviewUI_item> mCachedInstances;
			public SeasonRewardsPreviewUI_item GetInstance(bool ignoreSibling = false) {
				SeasonRewardsPreviewUI_item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonRewardsPreviewUI_item>(m_item);
					instance.ItemInit();
				}
				Transform t0 = m_item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonRewardsPreviewUI_item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonRewardsPreviewUI_item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonRewardsPreviewUI_item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_SeasonRewardsPreviewUI_RankRewardItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonRewardsPreviewUI_RankRewardItem m_RankRewardItem;
			public SeasonRewardsPreviewUI_RankRewardItem RankRewardItem { get { return m_RankRewardItem; } }

			[System.NonSerialized] public List<SeasonRewardsPreviewUI_RankRewardItem> mCachedList = new List<SeasonRewardsPreviewUI_RankRewardItem>();
			private Queue<SeasonRewardsPreviewUI_RankRewardItem> mCachedInstances;
			public SeasonRewardsPreviewUI_RankRewardItem GetInstance(bool ignoreSibling = false) {
				SeasonRewardsPreviewUI_RankRewardItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonRewardsPreviewUI_RankRewardItem>(m_RankRewardItem);
					instance.ItemInit();
				}
				Transform t0 = m_RankRewardItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonRewardsPreviewUI_RankRewardItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonRewardsPreviewUI_RankRewardItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonRewardsPreviewUI_RankRewardItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
