using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class KnapsackRoot_Item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_ItemNode;
		public RectTransform_Container ItemNode { get { return m_ItemNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Btn;
		public RectTransform_Button_Image_Container Btn { get { return m_Btn; } }

	}

}
