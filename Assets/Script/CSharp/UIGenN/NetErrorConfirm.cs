using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class NetErrorConfirm : LDBaseUI {

		[SerializeField]
		private RectTransform_Text_Container m_titletxt;
		public RectTransform_Text_Container titletxt { get { return m_titletxt; } }

		[SerializeField]
		private RectTransform_Text_Container m_contenttxt;
		public RectTransform_Text_Container contenttxt { get { return m_contenttxt; } }

		[SerializeField]
		private RectTransform_Container m_button_Node1;
		public RectTransform_Container button_Node1 { get { return m_button_Node1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_CancelBtn;
		public RectTransform_Button_Image_Container CancelBtn { get { return m_CancelBtn; } }

		[SerializeField]
		private RectTransform_Text_Container m_CancelTxt;
		public RectTransform_Text_Container CancelTxt { get { return m_CancelTxt; } }

		[SerializeField]
		private RectTransform_Container m_button_Node2;
		public RectTransform_Container button_Node2 { get { return m_button_Node2; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Green_btn_m;
		public RectTransform_Button_Image_Container Green_btn_m { get { return m_Green_btn_m; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_ConfirmBtn;
		public RectTransform_Button_Image_Container ConfirmBtn { get { return m_ConfirmBtn; } }

		[SerializeField]
		private RectTransform_Text_Container m_ConfirmTxt;
		public RectTransform_Text_Container ConfirmTxt { get { return m_ConfirmTxt; } }

	}

}
