using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class EquipmentReforgeUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_icon;
		public RectTransform_Image_Container icon { get { return m_icon; } }

		[SerializeField]
		private RectTransform_Text_Container m_EquipName;
		public RectTransform_Text_Container EquipName { get { return m_EquipName; } }

		[SerializeField]
		private RectTransform_Container m_powerNode;
		public RectTransform_Container powerNode { get { return m_powerNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_powerNum;
		public RectTransform_Text_Container powerNum { get { return m_powerNum; } }

		[SerializeField]
		private RectTransform_Container m_num;
		public RectTransform_Container num { get { return m_num; } }

		[SerializeField]
		private RectTransform_Text_Container m_up_num;
		public RectTransform_Text_Container up_num { get { return m_up_num; } }

		[SerializeField]
		private RectTransform_Text_Container m_down_num;
		public RectTransform_Text_Container down_num { get { return m_down_num; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Btn_switch;
		public RectTransform_Button_Image_Container Btn_switch { get { return m_Btn_switch; } }

		[SerializeField]
		private RectTransform_Image_Container m_lockNode;
		public RectTransform_Image_Container lockNode { get { return m_lockNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_unlockNode;
		public RectTransform_Image_Container unlockNode { get { return m_unlockNode; } }

		[SerializeField]
		private RectTransform_Container m_arrtOld;
		public RectTransform_Container arrtOld { get { return m_arrtOld; } }

		[SerializeField]
		private RectTransform_EquipmentReforgeUI_CitiaoItem_Container m_CitiaoItem;
		public RectTransform_EquipmentReforgeUI_CitiaoItem_Container CitiaoItem { get { return m_CitiaoItem; } }

		[SerializeField]
		private RectTransform_Container m_arrtNew;
		public RectTransform_Container arrtNew { get { return m_arrtNew; } }

		[SerializeField]
		private RectTransform_EquipmentReforgeUI_CitiaoItemNew_Container m_CitiaoItemNew;
		public RectTransform_EquipmentReforgeUI_CitiaoItemNew_Container CitiaoItemNew { get { return m_CitiaoItemNew; } }

		[SerializeField]
		private RectTransform_Container m_btn_node;
		public RectTransform_Container btn_node { get { return m_btn_node; } }

		[SerializeField]
		private RectTransform_Container m_replaceBtnNode;
		public RectTransform_Container replaceBtnNode { get { return m_replaceBtnNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Btn_replace;
		public RectTransform_Button_Image_Container Btn_replace { get { return m_Btn_replace; } }

		[SerializeField]
		private RectTransform_Container m_reforgeBtnNode;
		public RectTransform_Container reforgeBtnNode { get { return m_reforgeBtnNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Btn_reforge;
		public RectTransform_Button_Image_Container Btn_reforge { get { return m_Btn_reforge; } }

		[SerializeField]
		private RectTransform_EquipmentReforgeUI_itemCost_Container m_itemCost;
		public RectTransform_EquipmentReforgeUI_itemCost_Container itemCost { get { return m_itemCost; } }

		[SerializeField]
		private RectTransform_Container m_xinxi_Node;
		public RectTransform_Container xinxi_Node { get { return m_xinxi_Node; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_CheckAttrlBtn;
		public RectTransform_Button_Image_Container CheckAttrlBtn { get { return m_CheckAttrlBtn; } }

		[SerializeField]
		private RectTransform_Image_Container m_checkMark;
		public RectTransform_Image_Container checkMark { get { return m_checkMark; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[System.Serializable]
		public class RectTransform_EquipmentReforgeUI_CitiaoItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private EquipmentReforgeUI_CitiaoItem m_CitiaoItem;
			public EquipmentReforgeUI_CitiaoItem CitiaoItem { get { return m_CitiaoItem; } }

			[System.NonSerialized] public List<EquipmentReforgeUI_CitiaoItem> mCachedList = new List<EquipmentReforgeUI_CitiaoItem>();
			private Queue<EquipmentReforgeUI_CitiaoItem> mCachedInstances;
			public EquipmentReforgeUI_CitiaoItem GetInstance(bool ignoreSibling = false) {
				EquipmentReforgeUI_CitiaoItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<EquipmentReforgeUI_CitiaoItem>(m_CitiaoItem);
					instance.ItemInit();
				}
				Transform t0 = m_CitiaoItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(EquipmentReforgeUI_CitiaoItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<EquipmentReforgeUI_CitiaoItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<EquipmentReforgeUI_CitiaoItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_EquipmentReforgeUI_CitiaoItemNew_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private EquipmentReforgeUI_CitiaoItemNew m_CitiaoItemNew;
			public EquipmentReforgeUI_CitiaoItemNew CitiaoItemNew { get { return m_CitiaoItemNew; } }

			[System.NonSerialized] public List<EquipmentReforgeUI_CitiaoItemNew> mCachedList = new List<EquipmentReforgeUI_CitiaoItemNew>();
			private Queue<EquipmentReforgeUI_CitiaoItemNew> mCachedInstances;
			public EquipmentReforgeUI_CitiaoItemNew GetInstance(bool ignoreSibling = false) {
				EquipmentReforgeUI_CitiaoItemNew instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<EquipmentReforgeUI_CitiaoItemNew>(m_CitiaoItemNew);
					instance.ItemInit();
				}
				Transform t0 = m_CitiaoItemNew.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(EquipmentReforgeUI_CitiaoItemNew instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<EquipmentReforgeUI_CitiaoItemNew>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<EquipmentReforgeUI_CitiaoItemNew> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_EquipmentReforgeUI_itemCost_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private EquipmentReforgeUI_itemCost m_itemCost;
			public EquipmentReforgeUI_itemCost itemCost { get { return m_itemCost; } }

			[System.NonSerialized] public List<EquipmentReforgeUI_itemCost> mCachedList = new List<EquipmentReforgeUI_itemCost>();
			private Queue<EquipmentReforgeUI_itemCost> mCachedInstances;
			public EquipmentReforgeUI_itemCost GetInstance(bool ignoreSibling = false) {
				EquipmentReforgeUI_itemCost instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<EquipmentReforgeUI_itemCost>(m_itemCost);
					instance.ItemInit();
				}
				Transform t0 = m_itemCost.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(EquipmentReforgeUI_itemCost instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<EquipmentReforgeUI_itemCost>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<EquipmentReforgeUI_itemCost> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
