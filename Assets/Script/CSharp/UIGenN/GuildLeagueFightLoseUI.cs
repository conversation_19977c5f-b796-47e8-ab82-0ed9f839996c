using UnityEngine;

namespace LD {

	public partial class GuildLeagueFightLoseUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_MainFightResultUI;
		public RectTransform_Container MainFightResultUI { get { return m_MainFightResultUI; } }

		[SerializeField]
		private RectTransform_Container m_Rescue;
		public RectTransform_Container Rescue { get { return m_Rescue; } }

		[SerializeField]
		private RectTransform_Container m_Attack;
		public RectTransform_Container Attack { get { return m_Attack; } }

	}

}
