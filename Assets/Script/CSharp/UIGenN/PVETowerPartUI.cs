using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PVETowerPartUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_NormalBg;
		public RectTransform_Image_Container NormalBg { get { return m_NormalBg; } }

		[SerializeField]
		private RectTransform_Text_Container m_PartName;
		public RectTransform_Text_Container PartName { get { return m_PartName; } }

		[SerializeField]
		private RectTransform_Container m_img_mask_diypart;
		public RectTransform_Container img_mask_diypart { get { return m_img_mask_diypart; } }

		[SerializeField]
		private RectTransform_Image_Container m_PartItem;
		public RectTransform_Image_Container PartItem { get { return m_PartItem; } }

		[SerializeField]
		private RectTransform_Text_Container m_PartTips;
		public RectTransform_Text_Container PartTips { get { return m_PartTips; } }

	}

}
