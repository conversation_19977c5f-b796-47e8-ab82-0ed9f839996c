using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildUpgradeUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_BeforGuildLogoUI;
		public RectTransform_Container BeforGuildLogoUI { get { return m_BeforGuildLogoUI; } }

		[SerializeField]
		private RectTransform_Container m_AfterGuildLogoUI;
		public RectTransform_Container AfterGuildLogoUI { get { return m_AfterGuildLogoUI; } }

		[SerializeField]
		private RectTransform_Container m_BG;
		public RectTransform_Container BG { get { return m_BG; } }

		[SerializeField]
		private RectTransform_Text_Container m_BeforeNum;
		public RectTransform_Text_Container BeforeNum { get { return m_BeforeNum; } }

		[SerializeField]
		private RectTransform_Text_Container m_AfterNum;
		public RectTransform_Text_Container AfterNum { get { return m_AfterNum; } }

		[SerializeField]
		private RectTransform_Container m_array_switch;
		public RectTransform_Container array_switch { get { return m_array_switch; } }

		[SerializeField]
		private RectTransform_GuildUpgradeUI_AttrItem_Container m_AttrItem;
		public RectTransform_GuildUpgradeUI_AttrItem_Container AttrItem { get { return m_AttrItem; } }

		[System.Serializable]
		public class RectTransform_GuildUpgradeUI_AttrItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private GuildUpgradeUI_AttrItem m_AttrItem;
			public GuildUpgradeUI_AttrItem AttrItem { get { return m_AttrItem; } }

			[System.NonSerialized] public List<GuildUpgradeUI_AttrItem> mCachedList = new List<GuildUpgradeUI_AttrItem>();
			private Queue<GuildUpgradeUI_AttrItem> mCachedInstances;
			public GuildUpgradeUI_AttrItem GetInstance(bool ignoreSibling = false) {
				GuildUpgradeUI_AttrItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<GuildUpgradeUI_AttrItem>(m_AttrItem);
					instance.ItemInit();
				}
				Transform t0 = m_AttrItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(GuildUpgradeUI_AttrItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<GuildUpgradeUI_AttrItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<GuildUpgradeUI_AttrItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
