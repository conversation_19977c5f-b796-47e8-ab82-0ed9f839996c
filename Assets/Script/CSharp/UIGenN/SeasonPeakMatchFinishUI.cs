using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SeasonPeakMatchFinishUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Animator_Container m_point_1;
		public RectTransform_Animator_Container point_1 { get { return m_point_1; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_2;
		public RectTransform_Animator_Container point_2 { get { return m_point_2; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_3;
		public RectTransform_Animator_Container point_3 { get { return m_point_3; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_4;
		public RectTransform_Animator_Container point_4 { get { return m_point_4; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_5;
		public RectTransform_Animator_Container point_5 { get { return m_point_5; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_6;
		public RectTransform_Animator_Container point_6 { get { return m_point_6; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_7;
		public RectTransform_Animator_Container point_7 { get { return m_point_7; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_8;
		public RectTransform_Animator_Container point_8 { get { return m_point_8; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_9;
		public RectTransform_Animator_Container point_9 { get { return m_point_9; } }

		[SerializeField]
		private RectTransform_Animator_Container m_point_10;
		public RectTransform_Animator_Container point_10 { get { return m_point_10; } }

		[SerializeField]
		private RectTransform_Text_Container m_time;
		public RectTransform_Text_Container time { get { return m_time; } }

		[SerializeField]
		private RectTransform_Animator_Image_Container m_stageNode;
		public RectTransform_Animator_Image_Container stageNode { get { return m_stageNode; } }

		[SerializeField]
		private RectTransform_Container m_stage_battleRe;
		public RectTransform_Container stage_battleRe { get { return m_stage_battleRe; } }

		[SerializeField]
		private RectTransform_Text_Container m_stage_battleRe_Text;
		public RectTransform_Text_Container stage_battleRe_Text { get { return m_stage_battleRe_Text; } }

		[SerializeField]
		private RectTransform_SeasonPeakMatchFinishUI_rightItem_Container m_rightItem;
		public RectTransform_SeasonPeakMatchFinishUI_rightItem_Container rightItem { get { return m_rightItem; } }

		[SerializeField]
		private RectTransform_Animator_Image_Container m_show;
		public RectTransform_Animator_Image_Container show { get { return m_show; } }

		[SerializeField]
		private RectTransform_SeasonPeakMatchFinishUI_leftItem_Container m_leftItem;
		public RectTransform_SeasonPeakMatchFinishUI_leftItem_Container leftItem { get { return m_leftItem; } }

		[SerializeField]
		private RectTransform_Text_Container m_UnlockTips;
		public RectTransform_Text_Container UnlockTips { get { return m_UnlockTips; } }

		[SerializeField]
		private RectTransform_Container m_NodeParent;
		public RectTransform_Container NodeParent { get { return m_NodeParent; } }

		[SerializeField]
		private RectTransform_SeasonPeakMatchFinishUI_NodeInfo_Container m_NodeInfo;
		public RectTransform_SeasonPeakMatchFinishUI_NodeInfo_Container NodeInfo { get { return m_NodeInfo; } }

		[SerializeField]
		private RectTransform_Container m_TopBtns;
		public RectTransform_Container TopBtns { get { return m_TopBtns; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[System.Serializable]
		public class RectTransform_SeasonPeakMatchFinishUI_leftItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonPeakMatchFinishUI_leftItem m_leftItem;
			public SeasonPeakMatchFinishUI_leftItem leftItem { get { return m_leftItem; } }

			[System.NonSerialized] public List<SeasonPeakMatchFinishUI_leftItem> mCachedList = new List<SeasonPeakMatchFinishUI_leftItem>();
			private Queue<SeasonPeakMatchFinishUI_leftItem> mCachedInstances;
			public SeasonPeakMatchFinishUI_leftItem GetInstance(bool ignoreSibling = false) {
				SeasonPeakMatchFinishUI_leftItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonPeakMatchFinishUI_leftItem>(m_leftItem);
					instance.ItemInit();
				}
				Transform t0 = m_leftItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonPeakMatchFinishUI_leftItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonPeakMatchFinishUI_leftItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonPeakMatchFinishUI_leftItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_SeasonPeakMatchFinishUI_NodeInfo_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonPeakMatchFinishUI_NodeInfo m_NodeInfo;
			public SeasonPeakMatchFinishUI_NodeInfo NodeInfo { get { return m_NodeInfo; } }

			[System.NonSerialized] public List<SeasonPeakMatchFinishUI_NodeInfo> mCachedList = new List<SeasonPeakMatchFinishUI_NodeInfo>();
			private Queue<SeasonPeakMatchFinishUI_NodeInfo> mCachedInstances;
			public SeasonPeakMatchFinishUI_NodeInfo GetInstance(bool ignoreSibling = false) {
				SeasonPeakMatchFinishUI_NodeInfo instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonPeakMatchFinishUI_NodeInfo>(m_NodeInfo);
					instance.ItemInit();
				}
				Transform t0 = m_NodeInfo.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonPeakMatchFinishUI_NodeInfo instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonPeakMatchFinishUI_NodeInfo>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonPeakMatchFinishUI_NodeInfo> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_SeasonPeakMatchFinishUI_rightItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonPeakMatchFinishUI_rightItem m_rightItem;
			public SeasonPeakMatchFinishUI_rightItem rightItem { get { return m_rightItem; } }

			[System.NonSerialized] public List<SeasonPeakMatchFinishUI_rightItem> mCachedList = new List<SeasonPeakMatchFinishUI_rightItem>();
			private Queue<SeasonPeakMatchFinishUI_rightItem> mCachedInstances;
			public SeasonPeakMatchFinishUI_rightItem GetInstance(bool ignoreSibling = false) {
				SeasonPeakMatchFinishUI_rightItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonPeakMatchFinishUI_rightItem>(m_rightItem);
					instance.ItemInit();
				}
				Transform t0 = m_rightItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonPeakMatchFinishUI_rightItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonPeakMatchFinishUI_rightItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonPeakMatchFinishUI_rightItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
