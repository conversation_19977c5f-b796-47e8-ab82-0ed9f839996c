using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class AppPingJia : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_M;
		public RectTransform_Button_Image_Container Orange_btn_M { get { return m_Orange_btn_M; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Green_btn_M;
		public RectTransform_Button_Image_Container Green_btn_M { get { return m_Green_btn_M; } }

	}

}
