using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class MechaSpinBuyItem_item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_super_value;
		public RectTransform_Image_Container super_value { get { return m_super_value; } }

		[SerializeField]
		private RectTransform_Text_Container m_super_value_num;
		public RectTransform_Text_Container super_value_num { get { return m_super_value_num; } }

		[SerializeField]
		private RectTransform_Text_Container m_quota_text;
		public RectTransform_Text_Container quota_text { get { return m_quota_text; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_buyBtn;
		public RectTransform_Button_Image_Container buyBtn { get { return m_buyBtn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_ad_btn;
		public RectTransform_Button_Image_Container ad_btn { get { return m_ad_btn; } }

		[SerializeField]
		private RectTransform_Text_Container m_ad_tips;
		public RectTransform_Text_Container ad_tips { get { return m_ad_tips; } }

		[SerializeField]
		private RectTransform_Container m_RedTips;
		public RectTransform_Container RedTips { get { return m_RedTips; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Purchased_btn;
		public RectTransform_Button_Image_Container Purchased_btn { get { return m_Purchased_btn; } }

		[SerializeField]
		private RectTransform_MechaSpinBuyItem_item_item_Container m_item;
		public RectTransform_MechaSpinBuyItem_item_item_Container item { get { return m_item; } }

		[System.Serializable]
		public class RectTransform_MechaSpinBuyItem_item_item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private MechaSpinBuyItem_item_item m_item;
			public MechaSpinBuyItem_item_item item { get { return m_item; } }

			[System.NonSerialized] public List<MechaSpinBuyItem_item_item> mCachedList = new List<MechaSpinBuyItem_item_item>();
			private Queue<MechaSpinBuyItem_item_item> mCachedInstances;
			public MechaSpinBuyItem_item_item GetInstance(bool ignoreSibling = false) {
				MechaSpinBuyItem_item_item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<MechaSpinBuyItem_item_item>(m_item);
					instance.ItemInit();
				}
				Transform t0 = m_item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(MechaSpinBuyItem_item_item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<MechaSpinBuyItem_item_item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<MechaSpinBuyItem_item_item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
