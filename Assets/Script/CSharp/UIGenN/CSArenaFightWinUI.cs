using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class CSArenaFightWinUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_Rank;
		public RectTransform_Container Rank { get { return m_Rank; } }

		[SerializeField]
		private RectTransform_Container m_RankNode;
		public RectTransform_Container RankNode { get { return m_RankNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_ArrowUp;
		public RectTransform_Image_Container ArrowUp { get { return m_ArrowUp; } }

		[SerializeField]
		private RectTransform_Image_Container m_ArrowDown;
		public RectTransform_Image_Container ArrowDown { get { return m_ArrowDown; } }

		[SerializeField]
		private RectTransform_Text_Container m_Rank_Num;
		public RectTransform_Text_Container Rank_Num { get { return m_Rank_Num; } }

		[SerializeField]
		private RectTransform_Container m_Self;
		public RectTransform_Container Self { get { return m_Self; } }

		[SerializeField]
		private RectTransform_Container m_RoleHead_Self;
		public RectTransform_Container RoleHead_Self { get { return m_RoleHead_Self; } }

		[SerializeField]
		private RectTransform_Container m_Level_Node_Self;
		public RectTransform_Container Level_Node_Self { get { return m_Level_Node_Self; } }

		[SerializeField]
		private RectTransform_Text_Container m_Level_Txt_Self;
		public RectTransform_Text_Container Level_Txt_Self { get { return m_Level_Txt_Self; } }

		[SerializeField]
		private RectTransform_Text_Container m_PlayerName_Self;
		public RectTransform_Text_Container PlayerName_Self { get { return m_PlayerName_Self; } }

		[SerializeField]
		private RectTransform_Text_Container m_SelfScore;
		public RectTransform_Text_Container SelfScore { get { return m_SelfScore; } }

		[SerializeField]
		private RectTransform_Text_Container m_SelfUpNum;
		public RectTransform_Text_Container SelfUpNum { get { return m_SelfUpNum; } }

		[SerializeField]
		private RectTransform_Text_Container m_SelfDownNum;
		public RectTransform_Text_Container SelfDownNum { get { return m_SelfDownNum; } }

		[SerializeField]
		private RectTransform_Container m_Enemy;
		public RectTransform_Container Enemy { get { return m_Enemy; } }

		[SerializeField]
		private RectTransform_Container m_RoleHead_Enemy;
		public RectTransform_Container RoleHead_Enemy { get { return m_RoleHead_Enemy; } }

		[SerializeField]
		private RectTransform_Container m_Level_Node_Enemy;
		public RectTransform_Container Level_Node_Enemy { get { return m_Level_Node_Enemy; } }

		[SerializeField]
		private RectTransform_Text_Container m_Level_Txt_Enemy;
		public RectTransform_Text_Container Level_Txt_Enemy { get { return m_Level_Txt_Enemy; } }

		[SerializeField]
		private RectTransform_Text_Container m_PlayerName_Enemy;
		public RectTransform_Text_Container PlayerName_Enemy { get { return m_PlayerName_Enemy; } }

		[SerializeField]
		private RectTransform_Text_Container m_EnemyScore;
		public RectTransform_Text_Container EnemyScore { get { return m_EnemyScore; } }

		[SerializeField]
		private RectTransform_Text_Container m_EnemyUpNum;
		public RectTransform_Text_Container EnemyUpNum { get { return m_EnemyUpNum; } }

		[SerializeField]
		private RectTransform_Text_Container m_EnemyDownNum;
		public RectTransform_Text_Container EnemyDownNum { get { return m_EnemyDownNum; } }

		[SerializeField]
		private RectTransform_Container m_MainFightResultUI;
		public RectTransform_Container MainFightResultUI { get { return m_MainFightResultUI; } }

	}

}
