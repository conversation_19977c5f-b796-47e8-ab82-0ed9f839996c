using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class OtherLineupUI_ModuleItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_Offset;
		public RectTransform_Container Offset { get { return m_Offset; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality;
		public RectTransform_Image_Container Quality { get { return m_Quality; } }

		[SerializeField]
		private RectTransform_Container m_CanGet;
		public RectTransform_Container CanGet { get { return m_CanGet; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon01;
		public RectTransform_Image_Container Icon01 { get { return m_Icon01; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon02_JiaoBiao;
		public RectTransform_Image_Container Icon02_JiaoBiao { get { return m_Icon02_JiaoBiao; } }

		[SerializeField]
		private RectTransform_Image_Container m_Num_bg;
		public RectTransform_Image_Container Num_bg { get { return m_Num_bg; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

		[SerializeField]
		private RectTransform_Container m_Num_Quality;
		public RectTransform_Container Num_Quality { get { return m_Num_Quality; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality1;
		public RectTransform_Image_Container Quality1 { get { return m_Quality1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality2;
		public RectTransform_Image_Container Quality2 { get { return m_Quality2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality3;
		public RectTransform_Image_Container Quality3 { get { return m_Quality3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality4;
		public RectTransform_Image_Container Quality4 { get { return m_Quality4; } }

		[SerializeField]
		private RectTransform_Container m_Time;
		public RectTransform_Container Time { get { return m_Time; } }

		[SerializeField]
		private RectTransform_Text_Container m_Time_lbl;
		public RectTransform_Text_Container Time_lbl { get { return m_Time_lbl; } }

		[SerializeField]
		private RectTransform_Container m_Mask;
		public RectTransform_Container Mask { get { return m_Mask; } }

		[SerializeField]
		private RectTransform_Image_Container m_ClickBtn;
		public RectTransform_Image_Container ClickBtn { get { return m_ClickBtn; } }

		[SerializeField]
		private RectTransform_Container m_equip;
		public RectTransform_Container equip { get { return m_equip; } }

		[SerializeField]
		private RectTransform_Container m_locked;
		public RectTransform_Container locked { get { return m_locked; } }

		[SerializeField]
		private RectTransform_Image_Container m_Select;
		public RectTransform_Image_Container Select { get { return m_Select; } }

		[SerializeField]
		private RectTransform_Image_Container m_Red;
		public RectTransform_Image_Container Red { get { return m_Red; } }

		[SerializeField]
		private RectTransform_Image_Container m_Xi;
		public RectTransform_Image_Container Xi { get { return m_Xi; } }

		[SerializeField]
		private RectTransform_Container m_RightLocked;
		public RectTransform_Container RightLocked { get { return m_RightLocked; } }

		[SerializeField]
		private RectTransform_Image_Container m_uavType;
		public RectTransform_Image_Container uavType { get { return m_uavType; } }

		[SerializeField]
		private RectTransform_Image_Container m_uavinplay;
		public RectTransform_Image_Container uavinplay { get { return m_uavinplay; } }

		[SerializeField]
		private RectTransform_Image_Container m_Nothing;
		public RectTransform_Image_Container Nothing { get { return m_Nothing; } }

		[SerializeField]
		private RectTransform_Container m_Bonus;
		public RectTransform_Container Bonus { get { return m_Bonus; } }

		[SerializeField]
		private RectTransform_Container m_newNode;
		public RectTransform_Container newNode { get { return m_newNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_Rarity;
		public RectTransform_Image_Container Rarity { get { return m_Rarity; } }

		[SerializeField]
		private RectTransform_Container m_Ore;
		public RectTransform_Container Ore { get { return m_Ore; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_ore;
		public RectTransform_Text_Container txt_ore { get { return m_txt_ore; } }

	}

}
