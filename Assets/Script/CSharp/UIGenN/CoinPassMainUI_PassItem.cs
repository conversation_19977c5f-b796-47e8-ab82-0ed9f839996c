using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class CoinPassMainUI_PassItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_Select;
		public RectTransform_Image_Container Select { get { return m_Select; } }

		[SerializeField]
		private RectTransform_Text_Container m_num;
		public RectTransform_Text_Container num { get { return m_num; } }

		[SerializeField]
		private RectTransform_Container m_passitemlock;
		public RectTransform_Container passitemlock { get { return m_passitemlock; } }

		[SerializeField]
		private RectTransform_Image_Container m_lock_tips;
		public RectTransform_Image_Container lock_tips { get { return m_lock_tips; } }

		[SerializeField]
		private RectTransform_Text_Container m_lock_text;
		public RectTransform_Text_Container lock_text { get { return m_lock_text; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Btn;
		public RectTransform_Button_Image_Container Btn { get { return m_Btn; } }

	}

}
