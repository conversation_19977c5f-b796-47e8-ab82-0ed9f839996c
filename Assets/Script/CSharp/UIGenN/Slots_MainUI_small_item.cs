using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class Slots_MainUI_small_item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Animator_Container m_ani;
		public RectTransform_Animator_Container ani { get { return m_ani; } }

		[SerializeField]
		private RectTransform_Image_Container m_selected;
		public RectTransform_Image_Container selected { get { return m_selected; } }

		[SerializeField]
		private RectTransform_Container m_item;
		public RectTransform_Container item { get { return m_item; } }

		[SerializeField]
		private RectTransform_Container m_itemScale;
		public RectTransform_Container itemScale { get { return m_itemScale; } }

		[SerializeField]
		private RectTransform_Image_Container m_qualify;
		public RectTransform_Image_Container qualify { get { return m_qualify; } }

		[SerializeField]
		private RectTransform_Text_Container m_qualify_num;
		public RectTransform_Text_Container qualify_num { get { return m_qualify_num; } }

	}

}
