using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class Box_Tips : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_ClickTips;
		public RectTransform_Button_Image_Container ClickTips { get { return m_ClickTips; } }

		[SerializeField]
		private RectTransform_Container m_IconNode;
		public RectTransform_Container IconNode { get { return m_IconNode; } }

		[SerializeField]
		private RectTransform_TextMeshProUGUI_Container m_Item_Name;
		public RectTransform_TextMeshProUGUI_Container Item_Name { get { return m_Item_Name; } }

		[SerializeField]
		private RectTransform_Container m_numNode;
		public RectTransform_Container numNode { get { return m_numNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_Item_Num_01;
		public RectTransform_Text_Container Item_Num_01 { get { return m_Item_Num_01; } }

		[SerializeField]
		private RectTransform_Text_Container m_Item_Num_02;
		public RectTransform_Text_Container Item_Num_02 { get { return m_Item_Num_02; } }

		[SerializeField]
		private RectTransform_Text_Container m_Item_Tips;
		public RectTransform_Text_Container Item_Tips { get { return m_Item_Tips; } }

		[SerializeField]
		private RectTransform_Text_Container m_Box_Title;
		public RectTransform_Text_Container Box_Title { get { return m_Box_Title; } }

		[SerializeField]
		private RectTransform_Text_Container m_ChooseBox_Title;
		public RectTransform_Text_Container ChooseBox_Title { get { return m_ChooseBox_Title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Obtain_Button;
		public RectTransform_Button_Image_Container Obtain_Button { get { return m_Obtain_Button; } }

		[SerializeField]
		private RectTransform_Text_Container m_Item_Title;
		public RectTransform_Text_Container Item_Title { get { return m_Item_Title; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_Box_Tips_Item_Container m_Item;
		public RectTransform_Box_Tips_Item_Container Item { get { return m_Item; } }

		[SerializeField]
		private RectTransform_Container m_ObtainNode;
		public RectTransform_Container ObtainNode { get { return m_ObtainNode; } }

		[SerializeField]
		private RectTransform_InputField_Image_Container m_Input_ui;
		public RectTransform_InputField_Image_Container Input_ui { get { return m_Input_ui; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Add_Button;
		public RectTransform_Button_Image_Container Add_Button { get { return m_Add_Button; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Reduce_Button;
		public RectTransform_Button_Image_Container Reduce_Button { get { return m_Reduce_Button; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Max_Button;
		public RectTransform_Button_Image_Container Max_Button { get { return m_Max_Button; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Use_Button;
		public RectTransform_Button_Image_Container Use_Button { get { return m_Use_Button; } }

		[System.Serializable]
		public class RectTransform_Box_Tips_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private Box_Tips_Item m_Item;
			public Box_Tips_Item Item { get { return m_Item; } }

			[System.NonSerialized] public List<Box_Tips_Item> mCachedList = new List<Box_Tips_Item>();
			private Queue<Box_Tips_Item> mCachedInstances;
			public Box_Tips_Item GetInstance(bool ignoreSibling = false) {
				Box_Tips_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<Box_Tips_Item>(m_Item);
					instance.ItemInit();
				}
				Transform t0 = m_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(Box_Tips_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<Box_Tips_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<Box_Tips_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
