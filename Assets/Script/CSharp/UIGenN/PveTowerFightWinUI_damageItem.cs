using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PveTowerFightWinUI_damageItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_itemBanmaBG;
		public RectTransform_Image_Container itemBanmaBG { get { return m_itemBanmaBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_skillIcon;
		public RectTransform_Image_Container skillIcon { get { return m_skillIcon; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_damage_TD;
		public RectTransform_Text_Container txt_damage_TD { get { return m_txt_damage_TD; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_damage_DPS;
		public RectTransform_Text_Container txt_damage_DPS { get { return m_txt_damage_DPS; } }

	}

}
