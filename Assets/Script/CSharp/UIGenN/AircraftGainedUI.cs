using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class AircraftGainedUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_Model;
		public RectTransform_Container Model { get { return m_Model; } }

		[SerializeField]
		private RectTransform_Container m_fx_texiao;
		public RectTransform_Container fx_texiao { get { return m_fx_texiao; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_ModelImage;
		public RectTransform_RawImage_Container ModelImage { get { return m_ModelImage; } }

		[SerializeField]
		private RectTransform_Container m_NameNode;
		public RectTransform_Container NameNode { get { return m_NameNode; } }

		[SerializeField]
		private RectTransform_Container m_img_quaTitle1;
		public RectTransform_Container img_quaTitle1 { get { return m_img_quaTitle1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rank1;
		public RectTransform_Image_Container img_rank1 { get { return m_img_rank1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_nameBG;
		public RectTransform_Image_Container img_nameBG { get { return m_img_nameBG; } }

		[SerializeField]
		private Transform_Container m_fx;
		public Transform_Container fx { get { return m_fx; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_name_upgrade;
		public RectTransform_Text_Container txt_name_upgrade { get { return m_txt_name_upgrade; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_sortIcon1;
		public RectTransform_Image_Container img_sortIcon1 { get { return m_img_sortIcon1; } }

		[SerializeField]
		private RectTransform_Text_Container m_close;
		public RectTransform_Text_Container close { get { return m_close; } }

		[SerializeField]
		private RectTransform_Container m_CommonModelUI;
		public RectTransform_Container CommonModelUI { get { return m_CommonModelUI; } }

		[SerializeField]
		private Transform_Container m_ModelNodeRoot;
		public Transform_Container ModelNodeRoot { get { return m_ModelNodeRoot; } }

		[SerializeField]
		private Transform_Container m_WorldNode;
		public Transform_Container WorldNode { get { return m_WorldNode; } }

		[SerializeField]
		private Transform_Container m_ModelNode;
		public Transform_Container ModelNode { get { return m_ModelNode; } }

		[SerializeField]
		private Transform_Container m_CamerRootNode;
		public Transform_Container CamerRootNode { get { return m_CamerRootNode; } }

		[SerializeField]
		private Transform_Container m_RenderCameraNode;
		public Transform_Container RenderCameraNode { get { return m_RenderCameraNode; } }

		[SerializeField]
		private Transform_Animator_Container m_diybeijing;
		public Transform_Animator_Container diybeijing { get { return m_diybeijing; } }

	}

}
