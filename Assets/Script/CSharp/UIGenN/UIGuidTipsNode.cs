using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UIGuidTipsNode : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_BgNode;
		public RectTransform_Image_Container BgNode { get { return m_BgNode; } }

		[SerializeField]
		private RectTransform_TextMeshProUGUI_Container m_Text;
		public RectTransform_TextMeshProUGUI_Container Text { get { return m_Text; } }

		[SerializeField]
		private RectTransform_Container m_GuidHand;
		public RectTransform_Container GuidHand { get { return m_GuidHand; } }

		[SerializeField]
		private RectTransform_Container m_Click;
		public RectTransform_Container Click { get { return m_Click; } }

		[SerializeField]
		private RectTransform_Container m_Move;
		public RectTransform_Container Move { get { return m_Move; } }

		[SerializeField]
		private RectTransform_Container m_effect;
		public RectTransform_Container effect { get { return m_effect; } }

		[SerializeField]
		private RectTransform_Container m_effectN;
		public RectTransform_Container effectN { get { return m_effectN; } }

	}

}
