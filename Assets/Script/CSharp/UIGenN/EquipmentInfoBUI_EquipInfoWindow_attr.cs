using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class EquipmentInfoBUI_EquipInfoWindow_attr : LDBaseResUI {

		[SerializeField]
		private RectTransform_Text_Container m_attrName;
		public RectTransform_Text_Container attrName { get { return m_attrName; } }

		[SerializeField]
		private RectTransform_Text_Container m_attrNum;
		public RectTransform_Text_Container attrNum { get { return m_attrNum; } }

		[SerializeField]
		private RectTransform_Container m_arrow;
		public RectTransform_Container arrow { get { return m_arrow; } }

		[SerializeField]
		private RectTransform_Image_Container m_attrDown;
		public RectTransform_Image_Container attrDown { get { return m_attrDown; } }

		[SerializeField]
		private RectTransform_Image_Container m_attrUp;
		public RectTransform_Image_Container attrUp { get { return m_attrUp; } }

	}

}
