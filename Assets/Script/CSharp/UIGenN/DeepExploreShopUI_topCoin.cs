using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class DeepExploreShopUI_topCoin : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_topCoin_icon;
		public RectTransform_Image_Container topCoin_icon { get { return m_topCoin_icon; } }

		[SerializeField]
		private RectTransform_Text_Container m_CommonCoinNum;
		public RectTransform_Text_Container CommonCoinNum { get { return m_CommonCoinNum; } }

	}

}
