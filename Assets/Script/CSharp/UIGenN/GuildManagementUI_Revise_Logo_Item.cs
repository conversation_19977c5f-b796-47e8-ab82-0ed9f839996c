using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildManagementUI_Revise_Logo_Item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_icon;
		public RectTransform_Image_Container icon { get { return m_icon; } }

		[SerializeField]
		private RectTransform_Image_Container m_Select;
		public RectTransform_Image_Container Select { get { return m_Select; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Btn;
		public RectTransform_Button_Image_Container Btn { get { return m_Btn; } }

	}

}
