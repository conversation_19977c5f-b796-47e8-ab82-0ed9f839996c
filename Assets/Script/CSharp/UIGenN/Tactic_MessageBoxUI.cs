using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class Tactic_MessageBoxUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Text_Container m_Title;
		public RectTransform_Text_Container Title { get { return m_Title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Close_Btn;
		public RectTransform_Button_Image_Container Close_Btn { get { return m_Close_Btn; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_Tactic_MessageBoxUI_itemNode_Container m_itemNode;
		public RectTransform_Tactic_MessageBoxUI_itemNode_Container itemNode { get { return m_itemNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_Txt;
		public RectTransform_Text_Container Txt { get { return m_Txt; } }

		[SerializeField]
		private RectTransform_Container m_YIYongYou;
		public RectTransform_Container YIYongYou { get { return m_YIYongYou; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_Confirm;
		public RectTransform_Button_Image_Container Orange_btn_Confirm { get { return m_Orange_btn_Confirm; } }

		[SerializeField]
		private RectTransform_Container m_RedTips_Confirm;
		public RectTransform_Container RedTips_Confirm { get { return m_RedTips_Confirm; } }

		[SerializeField]
		private RectTransform_Container m_WeiYongYou;
		public RectTransform_Container WeiYongYou { get { return m_WeiYongYou; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Grey_btn_WeiYongYou;
		public RectTransform_Button_Image_Container Grey_btn_WeiYongYou { get { return m_Grey_btn_WeiYongYou; } }

		[System.Serializable]
		public class RectTransform_Tactic_MessageBoxUI_itemNode_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private Tactic_MessageBoxUI_itemNode m_itemNode;
			public Tactic_MessageBoxUI_itemNode itemNode { get { return m_itemNode; } }

			[System.NonSerialized] public List<Tactic_MessageBoxUI_itemNode> mCachedList = new List<Tactic_MessageBoxUI_itemNode>();
			private Queue<Tactic_MessageBoxUI_itemNode> mCachedInstances;
			public Tactic_MessageBoxUI_itemNode GetInstance(bool ignoreSibling = false) {
				Tactic_MessageBoxUI_itemNode instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<Tactic_MessageBoxUI_itemNode>(m_itemNode);
					instance.ItemInit();
				}
				Transform t0 = m_itemNode.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(Tactic_MessageBoxUI_itemNode instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<Tactic_MessageBoxUI_itemNode>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<Tactic_MessageBoxUI_itemNode> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
