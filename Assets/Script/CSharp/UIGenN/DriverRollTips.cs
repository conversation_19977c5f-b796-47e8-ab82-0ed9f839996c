using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class DriverRollTips : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_ScrollView;
		public RectTransform_Image_Container ScrollView { get { return m_ScrollView; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_DriverRollTips_list_Container m_list;
		public RectTransform_DriverRollTips_list_Container list { get { return m_list; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[System.Serializable]
		public class RectTransform_DriverRollTips_list_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private DriverRollTips_list m_list;
			public DriverRollTips_list list { get { return m_list; } }

			[System.NonSerialized] public List<DriverRollTips_list> mCachedList = new List<DriverRollTips_list>();
			private Queue<DriverRollTips_list> mCachedInstances;
			public DriverRollTips_list GetInstance(bool ignoreSibling = false) {
				DriverRollTips_list instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<DriverRollTips_list>(m_list);
					instance.ItemInit();
				}
				Transform t0 = m_list.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(DriverRollTips_list instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<DriverRollTips_list>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<DriverRollTips_list> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
