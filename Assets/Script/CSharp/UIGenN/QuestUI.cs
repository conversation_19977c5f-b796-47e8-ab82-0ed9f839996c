using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class QuestUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Container m_dailyQuestNode;
		public RectTransform_Container dailyQuestNode { get { return m_dailyQuestNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_DayRewardBG;
		public RectTransform_Image_Container DayRewardBG { get { return m_DayRewardBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_WeekRewardBG;
		public RectTransform_Image_Container WeekRewardBG { get { return m_WeekRewardBG; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_refreshInfo;
		public RectTransform_Text_Container txt_refreshInfo { get { return m_txt_refreshInfo; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_scorePro;
		public RectTransform_Image_Container img_scorePro { get { return m_img_scorePro; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_stepIcon_Day;
		public RectTransform_Image_Container img_stepIcon_Day { get { return m_img_stepIcon_Day; } }

		[SerializeField]
		private RectTransform_Container m_fx_baoguang_Day;
		public RectTransform_Container fx_baoguang_Day { get { return m_fx_baoguang_Day; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_stepIcon_Week;
		public RectTransform_Image_Container img_stepIcon_Week { get { return m_img_stepIcon_Week; } }

		[SerializeField]
		private RectTransform_Container m_fx_baoguang_Week;
		public RectTransform_Container fx_baoguang_Week { get { return m_fx_baoguang_Week; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_totalScore;
		public RectTransform_Text_Container txt_totalScore { get { return m_txt_totalScore; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_ok_node1;
		public RectTransform_Image_Container img_ok_node1 { get { return m_img_ok_node1; } }

		[SerializeField]
		private RectTransform_Container m_itemScaleNode1;
		public RectTransform_Container itemScaleNode1 { get { return m_itemScaleNode1; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_score1;
		public RectTransform_Text_Container txt_score1 { get { return m_txt_score1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_ok_node2;
		public RectTransform_Image_Container img_ok_node2 { get { return m_img_ok_node2; } }

		[SerializeField]
		private RectTransform_Container m_itemScaleNode2;
		public RectTransform_Container itemScaleNode2 { get { return m_itemScaleNode2; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_score2;
		public RectTransform_Text_Container txt_score2 { get { return m_txt_score2; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_ok_node3;
		public RectTransform_Image_Container img_ok_node3 { get { return m_img_ok_node3; } }

		[SerializeField]
		private RectTransform_Container m_itemScaleNode3;
		public RectTransform_Container itemScaleNode3 { get { return m_itemScaleNode3; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_score3;
		public RectTransform_Text_Container txt_score3 { get { return m_txt_score3; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_ok_node4;
		public RectTransform_Image_Container img_ok_node4 { get { return m_img_ok_node4; } }

		[SerializeField]
		private RectTransform_Container m_itemScaleNode4;
		public RectTransform_Container itemScaleNode4 { get { return m_itemScaleNode4; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_score4;
		public RectTransform_Text_Container txt_score4 { get { return m_txt_score4; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_ok_node5;
		public RectTransform_Image_Container img_ok_node5 { get { return m_img_ok_node5; } }

		[SerializeField]
		private RectTransform_Container m_itemScaleNode5;
		public RectTransform_Container itemScaleNode5 { get { return m_itemScaleNode5; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_score5;
		public RectTransform_Text_Container txt_score5 { get { return m_txt_score5; } }

		[SerializeField]
		private RectTransform_Image_Container m_questListView;
		public RectTransform_Image_Container questListView { get { return m_questListView; } }

		[SerializeField]
		private RectTransform_Container m_listContent;
		public RectTransform_Container listContent { get { return m_listContent; } }

		[SerializeField]
		private RectTransform_QuestUI_questItem_Container m_questItem;
		public RectTransform_QuestUI_questItem_Container questItem { get { return m_questItem; } }

		[SerializeField]
		private RectTransform_Container m_tabNode;
		public RectTransform_Container tabNode { get { return m_tabNode; } }

		[SerializeField]
		private RectTransform_Button_Container m_btn_dailyTab;
		public RectTransform_Button_Container btn_dailyTab { get { return m_btn_dailyTab; } }

		[SerializeField]
		private RectTransform_Image_Container m_dailySelect;
		public RectTransform_Image_Container dailySelect { get { return m_dailySelect; } }

		[SerializeField]
		private RectTransform_Image_Container m_dailyUnSelect;
		public RectTransform_Image_Container dailyUnSelect { get { return m_dailyUnSelect; } }

		[SerializeField]
		private RectTransform_Container m_DailyRedTips;
		public RectTransform_Container DailyRedTips { get { return m_DailyRedTips; } }

		[SerializeField]
		private RectTransform_Image_Container m_DailyRedNode;
		public RectTransform_Image_Container DailyRedNode { get { return m_DailyRedNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_DailyNum;
		public RectTransform_Text_Container DailyNum { get { return m_DailyNum; } }

		[SerializeField]
		private RectTransform_Button_Container m_btn_weeklyTab;
		public RectTransform_Button_Container btn_weeklyTab { get { return m_btn_weeklyTab; } }

		[SerializeField]
		private RectTransform_Image_Container m_weeklySelect;
		public RectTransform_Image_Container weeklySelect { get { return m_weeklySelect; } }

		[SerializeField]
		private RectTransform_Image_Container m_weeklyUnSelect;
		public RectTransform_Image_Container weeklyUnSelect { get { return m_weeklyUnSelect; } }

		[SerializeField]
		private RectTransform_Container m_WeeklyRedTips;
		public RectTransform_Container WeeklyRedTips { get { return m_WeeklyRedTips; } }

		[SerializeField]
		private RectTransform_Image_Container m_WeeklyRedNode;
		public RectTransform_Image_Container WeeklyRedNode { get { return m_WeeklyRedNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_WeeklyNum;
		public RectTransform_Text_Container WeeklyNum { get { return m_WeeklyNum; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_TestFly;
		public RectTransform_Button_Image_Container TestFly { get { return m_TestFly; } }

		[SerializeField]
		private RectTransform_Container m_Fly_Node;
		public RectTransform_Container Fly_Node { get { return m_Fly_Node; } }

		[SerializeField]
		private RectTransform_Container m_FlyPoint;
		public RectTransform_Container FlyPoint { get { return m_FlyPoint; } }

		[SerializeField]
		private RectTransform_Container m_Fly_Node_zidan;
		public RectTransform_Container Fly_Node_zidan { get { return m_Fly_Node_zidan; } }

		[SerializeField]
		private RectTransform_Container m_FlyPoint_zidan;
		public RectTransform_Container FlyPoint_zidan { get { return m_FlyPoint_zidan; } }

		[System.Serializable]
		public class RectTransform_QuestUI_questItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private QuestUI_questItem m_questItem;
			public QuestUI_questItem questItem { get { return m_questItem; } }

			[System.NonSerialized] public List<QuestUI_questItem> mCachedList = new List<QuestUI_questItem>();
			private Queue<QuestUI_questItem> mCachedInstances;
			public QuestUI_questItem GetInstance(bool ignoreSibling = false) {
				QuestUI_questItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<QuestUI_questItem>(m_questItem);
					instance.ItemInit();
				}
				Transform t0 = m_questItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(QuestUI_questItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<QuestUI_questItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<QuestUI_questItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
