using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ChallengeTaskUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_Part_Name_Quality;
		public RectTransform_Image_Container Part_Name_Quality { get { return m_Part_Name_Quality; } }

		[SerializeField]
		private RectTransform_Image_Container m_Part_Name_Element;
		public RectTransform_Image_Container Part_Name_Element { get { return m_Part_Name_Element; } }

		[SerializeField]
		private RectTransform_Text_Container m_Part_Name_Txt;
		public RectTransform_Text_Container Part_Name_Txt { get { return m_Part_Name_Txt; } }

		[SerializeField]
		private RectTransform_Text_Container m_Time_Txt;
		public RectTransform_Text_Container Time_Txt { get { return m_Time_Txt; } }

		[SerializeField]
		private RectTransform_Text_Container m_Progress_score;
		public RectTransform_Text_Container Progress_score { get { return m_Progress_score; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_scorePro;
		public RectTransform_Image_Container img_scorePro { get { return m_img_scorePro; } }

		[SerializeField]
		private RectTransform_Image_Container m_Progress_Item;
		public RectTransform_Image_Container Progress_Item { get { return m_Progress_Item; } }

		[SerializeField]
		private RectTransform_Container m_fx_baoguang;
		public RectTransform_Container fx_baoguang { get { return m_fx_baoguang; } }

		[SerializeField]
		private RectTransform_Container m_Last_Reward_Item;
		public RectTransform_Container Last_Reward_Item { get { return m_Last_Reward_Item; } }

		[SerializeField]
		private RectTransform_Container m_Offset;
		public RectTransform_Container Offset { get { return m_Offset; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality;
		public RectTransform_Image_Container Quality { get { return m_Quality; } }

		[SerializeField]
		private RectTransform_Container m_CanGet;
		public RectTransform_Container CanGet { get { return m_CanGet; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon01;
		public RectTransform_Image_Container Icon01 { get { return m_Icon01; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon02_JiaoBiao;
		public RectTransform_Image_Container Icon02_JiaoBiao { get { return m_Icon02_JiaoBiao; } }

		[SerializeField]
		private RectTransform_Image_Container m_Num_bg;
		public RectTransform_Image_Container Num_bg { get { return m_Num_bg; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

		[SerializeField]
		private RectTransform_Container m_Num_Quality;
		public RectTransform_Container Num_Quality { get { return m_Num_Quality; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality1;
		public RectTransform_Image_Container Quality1 { get { return m_Quality1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality2;
		public RectTransform_Image_Container Quality2 { get { return m_Quality2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality3;
		public RectTransform_Image_Container Quality3 { get { return m_Quality3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality4;
		public RectTransform_Image_Container Quality4 { get { return m_Quality4; } }

		[SerializeField]
		private RectTransform_Container m_Time;
		public RectTransform_Container Time { get { return m_Time; } }

		[SerializeField]
		private RectTransform_Text_Container m_Time_lbl;
		public RectTransform_Text_Container Time_lbl { get { return m_Time_lbl; } }

		[SerializeField]
		private RectTransform_Container m_Mask;
		public RectTransform_Container Mask { get { return m_Mask; } }

		[SerializeField]
		private RectTransform_Image_Container m_ClickBtn;
		public RectTransform_Image_Container ClickBtn { get { return m_ClickBtn; } }

		[SerializeField]
		private RectTransform_Container m_equip;
		public RectTransform_Container equip { get { return m_equip; } }

		[SerializeField]
		private RectTransform_Container m_locked;
		public RectTransform_Container locked { get { return m_locked; } }

		[SerializeField]
		private RectTransform_Image_Container m_Select;
		public RectTransform_Image_Container Select { get { return m_Select; } }

		[SerializeField]
		private RectTransform_Image_Container m_Red;
		public RectTransform_Image_Container Red { get { return m_Red; } }

		[SerializeField]
		private RectTransform_Image_Container m_Rarity;
		public RectTransform_Image_Container Rarity { get { return m_Rarity; } }

		[SerializeField]
		private RectTransform_Image_Container m_Xi;
		public RectTransform_Image_Container Xi { get { return m_Xi; } }

		[SerializeField]
		private RectTransform_Container m_RightLocked;
		public RectTransform_Container RightLocked { get { return m_RightLocked; } }

		[SerializeField]
		private RectTransform_Image_Container m_uavType;
		public RectTransform_Image_Container uavType { get { return m_uavType; } }

		[SerializeField]
		private RectTransform_Image_Container m_uavinplay;
		public RectTransform_Image_Container uavinplay { get { return m_uavinplay; } }

		[SerializeField]
		private RectTransform_Image_Container m_Nothing;
		public RectTransform_Image_Container Nothing { get { return m_Nothing; } }

		[SerializeField]
		private RectTransform_Text_Container m_scoreLast;
		public RectTransform_Text_Container scoreLast { get { return m_scoreLast; } }

		[SerializeField]
		private RectTransform_ChallengeTaskUI_Gift_Reward_Container m_Gift_Reward;
		public RectTransform_ChallengeTaskUI_Gift_Reward_Container Gift_Reward { get { return m_Gift_Reward; } }

		[SerializeField]
		private RectTransform_Container m_Gift_Reward01;
		public RectTransform_Container Gift_Reward01 { get { return m_Gift_Reward01; } }

		[SerializeField]
		private RectTransform_Container m_Gift_Reward02;
		public RectTransform_Container Gift_Reward02 { get { return m_Gift_Reward02; } }

		[SerializeField]
		private RectTransform_Container m_Item_Reward03;
		public RectTransform_Container Item_Reward03 { get { return m_Item_Reward03; } }

		[SerializeField]
		private RectTransform_Container m_Gift_Reward04;
		public RectTransform_Container Gift_Reward04 { get { return m_Gift_Reward04; } }

		[SerializeField]
		private RectTransform_Container m_Gift_Reward05;
		public RectTransform_Container Gift_Reward05 { get { return m_Gift_Reward05; } }

		[SerializeField]
		private RectTransform_Container m_Item_Reward06;
		public RectTransform_Container Item_Reward06 { get { return m_Item_Reward06; } }

		[SerializeField]
		private RectTransform_Container m_Gift_Reward07;
		public RectTransform_Container Gift_Reward07 { get { return m_Gift_Reward07; } }

		[SerializeField]
		private RectTransform_ChallengeTaskUI_TaskList_Container m_TaskList;
		public RectTransform_ChallengeTaskUI_TaskList_Container TaskList { get { return m_TaskList; } }

		[SerializeField]
		private RectTransform_ChallengeTaskUI_Date_Container m_Date;
		public RectTransform_ChallengeTaskUI_Date_Container Date { get { return m_Date; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Container m_Gift_Tips;
		public RectTransform_Container Gift_Tips { get { return m_Gift_Tips; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_TipsClose;
		public RectTransform_Button_Image_Container TipsClose { get { return m_TipsClose; } }

		[SerializeField]
		private RectTransform_Container m_Tips;
		public RectTransform_Container Tips { get { return m_Tips; } }

		[SerializeField]
		private RectTransform_ChallengeTaskUI_Gift_Tips_Item_Container m_Gift_Tips_Item;
		public RectTransform_ChallengeTaskUI_Gift_Tips_Item_Container Gift_Tips_Item { get { return m_Gift_Tips_Item; } }

		[SerializeField]
		private RectTransform_Container m_Fly_Node;
		public RectTransform_Container Fly_Node { get { return m_Fly_Node; } }

		[SerializeField]
		private RectTransform_Container m_FlyPoint;
		public RectTransform_Container FlyPoint { get { return m_FlyPoint; } }

		[System.Serializable]
		public class RectTransform_ChallengeTaskUI_Date_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ChallengeTaskUI_Date m_Date;
			public ChallengeTaskUI_Date Date { get { return m_Date; } }

			[System.NonSerialized] public List<ChallengeTaskUI_Date> mCachedList = new List<ChallengeTaskUI_Date>();
			private Queue<ChallengeTaskUI_Date> mCachedInstances;
			public ChallengeTaskUI_Date GetInstance(bool ignoreSibling = false) {
				ChallengeTaskUI_Date instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ChallengeTaskUI_Date>(m_Date);
					instance.ItemInit();
				}
				Transform t0 = m_Date.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ChallengeTaskUI_Date instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ChallengeTaskUI_Date>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ChallengeTaskUI_Date> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_ChallengeTaskUI_Gift_Reward_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ChallengeTaskUI_Gift_Reward m_Gift_Reward;
			public ChallengeTaskUI_Gift_Reward Gift_Reward { get { return m_Gift_Reward; } }

			[System.NonSerialized] public List<ChallengeTaskUI_Gift_Reward> mCachedList = new List<ChallengeTaskUI_Gift_Reward>();
			private Queue<ChallengeTaskUI_Gift_Reward> mCachedInstances;
			public ChallengeTaskUI_Gift_Reward GetInstance(bool ignoreSibling = false) {
				ChallengeTaskUI_Gift_Reward instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ChallengeTaskUI_Gift_Reward>(m_Gift_Reward);
					instance.ItemInit();
				}
				Transform t0 = m_Gift_Reward.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ChallengeTaskUI_Gift_Reward instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ChallengeTaskUI_Gift_Reward>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ChallengeTaskUI_Gift_Reward> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_ChallengeTaskUI_Gift_Tips_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ChallengeTaskUI_Gift_Tips_Item m_Gift_Tips_Item;
			public ChallengeTaskUI_Gift_Tips_Item Gift_Tips_Item { get { return m_Gift_Tips_Item; } }

			[System.NonSerialized] public List<ChallengeTaskUI_Gift_Tips_Item> mCachedList = new List<ChallengeTaskUI_Gift_Tips_Item>();
			private Queue<ChallengeTaskUI_Gift_Tips_Item> mCachedInstances;
			public ChallengeTaskUI_Gift_Tips_Item GetInstance(bool ignoreSibling = false) {
				ChallengeTaskUI_Gift_Tips_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ChallengeTaskUI_Gift_Tips_Item>(m_Gift_Tips_Item);
					instance.ItemInit();
				}
				Transform t0 = m_Gift_Tips_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ChallengeTaskUI_Gift_Tips_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ChallengeTaskUI_Gift_Tips_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ChallengeTaskUI_Gift_Tips_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_ChallengeTaskUI_TaskList_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ChallengeTaskUI_TaskList m_TaskList;
			public ChallengeTaskUI_TaskList TaskList { get { return m_TaskList; } }

			[System.NonSerialized] public List<ChallengeTaskUI_TaskList> mCachedList = new List<ChallengeTaskUI_TaskList>();
			private Queue<ChallengeTaskUI_TaskList> mCachedInstances;
			public ChallengeTaskUI_TaskList GetInstance(bool ignoreSibling = false) {
				ChallengeTaskUI_TaskList instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ChallengeTaskUI_TaskList>(m_TaskList);
					instance.ItemInit();
				}
				Transform t0 = m_TaskList.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ChallengeTaskUI_TaskList instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ChallengeTaskUI_TaskList>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ChallengeTaskUI_TaskList> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
