using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UImagnet : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_buyNode;
		public RectTransform_Container buyNode { get { return m_buyNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_buy;
		public RectTransform_Button_Image_Container buy { get { return m_buy; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_purchased;
		public RectTransform_Button_Image_Container purchased { get { return m_purchased; } }

		[SerializeField]
		private RectTransform_Container m_fx_texiao;
		public RectTransform_Container fx_texiao { get { return m_fx_texiao; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_cancel;
		public RectTransform_Button_Image_Container cancel { get { return m_cancel; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_use;
		public RectTransform_Button_Image_Container use { get { return m_use; } }

		[SerializeField]
		private RectTransform_Image_Container m_adImage;
		public RectTransform_Image_Container adImage { get { return m_adImage; } }

		[SerializeField]
		private RectTransform_Image_Container m_adFree;
		public RectTransform_Image_Container adFree { get { return m_adFree; } }

		[SerializeField]
		private RectTransform_Text_Container m_tips;
		public RectTransform_Text_Container tips { get { return m_tips; } }

		[SerializeField]
		private RectTransform_Text_Container m_num;
		public RectTransform_Text_Container num { get { return m_num; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

	}

}
