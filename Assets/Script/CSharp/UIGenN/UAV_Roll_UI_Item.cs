using UnityEngine;

namespace LD {

	public partial class UAV_Roll_UI_Item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_Item;
		public RectTransform_Container Item { get { return m_Item; } }

		[SerializeField]
		private RectTransform_Container m_fx_baozhuanhuan;
		public RectTransform_Container fx_baozhuanhuan { get { return m_fx_baozhuanhuan; } }

		[SerializeField]
		private RectTransform_Container m_fx_danbao;
		public RectTransform_Container fx_danbao { get { return m_fx_danbao; } }

	}

}
