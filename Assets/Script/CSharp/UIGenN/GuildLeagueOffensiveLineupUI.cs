using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildLeagueOffensiveLineupUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Text_Container m_My_Name;
		public RectTransform_Text_Container My_Name { get { return m_My_Name; } }

		[SerializeField]
		private RectTransform_Text_Container m_Other_Name;
		public RectTransform_Text_Container Other_Name { get { return m_Other_Name; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model1;
		public RectTransform_RawImage_Container Mecha_Model1 { get { return m_Mecha_Model1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Add1;
		public RectTransform_Image_Container Add1 { get { return m_Add1; } }

		[SerializeField]
		private RectTransform_Container m_PowerNode1;
		public RectTransform_Container PowerNode1 { get { return m_PowerNode1; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_BG_01;
		public RectTransform_Text_Container Power_BG_01 { get { return m_Power_BG_01; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Info1;
		public RectTransform_Container Mecha_Info1 { get { return m_Mecha_Info1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_DamageSystem1;
		public RectTransform_Image_Container Mecha_DamageSystem1 { get { return m_Mecha_DamageSystem1; } }

		[SerializeField]
		private RectTransform_Text_Container m_Mecha_Name1;
		public RectTransform_Text_Container Mecha_Name1 { get { return m_Mecha_Name1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Drop1;
		public RectTransform_Image_Container Drop1 { get { return m_Drop1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_RefitLockBtn1;
		public RectTransform_Button_Image_Container RefitLockBtn1 { get { return m_RefitLockBtn1; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model2;
		public RectTransform_RawImage_Container Mecha_Model2 { get { return m_Mecha_Model2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Add2;
		public RectTransform_Image_Container Add2 { get { return m_Add2; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Info2;
		public RectTransform_Container Mecha_Info2 { get { return m_Mecha_Info2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_DamageSystem2;
		public RectTransform_Image_Container Mecha_DamageSystem2 { get { return m_Mecha_DamageSystem2; } }

		[SerializeField]
		private RectTransform_Text_Container m_Mecha_Name2;
		public RectTransform_Text_Container Mecha_Name2 { get { return m_Mecha_Name2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Drop2;
		public RectTransform_Image_Container Drop2 { get { return m_Drop2; } }

		[SerializeField]
		private RectTransform_Container m_PowerNode2;
		public RectTransform_Container PowerNode2 { get { return m_PowerNode2; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_BG_02;
		public RectTransform_Text_Container Power_BG_02 { get { return m_Power_BG_02; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_RefitLockBtn2;
		public RectTransform_Button_Image_Container RefitLockBtn2 { get { return m_RefitLockBtn2; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model3;
		public RectTransform_RawImage_Container Mecha_Model3 { get { return m_Mecha_Model3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Add3;
		public RectTransform_Image_Container Add3 { get { return m_Add3; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Info3;
		public RectTransform_Container Mecha_Info3 { get { return m_Mecha_Info3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_DamageSystem3;
		public RectTransform_Image_Container Mecha_DamageSystem3 { get { return m_Mecha_DamageSystem3; } }

		[SerializeField]
		private RectTransform_Text_Container m_Mecha_Name3;
		public RectTransform_Text_Container Mecha_Name3 { get { return m_Mecha_Name3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Drop3;
		public RectTransform_Image_Container Drop3 { get { return m_Drop3; } }

		[SerializeField]
		private RectTransform_Container m_PowerNode3;
		public RectTransform_Container PowerNode3 { get { return m_PowerNode3; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_BG_03;
		public RectTransform_Text_Container Power_BG_03 { get { return m_Power_BG_03; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_RefitLockBtn3;
		public RectTransform_Button_Image_Container RefitLockBtn3 { get { return m_RefitLockBtn3; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model1_Other;
		public RectTransform_RawImage_Container Mecha_Model1_Other { get { return m_Mecha_Model1_Other; } }

		[SerializeField]
		private RectTransform_Image_Container m_Empty_Other_1;
		public RectTransform_Image_Container Empty_Other_1 { get { return m_Empty_Other_1; } }

		[SerializeField]
		private RectTransform_Container m_PowerNodeOther1;
		public RectTransform_Container PowerNodeOther1 { get { return m_PowerNodeOther1; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_BG_01_Other;
		public RectTransform_Text_Container Power_BG_01_Other { get { return m_Power_BG_01_Other; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model2_Other;
		public RectTransform_RawImage_Container Mecha_Model2_Other { get { return m_Mecha_Model2_Other; } }

		[SerializeField]
		private RectTransform_Image_Container m_Empty_Other_2;
		public RectTransform_Image_Container Empty_Other_2 { get { return m_Empty_Other_2; } }

		[SerializeField]
		private RectTransform_Container m_PowerNodeOther2;
		public RectTransform_Container PowerNodeOther2 { get { return m_PowerNodeOther2; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_BG_02_Other;
		public RectTransform_Text_Container Power_BG_02_Other { get { return m_Power_BG_02_Other; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model3_Other;
		public RectTransform_RawImage_Container Mecha_Model3_Other { get { return m_Mecha_Model3_Other; } }

		[SerializeField]
		private RectTransform_Image_Container m_Empty_Other_3;
		public RectTransform_Image_Container Empty_Other_3 { get { return m_Empty_Other_3; } }

		[SerializeField]
		private RectTransform_Container m_PowerNodeOther3;
		public RectTransform_Container PowerNodeOther3 { get { return m_PowerNodeOther3; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_BG_03_Other;
		public RectTransform_Text_Container Power_BG_03_Other { get { return m_Power_BG_03_Other; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_Battle;
		public RectTransform_Button_Image_Container Orange_btn_Battle { get { return m_Orange_btn_Battle; } }

		[SerializeField]
		private RectTransform_GuildLeagueOffensiveLineupUI_MechaItem_Container m_MechaItem;
		public RectTransform_GuildLeagueOffensiveLineupUI_MechaItem_Container MechaItem { get { return m_MechaItem; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Privilege_Right_Arrow;
		public RectTransform_Button_Image_Container Privilege_Right_Arrow { get { return m_Privilege_Right_Arrow; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Privilege_Left_Arrow;
		public RectTransform_Button_Image_Container Privilege_Left_Arrow { get { return m_Privilege_Left_Arrow; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mask;
		public RectTransform_Image_Container Mask { get { return m_Mask; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_DragMechaImg;
		public RectTransform_RawImage_Container DragMechaImg { get { return m_DragMechaImg; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Container m_DragCommonModelUI;
		public RectTransform_Container DragCommonModelUI { get { return m_DragCommonModelUI; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUI1;
		public RectTransform_Container SlotCommonModelUI1 { get { return m_SlotCommonModelUI1; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUI2;
		public RectTransform_Container SlotCommonModelUI2 { get { return m_SlotCommonModelUI2; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUI3;
		public RectTransform_Container SlotCommonModelUI3 { get { return m_SlotCommonModelUI3; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUIEnemy1;
		public RectTransform_Container SlotCommonModelUIEnemy1 { get { return m_SlotCommonModelUIEnemy1; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUIEnemy2;
		public RectTransform_Container SlotCommonModelUIEnemy2 { get { return m_SlotCommonModelUIEnemy2; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUIEnemy3;
		public RectTransform_Container SlotCommonModelUIEnemy3 { get { return m_SlotCommonModelUIEnemy3; } }

		[System.Serializable]
		public class RectTransform_GuildLeagueOffensiveLineupUI_MechaItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private GuildLeagueOffensiveLineupUI_MechaItem m_MechaItem;
			public GuildLeagueOffensiveLineupUI_MechaItem MechaItem { get { return m_MechaItem; } }

			[System.NonSerialized] public List<GuildLeagueOffensiveLineupUI_MechaItem> mCachedList = new List<GuildLeagueOffensiveLineupUI_MechaItem>();
			private Queue<GuildLeagueOffensiveLineupUI_MechaItem> mCachedInstances;
			public GuildLeagueOffensiveLineupUI_MechaItem GetInstance(bool ignoreSibling = false) {
				GuildLeagueOffensiveLineupUI_MechaItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<GuildLeagueOffensiveLineupUI_MechaItem>(m_MechaItem);
					instance.ItemInit();
				}
				Transform t0 = m_MechaItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(GuildLeagueOffensiveLineupUI_MechaItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<GuildLeagueOffensiveLineupUI_MechaItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<GuildLeagueOffensiveLineupUI_MechaItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
