using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class NMainUi_ActivityBtn : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_ActivityIcon;
		public RectTransform_Image_Container ActivityIcon { get { return m_ActivityIcon; } }

		[SerializeField]
		private RectTransform_Text_Container m_ActivityName;
		public RectTransform_Text_Container ActivityName { get { return m_ActivityName; } }

		[SerializeField]
		private RectTransform_Text_Container m_ActivityTime;
		public RectTransform_Text_Container ActivityTime { get { return m_ActivityTime; } }

		[SerializeField]
		private RectTransform_Container m_RedTips;
		public RectTransform_Container RedTips { get { return m_RedTips; } }

		[SerializeField]
		private RectTransform_Animator_Image_Container m_Arrow;
		public RectTransform_Animator_Image_Container Arrow { get { return m_Arrow; } }

		[SerializeField]
		private RectTransform_Image_Container m_RedNode;
		public RectTransform_Image_Container RedNode { get { return m_RedNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_NumNode;
		public RectTransform_Image_Container NumNode { get { return m_NumNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

	}

}
