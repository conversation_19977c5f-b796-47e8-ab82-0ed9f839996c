using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ArenaLineupUI : LDBaseUI {

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model1;
		public RectTransform_RawImage_Container Mecha_Model1 { get { return m_Mecha_Model1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Add1;
		public RectTransform_Image_Container Add1 { get { return m_Add1; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Info1;
		public RectTransform_Container Mecha_Info1 { get { return m_Mecha_Info1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_DamageSystem1;
		public RectTransform_Image_Container Mecha_DamageSystem1 { get { return m_Mecha_DamageSystem1; } }

		[SerializeField]
		private RectTransform_Text_Container m_Mecha_Name1;
		public RectTransform_Text_Container Mecha_Name1 { get { return m_Mecha_Name1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Drop1;
		public RectTransform_Image_Container Drop1 { get { return m_Drop1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_RefitLockBtn1;
		public RectTransform_Button_Image_Container RefitLockBtn1 { get { return m_RefitLockBtn1; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model2;
		public RectTransform_RawImage_Container Mecha_Model2 { get { return m_Mecha_Model2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Add2;
		public RectTransform_Image_Container Add2 { get { return m_Add2; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Info2;
		public RectTransform_Container Mecha_Info2 { get { return m_Mecha_Info2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_DamageSystem2;
		public RectTransform_Image_Container Mecha_DamageSystem2 { get { return m_Mecha_DamageSystem2; } }

		[SerializeField]
		private RectTransform_Text_Container m_Mecha_Name2;
		public RectTransform_Text_Container Mecha_Name2 { get { return m_Mecha_Name2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Drop2;
		public RectTransform_Image_Container Drop2 { get { return m_Drop2; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_RefitLockBtn2;
		public RectTransform_Button_Image_Container RefitLockBtn2 { get { return m_RefitLockBtn2; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_Model3;
		public RectTransform_RawImage_Container Mecha_Model3 { get { return m_Mecha_Model3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Add3;
		public RectTransform_Image_Container Add3 { get { return m_Add3; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Info3;
		public RectTransform_Container Mecha_Info3 { get { return m_Mecha_Info3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_DamageSystem3;
		public RectTransform_Image_Container Mecha_DamageSystem3 { get { return m_Mecha_DamageSystem3; } }

		[SerializeField]
		private RectTransform_Text_Container m_Mecha_Name3;
		public RectTransform_Text_Container Mecha_Name3 { get { return m_Mecha_Name3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Drop3;
		public RectTransform_Image_Container Drop3 { get { return m_Drop3; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_RefitLockBtn3;
		public RectTransform_Button_Image_Container RefitLockBtn3 { get { return m_RefitLockBtn3; } }

		[SerializeField]
		private RectTransform_ArenaLineupUI_MechaItem_Container m_MechaItem;
		public RectTransform_ArenaLineupUI_MechaItem_Container MechaItem { get { return m_MechaItem; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Privilege_Right_Arrow;
		public RectTransform_Button_Image_Container Privilege_Right_Arrow { get { return m_Privilege_Right_Arrow; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Privilege_Left_Arrow;
		public RectTransform_Button_Image_Container Privilege_Left_Arrow { get { return m_Privilege_Left_Arrow; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_DragMechaImg;
		public RectTransform_RawImage_Container DragMechaImg { get { return m_DragMechaImg; } }

		[SerializeField]
		private RectTransform_Container m_DragCommonModelUI;
		public RectTransform_Container DragCommonModelUI { get { return m_DragCommonModelUI; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUI1;
		public RectTransform_Container SlotCommonModelUI1 { get { return m_SlotCommonModelUI1; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUI2;
		public RectTransform_Container SlotCommonModelUI2 { get { return m_SlotCommonModelUI2; } }

		[SerializeField]
		private RectTransform_Container m_SlotCommonModelUI3;
		public RectTransform_Container SlotCommonModelUI3 { get { return m_SlotCommonModelUI3; } }

		[System.Serializable]
		public class RectTransform_ArenaLineupUI_MechaItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ArenaLineupUI_MechaItem m_MechaItem;
			public ArenaLineupUI_MechaItem MechaItem { get { return m_MechaItem; } }

			[System.NonSerialized] public List<ArenaLineupUI_MechaItem> mCachedList = new List<ArenaLineupUI_MechaItem>();
			private Queue<ArenaLineupUI_MechaItem> mCachedInstances;
			public ArenaLineupUI_MechaItem GetInstance(bool ignoreSibling = false) {
				ArenaLineupUI_MechaItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ArenaLineupUI_MechaItem>(m_MechaItem);
					instance.ItemInit();
				}
				Transform t0 = m_MechaItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ArenaLineupUI_MechaItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ArenaLineupUI_MechaItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ArenaLineupUI_MechaItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
