using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SeasonMissionTop5UI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_SeasonMissionTop5UI_TopItem_Container m_TopItem;
		public RectTransform_SeasonMissionTop5UI_TopItem_Container TopItem { get { return m_TopItem; } }

		[System.Serializable]
		public class RectTransform_SeasonMissionTop5UI_TopItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonMissionTop5UI_TopItem m_TopItem;
			public SeasonMissionTop5UI_TopItem TopItem { get { return m_TopItem; } }

			[System.NonSerialized] public List<SeasonMissionTop5UI_TopItem> mCachedList = new List<SeasonMissionTop5UI_TopItem>();
			private Queue<SeasonMissionTop5UI_TopItem> mCachedInstances;
			public SeasonMissionTop5UI_TopItem GetInstance(bool ignoreSibling = false) {
				SeasonMissionTop5UI_TopItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonMissionTop5UI_TopItem>(m_TopItem);
					instance.ItemInit();
				}
				Transform t0 = m_TopItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonMissionTop5UI_TopItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonMissionTop5UI_TopItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonMissionTop5UI_TopItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
