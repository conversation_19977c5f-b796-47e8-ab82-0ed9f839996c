using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class DeleteFriendUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_DeleteFriend_Txt;
		public RectTransform_Text_Container DeleteFriend_Txt { get { return m_DeleteFriend_Txt; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Green_btn_Cancel;
		public RectTransform_Button_Image_Container Green_btn_Cancel { get { return m_Green_btn_Cancel; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_Confirm;
		public RectTransform_Button_Image_Container Orange_btn_Confirm { get { return m_Orange_btn_Confirm; } }

		[SerializeField]
		private RectTransform_Toggle_Image_Container m_Day_Reminder_Button;
		public RectTransform_Toggle_Image_Container Day_Reminder_Button { get { return m_Day_Reminder_Button; } }

		[SerializeField]
		private RectTransform_Image_Container m_Day_Reminder_Select;
		public RectTransform_Image_Container Day_Reminder_Select { get { return m_Day_Reminder_Select; } }

	}

}
