using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PreviewCommonGiftUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Image_Container m_ActivityImage;
		public RectTransform_Image_Container ActivityImage { get { return m_ActivityImage; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_Title;
		public RectTransform_Text_Container txt_Title { get { return m_txt_Title; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_Help;
		public RectTransform_Text_Container txt_Help { get { return m_txt_Help; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_time;
		public RectTransform_Text_Container txt_time { get { return m_txt_time; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_OK;
		public RectTransform_Button_Image_Container Orange_btn_OK { get { return m_Orange_btn_OK; } }

	}

}
