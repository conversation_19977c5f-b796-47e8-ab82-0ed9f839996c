using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PartUpgradeUI_AttrItem_wpn : LDBaseResUI {

		[SerializeField]
		private RectTransform_Text_Container m_AttrName;
		public RectTransform_Text_Container AttrName { get { return m_AttrName; } }

		[SerializeField]
		private RectTransform_Text_Container m_AttrVal1;
		public RectTransform_Text_Container AttrVal1 { get { return m_AttrVal1; } }

		[SerializeField]
		private RectTransform_Container m_UpInfo;
		public RectTransform_Container UpInfo { get { return m_UpInfo; } }

		[SerializeField]
		private RectTransform_Text_Container m_AttrVal2;
		public RectTransform_Text_Container AttrVal2 { get { return m_AttrVal2; } }

	}

}
