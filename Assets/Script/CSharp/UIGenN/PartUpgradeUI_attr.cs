using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PartUpgradeUI_attr : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_img_attrBG;
		public RectTransform_Image_Container img_attrBG { get { return m_img_attrBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_attrIcon;
		public RectTransform_Image_Container img_attrIcon { get { return m_img_attrIcon; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_attrName;
		public RectTransform_Text_Container txt_attrName { get { return m_txt_attrName; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_oldAttr;
		public RectTransform_Text_Container txt_oldAttr { get { return m_txt_oldAttr; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_arrow;
		public RectTransform_Image_Container img_arrow { get { return m_img_arrow; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_nextAttr;
		public RectTransform_Text_Container txt_nextAttr { get { return m_txt_nextAttr; } }

	}

}
