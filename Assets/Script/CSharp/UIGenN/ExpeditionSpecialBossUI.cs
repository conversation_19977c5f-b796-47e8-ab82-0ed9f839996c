using System.Collections.Generic;
using UnityEngine;

namespace LD {

	public partial class ExpeditionSpecialBossUI : LDBaseUI {

		[SerializeField]
		private RectTransform_ExpeditionSpecialBossUI_aiattr_Container m_aiattr;
		public RectTransform_ExpeditionSpecialBossUI_aiattr_Container aiattr { get { return m_aiattr; } }

		[System.Serializable]
		public class RectTransform_ExpeditionSpecialBossUI_aiattr_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ExpeditionSpecialBossUI_aiattr m_aiattr;
			public ExpeditionSpecialBossUI_aiattr aiattr { get { return m_aiattr; } }

			[System.NonSerialized] public List<ExpeditionSpecialBossUI_aiattr> mCachedList = new List<ExpeditionSpecialBossUI_aiattr>();
			private Queue<ExpeditionSpecialBossUI_aiattr> mCachedInstances;
			public ExpeditionSpecialBossUI_aiattr GetInstance(bool ignoreSibling = false) {
				ExpeditionSpecialBossUI_aiattr instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ExpeditionSpecialBossUI_aiattr>(m_aiattr);
					instance.ItemInit();
				}
				Transform t0 = m_aiattr.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ExpeditionSpecialBossUI_aiattr instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ExpeditionSpecialBossUI_aiattr>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ExpeditionSpecialBossUI_aiattr> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
