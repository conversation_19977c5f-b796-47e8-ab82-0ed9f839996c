using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ExpeditionBaoWuUI_Option : LDBaseResUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_Option_Click;
		public RectTransform_Button_Image_Container Option_Click { get { return m_Option_Click; } }

		[SerializeField]
		private RectTransform_Image_Container m_Option_BG_Orange;
		public RectTransform_Image_Container Option_BG_Orange { get { return m_Option_BG_Orange; } }

		[SerializeField]
		private RectTransform_Image_Container m_Option_BG_Purple;
		public RectTransform_Image_Container Option_BG_Purple { get { return m_Option_BG_Purple; } }

		[SerializeField]
		private RectTransform_Image_Container m_Option_BG_Blue;
		public RectTransform_Image_Container Option_BG_Blue { get { return m_Option_BG_Blue; } }

		[SerializeField]
		private RectTransform_Image_Container m_Option_BG_Green;
		public RectTransform_Image_Container Option_BG_Green { get { return m_Option_BG_Green; } }

		[SerializeField]
		private RectTransform_Text_Container m_Option_ItemName;
		public RectTransform_Text_Container Option_ItemName { get { return m_Option_ItemName; } }

		[SerializeField]
		private RectTransform_Text_Container m_Option_ItemTips;
		public RectTransform_Text_Container Option_ItemTips { get { return m_Option_ItemTips; } }

		[SerializeField]
		private RectTransform_Container m_Item;
		public RectTransform_Container Item { get { return m_Item; } }

		[SerializeField]
		private RectTransform_Container m_Offset;
		public RectTransform_Container Offset { get { return m_Offset; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality;
		public RectTransform_Image_Container Quality { get { return m_Quality; } }

		[SerializeField]
		private RectTransform_Container m_CanGet;
		public RectTransform_Container CanGet { get { return m_CanGet; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon01;
		public RectTransform_Image_Container Icon01 { get { return m_Icon01; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon02_JiaoBiao;
		public RectTransform_Image_Container Icon02_JiaoBiao { get { return m_Icon02_JiaoBiao; } }

		[SerializeField]
		private RectTransform_Image_Container m_Num_bg;
		public RectTransform_Image_Container Num_bg { get { return m_Num_bg; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

		[SerializeField]
		private RectTransform_Container m_Num_Quality;
		public RectTransform_Container Num_Quality { get { return m_Num_Quality; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality1;
		public RectTransform_Image_Container Quality1 { get { return m_Quality1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality2;
		public RectTransform_Image_Container Quality2 { get { return m_Quality2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality3;
		public RectTransform_Image_Container Quality3 { get { return m_Quality3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality4;
		public RectTransform_Image_Container Quality4 { get { return m_Quality4; } }

		[SerializeField]
		private RectTransform_Container m_Time;
		public RectTransform_Container Time { get { return m_Time; } }

		[SerializeField]
		private RectTransform_Text_Container m_Time_lbl;
		public RectTransform_Text_Container Time_lbl { get { return m_Time_lbl; } }

		[SerializeField]
		private RectTransform_Container m_Mask;
		public RectTransform_Container Mask { get { return m_Mask; } }

		[SerializeField]
		private RectTransform_Image_Container m_ClickBtn;
		public RectTransform_Image_Container ClickBtn { get { return m_ClickBtn; } }

		[SerializeField]
		private RectTransform_Container m_equip;
		public RectTransform_Container equip { get { return m_equip; } }

		[SerializeField]
		private RectTransform_Container m_locked;
		public RectTransform_Container locked { get { return m_locked; } }

		[SerializeField]
		private RectTransform_Image_Container m_Select;
		public RectTransform_Image_Container Select { get { return m_Select; } }

		[SerializeField]
		private RectTransform_Image_Container m_Red;
		public RectTransform_Image_Container Red { get { return m_Red; } }

		[SerializeField]
		private RectTransform_Image_Container m_Rarity;
		public RectTransform_Image_Container Rarity { get { return m_Rarity; } }

		[SerializeField]
		private RectTransform_Image_Container m_Xi;
		public RectTransform_Image_Container Xi { get { return m_Xi; } }

		[SerializeField]
		private RectTransform_Container m_RightLocked;
		public RectTransform_Container RightLocked { get { return m_RightLocked; } }

		[SerializeField]
		private RectTransform_Container m_Front;
		public RectTransform_Container Front { get { return m_Front; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon;
		public RectTransform_Image_Container Icon { get { return m_Icon; } }

		[SerializeField]
		private RectTransform_Image_Container m_ItemSelect;
		public RectTransform_Image_Container ItemSelect { get { return m_ItemSelect; } }

		[SerializeField]
		private RectTransform_Container m_View;
		public RectTransform_Container View { get { return m_View; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_View_Click;
		public RectTransform_Button_Image_Container View_Click { get { return m_View_Click; } }

		[SerializeField]
		private RectTransform_Image_Container m_View_BG;
		public RectTransform_Image_Container View_BG { get { return m_View_BG; } }

	}

}
