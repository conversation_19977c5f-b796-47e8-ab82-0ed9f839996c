using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UIFightView_Mecha_op : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_Mecha_Common;
		public RectTransform_Container Mecha_Common { get { return m_Mecha_Common; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_Common_Icon;
		public RectTransform_Image_Container Mecha_Common_Icon { get { return m_Mecha_Common_Icon; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Battle;
		public RectTransform_Container Mecha_Battle { get { return m_Mecha_Battle; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_Battle_Icon;
		public RectTransform_Image_Container Mecha_Battle_Icon { get { return m_Mecha_Battle_Icon; } }

		[SerializeField]
		private RectTransform_Container m_Mecha_Die;
		public RectTransform_Container Mecha_Die { get { return m_Mecha_Die; } }

		[SerializeField]
		private RectTransform_Image_Container m_Mecha_Die_Icon;
		public RectTransform_Image_Container Mecha_Die_Icon { get { return m_Mecha_Die_Icon; } }

	}

}
