using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ShopUI_BuyGold_GoldItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_buy_btn;
		public RectTransform_Button_Image_Container buy_btn { get { return m_buy_btn; } }

		[SerializeField]
		private RectTransform_Container m_buy_limit_tips;
		public RectTransform_Container buy_limit_tips { get { return m_buy_limit_tips; } }

		[SerializeField]
		private RectTransform_Text_Container m_tips;
		public RectTransform_Text_Container tips { get { return m_tips; } }

		[SerializeField]
		private RectTransform_Text_Container m_Price;
		public RectTransform_Text_Container Price { get { return m_Price; } }

		[SerializeField]
		private RectTransform_Image_Container m_icon1;
		public RectTransform_Image_Container icon1 { get { return m_icon1; } }

		[SerializeField]
		private RectTransform_Text_Container m_gold_num1;
		public RectTransform_Text_Container gold_num1 { get { return m_gold_num1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_free_btn;
		public RectTransform_Button_Image_Container free_btn { get { return m_free_btn; } }

		[SerializeField]
		private RectTransform_Image_Container m_icon2;
		public RectTransform_Image_Container icon2 { get { return m_icon2; } }

		[SerializeField]
		private RectTransform_Text_Container m_gold_num2;
		public RectTransform_Text_Container gold_num2 { get { return m_gold_num2; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_ad_btn;
		public RectTransform_Button_Image_Container ad_btn { get { return m_ad_btn; } }

		[SerializeField]
		private RectTransform_Text_Container m_blue_lable;
		public RectTransform_Text_Container blue_lable { get { return m_blue_lable; } }

		[SerializeField]
		private RectTransform_Text_Container m_CD;
		public RectTransform_Text_Container CD { get { return m_CD; } }

		[SerializeField]
		private RectTransform_Container m_AdRedTips;
		public RectTransform_Container AdRedTips { get { return m_AdRedTips; } }

		[SerializeField]
		private RectTransform_Image_Container m_icon3;
		public RectTransform_Image_Container icon3 { get { return m_icon3; } }

		[SerializeField]
		private RectTransform_Text_Container m_gold_num3;
		public RectTransform_Text_Container gold_num3 { get { return m_gold_num3; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_sold_out_btn;
		public RectTransform_Button_Image_Container sold_out_btn { get { return m_sold_out_btn; } }

		[SerializeField]
		private RectTransform_Image_Container m_icon4;
		public RectTransform_Image_Container icon4 { get { return m_icon4; } }

		[SerializeField]
		private RectTransform_Text_Container m_gold_num4;
		public RectTransform_Text_Container gold_num4 { get { return m_gold_num4; } }

	}

}
