using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class NMainUi_PassRewardItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_Reward;
		public RectTransform_Container Reward { get { return m_Reward; } }

		[SerializeField]
		private RectTransform_Text_Container m_RewardTxt;
		public RectTransform_Text_Container RewardTxt { get { return m_RewardTxt; } }

		[SerializeField]
		private RectTransform_Container m_CanReceive;
		public RectTransform_Container CanReceive { get { return m_CanReceive; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_ReceiveBtn;
		public RectTransform_Button_Image_Container ReceiveBtn { get { return m_ReceiveBtn; } }

		[SerializeField]
		private RectTransform_Container m_Received;
		public RectTransform_Container Received { get { return m_Received; } }

	}

}
