using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SurvivorUpgradeUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_attrTips;
		public RectTransform_Button_Image_Container btn_attrTips { get { return m_btn_attrTips; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_quaBG;
		public RectTransform_Image_Container img_quaBG { get { return m_img_quaBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_icon;
		public RectTransform_Image_Container img_icon { get { return m_img_icon; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_name;
		public RectTransform_Text_Container txt_name { get { return m_txt_name; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_level;
		public RectTransform_Text_Container txt_level { get { return m_txt_level; } }

		[SerializeField]
		private RectTransform_Text_Container m_AttrName;
		public RectTransform_Text_Container AttrName { get { return m_AttrName; } }

		[SerializeField]
		private RectTransform_Text_Container m_AttrVal;
		public RectTransform_Text_Container AttrVal { get { return m_AttrVal; } }

		[SerializeField]
		private RectTransform_Container m_attrNode;
		public RectTransform_Container attrNode { get { return m_attrNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_Node__pnadvanced;
		public RectTransform_Image_Container Node__pnadvanced { get { return m_Node__pnadvanced; } }

		[SerializeField]
		private RectTransform_Image_Container m_ShowNode;
		public RectTransform_Image_Container ShowNode { get { return m_ShowNode; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_SurvivorUpgradeUI_entry_bg_Container m_entry_bg;
		public RectTransform_SurvivorUpgradeUI_entry_bg_Container entry_bg { get { return m_entry_bg; } }

		[SerializeField]
		private RectTransform_Container m_Node__cost;
		public RectTransform_Container Node__cost { get { return m_Node__cost; } }

		[SerializeField]
		private RectTransform_Container m_ResNode_pnadvanced;
		public RectTransform_Container ResNode_pnadvanced { get { return m_ResNode_pnadvanced; } }

		[SerializeField]
		private RectTransform_SurvivorUpgradeUI_UpgradeRes_pnadvanced_Container m_UpgradeRes_pnadvanced;
		public RectTransform_SurvivorUpgradeUI_UpgradeRes_pnadvanced_Container UpgradeRes_pnadvanced { get { return m_UpgradeRes_pnadvanced; } }

		[SerializeField]
		private RectTransform_Container m_UpgradeBtnNode_m_pnadvanced;
		public RectTransform_Container UpgradeBtnNode_m_pnadvanced { get { return m_UpgradeBtnNode_m_pnadvanced; } }

		[SerializeField]
		private Transform_Container m_button_btn_m_pnadvanced;
		public Transform_Container button_btn_m_pnadvanced { get { return m_button_btn_m_pnadvanced; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_m_pnadvanced;
		public RectTransform_Button_Image_Container Orange_btn_m_pnadvanced { get { return m_Orange_btn_m_pnadvanced; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_rankup_m_pnadvanced;
		public RectTransform_Text_Container txt_rankup_m_pnadvanced { get { return m_txt_rankup_m_pnadvanced; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_ok_m_pnadvanced;
		public RectTransform_Text_Container txt_ok_m_pnadvanced { get { return m_txt_ok_m_pnadvanced; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Green_btn_m_pnadvanced;
		public RectTransform_Button_Image_Container Green_btn_m_pnadvanced { get { return m_Green_btn_m_pnadvanced; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_QualityMaxBtn_m_pnadvanced;
		public RectTransform_Button_Image_Container QualityMaxBtn_m_pnadvanced { get { return m_QualityMaxBtn_m_pnadvanced; } }

		[SerializeField]
		private RectTransform_Container m_max_Node;
		public RectTransform_Container max_Node { get { return m_max_Node; } }

		[SerializeField]
		private RectTransform_Container m_attrDesc_tips;
		public RectTransform_Container attrDesc_tips { get { return m_attrDesc_tips; } }

		[System.Serializable]
		public class RectTransform_SurvivorUpgradeUI_entry_bg_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SurvivorUpgradeUI_entry_bg m_entry_bg;
			public SurvivorUpgradeUI_entry_bg entry_bg { get { return m_entry_bg; } }

			[System.NonSerialized] public List<SurvivorUpgradeUI_entry_bg> mCachedList = new List<SurvivorUpgradeUI_entry_bg>();
			private Queue<SurvivorUpgradeUI_entry_bg> mCachedInstances;
			public SurvivorUpgradeUI_entry_bg GetInstance(bool ignoreSibling = false) {
				SurvivorUpgradeUI_entry_bg instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SurvivorUpgradeUI_entry_bg>(m_entry_bg);
					instance.ItemInit();
				}
				Transform t0 = m_entry_bg.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SurvivorUpgradeUI_entry_bg instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SurvivorUpgradeUI_entry_bg>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SurvivorUpgradeUI_entry_bg> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_SurvivorUpgradeUI_UpgradeRes_pnadvanced_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SurvivorUpgradeUI_UpgradeRes_pnadvanced m_UpgradeRes_pnadvanced;
			public SurvivorUpgradeUI_UpgradeRes_pnadvanced UpgradeRes_pnadvanced { get { return m_UpgradeRes_pnadvanced; } }

			[System.NonSerialized] public List<SurvivorUpgradeUI_UpgradeRes_pnadvanced> mCachedList = new List<SurvivorUpgradeUI_UpgradeRes_pnadvanced>();
			private Queue<SurvivorUpgradeUI_UpgradeRes_pnadvanced> mCachedInstances;
			public SurvivorUpgradeUI_UpgradeRes_pnadvanced GetInstance(bool ignoreSibling = false) {
				SurvivorUpgradeUI_UpgradeRes_pnadvanced instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SurvivorUpgradeUI_UpgradeRes_pnadvanced>(m_UpgradeRes_pnadvanced);
					instance.ItemInit();
				}
				Transform t0 = m_UpgradeRes_pnadvanced.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SurvivorUpgradeUI_UpgradeRes_pnadvanced instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SurvivorUpgradeUI_UpgradeRes_pnadvanced>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SurvivorUpgradeUI_UpgradeRes_pnadvanced> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
