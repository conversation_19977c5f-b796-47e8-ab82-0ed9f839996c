using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class EquipmentCreationUI_item_weapon : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_EmptyEquipNode;
		public RectTransform_Image_Container EmptyEquipNode { get { return m_EmptyEquipNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_EmptyEquip;
		public RectTransform_Image_Container EmptyEquip { get { return m_EmptyEquip; } }

		[SerializeField]
		private RectTransform_Container m_EquipNode;
		public RectTransform_Container EquipNode { get { return m_EquipNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Btn;
		public RectTransform_Button_Image_Container Btn { get { return m_Btn; } }

		[SerializeField]
		private RectTransform_Container m_RedTips;
		public RectTransform_Container RedTips { get { return m_RedTips; } }

	}

}
