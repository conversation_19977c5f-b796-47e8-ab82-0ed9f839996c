using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GiftLogUI_listItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Text_Container m_treasure_name;
		public RectTransform_Text_Container treasure_name { get { return m_treasure_name; } }

		[SerializeField]
		private RectTransform_GiftLogUI_listItem_item_Container m_item;
		public RectTransform_GiftLogUI_listItem_item_Container item { get { return m_item; } }

		[System.Serializable]
		public class RectTransform_GiftLogUI_listItem_item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private GiftLogUI_listItem_item m_item;
			public GiftLogUI_listItem_item item { get { return m_item; } }

			[System.NonSerialized] public List<GiftLogUI_listItem_item> mCachedList = new List<GiftLogUI_listItem_item>();
			private Queue<GiftLogUI_listItem_item> mCachedInstances;
			public GiftLogUI_listItem_item GetInstance(bool ignoreSibling = false) {
				GiftLogUI_listItem_item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<GiftLogUI_listItem_item>(m_item);
					instance.ItemInit();
				}
				Transform t0 = m_item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(GiftLogUI_listItem_item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<GiftLogUI_listItem_item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<GiftLogUI_listItem_item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
