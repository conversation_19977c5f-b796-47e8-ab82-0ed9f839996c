using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class DiyPartListUI_lineNode_part : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_rootnode;
		public RectTransform_Container rootnode { get { return m_rootnode; } }

		[SerializeField]
		private RectTransform_Button_Container m_img_quaBg;
		public RectTransform_Button_Container img_quaBg { get { return m_img_quaBg; } }

		[SerializeField]
		private RectTransform_Container m_fx;
		public RectTransform_Container fx { get { return m_fx; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_icon;
		public RectTransform_Image_Container img_icon { get { return m_img_icon; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankIcon;
		public RectTransform_Image_Container img_rankIcon { get { return m_img_rankIcon; } }

		[SerializeField]
		private RectTransform_Container m_Num_Quality;
		public RectTransform_Container Num_Quality { get { return m_Num_Quality; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality1;
		public RectTransform_Image_Container Quality1 { get { return m_Quality1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality2;
		public RectTransform_Image_Container Quality2 { get { return m_Quality2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality3;
		public RectTransform_Image_Container Quality3 { get { return m_Quality3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality4;
		public RectTransform_Image_Container Quality4 { get { return m_Quality4; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality5;
		public RectTransform_Image_Container Quality5 { get { return m_Quality5; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_sortIcon;
		public RectTransform_Image_Container img_sortIcon { get { return m_img_sortIcon; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_partLvl;
		public RectTransform_Text_Container txt_partLvl { get { return m_txt_partLvl; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_equip;
		public RectTransform_Image_Container img_equip { get { return m_img_equip; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_lockBG;
		public RectTransform_Image_Container img_lockBG { get { return m_img_lockBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_lock;
		public RectTransform_Image_Container img_lock { get { return m_img_lock; } }

		[SerializeField]
		private RectTransform_Animator_Image_Container m_unLock_1;
		public RectTransform_Animator_Image_Container unLock_1 { get { return m_unLock_1; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_unlockTips;
		public RectTransform_Text_Container txt_unlockTips { get { return m_txt_unlockTips; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_proBG;
		public RectTransform_Image_Container img_proBG { get { return m_img_proBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_progress;
		public RectTransform_Image_Container progress { get { return m_progress; } }

		[SerializeField]
		private RectTransform_Image_Container m_progress_full;
		public RectTransform_Image_Container progress_full { get { return m_progress_full; } }

		[SerializeField]
		private RectTransform_Image_Container m_chipIcon;
		public RectTransform_Image_Container chipIcon { get { return m_chipIcon; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_chipNum;
		public RectTransform_Text_Container txt_chipNum { get { return m_txt_chipNum; } }

		[SerializeField]
		private RectTransform_Container m_RedTips;
		public RectTransform_Container RedTips { get { return m_RedTips; } }

		[SerializeField]
		private RectTransform_Image_Container m_NumNode;
		public RectTransform_Image_Container NumNode { get { return m_NumNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

	}

}
