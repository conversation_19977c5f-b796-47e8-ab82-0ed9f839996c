using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SeasonUI_Tab : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_Check_Btn;
		public RectTransform_Image_Container Check_Btn { get { return m_Check_Btn; } }

		[SerializeField]
		private RectTransform_Image_Container m_Check_Dec;
		public RectTransform_Image_Container Check_Dec { get { return m_Check_Dec; } }

		[SerializeField]
		private RectTransform_Text_Container m_Check_Btn_txt;
		public RectTransform_Text_Container Check_Btn_txt { get { return m_Check_Btn_txt; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Uncheck_Btn;
		public RectTransform_Button_Image_Container Uncheck_Btn { get { return m_Uncheck_Btn; } }

		[SerializeField]
		private RectTransform_Image_Container m_Uncheck_Dec;
		public RectTransform_Image_Container Uncheck_Dec { get { return m_Uncheck_Dec; } }

		[SerializeField]
		private RectTransform_Text_Container m_Uncheck_Btn_txt;
		public RectTransform_Text_Container Uncheck_Btn_txt { get { return m_Uncheck_Btn_txt; } }

		[SerializeField]
		private RectTransform_Container m_RedTips;
		public RectTransform_Container RedTips { get { return m_RedTips; } }

	}

}
