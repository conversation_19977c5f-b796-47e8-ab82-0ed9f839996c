using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class Item_Tips_Obtain_Item01 : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_Obtain_BG_01;
		public RectTransform_Image_Container Obtain_BG_01 { get { return m_Obtain_BG_01; } }

		[SerializeField]
		private RectTransform_Image_Container m_Obtain_BG_02;
		public RectTransform_Image_Container Obtain_BG_02 { get { return m_Obtain_BG_02; } }

		[SerializeField]
		private RectTransform_Image_Container m_Obtain_Dec;
		public RectTransform_Image_Container Obtain_Dec { get { return m_Obtain_Dec; } }

		[SerializeField]
		private RectTransform_Text_Container m_Obtain_Name;
		public RectTransform_Text_Container Obtain_Name { get { return m_Obtain_Name; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Obtain_Button;
		public RectTransform_Button_Image_Container Obtain_Button { get { return m_Obtain_Button; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Obtain_Button_Grey;
		public RectTransform_Button_Image_Container Obtain_Button_Grey { get { return m_Obtain_Button_Grey; } }

	}

}
