using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildBossHurtRankUI_PlayerRank : LDBaseResUI {

		[SerializeField]
		private RectTransform_Text_Container m_Time_Txt;
		public RectTransform_Text_Container Time_Txt { get { return m_Time_Txt; } }

		[SerializeField]
		private RectTransform_GuildBossHurtRankUI_PlayerRank_PlayerRankItem_Container m_PlayerRankItem;
		public RectTransform_GuildBossHurtRankUI_PlayerRank_PlayerRankItem_Container PlayerRankItem { get { return m_PlayerRankItem; } }

		[SerializeField]
		private RectTransform_Container m_My_Information;
		public RectTransform_Container My_Information { get { return m_My_Information; } }

		[SerializeField]
		private RectTransform_Container m_No_Rank;
		public RectTransform_Container No_Rank { get { return m_No_Rank; } }

		[SerializeField]
		private RectTransform_Container m_myrank;
		public RectTransform_Container myrank { get { return m_myrank; } }

		[SerializeField]
		private RectTransform_Text_Container m_ranknum;
		public RectTransform_Text_Container ranknum { get { return m_ranknum; } }

		[SerializeField]
		private RectTransform_Text_Container m_norank;
		public RectTransform_Text_Container norank { get { return m_norank; } }

		[SerializeField]
		private RectTransform_Text_Container m_MyHurt_Num;
		public RectTransform_Text_Container MyHurt_Num { get { return m_MyHurt_Num; } }

		[System.Serializable]
		public class RectTransform_GuildBossHurtRankUI_PlayerRank_PlayerRankItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private GuildBossHurtRankUI_PlayerRank_PlayerRankItem m_PlayerRankItem;
			public GuildBossHurtRankUI_PlayerRank_PlayerRankItem PlayerRankItem { get { return m_PlayerRankItem; } }

			[System.NonSerialized] public List<GuildBossHurtRankUI_PlayerRank_PlayerRankItem> mCachedList = new List<GuildBossHurtRankUI_PlayerRank_PlayerRankItem>();
			private Queue<GuildBossHurtRankUI_PlayerRank_PlayerRankItem> mCachedInstances;
			public GuildBossHurtRankUI_PlayerRank_PlayerRankItem GetInstance(bool ignoreSibling = false) {
				GuildBossHurtRankUI_PlayerRank_PlayerRankItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<GuildBossHurtRankUI_PlayerRank_PlayerRankItem>(m_PlayerRankItem);
					instance.ItemInit();
				}
				Transform t0 = m_PlayerRankItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(GuildBossHurtRankUI_PlayerRank_PlayerRankItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<GuildBossHurtRankUI_PlayerRank_PlayerRankItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<GuildBossHurtRankUI_PlayerRank_PlayerRankItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
