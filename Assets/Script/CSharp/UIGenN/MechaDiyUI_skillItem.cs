using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class MechaDiyUI_skillItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Text_Container m_txt_skillName;
		public RectTransform_Text_Container txt_skillName { get { return m_txt_skillName; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_skillIcon1;
		public RectTransform_Image_Container img_skillIcon1 { get { return m_img_skillIcon1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_skillIcon2;
		public RectTransform_Image_Container img_skillIcon2 { get { return m_img_skillIcon2; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_skillNameNew;
		public RectTransform_Text_Container txt_skillNameNew { get { return m_txt_skillNameNew; } }

	}

}
