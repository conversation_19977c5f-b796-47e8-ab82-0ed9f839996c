using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ExpeditionRankUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_ExpeditionRankUI_RankItem_Container m_RankItem;
		public RectTransform_ExpeditionRankUI_RankItem_Container RankItem { get { return m_RankItem; } }

		[SerializeField]
		private RectTransform_Container m_My_Information;
		public RectTransform_Container My_Information { get { return m_My_Information; } }

		[SerializeField]
		private RectTransform_Container m_No_Rank;
		public RectTransform_Container No_Rank { get { return m_No_Rank; } }

		[SerializeField]
		private RectTransform_Container m_myrank;
		public RectTransform_Container myrank { get { return m_myrank; } }

		[SerializeField]
		private RectTransform_Text_Container m_ranknum;
		public RectTransform_Text_Container ranknum { get { return m_ranknum; } }

		[SerializeField]
		private RectTransform_Text_Container m_norank;
		public RectTransform_Text_Container norank { get { return m_norank; } }

		[SerializeField]
		private RectTransform_ExpeditionRankUI_MyItem_Container m_MyItem;
		public RectTransform_ExpeditionRankUI_MyItem_Container MyItem { get { return m_MyItem; } }

		[System.Serializable]
		public class RectTransform_ExpeditionRankUI_MyItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ExpeditionRankUI_MyItem m_MyItem;
			public ExpeditionRankUI_MyItem MyItem { get { return m_MyItem; } }

			[System.NonSerialized] public List<ExpeditionRankUI_MyItem> mCachedList = new List<ExpeditionRankUI_MyItem>();
			private Queue<ExpeditionRankUI_MyItem> mCachedInstances;
			public ExpeditionRankUI_MyItem GetInstance(bool ignoreSibling = false) {
				ExpeditionRankUI_MyItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ExpeditionRankUI_MyItem>(m_MyItem);
					instance.ItemInit();
				}
				Transform t0 = m_MyItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ExpeditionRankUI_MyItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ExpeditionRankUI_MyItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ExpeditionRankUI_MyItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_ExpeditionRankUI_RankItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ExpeditionRankUI_RankItem m_RankItem;
			public ExpeditionRankUI_RankItem RankItem { get { return m_RankItem; } }

			[System.NonSerialized] public List<ExpeditionRankUI_RankItem> mCachedList = new List<ExpeditionRankUI_RankItem>();
			private Queue<ExpeditionRankUI_RankItem> mCachedInstances;
			public ExpeditionRankUI_RankItem GetInstance(bool ignoreSibling = false) {
				ExpeditionRankUI_RankItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ExpeditionRankUI_RankItem>(m_RankItem);
					instance.ItemInit();
				}
				Transform t0 = m_RankItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ExpeditionRankUI_RankItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ExpeditionRankUI_RankItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ExpeditionRankUI_RankItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
