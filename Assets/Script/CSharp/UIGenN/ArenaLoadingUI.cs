using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ArenaLoadingUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_RoleHeadMySelf;
		public RectTransform_Container RoleHeadMySelf { get { return m_RoleHeadMySelf; } }

		[SerializeField]
		private RectTransform_Text_Container m_Level_Txt_Self;
		public RectTransform_Text_Container Level_Txt_Self { get { return m_Level_Txt_Self; } }

		[SerializeField]
		private RectTransform_Text_Container m_PlayerName_Self;
		public RectTransform_Text_Container PlayerName_Self { get { return m_PlayerName_Self; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_Self;
		public RectTransform_Text_Container Power_Self { get { return m_Power_Self; } }

		[SerializeField]
		private RectTransform_Container m_RoleHeadOthers;
		public RectTransform_Container RoleHeadOthers { get { return m_RoleHeadOthers; } }

		[SerializeField]
		private RectTransform_Text_Container m_Level_Txt_Other;
		public RectTransform_Text_Container Level_Txt_Other { get { return m_Level_Txt_Other; } }

		[SerializeField]
		private RectTransform_Text_Container m_PlayerName_Other;
		public RectTransform_Text_Container PlayerName_Other { get { return m_PlayerName_Other; } }

		[SerializeField]
		private RectTransform_Text_Container m_Power_Other;
		public RectTransform_Text_Container Power_Other { get { return m_Power_Other; } }

	}

}
