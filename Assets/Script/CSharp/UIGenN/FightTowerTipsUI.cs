using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class FightTowerTipsUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_lable01;
		public RectTransform_Text_Container lable01 { get { return m_lable01; } }

		[SerializeField]
		private RectTransform_Text_Container m_lable02;
		public RectTransform_Text_Container lable02 { get { return m_lable02; } }

		[SerializeField]
		private RectTransform_Text_Container m_lable03;
		public RectTransform_Text_Container lable03 { get { return m_lable03; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Day_Reminder_Button;
		public RectTransform_Button_Image_Container Day_Reminder_Button { get { return m_Day_Reminder_Button; } }

		[SerializeField]
		private RectTransform_Image_Container m_Day_Reminder_Select;
		public RectTransform_Image_Container Day_Reminder_Select { get { return m_Day_Reminder_Select; } }

		[SerializeField]
		private RectTransform_Container m_Button_Last;
		public RectTransform_Container Button_Last { get { return m_Button_Last; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Battle_Last_Btn;
		public RectTransform_Button_Image_Container Battle_Last_Btn { get { return m_Battle_Last_Btn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Battle_Button;
		public RectTransform_Button_Image_Container Battle_Button { get { return m_Battle_Button; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Cancel_Button;
		public RectTransform_Button_Image_Container Cancel_Button { get { return m_Cancel_Button; } }

	}

}
