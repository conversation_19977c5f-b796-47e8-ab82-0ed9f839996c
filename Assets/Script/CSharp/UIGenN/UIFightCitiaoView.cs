using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UIFightCitiaoView : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_CommonModelUI;
		public RectTransform_Container CommonModelUI { get { return m_CommonModelUI; } }

		[SerializeField]
		private Transform_Container m_ModelNodeRoot;
		public Transform_Container ModelNodeRoot { get { return m_ModelNodeRoot; } }

		[SerializeField]
		private Transform_Container m_WorldNode;
		public Transform_Container WorldNode { get { return m_WorldNode; } }

		[SerializeField]
		private Transform_Container m_ModelNode;
		public Transform_Container ModelNode { get { return m_ModelNode; } }

		[SerializeField]
		private Transform_Container m_CamerRootNode;
		public Transform_Container CamerRootNode { get { return m_CamerRootNode; } }

		[SerializeField]
		private Transform_Container m_RenderCameraNode;
		public Transform_Container RenderCameraNode { get { return m_RenderCameraNode; } }

		[SerializeField]
		private Transform_Animator_Container m_diybeijing;
		public Transform_Animator_Container diybeijing { get { return m_diybeijing; } }

		[SerializeField]
		private RectTransform_Container m_root;
		public RectTransform_Container root { get { return m_root; } }

		[SerializeField]
		private RectTransform_Image_Container m_BG;
		public RectTransform_Image_Container BG { get { return m_BG; } }

		[SerializeField]
		private RectTransform_TextMeshProUGUI_Container m_txt_xi_des;
		public RectTransform_TextMeshProUGUI_Container txt_xi_des { get { return m_txt_xi_des; } }

		[SerializeField]
		private RectTransform_Container m_autoNode;
		public RectTransform_Container autoNode { get { return m_autoNode; } }

		[SerializeField]
		private RectTransform_Container m_timeShowNode;
		public RectTransform_Container timeShowNode { get { return m_timeShowNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_timeShow;
		public RectTransform_Text_Container txt_timeShow { get { return m_txt_timeShow; } }

		[SerializeField]
		private RectTransform_Container m_autoChooseNode;
		public RectTransform_Container autoChooseNode { get { return m_autoChooseNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_auto;
		public RectTransform_Button_Image_Container btn_auto { get { return m_btn_auto; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_choose;
		public RectTransform_Image_Container img_choose { get { return m_img_choose; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_RawImage;
		public RectTransform_RawImage_Container RawImage { get { return m_RawImage; } }

		[SerializeField]
		private RectTransform_Container m_CitiaoPanel;
		public RectTransform_Container CitiaoPanel { get { return m_CitiaoPanel; } }

		[SerializeField]
		private RectTransform_Container m_fresh_rootNode;
		public RectTransform_Container fresh_rootNode { get { return m_fresh_rootNode; } }

		[SerializeField]
		private RectTransform_Container m_fresh_btn_node;
		public RectTransform_Container fresh_btn_node { get { return m_fresh_btn_node; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_orange_btn_ad;
		public RectTransform_Button_Image_Container orange_btn_ad { get { return m_orange_btn_ad; } }

		[SerializeField]
		private RectTransform_Text_Container m_cyan_lable_ad_b;
		public RectTransform_Text_Container cyan_lable_ad_b { get { return m_cyan_lable_ad_b; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_refresh_grey;
		public RectTransform_Button_Image_Container btn_refresh_grey { get { return m_btn_refresh_grey; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_refresh;
		public RectTransform_Button_Image_Container btn_refresh { get { return m_btn_refresh; } }

		[SerializeField]
		private RectTransform_Text_Container m_cyan_lable_ad_a;
		public RectTransform_Text_Container cyan_lable_ad_a { get { return m_cyan_lable_ad_a; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_count;
		public RectTransform_Text_Container txt_count { get { return m_txt_count; } }

		[SerializeField]
		private RectTransform_Container m_modelNode;
		public RectTransform_Container modelNode { get { return m_modelNode; } }

		[SerializeField]
		private RectTransform_Container m_maxCitiao;
		public RectTransform_Container maxCitiao { get { return m_maxCitiao; } }

		[SerializeField]
		private RectTransform_UIFightCitiaoView_max_citiao_show_Container m_max_citiao_show;
		public RectTransform_UIFightCitiaoView_max_citiao_show_Container max_citiao_show { get { return m_max_citiao_show; } }

		[SerializeField]
		private RectTransform_Animator_Container m_SkillTipsNode;
		public RectTransform_Animator_Container SkillTipsNode { get { return m_SkillTipsNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_mechaIcon;
		public RectTransform_Image_Container mechaIcon { get { return m_mechaIcon; } }

		[System.Serializable]
		public class RectTransform_UIFightCitiaoView_max_citiao_show_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private UIFightCitiaoView_max_citiao_show m_max_citiao_show;
			public UIFightCitiaoView_max_citiao_show max_citiao_show { get { return m_max_citiao_show; } }

			[System.NonSerialized] public List<UIFightCitiaoView_max_citiao_show> mCachedList = new List<UIFightCitiaoView_max_citiao_show>();
			private Queue<UIFightCitiaoView_max_citiao_show> mCachedInstances;
			public UIFightCitiaoView_max_citiao_show GetInstance(bool ignoreSibling = false) {
				UIFightCitiaoView_max_citiao_show instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<UIFightCitiaoView_max_citiao_show>(m_max_citiao_show);
					instance.ItemInit();
				}
				Transform t0 = m_max_citiao_show.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(UIFightCitiaoView_max_citiao_show instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<UIFightCitiaoView_max_citiao_show>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<UIFightCitiaoView_max_citiao_show> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
