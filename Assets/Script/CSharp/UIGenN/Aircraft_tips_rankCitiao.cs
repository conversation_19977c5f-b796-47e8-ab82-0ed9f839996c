using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class Aircraft_tips_rankCitiao : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_img_citiaoBg;
		public RectTransform_Image_Container img_citiaoBg { get { return m_img_citiaoBg; } }

		[SerializeField]
		private RectTransform_Image_Container m_learned;
		public RectTransform_Image_Container learned { get { return m_learned; } }

		[SerializeField]
		private RectTransform_Image_Container m_near_learned;
		public RectTransform_Image_Container near_learned { get { return m_near_learned; } }

		[SerializeField]
		private RectTransform_Image_Container m_unLearned;
		public RectTransform_Image_Container unLearned { get { return m_unLearned; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_citiaoDesc;
		public RectTransform_Text_Container txt_citiaoDesc { get { return m_txt_citiaoDesc; } }

		[SerializeField]
		private RectTransform_Container m_ItemRank;
		public RectTransform_Container ItemRank { get { return m_ItemRank; } }

		[SerializeField]
		private RectTransform_Container m_effectNode;
		public RectTransform_Container effectNode { get { return m_effectNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLight;
		public RectTransform_Image_Container img_rankLight { get { return m_img_rankLight; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLogo;
		public RectTransform_Image_Container img_rankLogo { get { return m_img_rankLogo; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLock;
		public RectTransform_Image_Container img_rankLock { get { return m_img_rankLock; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_rankName;
		public RectTransform_Text_Container txt_rankName { get { return m_txt_rankName; } }

		[SerializeField]
		private RectTransform_Container m_skinStar;
		public RectTransform_Container skinStar { get { return m_skinStar; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_starNum;
		public RectTransform_Text_Container txt_starNum { get { return m_txt_starNum; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_starCitiaovView;
		public RectTransform_Button_Image_Container btn_starCitiaovView { get { return m_btn_starCitiaovView; } }

	}

}
