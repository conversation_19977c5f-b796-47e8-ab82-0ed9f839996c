using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PveTowerFightWinUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_MainFightResultUI;
		public RectTransform_Container MainFightResultUI { get { return m_MainFightResultUI; } }

		[SerializeField]
		private RectTransform_Container m_root;
		public RectTransform_Container root { get { return m_root; } }

		[SerializeField]
		private RectTransform_Container m_title_win;
		public RectTransform_Container title_win { get { return m_title_win; } }

		[SerializeField]
		private RectTransform_Container m_title_fail;
		public RectTransform_Container title_fail { get { return m_title_fail; } }

		[SerializeField]
		private RectTransform_Container m_infoNode;
		public RectTransform_Container infoNode { get { return m_infoNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_stageName;
		public RectTransform_Text_Container txt_stageName { get { return m_txt_stageName; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_passPro;
		public RectTransform_Text_Container txt_passPro { get { return m_txt_passPro; } }

		[SerializeField]
		private RectTransform_Image_Container m_newRecord;
		public RectTransform_Image_Container newRecord { get { return m_newRecord; } }

		[SerializeField]
		private RectTransform_Image_Container m_bg_win;
		public RectTransform_Image_Container bg_win { get { return m_bg_win; } }

		[SerializeField]
		private RectTransform_Image_Container m_bg_lose;
		public RectTransform_Image_Container bg_lose { get { return m_bg_lose; } }

		[SerializeField]
		private RectTransform_Image_Container m_Nothing;
		public RectTransform_Image_Container Nothing { get { return m_Nothing; } }

		[SerializeField]
		private RectTransform_PveTowerFightWinUI_dropItem_Container m_dropItem;
		public RectTransform_PveTowerFightWinUI_dropItem_Container dropItem { get { return m_dropItem; } }

		[SerializeField]
		private RectTransform_PveTowerFightWinUI_damageItem_Container m_damageItem;
		public RectTransform_PveTowerFightWinUI_damageItem_Container damageItem { get { return m_damageItem; } }

		[SerializeField]
		private RectTransform_Container m_Button_ad_play;
		public RectTransform_Container Button_ad_play { get { return m_Button_ad_play; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_fightresult_btn_ad;
		public RectTransform_Button_Image_Container fightresult_btn_ad { get { return m_fightresult_btn_ad; } }

		[SerializeField]
		private RectTransform_Text_Container m_fightresult_lable_ad_a;
		public RectTransform_Text_Container fightresult_lable_ad_a { get { return m_fightresult_lable_ad_a; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_remain;
		public RectTransform_Text_Container txt_remain { get { return m_txt_remain; } }

		[SerializeField]
		private RectTransform_Container m_Button_Return_M;
		public RectTransform_Container Button_Return_M { get { return m_Button_Return_M; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Back_btn_m;
		public RectTransform_Button_Image_Container Back_btn_m { get { return m_Back_btn_m; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_SendBtn;
		public RectTransform_Button_Image_Container SendBtn { get { return m_SendBtn; } }

		[System.Serializable]
		public class RectTransform_PveTowerFightWinUI_damageItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private PveTowerFightWinUI_damageItem m_damageItem;
			public PveTowerFightWinUI_damageItem damageItem { get { return m_damageItem; } }

			[System.NonSerialized] public List<PveTowerFightWinUI_damageItem> mCachedList = new List<PveTowerFightWinUI_damageItem>();
			private Queue<PveTowerFightWinUI_damageItem> mCachedInstances;
			public PveTowerFightWinUI_damageItem GetInstance(bool ignoreSibling = false) {
				PveTowerFightWinUI_damageItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<PveTowerFightWinUI_damageItem>(m_damageItem);
					instance.ItemInit();
				}
				Transform t0 = m_damageItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(PveTowerFightWinUI_damageItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<PveTowerFightWinUI_damageItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<PveTowerFightWinUI_damageItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_PveTowerFightWinUI_dropItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private PveTowerFightWinUI_dropItem m_dropItem;
			public PveTowerFightWinUI_dropItem dropItem { get { return m_dropItem; } }

			[System.NonSerialized] public List<PveTowerFightWinUI_dropItem> mCachedList = new List<PveTowerFightWinUI_dropItem>();
			private Queue<PveTowerFightWinUI_dropItem> mCachedInstances;
			public PveTowerFightWinUI_dropItem GetInstance(bool ignoreSibling = false) {
				PveTowerFightWinUI_dropItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<PveTowerFightWinUI_dropItem>(m_dropItem);
					instance.ItemInit();
				}
				Transform t0 = m_dropItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(PveTowerFightWinUI_dropItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<PveTowerFightWinUI_dropItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<PveTowerFightWinUI_dropItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
