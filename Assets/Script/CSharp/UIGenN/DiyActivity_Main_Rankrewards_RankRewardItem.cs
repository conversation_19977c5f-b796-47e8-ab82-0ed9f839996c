using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class DiyActivity_Main_Rankrewards_RankRewardItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_Top1;
		public RectTransform_Image_Container Top1 { get { return m_Top1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Top2;
		public RectTransform_Image_Container Top2 { get { return m_Top2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Top3;
		public RectTransform_Image_Container Top3 { get { return m_Top3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Top4;
		public RectTransform_Image_Container Top4 { get { return m_Top4; } }

		[SerializeField]
		private RectTransform_Text_Container m_Top_Num;
		public RectTransform_Text_Container Top_Num { get { return m_Top_Num; } }

		[SerializeField]
		private RectTransform_Container m_Items;
		public RectTransform_Container Items { get { return m_Items; } }

		[SerializeField]
		private RectTransform_DiyActivity_Main_Rankrewards_RankRewardItem_item_Container m_item;
		public RectTransform_DiyActivity_Main_Rankrewards_RankRewardItem_item_Container item { get { return m_item; } }

		[System.Serializable]
		public class RectTransform_DiyActivity_Main_Rankrewards_RankRewardItem_item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private DiyActivity_Main_Rankrewards_RankRewardItem_item m_item;
			public DiyActivity_Main_Rankrewards_RankRewardItem_item item { get { return m_item; } }

			[System.NonSerialized] public List<DiyActivity_Main_Rankrewards_RankRewardItem_item> mCachedList = new List<DiyActivity_Main_Rankrewards_RankRewardItem_item>();
			private Queue<DiyActivity_Main_Rankrewards_RankRewardItem_item> mCachedInstances;
			public DiyActivity_Main_Rankrewards_RankRewardItem_item GetInstance(bool ignoreSibling = false) {
				DiyActivity_Main_Rankrewards_RankRewardItem_item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<DiyActivity_Main_Rankrewards_RankRewardItem_item>(m_item);
					instance.ItemInit();
				}
				Transform t0 = m_item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(DiyActivity_Main_Rankrewards_RankRewardItem_item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<DiyActivity_Main_Rankrewards_RankRewardItem_item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<DiyActivity_Main_Rankrewards_RankRewardItem_item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
