using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GetNewSkinUI : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_mechaModel;
		public RectTransform_Container mechaModel { get { return m_mechaModel; } }

		[SerializeField]
		private RectTransform_Container m_fx_texiao;
		public RectTransform_Container fx_texiao { get { return m_fx_texiao; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_ModelImage;
		public RectTransform_RawImage_Container ModelImage { get { return m_ModelImage; } }

		[SerializeField]
		private RectTransform_Image_Container m_partImage;
		public RectTransform_Image_Container partImage { get { return m_partImage; } }

		[SerializeField]
		private RectTransform_Container m_NameNode;
		public RectTransform_Container NameNode { get { return m_NameNode; } }

		[SerializeField]
		private RectTransform_Container m_img_quaTitle1;
		public RectTransform_Container img_quaTitle1 { get { return m_img_quaTitle1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rank1;
		public RectTransform_Image_Container img_rank1 { get { return m_img_rank1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_nameBG;
		public RectTransform_Image_Container img_nameBG { get { return m_img_nameBG; } }

		[SerializeField]
		private Transform_Container m_fx;
		public Transform_Container fx { get { return m_fx; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_name_upgrade;
		public RectTransform_Text_Container txt_name_upgrade { get { return m_txt_name_upgrade; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_sortIcon1;
		public RectTransform_Image_Container img_sortIcon1 { get { return m_img_sortIcon1; } }

		[SerializeField]
		private RectTransform_Text_Container m_info;
		public RectTransform_Text_Container info { get { return m_info; } }

		[SerializeField]
		private RectTransform_GetNewSkinUI_info_ShiPin_Container m_info_ShiPin;
		public RectTransform_GetNewSkinUI_info_ShiPin_Container info_ShiPin { get { return m_info_ShiPin; } }

		[SerializeField]
		private RectTransform_Text_Container m_close;
		public RectTransform_Text_Container close { get { return m_close; } }

		[SerializeField]
		private RectTransform_Image_Container m_Image2;
		public RectTransform_Image_Container Image2 { get { return m_Image2; } }

		[SerializeField]
		private RectTransform_Container m_CommonModelUI;
		public RectTransform_Container CommonModelUI { get { return m_CommonModelUI; } }

		[SerializeField]
		private Transform_Container m_ModelNodeRoot;
		public Transform_Container ModelNodeRoot { get { return m_ModelNodeRoot; } }

		[SerializeField]
		private Transform_Container m_WorldNode;
		public Transform_Container WorldNode { get { return m_WorldNode; } }

		[SerializeField]
		private Transform_Container m_ModelNode;
		public Transform_Container ModelNode { get { return m_ModelNode; } }

		[SerializeField]
		private Transform_Container m_CamerRootNode;
		public Transform_Container CamerRootNode { get { return m_CamerRootNode; } }

		[SerializeField]
		private Transform_Container m_RenderCameraNode;
		public Transform_Container RenderCameraNode { get { return m_RenderCameraNode; } }

		[SerializeField]
		private Transform_Animator_Container m_diybeijing;
		public Transform_Animator_Container diybeijing { get { return m_diybeijing; } }

		[System.Serializable]
		public class RectTransform_GetNewSkinUI_info_ShiPin_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private GetNewSkinUI_info_ShiPin m_info_ShiPin;
			public GetNewSkinUI_info_ShiPin info_ShiPin { get { return m_info_ShiPin; } }

			[System.NonSerialized] public List<GetNewSkinUI_info_ShiPin> mCachedList = new List<GetNewSkinUI_info_ShiPin>();
			private Queue<GetNewSkinUI_info_ShiPin> mCachedInstances;
			public GetNewSkinUI_info_ShiPin GetInstance(bool ignoreSibling = false) {
				GetNewSkinUI_info_ShiPin instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<GetNewSkinUI_info_ShiPin>(m_info_ShiPin);
					instance.ItemInit();
				}
				Transform t0 = m_info_ShiPin.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(GetNewSkinUI_info_ShiPin instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<GetNewSkinUI_info_ShiPin>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<GetNewSkinUI_info_ShiPin> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
