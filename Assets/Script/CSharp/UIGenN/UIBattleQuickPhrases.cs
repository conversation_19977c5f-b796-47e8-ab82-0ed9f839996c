using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UIBattleQuickPhrases : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_FollowNode;
		public RectTransform_Container FollowNode { get { return m_FollowNode; } }

		[SerializeField]
		private RectTransform_Container m_BattleQuickPhrases;
		public RectTransform_Container BattleQuickPhrases { get { return m_BattleQuickPhrases; } }

		[SerializeField]
		private RectTransform_Text_Container m_QuickPhrases;
		public RectTransform_Text_Container QuickPhrases { get { return m_QuickPhrases; } }

	}

}
