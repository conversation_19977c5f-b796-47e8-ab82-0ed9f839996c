using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class CombatTarget_Main_task_Progress : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_img_scorePro;
		public RectTransform_Image_Container img_scorePro { get { return m_img_scorePro; } }

		[SerializeField]
		private RectTransform_Image_Container m_Progress_Item;
		public RectTransform_Image_Container Progress_Item { get { return m_Progress_Item; } }

		[SerializeField]
		private RectTransform_Text_Container m_Progress_score;
		public RectTransform_Text_Container Progress_score { get { return m_Progress_score; } }

		[SerializeField]
		private RectTransform_Container m_Node_1;
		public RectTransform_Container Node_1 { get { return m_Node_1; } }

		[SerializeField]
		private RectTransform_Container m_Last_Reward_Item1;
		public RectTransform_Container Last_Reward_Item1 { get { return m_Last_Reward_Item1; } }

		[SerializeField]
		private RectTransform_Text_Container m_scoreLast1;
		public RectTransform_Text_Container scoreLast1 { get { return m_scoreLast1; } }

		[SerializeField]
		private RectTransform_Container m_Node_2;
		public RectTransform_Container Node_2 { get { return m_Node_2; } }

		[SerializeField]
		private RectTransform_Container m_Last_Reward_Item2;
		public RectTransform_Container Last_Reward_Item2 { get { return m_Last_Reward_Item2; } }

		[SerializeField]
		private RectTransform_Text_Container m_scoreLast2;
		public RectTransform_Text_Container scoreLast2 { get { return m_scoreLast2; } }

		[SerializeField]
		private RectTransform_Container m_Node_3;
		public RectTransform_Container Node_3 { get { return m_Node_3; } }

		[SerializeField]
		private RectTransform_Container m_Last_Reward_Item3;
		public RectTransform_Container Last_Reward_Item3 { get { return m_Last_Reward_Item3; } }

		[SerializeField]
		private RectTransform_Text_Container m_scoreLast3;
		public RectTransform_Text_Container scoreLast3 { get { return m_scoreLast3; } }

		[SerializeField]
		private RectTransform_Container m_Node_4;
		public RectTransform_Container Node_4 { get { return m_Node_4; } }

		[SerializeField]
		private RectTransform_Container m_Last_Reward_Item4;
		public RectTransform_Container Last_Reward_Item4 { get { return m_Last_Reward_Item4; } }

		[SerializeField]
		private RectTransform_Text_Container m_scoreLast4;
		public RectTransform_Text_Container scoreLast4 { get { return m_scoreLast4; } }

	}

}
