using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class DriverTips_Driver_ComboSkill : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_Bg;
		public RectTransform_Image_Container Bg { get { return m_Bg; } }

		[SerializeField]
		private RectTransform_Image_Container m_comboskill_icon;
		public RectTransform_Image_Container comboskill_icon { get { return m_comboskill_icon; } }

		[SerializeField]
		private RectTransform_Text_Container m_comboskill_name;
		public RectTransform_Text_Container comboskill_name { get { return m_comboskill_name; } }

		[SerializeField]
		private RectTransform_Text_Container m_comboskill_info;
		public RectTransform_Text_Container comboskill_info { get { return m_comboskill_info; } }

	}

}
