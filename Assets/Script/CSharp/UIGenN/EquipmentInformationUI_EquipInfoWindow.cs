using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class EquipmentInformationUI_EquipInfoWindow : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_CurNode;
		public RectTransform_Container CurNode { get { return m_CurNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Container m_EquipInfo;
		public RectTransform_Container EquipInfo { get { return m_EquipInfo; } }

		[SerializeField]
		private RectTransform_Container m_IconNode;
		public RectTransform_Container IconNode { get { return m_IconNode; } }

		[SerializeField]
		private RectTransform_TextMeshProUGUI_Container m_EquipName;
		public RectTransform_TextMeshProUGUI_Container EquipName { get { return m_EquipName; } }

		[SerializeField]
		private RectTransform_Text_Container m_combat_power;
		public RectTransform_Text_Container combat_power { get { return m_combat_power; } }

		[SerializeField]
		private RectTransform_Button_Container m_lockBtn;
		public RectTransform_Button_Container lockBtn { get { return m_lockBtn; } }

		[SerializeField]
		private RectTransform_Image_Container m_unlockStatus;
		public RectTransform_Image_Container unlockStatus { get { return m_unlockStatus; } }

		[SerializeField]
		private RectTransform_Image_Container m_lockStatus;
		public RectTransform_Image_Container lockStatus { get { return m_lockStatus; } }

		[SerializeField]
		private RectTransform_Container m_AttrNode;
		public RectTransform_Container AttrNode { get { return m_AttrNode; } }

		[SerializeField]
		private RectTransform_EquipmentInformationUI_EquipInfoWindow_attr_Container m_attr;
		public RectTransform_EquipmentInformationUI_EquipInfoWindow_attr_Container attr { get { return m_attr; } }

		[SerializeField]
		private RectTransform_Container m_BtnNode;
		public RectTransform_Container BtnNode { get { return m_BtnNode; } }

		[SerializeField]
		private RectTransform_Container m_fenjieBtn;
		public RectTransform_Container fenjieBtn { get { return m_fenjieBtn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_Fenjie;
		public RectTransform_Button_Image_Container btn_Fenjie { get { return m_btn_Fenjie; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_unFenjie;
		public RectTransform_Button_Image_Container btn_unFenjie { get { return m_btn_unFenjie; } }

		[SerializeField]
		private RectTransform_Container m_chucunBtn;
		public RectTransform_Container chucunBtn { get { return m_chucunBtn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_Chucun;
		public RectTransform_Button_Image_Container btn_Chucun { get { return m_btn_Chucun; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_unChucun;
		public RectTransform_Button_Image_Container btn_unChucun { get { return m_btn_unChucun; } }

		[SerializeField]
		private RectTransform_Container m_tihuanBtn;
		public RectTransform_Container tihuanBtn { get { return m_tihuanBtn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_tihuan;
		public RectTransform_Button_Image_Container btn_tihuan { get { return m_btn_tihuan; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_untihuan;
		public RectTransform_Button_Image_Container btn_untihuan { get { return m_btn_untihuan; } }

		[SerializeField]
		private RectTransform_Container m_AfterChangeDispose;
		public RectTransform_Container AfterChangeDispose { get { return m_AfterChangeDispose; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_ChangeDisposeBtn;
		public RectTransform_Button_Image_Container ChangeDisposeBtn { get { return m_ChangeDisposeBtn; } }

		[SerializeField]
		private RectTransform_Image_Container m_ChangeDisposeBtnSelected;
		public RectTransform_Image_Container ChangeDisposeBtnSelected { get { return m_ChangeDisposeBtnSelected; } }

		[SerializeField]
		private RectTransform_Container m_CheckAllAttrNode;
		public RectTransform_Container CheckAllAttrNode { get { return m_CheckAllAttrNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_CheckAttrlBtn;
		public RectTransform_Button_Image_Container CheckAttrlBtn { get { return m_CheckAttrlBtn; } }

		[SerializeField]
		private RectTransform_Image_Container m_CheckSelected;
		public RectTransform_Image_Container CheckSelected { get { return m_CheckSelected; } }

		[System.Serializable]
		public class RectTransform_EquipmentInformationUI_EquipInfoWindow_attr_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private EquipmentInformationUI_EquipInfoWindow_attr m_attr;
			public EquipmentInformationUI_EquipInfoWindow_attr attr { get { return m_attr; } }

			[System.NonSerialized] public List<EquipmentInformationUI_EquipInfoWindow_attr> mCachedList = new List<EquipmentInformationUI_EquipInfoWindow_attr>();
			private Queue<EquipmentInformationUI_EquipInfoWindow_attr> mCachedInstances;
			public EquipmentInformationUI_EquipInfoWindow_attr GetInstance(bool ignoreSibling = false) {
				EquipmentInformationUI_EquipInfoWindow_attr instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<EquipmentInformationUI_EquipInfoWindow_attr>(m_attr);
					instance.ItemInit();
				}
				Transform t0 = m_attr.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(EquipmentInformationUI_EquipInfoWindow_attr instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<EquipmentInformationUI_EquipInfoWindow_attr>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<EquipmentInformationUI_EquipInfoWindow_attr> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
