using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class StartAwardUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Image_Container m_Model;
		public RectTransform_Image_Container Model { get { return m_Model; } }

		[SerializeField]
		private RectTransform_Text_Container m_Title;
		public RectTransform_Text_Container Title { get { return m_Title; } }

		[SerializeField]
		private RectTransform_Container m_Reward_Node;
		public RectTransform_Container Reward_Node { get { return m_Reward_Node; } }

		[SerializeField]
		private RectTransform_StartAwardUI_DayNormal_Container m_DayNormal;
		public RectTransform_StartAwardUI_DayNormal_Container DayNormal { get { return m_DayNormal; } }

		[SerializeField]
		private RectTransform_Container m_Day07;
		public RectTransform_Container Day07 { get { return m_Day07; } }

		[SerializeField]
		private RectTransform_Text_Container m_date_txt;
		public RectTransform_Text_Container date_txt { get { return m_date_txt; } }

		[SerializeField]
		private RectTransform_Button_Container m_Receive;
		public RectTransform_Button_Container Receive { get { return m_Receive; } }

		[SerializeField]
		private RectTransform_Image_Container m_Receive_BG;
		public RectTransform_Image_Container Receive_BG { get { return m_Receive_BG; } }

		[SerializeField]
		private RectTransform_Container m_YiLingQu;
		public RectTransform_Container YiLingQu { get { return m_YiLingQu; } }

		[SerializeField]
		private RectTransform_Container m_WeiLingQu;
		public RectTransform_Container WeiLingQu { get { return m_WeiLingQu; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_WelLingBtn;
		public RectTransform_Button_Image_Container WelLingBtn { get { return m_WelLingBtn; } }

		[SerializeField]
		private RectTransform_Container m_KeLingQu;
		public RectTransform_Container KeLingQu { get { return m_KeLingQu; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_KeLingQu_Light;
		public RectTransform_Button_Image_Container KeLingQu_Light { get { return m_KeLingQu_Light; } }

		[SerializeField]
		private RectTransform_Container m_Day01Node;
		public RectTransform_Container Day01Node { get { return m_Day01Node; } }

		[SerializeField]
		private RectTransform_Container m_Day02Node;
		public RectTransform_Container Day02Node { get { return m_Day02Node; } }

		[SerializeField]
		private RectTransform_Container m_Day03Node;
		public RectTransform_Container Day03Node { get { return m_Day03Node; } }

		[SerializeField]
		private RectTransform_Container m_Day04Node;
		public RectTransform_Container Day04Node { get { return m_Day04Node; } }

		[SerializeField]
		private RectTransform_Container m_Day05Node;
		public RectTransform_Container Day05Node { get { return m_Day05Node; } }

		[SerializeField]
		private RectTransform_Container m_Day06Node;
		public RectTransform_Container Day06Node { get { return m_Day06Node; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rank1;
		public RectTransform_Image_Container img_rank1 { get { return m_img_rank1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rank2;
		public RectTransform_Image_Container img_rank2 { get { return m_img_rank2; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_name_upgrade;
		public RectTransform_Text_Container txt_name_upgrade { get { return m_txt_name_upgrade; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_title;
		public RectTransform_Text_Container txt_title { get { return m_txt_title; } }

		[SerializeField]
		private RectTransform_Text_Container m_Detail_txt;
		public RectTransform_Text_Container Detail_txt { get { return m_Detail_txt; } }

		[SerializeField]
		private RectTransform_Container m_Detail_Btn;
		public RectTransform_Container Detail_Btn { get { return m_Detail_Btn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Detail_Btn_BG;
		public RectTransform_Button_Image_Container Detail_Btn_BG { get { return m_Detail_Btn_BG; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_01;
		public RectTransform_Container Tomorrow_01 { get { return m_Tomorrow_01; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_02;
		public RectTransform_Container Tomorrow_02 { get { return m_Tomorrow_02; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_03;
		public RectTransform_Container Tomorrow_03 { get { return m_Tomorrow_03; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_04;
		public RectTransform_Container Tomorrow_04 { get { return m_Tomorrow_04; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_05;
		public RectTransform_Container Tomorrow_05 { get { return m_Tomorrow_05; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_06;
		public RectTransform_Container Tomorrow_06 { get { return m_Tomorrow_06; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_07;
		public RectTransform_Container Tomorrow_07 { get { return m_Tomorrow_07; } }

		[SerializeField]
		private RectTransform_Container m_Tomorrow_Mark;
		public RectTransform_Container Tomorrow_Mark { get { return m_Tomorrow_Mark; } }

		[System.Serializable]
		public class RectTransform_StartAwardUI_DayNormal_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private StartAwardUI_DayNormal m_DayNormal;
			public StartAwardUI_DayNormal DayNormal { get { return m_DayNormal; } }

			[System.NonSerialized] public List<StartAwardUI_DayNormal> mCachedList = new List<StartAwardUI_DayNormal>();
			private Queue<StartAwardUI_DayNormal> mCachedInstances;
			public StartAwardUI_DayNormal GetInstance(bool ignoreSibling = false) {
				StartAwardUI_DayNormal instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<StartAwardUI_DayNormal>(m_DayNormal);
					instance.ItemInit();
				}
				Transform t0 = m_DayNormal.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(StartAwardUI_DayNormal instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<StartAwardUI_DayNormal>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<StartAwardUI_DayNormal> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
