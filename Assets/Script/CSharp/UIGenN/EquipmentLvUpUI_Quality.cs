using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class EquipmentLvUpUI_Quality : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_quality_icon;
		public RectTransform_Image_Container quality_icon { get { return m_quality_icon; } }

		[SerializeField]
		private RectTransform_Text_Container m_QualityName;
		public RectTransform_Text_Container QualityName { get { return m_QualityName; } }

		[SerializeField]
		private RectTransform_Text_Container m_gailvVal1;
		public RectTransform_Text_Container gailvVal1 { get { return m_gailvVal1; } }

		[SerializeField]
		private RectTransform_Container m_UpInfo;
		public RectTransform_Container UpInfo { get { return m_UpInfo; } }

		[SerializeField]
		private RectTransform_Text_Container m_gailvVal2;
		public RectTransform_Text_Container gailvVal2 { get { return m_gailvVal2; } }

		[SerializeField]
		private RectTransform_Image_Container m_up;
		public RectTransform_Image_Container up { get { return m_up; } }

	}

}
