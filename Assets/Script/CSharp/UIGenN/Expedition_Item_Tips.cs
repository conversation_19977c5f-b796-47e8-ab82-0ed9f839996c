using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class Expedition_Item_Tips : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_ItemNode;
		public RectTransform_Container ItemNode { get { return m_ItemNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_Item_Name;
		public RectTransform_Text_Container Item_Name { get { return m_Item_Name; } }

		[SerializeField]
		private RectTransform_Text_Container m_Item_Condition;
		public RectTransform_Text_Container Item_Condition { get { return m_Item_Condition; } }

		[SerializeField]
		private RectTransform_Text_Container m_Item_Tips;
		public RectTransform_Text_Container Item_Tips { get { return m_Item_Tips; } }

	}

}
