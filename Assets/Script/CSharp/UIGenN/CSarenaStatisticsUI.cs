using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class CSarenaStatisticsUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Container m_player1;
		public RectTransform_Container player1 { get { return m_player1; } }

		[SerializeField]
		private RectTransform_Container m_RoleHead1;
		public RectTransform_Container RoleHead1 { get { return m_RoleHead1; } }

		[SerializeField]
		private RectTransform_Text_Container m_player1_name;
		public RectTransform_Text_Container player1_name { get { return m_player1_name; } }

		[SerializeField]
		private RectTransform_Container m_player2;
		public RectTransform_Container player2 { get { return m_player2; } }

		[SerializeField]
		private RectTransform_Container m_RoleHead2;
		public RectTransform_Container RoleHead2 { get { return m_RoleHead2; } }

		[SerializeField]
		private RectTransform_Text_Container m_player2_name;
		public RectTransform_Text_Container player2_name { get { return m_player2_name; } }

		[SerializeField]
		private RectTransform_Image_Container m_Damage_proportio;
		public RectTransform_Image_Container Damage_proportio { get { return m_Damage_proportio; } }

		[SerializeField]
		private RectTransform_Text_Container m_player1_num;
		public RectTransform_Text_Container player1_num { get { return m_player1_num; } }

		[SerializeField]
		private RectTransform_Text_Container m_player2_num;
		public RectTransform_Text_Container player2_num { get { return m_player2_num; } }

		[SerializeField]
		private RectTransform_Image_Container m_ScrollView;
		public RectTransform_Image_Container ScrollView { get { return m_ScrollView; } }

		[SerializeField]
		private RectTransform_CSarenaStatisticsUI_myInfo_Container m_myInfo;
		public RectTransform_CSarenaStatisticsUI_myInfo_Container myInfo { get { return m_myInfo; } }

		[System.Serializable]
		public class RectTransform_CSarenaStatisticsUI_myInfo_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private CSarenaStatisticsUI_myInfo m_myInfo;
			public CSarenaStatisticsUI_myInfo myInfo { get { return m_myInfo; } }

			[System.NonSerialized] public List<CSarenaStatisticsUI_myInfo> mCachedList = new List<CSarenaStatisticsUI_myInfo>();
			private Queue<CSarenaStatisticsUI_myInfo> mCachedInstances;
			public CSarenaStatisticsUI_myInfo GetInstance(bool ignoreSibling = false) {
				CSarenaStatisticsUI_myInfo instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<CSarenaStatisticsUI_myInfo>(m_myInfo);
					instance.ItemInit();
				}
				Transform t0 = m_myInfo.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(CSarenaStatisticsUI_myInfo instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<CSarenaStatisticsUI_myInfo>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<CSarenaStatisticsUI_myInfo> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
