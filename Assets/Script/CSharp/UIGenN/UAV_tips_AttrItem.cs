using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UAV_tips_AttrItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_Bg;
		public RectTransform_Image_Container Bg { get { return m_Bg; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon;
		public RectTransform_Image_Container Icon { get { return m_Icon; } }

		[SerializeField]
		private RectTransform_Text_Container m_AttrName;
		public RectTransform_Text_Container AttrName { get { return m_AttrName; } }

		[SerializeField]
		private RectTransform_Text_Container m_AttrVal1;
		public RectTransform_Text_Container AttrVal1 { get { return m_AttrVal1; } }

	}

}
