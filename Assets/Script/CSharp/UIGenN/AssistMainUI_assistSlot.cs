using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class AssistMainUI_assistSlot : LDBaseResUI {

		[SerializeField]
		private RectTransform_Animator_Container m_slot_state;
		public RectTransform_Animator_Container slot_state { get { return m_slot_state; } }

		[SerializeField]
		private RectTransform_Container m_root;
		public RectTransform_Container root { get { return m_root; } }

		[SerializeField]
		private RectTransform_Container m_jijiatai;
		public RectTransform_Container jijiatai { get { return m_jijiatai; } }

		[SerializeField]
		private RectTransform_Container m_NotUsed;
		public RectTransform_Container NotUsed { get { return m_NotUsed; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_AddBtn;
		public RectTransform_Button_Image_Container AddBtn { get { return m_AddBtn; } }

		[SerializeField]
		private RectTransform_Text_Container m_lvNotUsed;
		public RectTransform_Text_Container lvNotUsed { get { return m_lvNotUsed; } }

		[SerializeField]
		private RectTransform_Text_Container m_lvMaxNotUsed;
		public RectTransform_Text_Container lvMaxNotUsed { get { return m_lvMaxNotUsed; } }

		[SerializeField]
		private RectTransform_Text_Container m_tips;
		public RectTransform_Text_Container tips { get { return m_tips; } }

		[SerializeField]
		private RectTransform_Container m_InUse;
		public RectTransform_Container InUse { get { return m_InUse; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_Mecha_model;
		public RectTransform_RawImage_Container Mecha_model { get { return m_Mecha_model; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_MechaBtn;
		public RectTransform_Button_Image_Container MechaBtn { get { return m_MechaBtn; } }

		[SerializeField]
		private RectTransform_Text_Container m_lvUse;
		public RectTransform_Text_Container lvUse { get { return m_lvUse; } }

		[SerializeField]
		private RectTransform_Text_Container m_lvMaxUse;
		public RectTransform_Text_Container lvMaxUse { get { return m_lvMaxUse; } }

		[SerializeField]
		private RectTransform_Container m_Lock;
		public RectTransform_Container Lock { get { return m_Lock; } }

		[SerializeField]
		private RectTransform_Text_Container m_lock_text;
		public RectTransform_Text_Container lock_text { get { return m_lock_text; } }

		[SerializeField]
		private RectTransform_Container m_ModelOffset;
		public RectTransform_Container ModelOffset { get { return m_ModelOffset; } }

		[SerializeField]
		private RectTransform_Container m_teixao;
		public RectTransform_Container teixao { get { return m_teixao; } }

	}

}
