using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDSDKMgr
    {
        public LDAccountMgr gAccountMgr;
        public LDSDKPaymentMgr gPaymentMgr;
        public LDADMgr gADMgr;
        public LDPushSDKMgr gPushSDKMgr;
        public LDOneMTMgr gOneMTMgr;
        public LDFirebaseMgr gFirebaseMgr;
        public LDNoticeMgr gNoticeMgr;
        public LDReportSDKMgr gReportSDKMgr;
        public LDToolKitMgr gToolKitMgr;
        public LDSDKFAQMgr gFAQMgr;
        public LDQuerySurveyMgr gQuerySurveyMgr;
        public LDSDKMgr()
        {
            gOneMTMgr = new LDOneMTMgr();
            gAccountMgr = new LDAccountMgr();
            gPaymentMgr = new LDSDKPaymentMgr();
            gPushSDKMgr = new LDPushSDKMgr();
            gADMgr = new LDADMgr();
            gNoticeMgr = new LDNoticeMgr();
            gFirebaseMgr = new LDFirebaseMgr();
            gReportSDKMgr = new LDReportSDKMgr();
            gToolKitMgr = new LDToolKitMgr();
            gFAQMgr = new LDSDKFAQMgr();
            gQuerySurveyMgr = new LDQuerySurveyMgr();
        }
        public void InitSdk()
        {
            gOneMTMgr.Init();
            gAccountMgr.Init();
            gPaymentMgr.Init();
            //gADMgr.Init();
            gPushSDKMgr.Init();
            gNoticeMgr.Init();
            gFirebaseMgr.Init();
            gReportSDKMgr.Init();
            gToolKitMgr.Init();
            gFAQMgr.Init();
            gQuerySurveyMgr.Init();
        }
        public void AfterInit()
        {
            gOneMTMgr.AfterInit();
        }
        public static string PrivacyPolicy = "PrivacyPolicy";

        public bool IsAgreePrivacyPolicy()
        {
            int localTag = LDPlayerPrefs.GetInt(PrivacyPolicy,0);
            return localTag > 0;
        }       
        public void SetAgreePrivacyPolicy()
        {
            LDPlayerPrefs.SetInt(PrivacyPolicy, 1);
            LDPlayerPrefs.Save();
        }
        public static string GetGuid()
        {
            return System.Guid.NewGuid().ToString();
        }
        public bool CanLangTranslate()
        {
            DEVServerConfigItem loginItemData = LDLoginCfg.GetServerItem();
            return loginItemData.translate > 0;
        }
        public int GetZone()
        {
            LDServerListItem serverItem = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerItem();
            if (serverItem != null)
            {
                return serverItem.id / 10000;
            }
            else
            {
                DEVServerConfigItem loginItemData = LDLoginCfg.GetServerItem();
                return loginItemData.id % 10000;
            }
        }
    }
}
