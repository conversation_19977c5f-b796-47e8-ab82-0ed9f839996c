using OneMT.SDK;
using static OneMT.SDK.OneMTTool;

namespace LD
{
    public class LDToolKitMgr : IAppReviewCompletedListener, OMTOpenExternalAppListener
    {
        public void Init()
        {

        }

        public void OnFailure(int errCode)
        {
            UnityEngine.Debug.Log("Revivew OnFailure code " + errCode);
        }

        public void OnSuccess()
        {
            UnityEngine.Debug.Log("Revivew OnSuccess ");
        }
        public void showAppReview()
        {
            if (RuntimeSettings.UseSDK)
            {
#if UNITY_ANDROID
            OneMTTool.showAppReview(RuntimeSettings.PingJiaAndroidKey, this);
#elif UNITY_IOS
                OneMTTool.showAppReview(RuntimeSettings.PingJiaIOSKey, this);
#endif
            }
        }
        public void openExternalApp(string url)
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTTool.openExternalApp(url,this);
            }
        }

        public void OpenResult(bool isSuccess)
        {
        }
        public int GetTotalDiskSpace()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.GetTotalDiskSpace();
            }
            else
            {
                return 102400;
            }
        }     
        public int GetAvailableDiskSpace()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.GetAvailableDiskSpace();
            }
            else
            {
                return 10240;
            }
        }      
        public double getAvailableMemory()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.getAvailableMemory();
            }
            else
            {
                return 0;
            }
        }        
        public double getUsedMemory()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.getUsedMemory();
            }
            else
            {
                return 0;
            }
        }


        public double getTotalMemory()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.getTotalMemory();
            }
            else
            {
                return 0;
            }
        }       
        public bool isNotchScreen()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.isNotchScreen();
            }
            else
            {
                return false;
            }
        }        
        public bool isEmulator()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.isEmulator();
            }
            else
            {
                return false;
            }
        }        
        public int getStatusBarHeight()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.getStatusBarHeight();
            }
            else
            {
                return 0;
            }
        }
        //public OneMTToolNotchProperty getNotchScreenSync()
        //{
        //    if (RuntimeSettings.UseSDK)
        //    {
        //        return OneMTTool.getNotchScreenSync();
        //    }
        //    else
        //    {
        //        return default(OneMTToolNotchProperty);
        //    }
        //}
    }
}
