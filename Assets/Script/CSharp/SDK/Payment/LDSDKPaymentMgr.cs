using Google.Protobuf;
using LD.Protocol;
using LitJson;
using OneMT.SDK;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDPaymentCfg
    {
        public static int DiamondMall = 1;
        public static int GifgMall = 5;
    }

    public class LDPaymentInfo
    {
    }

    public class LDGoodsDetail
    {
        public Dictionary<string, string> items;
    }

    public class LDSDKPaymentMgr : OneMT.SDK.IPaymentCallback
    {
        public Dictionary<string, OneMT.SDK.OMTProductInfo> ProductInfo { get; set; } = new Dictionary<string, OneMT.SDK.OMTProductInfo>();
        public bool CanCharge { get; private set; }
        private OneMT.SDK.OMTPayChannel m_PayChanel;
        private int m_TryRequestProductListTimes = 3;

        public void Init()
        {
            if (RuntimeSettings.UseSDK)
            {
                if (Application.platform == RuntimePlatform.Android)
                {
                    if (Global.gApp.gSdkMgr.gOneMTMgr.IsHuaWeiChannel())
                    {
                        m_PayChanel = OMTPayChannel.HuaWei;
                    }
                    else
                    {
                        m_PayChanel = OMTPayChannel.Google;
                    }
                }
                else if (Application.platform == RuntimePlatform.IPhonePlayer)
                {
                    m_PayChanel = OMTPayChannel.Apple;
                }

                OneMT.SDK.OneMTPayment.init(m_PayChanel, this);
            }
        }

        // sdk ��ʼ���ɹ��ص�1
        public void OnInit(bool isSuccess)
        {
            Global.LogGreen("PayChanel OnInit" + isSuccess);
            CanCharge = isSuccess;
            if (isSuccess)
            {
                requestProductList();
            }
        }

        // sdk ��ʼ���ɹ��ص�2
        public void OnRequestBizInfo()
        {
            Global.LogGreen("OnRequestBizInfo");
            Hashtable info = new Hashtable();
            info.Add("zoneId", Global.gApp.gSdkMgr.GetZone());
            info.Add("serverId", Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerId());
            info.Add("gameUid", Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId());
            // TODO: dqx �����������⴦���
            info.Add("gameLang", Global.gApp.gGameData.GetSystemCurLanguage());
            info.Add("vipLevel", Global.gApp.gSystemMgr.gRoleMgr.GetVipLevel());
            info.Add("level", Global.gApp.gSystemMgr.gRoleMgr.GetLevel());
            OneMT.SDK.OneMTPayment.onBizInfoResponse(info);
        }

        //ȡ��Ʒ�б���Ҫ����Ĳ�������Ӫ���̵��̨���õ���Ʒid�б��������ʱ������֧��SDK��ʼ�������Ӫ���̵��̨��������Ʒid����������һ�Ρ�
        public void requestProductList()
        {
            Global.LogGreen("requestProductList");
            OneMT.SDK.OneMTPayment.requestProductList(GetProductList(), OnGetProductListCompleted);
        }

        private string[] GetProductList()
        {
            List<string> list = new List<string>();
            foreach (iapItem item in iap.Data.items)
            {
                list.Add(GetProductImp(item));
            }

            string[] product = list.ToArray();
            foreach (var item in list)
            {
                Debug.Log("GoodsID  " + item);
            }

            return product;
        }

        private string GetProductId(int mallId)
        {
            MallGoodsItem mallGoodsItem = MallGoods.Data.Get(mallId);
            iapItem item = iap.Data.Get(mallGoodsItem.iap);
            return GetProductImp(item);
        }

        public string GetProductImp(iapItem item)
        {
            // ���԰����⴦��һ��
            if (Application.platform == RuntimePlatform.Android)
            {
                return item.google_play_goods_id;
            }
            //else if (Application.platform == RuntimePlatform.IPhonePlayer)
            else
            {
                if (RuntimeSettings.Release)
                {
                    return item.appstore_goods_id;
                }
                else
                {
                    return item.appstore_goods_id + "test";
                }
            }
        }

        void OnGetProductListCompleted(bool isSuccess, ArrayList infos)
        {
            if (isSuccess)
            {
                Debug.Log("GetProductListCompleted  111 sucess");
                if (infos != null)
                {
                    foreach (OneMT.SDK.OMTProductInfo item in infos)
                    {
                        ProductInfo[item.ProductId] = item;
                    }

                    Global.gApp.gMsgDispatcher.Broadcast(MsgIds.GetProductListCompleted);
                }
                else
                {
                    Global.gApp.gToastMgr.ShowGameTips(95101);
                }

                Debug.Log("GetProductListCompleted  222 sucess");
            }
            else
            {
                if (m_TryRequestProductListTimes > 0)
                {
                    m_TryRequestProductListTimes--;
                    Debug.Log("GetProductListCompleted failed");
                    Global.gApp.gTimerMgr.AddTimer(60, 1, (float a, bool b) => { requestProductList(); });
                }
            }
        }

        public string GetUsPrice(int mallId)
        {
            string pid = GetProductId(mallId);
            if (ProductInfo.ContainsKey(pid))
            {
                return ProductInfo[pid].Price;
            }
            else
            {
                return GetIapConfigPrice(mallId).ToString();
            }
        }

        public string GetDisplayPrice(int mallId)
        {
            string pid = GetProductId(mallId);
            if (ProductInfo.ContainsKey(pid))
            {
                return ProductInfo[pid].DisplayPrice;
            }
            else
            {
#if UNITY_EDITOR
                return GetIapConfigPrice(mallId).ToString();
#else
                return string.Empty;
#endif
            }
        }

        public string GetCurrencyPrice(float price, bool withDisplay = true, bool showProxyCurrency = false)
        {
            int mailId = 1;
            MallGoodsItem mallGoodsItem = MallGoods.Data.Get(mailId);
            iapItem item = iap.Data.Get(mallGoodsItem.iap);
            string pid = GetProductId(mailId);
            if (ProductInfo.ContainsKey(pid))
            {
                float curPrice;
                string curPirceStr = ProductInfo[pid].Price;
                if (LDParseTools.TryFloatParse(curPirceStr, out curPrice))
                {
                    long proxyCurrency = 1;
                    if (showProxyCurrency)
                    {
                        GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(80158);
                        if (globalCfgItem != null)
                        {
                            proxyCurrency = globalCfgItem.valueInt;
                        }
                    }

                    if (withDisplay)
                    {
                        if (showProxyCurrency)
                        {
                            long a = EZMath.IDFloatToInt(100 * price) * proxyCurrency;
                            return GetPriceFloat(a);
                        }
                        else
                        {
                            long a = EZMath.IDFloatToInt(100 * (price * curPrice / item.price)) * proxyCurrency;
                            return LDCommonTools.GetSubstringFitstNotDigit(ProductInfo[pid].DisplayPrice) + GetPriceFloat(a);
                        }
                    }
                    else
                    {
                        if (showProxyCurrency)
                        {
                            long a = EZMath.IDFloatToInt(100 * price) * proxyCurrency;
                            return GetPriceFloat(a);
                        }
                        else
                        {
                            long a = EZMath.IDFloatToInt(100 * price * curPrice / item.price) * proxyCurrency;
                            return GetPriceFloat(a);
                        }
                    }
                }

                else
                {
                    return string.Empty;
                }
            }
            else
            {
                long proxyCurrency = 1;
                if (showProxyCurrency)
                {
                    GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(80158);
                    if (globalCfgItem != null)
                    {
                        proxyCurrency = globalCfgItem.valueInt;
                    }
                }
#if UNITY_EDITOR

                var a = EZMath.IDFloatToInt(100 * price) * proxyCurrency;
                return GetPriceFloat(a);
#else
                return string.Empty;
#endif
            }
        }

        private string GetPriceFloat(long val)
        {
            string valStr = val.ToString();
            if (valStr.Length == 1)
            {
                return "0.0" + valStr;
            }
            else if (valStr.Length == 2)
            {
                return "0." + valStr;
            }
            else
            {
                string intPart = valStr.Substring(0, valStr.Length - 2);
                string decimalPart = valStr.Substring(valStr.Length - 2);
                return $"{intPart}.{decimalPart}";
            }
        }

        public string GetUsCurrency(int goodsId)
        {
            string pid = GetProductId(goodsId);
            if (ProductInfo.ContainsKey(pid))
            {
                return ProductInfo[pid].Currency;
            }
            else
            {
                return "USD";
            }
        }

        private float GetIapConfigPrice(int mallId)
        {
            MallGoodsItem mallGoodsItem = MallGoods.Data.Get(mallId);
            iapItem item = iap.Data.Get(mallGoodsItem.iap);
            return item.sourceprice / 100.0f;
        }

        private Protocol.IAPGenGameOrderResponse m_CurOrderData;

        // 1 �ȴ������� ѯ�ʷ������ǲ��ǿ��Թ���
        // 2 ��ȡsdk ֧��
        public void purchase(Protocol.IAPGenGameOrderResponse orderData)
        {
            m_CurOrderData = orderData;
            if (RuntimeSettings.UseSDK)
            {
                int goodId = orderData.GoodId;
                string pid = GetProductId(goodId);
                LDSDKEvent.SendPurseSdkEvent(goodId, orderData.OrderId);
                if (CanCharge) // && ProductInfo.ContainsKey(pid))
                {
                    string price = GetUsPrice(goodId);
                    string usPrice = GetIapConfigPrice(goodId).ToString();
                    // string currency = ProductInfo[pid].Currency;

                    OneMT.SDK.OMTPayInfo payInfo = new OneMT.SDK.OMTPayInfo();

                    int count = 1;
                    payInfo.ProductId = pid;
                    payInfo.ProductQuantity = count;
                    payInfo.GoodsId = goodId;
                    payInfo.GoodsQuantity = count;
                    payInfo.GoodsAmount = usPrice;
                    // TODO: dqx ����������
                    payInfo.GoodsCurrency = "USD";
                    payInfo.PaidAmount = price;
                    payInfo.PaidCurrency = GetUsCurrency(goodId);
                    payInfo.PayChannel = (int)(m_PayChanel);
                    payInfo.ExtensionInfo = "";
                    payInfo.ZoneId = Global.gApp.gSdkMgr.GetZone();
                    payInfo.ServerId = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerId();
                    payInfo.GameUid = Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId().ToString();
                    payInfo.Level = Global.gApp.gSystemMgr.gRoleMgr.GetLevel();
                    payInfo.VipLevel = Global.gApp.gSystemMgr.gRoleMgr.GetVipLevel();


                    MallGoodsItem mallGoodsItem = MallGoods.Data.Get(goodId);

                    // string[] itemstrings = mallGoodsItem.item.Split(",");

                    Dictionary<string, string> items = new Dictionary<string, string>();

                    // foreach (string item in itemstrings)
                    // {
                    //     string[] itemstring = mallGoodsItem.item.Split("_");
                    //     items[itemstring[1]] = itemstring[2];
                    // }

                    LDGoodsDetail GoodsDetail = new LDGoodsDetail();
                    GoodsDetail.items = items;

                    string goodStr = JsonMapper.ToJson(GoodsDetail);
                    // TODO: dqx ���������⴦��
                    string language = Global.gApp.gGameData.GetSystemCurLanguage();
                    Hashtable bizInfo = new Hashtable();
                    bizInfo.Add("zoneId", Global.gApp.gSdkMgr.GetZone());
                    bizInfo.Add("serverId", Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerId());
                    bizInfo.Add("gameUid", Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId());
                    bizInfo.Add("roleId", 0);
                    bizInfo.Add("gameLang", language);
                    bizInfo.Add("vipLevel", Global.gApp.gSystemMgr.gRoleMgr.GetVipLevel());
                    bizInfo.Add("level", Global.gApp.gSystemMgr.gRoleMgr.GetLevel());
                    bizInfo.Add("goodsDetail", goodStr);
                    payInfo.ZoneId = Global.gApp.gSdkMgr.GetZone();
                    payInfo.BizInfo = JsonMapper.ToJson(bizInfo);


                    payInfo.OrderCreated = orderData.Timestamp;
                    payInfo.GameOrderSn = orderData.OrderId;

                    OneMT.SDK.OneMTPayment.purchase(payInfo, OnPurchaseCompleted);


                    Global.gApp.gUiMgr.gTimerMgr.AddTimer(0.01f, 1, (_, _) =>
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<PoorNetworkUI>(LDUICfg.PoorNetworkUI).SetLoadedCall(baseUI =>
                        {
                            baseUI.WaitTime = 5;
                            baseUI.MaskWaitTime = 0.1f;
                        });
                    });
                }
                else
                {
                    if (!ProductInfo.ContainsKey(pid))
                    {
                        Global.gApp.gToastMgr.ShowGameTips(95102);
                    }
                }
            }
        }

        private void OnPurchaseCompleted(OneMT.SDK.OMTPurchaseState state, string message)
        {
            Debug.Log("֧��״̬:" + state + " message:" + message);
            Global.gApp.gUiMgr.gTimerMgr.AddTimer(0.01f, 1, (_, _) => { Global.gApp.gUiMgr.CloseUI(LDUICfg.PoorNetworkUI); });

            if (m_CurOrderData == null)
            {
                return;
            }

            if (state == OMTPurchaseState.Success)
            {
                LDSDKEvent.SendChargeSdkSucessEvent(m_CurOrderData.GoodId, m_CurOrderData.OrderId);
            }
            else
            {
                LDSDKEvent.SendChargeSdkFailedEvent(m_CurOrderData.GoodId, m_CurOrderData.OrderId, state, message);
            }
        }

        public void restoreTransaction()
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTPayment.restoreTransaction();
            }
        }
    }
}