using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.AddressableAssets.ResourceLocators;
using UnityEngine.Networking;
using UnityEngine.ResourceManagement.AsyncOperations;

namespace LD
{
    public class LDEnterGame
    {
        public static List<string> AotDllList = new List<string>
                {
                    "WWW1.bytes",
                    "WWW2.bytes",
                    "WWW3.bytes",
                    "WWW4.bytes",
                    "WWW5.bytes",
                };
        public static void StartGame(LDBaseMono monoUtils)
        {
            MainUtils.OMTReportEvent("1010210");
            Debug.Log("EnterGame ");
            monoUtils.StartCoroutine(LoadAOT5());
        }
        public static IEnumerator LoadAOT5()
        {
            Debug.Log("MainApp   LoadAOT5 5");
            // 可以加载任意aot assembly的对应的dll。但要求dll必须与unity build过程中生成的裁剪后的dll一致，而不能直接使用原始dll。
            // 我们在BuildProcessor_xxx里添加了处理代码，这些裁剪后的dll在打包时自动被复制到 {项目目录}/HybridCLRData/AssembliesPostIl2CppStrip/{Target} 目录。

            /// 注意，补充元数据是给AOT dll补充元数据，而不是给热更新dll补充元数据。
            /// 热更新dll不缺元数据，不需要补充，如果调用LoadMetadataForAOTAssembly会返回错误

            LHUpdateTips.FreshTipsUI(LHUpdateTips.Str1, 0);
            yield return new WaitForSeconds(0.1f);
            // 加载结果
            bool loadResult = true;
            if (RuntimeSettings.HybridCLREnable)
            {
                yield return new WaitForSeconds(0.1f);
                foreach (var aotDllName in AotDllList)
                {
                    var aotPath = $"{MainIoUtils.StreamAssetsPath}{aotDllName}";
                    using (UnityWebRequest www = UnityWebRequest.Get(aotPath))
                    {
                        yield return www.SendWebRequest();

                        if (www.result != UnityWebRequest.Result.Success)
                        {
                            loadResult = false;
                            // 加载失败弹出提示 退出游戏。这里 不做 aot 的更新 否则 多增加好多步骤
                            Debug.LogError(www.error);
                            LHUpdateTips.AOTLoadError(aotPath);
                            break;
                        }
                        else
                        {
                            byte[] dllBytes = www.downloadHandler.data;
                            AOTErrorCode err = (AOTErrorCode)HybridCLR.RuntimeApi.LoadMetadataForAOTAssembly(dllBytes, HybridCLR.HomologousImageMode.SuperSet);
                            Debug.Log($"Main LoadMetadataForAOTAssembly:{aotDllName} ret:{err}");
                        }
                    }
                }
            }
            if (loadResult)
            {
                StartGameImp();
            }
        }
        private static void StartGameImp()
        {
            InitAddressables();
            LoadGameDLL();
            LoadKeepNode();
        }
        public static void LoadGameDLL()
        {
            Debug.Log(" MainUpdate Main LoadGameDLL LoadGameDLL  ");
            if (RuntimeSettings.HybridCLREnable)
            {
                string bundlePath = MainIoUtils.GameDLLBundlePath;
                Debug.Log(bundlePath);

                Debug.Log(" MainUpdate Main Load sdcard bundlePath ");
                AsyncOperationHandle<TextAsset> operationHandle = Addressables.LoadAssetAsync<TextAsset>(bundlePath);
                TextAsset assetBundle = operationHandle.WaitForCompletion();
                System.Reflection.Assembly.Load(assetBundle.bytes);
                Debug.Log(" MainUpdate Main Load Game Ended");

                //if (System.IO.File.Exists(bundlePath))
                //{
                   
                //    byte[] assemblyData = System.IO.File.ReadAllBytes(bundlePath);
    

                //}
            }
        }
        public static void LoadKeepNode()
        {
            ReloadAddressables();
            AsyncOperationHandle<GameObject> keepNodeHandle = Addressables.InstantiateAsync("PrefabsN/KeepNode.prefab");
            keepNodeHandle.WaitForCompletion();
        }

        private static void InitAddressables()
        {
#if USE_ADDRESSABLES
            AsyncOperationHandle<IResourceLocator> init = Addressables.InitializeAsync();
            init.WaitForCompletion();
            ReloadAddressables();
#endif

        }
        private static void ReloadAddressables()
        {
#if USE_ADDRESSABLES
            string catalogFile = MainIoUtils.BundlePath + "/catalog.json";
            Debug.Log(" InitAddressables 111 " + catalogFile);
            if (!File.Exists(catalogFile))
            {
                return;
            }
            Debug.Log(" InitAddressables 2222 " + catalogFile);
            Addressables.ClearResourceLocators();
            AsyncOperationHandle<IResourceLocator> loadLog = Addressables.LoadContentCatalogAsync(catalogFile);
            loadLog.WaitForCompletion();
#endif

        }
    }
}
