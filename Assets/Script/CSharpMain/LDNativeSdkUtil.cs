using OneMT.SDK;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDNativeSdkUtil
    {
        // 初始化sdk
        public static void InitSDK(Action OnInitSdk)
        {
            MainUtils.OMTReportEvent("1010120");
            if (RuntimeSettings.UseSDK)
            {
                OneMT.SDK.OneMTSDK.Init(() =>
                {

                    MainUtils.OMTReportEvent("1010130");
                    MainUtils.OMTReportEvent("1010140");
                    TryRegistCMP();
                    OnInitSdk();
                });
            }
            else
            {
                OnInitSdk();
            }
        }
        public static void TryRegistCMP()
        {
            Action<bool> callback = delegate (bool isArgee) {
                if (!isArgee)
                {
                    //执行退出游戏
#if UNITY_EDITOR
                        UnityEditor.EditorApplication.isPlaying = false;
#else
                        Application.Quit();
#endif
                }
            };
            OneMTTerms.RegisterTermsPrivacyAgreementCallback(callback);
        }

        public static List<string> CMPCountry = new List<string>()
        {          
            "AT","BE","BG","CY","CZ","DE","DK",
            "EE","ES","FI","FR","GB","GR","HR","HU",
            "IE","IT","LT","LU","LV","MT","NL",
            "PL","RO","SE","SI","SK","CH"
        };
       
        public static void ShowGDPR3(Action OnShowGDPR, UpdateVer RomateResVersion)
        {
            if (RuntimeSettings.UseSDK)
            {
                Debug.Log("MainApp ShowGDPR3  3");
                RuntimeSettings.SyncCountry(RomateResVersion.MainVersionData.country);
                bool cmpState = false;
                if (RuntimeSettings.PackageType == PackageType.EN)
                {
                    cmpState = CMPCountry.Contains(RomateResVersion.MainVersionData.country);
                }
                RuntimeSettings.ISCMPEnabled = cmpState;
                // 调用SDK接口初始化第三方SDK
                var options = new Hashtable
                {
                    // 如果游戏面向欧盟地区，可以在版本更新接口中获取到玩家位置，判断是否是EEA地区，如果是EEA地区，则CMP传true，否则传false，不影响EEA地区外的用户
                    // 这就得改 更新逻辑了。可能得先拉去 版本文件 
                    ["cmp"] = cmpState
                };
                OneMT.SDK.OneMTSDK.InitThirdPartySDKs(options, delegate ()
                {
                    MainUtils.OMTReportEvent("1010150");

                    Debug.LogFormat("第三方SDK初始化完成");
                    OnShowGDPR();
                // 启动依赖SDK接口的场景
                });
            }
            else
            {
                OnShowGDPR();
            }
        }
        public static bool NetOk()
        {
            return true;
        }
    }
}
