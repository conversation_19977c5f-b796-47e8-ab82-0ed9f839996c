using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Data;
using Excel;
using System.Security.Cryptography;
using System;
using UnityEditor.Build.Content;

public class PackerEditor : EditorWindow
{

    [MenuItem("打包/IOS/IOSDebug包")]
    static void PackerIosDebug()
    {
        PackerIosEditor window = GetWindow<PackerIosEditor>("IOSDebug包");
        window.OnInit(true);
        window.autoRepaintOnSceneChange = true;
        window.Show();

    }
    [MenuItem("打包/IOS/IOS正式包")]
    static void PackerIosOfficial()
    {
        PackerIosEditor window = GetWindow<PackerIosEditor>("IOS正式包");
        window.OnInit(false);
        window.autoRepaintOnSceneChange = true;
        window.Show();

    }
    [MenuItem("打包/Android/AndroidDebug包")]
    static void PackerAndroidDebug()
    {
        PackerAndroidEditor window = GetWindow<PackerAndroidEditor>("AndroidDebug包");
        window.OnInit(true);
        window.autoRepaintOnSceneChange = true;
        window.Show();

    }
    [MenuItem("打包/Android/Android正式包")]
    static void PackerAndroidOfficial()
    {
        PackerAndroidEditor window = GetWindow<PackerAndroidEditor>("Android正式包");
        window.OnInit(false);
        window.autoRepaintOnSceneChange = true;
        window.Show();

    }
}