using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class ReplectShader : EditorWindow
{
    private List<Material> matList = new List<Material>();
    private static Shader targetShader;
    private static Shader RepShader;
    private GUIStyle style;
    
    [MenuItem("TATools/ReplectShader")]
    static void myWin()
    {
        
        var win = EditorWindow.GetWindow<ReplectShader>();
        win.Show();
    }

    private void OnGUI()
    {

        style = new GUIStyle();
        style.normal.textColor = Color.red;
        GUILayout.Label("目标Shader");
        targetShader = EditorGUILayout.ObjectField(targetShader, typeof(Shader), true) as Shader;
        if (GUILayout.Button("检查 Shader"))
        {
            matList.Clear();
            GameObject obj = Selection.activeGameObject;
            checkObject(obj);
            foreach (Material mat in matList)
            {
                if (mat !=null)//粒子系统的render 内的material可能为空
                {
                    if (mat.shader == targetShader)
                    {
                        Debug.LogError(mat.name);

                    }
                }
            }
        }
        GUILayout.Label("替换Shader");
        RepShader = EditorGUILayout.ObjectField(RepShader, typeof(Shader), true) as Shader;
        if (GUILayout.Button("替换 shader"))
        {
            matList.Clear();
            GameObject obj = Selection.activeGameObject;
            checkObject(obj);
            Debug.Log("replect shader!~!"+":"+matList.Count);
            foreach (Material mat in matList)
            {
                if (mat !=null)//粒子系统的render 内的material可能为空
                {
                    Debug.Log(mat.name);
                    if (mat.shader == targetShader||mat.shader == Shader.Find("Hidden/InternalErrorShader"))
                    {
                        mat.shader = RepShader;
                        EditorUtility.SetDirty(mat);
                    }
                }
            }
            
            AssetDatabase.SaveAssets();
        }
        
        GUILayout.Label("使用说明：" +
                        "\n填入目标Shader后可以在Hierarchy内点选物体，" +
                        "\n查找该物体及其子物体是否使用目标Shader." +
                        "\n填入目标Shader后可以在Hierarychy内点选物体" +
                        "\n替换该物体及其子物体上的目标Shader",style);
    }


    public void checkObject(GameObject obj)
    {
        
        Renderer rd;
        if (obj.TryGetComponent<Renderer>(out rd))
        {
            Material[] mats = rd.sharedMaterials;
            if (mats.Length>0)
            {
                foreach (var mat in mats)
                {
                    if (!matList.Contains(mat))
                    {
                        matList.Add(mat);
                    }
                }
            }
        }
        
        
        if (obj.transform.childCount == 0)
        {
            return;
        }

        for (int i =0;i<obj.transform.childCount;i++) {
            checkObject(obj.transform.GetChild(i).gameObject);
        }

    }

}
