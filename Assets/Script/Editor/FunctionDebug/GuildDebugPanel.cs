using System.Collections.Generic;
using System.Text;
using UnityEditor;
using UnityEngine;
using LD;
using LitJson;

public class GuildDebugPanel : EditorWindow
{
    private StringBuilder displayedText = new StringBuilder();
    private Vector2 scrollPosition;
    
    private const float windowWidth = 600f;
    private const float windowHeight = 800f;

    [MenuItem("Tools/Debug/公会")]
    public static void ShowWindow()
    {
        GuildDebugPanel window = GetWindow<GuildDebugPanel>("公会Debug");
        window.minSize = new Vector2(windowWidth, windowHeight);
        window.maxSize = new Vector2(windowWidth, windowHeight);
        window.Show();
    }

    private void OnGUI()
    {
        GUILayout.BeginVertical();

        LDNetGuildMgr guildMgr = Global.gApp.gSystemMgr.gGuildMgr;
        LDNetGuildDonationMgr guildDonationMgr = Global.gApp.gSystemMgr.gGuildDonationMgr;
        LDNetGuildBargainMgr guildBargainMgr = Global.gApp.gSystemMgr.gGuildBargainMgr;
        LDNetGuildBossMgr guildBossMgr = Global.gApp.gSystemMgr.gGuildBossMgr;
        if (GUILayout.Button("我的公会信息"))
        {
            displayedText.Clear();
            if (guildMgr.Data.GuildId > 0)
            {
                displayedText.AppendLine("数据可能不全  有的字段服务器没给就是默认值");
                displayedText.AppendLine();
                
                LDGuildInfo guildInfo = guildMgr.GetGuildInfo();
                displayedText.AppendLine("基本信息 : " + JsonMapper.ToJson(guildInfo.BriefInfo));
                displayedText.AppendLine("最后一次修改Logo的时间 : " + guildInfo.LastModifyLogoTime);
                displayedText.AppendLine("最后一次改名时间 : " + guildInfo.LastRenameTime);
                displayedText.AppendLine("公会资金 : " + guildInfo.GuildCoin);
                
                displayedText.AppendLine();
                displayedText.AppendLine("公会成员:");
                foreach (KeyValuePair<long,LDGuildMemberInfo> memberInfo in guildInfo.Members)
                {
                    displayedText.AppendLine(JsonMapper.ToJson(memberInfo.Value));
                    displayedText.AppendLine();
                }
            }
            else
            {
                displayedText.AppendLine("没有公会");
            }
        }
        if (GUILayout.Button("公会捐献数据"))
        {
            displayedText.Clear();
            if (guildMgr.Data.GuildId > 0)
            {
                displayedText.AppendLine(JsonMapper.ToJson(guildDonationMgr.Data));
            }
            else
            {
                displayedText.AppendLine("没有公会");
            }
        }
        if (GUILayout.Button("公会议价数据"))
        {
            displayedText.Clear();
            if (guildMgr.Data.GuildId > 0)
            {
                displayedText.AppendLine("CfdId : " + guildBargainMgr.Data.BargainData.CfdId);
                displayedText.AppendLine("CurPrice : " + guildBargainMgr.Data.BargainData.CurPrice);
                
                displayedText.AppendLine();
                displayedText.AppendLine("日志:");
                foreach (LDGuildBarginLog log in guildBargainMgr.Data.BargainData.Logs.Values)
                {
                    displayedText.AppendLine(JsonMapper.ToJson(log));
                    displayedText.AppendLine();
                }
                
                displayedText.AppendLine();
                displayedText.AppendLine("玩家数据:");
                displayedText.AppendLine(JsonMapper.ToJson(guildBargainMgr.Data.BargainPlayerData));
            }
            else
            {
                displayedText.AppendLine("没有公会");
            }
        }
        if (GUILayout.Button("公会Boss数据"))
        {
            displayedText.Clear();
            if (guildMgr.Data.GuildId > 0)
            {
                displayedText.AppendLine("公会数据:");
                displayedText.AppendLine(JsonMapper.ToJson(guildBossMgr.Data.GuildBossData));
                
                displayedText.AppendLine();
                displayedText.AppendLine("玩家数据:");
                displayedText.AppendLine(JsonMapper.ToJson(guildBossMgr.Data.PlayerData));
                
                displayedText.AppendLine();
                displayedText.AppendLine("公会内伤害数据:");
                displayedText.AppendLine(JsonMapper.ToJson(guildBossMgr.Data.RankData));
            }
            else
            {
                displayedText.AppendLine("没有公会");
            }
        }









        #region

        GUILayout.FlexibleSpace();
        DrawSeparatorLine();
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(500));
        EditorGUILayout.LabelField(displayedText.ToString(), EditorStyles.wordWrappedLabel);
        EditorGUILayout.EndScrollView();

        #endregion
        
        GUILayout.EndVertical();
    }
    
    

    private void DrawSeparatorLine()
    {
        Color originalColor = GUI.color;
        GUI.color = Color.gray;
        GUILayout.Space(10);
        EditorGUI.DrawRect(GUILayoutUtility.GetRect(0, 2), GUI.color);
        GUI.color = originalColor;
    }
}