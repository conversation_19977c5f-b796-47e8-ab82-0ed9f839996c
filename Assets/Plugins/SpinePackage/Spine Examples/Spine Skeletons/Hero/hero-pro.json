{"skeleton": {"hash": "1GKrwevyJb4", "spine": "4.2.22", "x": -85.7, "y": -0.02, "width": 321.77, "height": 330.12, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "hip", "parent": "root", "y": 94.89}, {"name": "body", "parent": "hip", "length": 60.79, "rotation": 96.39, "x": 4.09, "y": 3.18}, {"name": "look-target", "parent": "root", "x": 112.83, "y": 218.21, "color": "8a009bff"}, {"name": "look-constraint-goal", "parent": "look-target", "y": -43.82, "color": "ff3f00ff"}, {"name": "head", "parent": "body", "length": 24.35, "rotation": -92.69, "x": 74.55, "y": -4.84}, {"name": "bone2", "parent": "head", "x": 12.91, "y": 110.65}, {"name": "mantles", "parent": "body", "rotation": -2.25, "x": 48.48, "y": -9}, {"name": "cape-root", "parent": "mantles", "rotation": -90.45, "x": 19.62, "y": 14.01}, {"name": "cape1", "parent": "cape-root", "length": 29.18, "rotation": -102.96, "x": -2.88, "y": -14.83}, {"name": "cape2", "parent": "cape1", "length": 30.14, "rotation": 2.27, "x": 29.18, "y": -0.06}, {"name": "cape3", "parent": "cape2", "length": 32.51, "rotation": 1.8, "x": 30.14, "y": 0.23}, {"name": "cape4", "parent": "cape3", "length": 33.02, "rotation": 4.95, "x": 32.51, "y": 0.07}, {"name": "left-ground", "parent": "root", "x": -31.29, "y": 0.26, "color": "00ff00ff"}, {"name": "left-ankle", "parent": "left-ground", "x": 0.26, "y": 33.06, "color": "ff3f00ff"}, {"name": "thigh1", "parent": "hip", "length": 23.95, "rotation": -107.07, "x": -17.52, "y": -5.23}, {"name": "shin1", "parent": "thigh1", "length": 28.24, "rotation": 10.27, "x": 29.77, "y": -0.91}, {"name": "foot1", "parent": "shin1", "length": 27.08, "rotation": -90.96, "x": 28.17, "y": -0.49, "inherit": "noRotationOrReflection"}, {"name": "right-ground", "parent": "root", "x": 28.12, "y": 0.26, "color": "ff0000ff"}, {"name": "right-ankle", "parent": "right-ground", "y": 31.79, "color": "ff3f00ff"}, {"name": "thigh2", "parent": "hip", "length": 23.66, "rotation": -85.46, "x": 21.86, "y": -5.87}, {"name": "shin2", "parent": "thigh2", "length": 25.35, "rotation": -2.25, "x": 31.99, "y": 2.97}, {"name": "foot2", "parent": "shin2", "length": 22.29, "rotation": -90.42, "x": 25.55, "y": 0.28, "inherit": "noRotationOrReflection"}, {"name": "upper-arm1", "parent": "body", "length": 19.36, "rotation": 130.11, "x": 52.5, "y": 41.85}, {"name": "forearm1", "parent": "upper-arm1", "length": 22.06, "rotation": 30.56, "x": 23.97, "y": 5.16}, {"name": "upper-arm2", "parent": "body", "length": 25.99, "rotation": -178.96, "x": 57.95, "y": -38.94}, {"name": "forearm2", "parent": "upper-arm2", "length": 15.3, "rotation": 6.8, "x": 28.74, "y": -0.9}, {"name": "hair01", "parent": "head", "x": 60.53, "y": 86.71}, {"name": "hand1", "parent": "forearm1", "length": 28.01, "rotation": 16.24, "x": 27.54, "y": 0.45}, {"name": "hand2", "parent": "forearm2", "length": 23.77, "rotation": 1.35, "x": 22.8, "y": -0.22}, {"name": "weapon-sword", "parent": "hand2", "length": 157.4, "rotation": 77.91, "x": 15.98, "y": 1.43, "skin": true}, {"name": "weapon-morningstar", "parent": "hand2", "length": 98.14, "rotation": 77.91, "x": 15.98, "y": 1.43, "skin": true}, {"name": "chain1", "parent": "weapon-morningstar", "length": 8, "x": 97.57, "skin": true, "color": "ff0000ff"}, {"name": "chain2", "parent": "chain1", "length": 8, "x": 8, "skin": true, "color": "ff0000ff"}, {"name": "chain3", "parent": "chain2", "length": 8, "x": 8, "skin": true, "color": "ff0000ff"}, {"name": "chain4", "parent": "chain3", "length": 8, "x": 8, "skin": true, "color": "ff0000ff"}, {"name": "chain5", "parent": "chain4", "length": 8, "x": 8, "skin": true, "color": "ff0000ff"}, {"name": "chain6", "parent": "chain5", "length": 8, "x": 8, "skin": true, "color": "ff0000ff"}, {"name": "chain7", "parent": "chain6", "length": 8, "x": 8, "skin": true, "color": "ff0000ff"}, {"name": "chain8", "parent": "chain7", "length": 8, "x": 8, "skin": true, "color": "ff0000ff"}, {"name": "weapon-morningstar-physics1", "parent": "weapon-morningstar", "length": 15.94, "x": 98.35, "y": -0.01, "skin": true}, {"name": "weapon-morningstar-physics2", "parent": "weapon-morningstar-physics1", "length": 15.94, "x": 15.94, "skin": true}, {"name": "weapon-morningstar-physics3", "parent": "weapon-morningstar-physics2", "length": 15.94, "x": 15.94, "skin": true}, {"name": "weapon-morningstar-physics4", "parent": "weapon-morningstar-physics3", "length": 15.94, "x": 15.94, "skin": true}], "slots": [{"name": "chain-ball", "bone": "chain8", "attachment": "chain-ball"}, {"name": "chain-round", "bone": "chain2", "attachment": "chain-round"}, {"name": "chain-round2", "bone": "chain4", "attachment": "chain-round"}, {"name": "chain-round3", "bone": "chain6", "attachment": "chain-round"}, {"name": "chain-flat", "bone": "chain1", "attachment": "chain-flat"}, {"name": "chain-flat2", "bone": "chain3", "attachment": "chain-flat"}, {"name": "chain-flat3", "bone": "chain5", "attachment": "chain-flat"}, {"name": "chain-flat4", "bone": "chain7", "attachment": "chain-flat"}, {"name": "weapon-morningstar-path", "bone": "weapon-morningstar", "attachment": "weapon-morningstar-path"}, {"name": "cape", "bone": "cape-root", "attachment": "cape"}, {"name": "upper-arm2", "bone": "upper-arm2", "attachment": "upper-arm2"}, {"name": "sword", "bone": "root"}, {"name": "hand2", "bone": "hand2", "attachment": "hand2"}, {"name": "weapon-sword", "bone": "weapon-sword", "attachment": "sword"}, {"name": "handle", "bone": "weapon-morningstar", "attachment": "handle"}, {"name": "fingers", "bone": "hand2", "attachment": "fingers"}, {"name": "forearm2", "bone": "forearm2", "attachment": "forearm2"}, {"name": "thigh2", "bone": "thigh2", "attachment": "thigh2"}, {"name": "foot2", "bone": "foot2", "attachment": "foot2"}, {"name": "shin2", "bone": "shin2", "attachment": "shin2"}, {"name": "thigh1", "bone": "thigh1", "attachment": "thigh1"}, {"name": "foot1", "bone": "foot1", "attachment": "foot1"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "shin1", "bone": "shin1", "attachment": "shin1"}, {"name": "upper-arm1", "bone": "upper-arm1", "attachment": "upper-arm1"}, {"name": "mantles", "bone": "mantles", "attachment": "mantles"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "mouth", "bone": "head", "attachment": "mouth"}, {"name": "eyes", "bone": "head", "attachment": "eyes"}, {"name": "forearm1", "bone": "forearm1", "attachment": "forearm1"}, {"name": "hand1", "bone": "hand1", "attachment": "hand1"}], "ik": [{"name": "left-leg", "bones": ["thigh1", "shin1"], "target": "left-ankle", "bendPositive": false}, {"name": "look-constraint", "order": 1, "bones": ["head"], "target": "look-constraint-goal", "mix": 0}, {"name": "right-leg", "order": 2, "bones": ["thigh2", "shin2"], "target": "right-ankle", "bendPositive": false}], "path": [{"name": "weapon-morningstar", "order": 3, "skin": true, "bones": ["chain1", "chain2", "chain3", "chain4", "chain5", "chain6", "chain7", "chain8"], "target": "weapon-morningstar-path", "rotateMode": "chain"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [1, 0.11883, 1, 0.46367, 0.9431, 0.58541, 0.89886, 0.70683, 0.94216, 0.8262, 0.75814, 1, 0.59793, 1, 0.15412, 0.85494, 0, 0.87041, 0.06103, 0.65599, 0.03757, 0.48937, 0, 0.32662, 0, 0, 0.23422, 0, 0.72783, 0, 0.90102, 0, 0.6802, 0.75104, 0.74731, 0.56315, 0.23001, 0.54172, 0.31432, 0.27357, 0.19509, 0.68582, 0.73753, 0.2804], "triangles": [6, 16, 5, 4, 16, 3, 4, 5, 16, 7, 20, 6, 6, 20, 16, 8, 9, 7, 7, 9, 20, 20, 18, 16, 16, 17, 3, 16, 18, 17, 3, 17, 2, 20, 9, 18, 9, 10, 18, 2, 17, 1, 18, 19, 17, 19, 21, 17, 17, 21, 1, 18, 10, 19, 10, 11, 19, 21, 0, 1, 11, 13, 19, 11, 12, 13, 19, 14, 21, 21, 15, 0, 21, 14, 15, 19, 13, 14], "vertices": [63.56, -47.37, 30.84, -45.83, 19.55, -39.78, 8.23, -34.95, -3.3, -38.61, -18.95, -20.01, -18.22, -4.48, -2.44, 37.87, -3.2, 52.87, 16.87, 46, 32.78, 47.53, 48.4, 50.45, 79.39, 48.99, 78.33, 26.3, 76.08, -21.53, 75.29, -38.31, 5.03, -13.57, 22.55, -20.91, 26.94, 29.12, 52, 19.75, 13.42, 33.15, 49.43, -21.22], "hull": 16, "edges": [12, 32, 32, 34, 36, 38, 16, 14, 14, 12, 10, 12, 10, 8, 8, 6, 4, 34, 6, 4, 4, 2, 2, 0, 28, 30, 0, 30, 14, 40, 40, 36, 20, 36, 20, 18, 18, 16, 22, 24, 20, 22, 24, 26, 26, 28, 38, 26, 28, 42, 42, 34, 38, 42, 42, 0, 40, 18, 40, 32, 36, 34], "width": 97, "height": 95}}, "cape": {"cape": {"type": "mesh", "uvs": [0.1185, 0.14746, 0.16674, 0.03102, 0.25, 0, 0.50474, 0.02792, 0.74709, 0.01516, 0.99999, 0.00325, 0.96323, 0.13434, 0.90539, 0.25241, 0.87922, 0.38065, 0.87796, 0.49955, 0.8814, 0.59065, 0.87679, 0.69448, 0.89331, 0.79565, 0.89617, 0.88415, 0.78954, 0.94212, 0.55644, 1, 0.27942, 0.99999, 0, 0.99402, 0.005, 0.88058, 0.0478, 0.75594, 0.05868, 0.63529, 0.05398, 0.49965, 0.06713, 0.37447, 0.08837, 0.25291, 0.25, 0.25, 0.25, 0.5, 0.25, 0.75, 0.5, 0.25, 0.5, 0.5, 0.5, 0.75, 0.70676, 0.24429, 0.71223, 0.49778, 0.71678, 0.73222, 0.26778, 0.88482, 0.24726, 0.62999, 0.25099, 0.38322, 0.25091, 0.1454, 0.53149, 0.8812, 0.50274, 0.62966, 0.49809, 0.38546, 0.49804, 0.14766, 0.72162, 0.13609, 0.71721, 0.38044, 0.71327, 0.61493, 0.73166, 0.83027], "triangles": [16, 37, 15, 15, 44, 14, 15, 37, 44, 16, 17, 33, 17, 18, 33, 16, 33, 37, 14, 44, 13, 18, 19, 33, 19, 26, 33, 33, 29, 37, 33, 26, 29, 44, 12, 13, 44, 29, 32, 44, 37, 29, 44, 32, 12, 32, 11, 12, 19, 20, 26, 29, 38, 32, 38, 29, 34, 20, 34, 26, 29, 26, 34, 38, 43, 32, 32, 43, 11, 11, 43, 10, 20, 21, 34, 38, 34, 25, 34, 21, 25, 38, 28, 43, 38, 25, 28, 28, 31, 43, 10, 31, 9, 10, 43, 31, 25, 39, 28, 28, 39, 31, 21, 22, 25, 25, 35, 39, 25, 22, 35, 31, 42, 9, 9, 42, 8, 31, 39, 42, 39, 30, 42, 39, 35, 27, 22, 23, 35, 39, 27, 30, 23, 24, 35, 35, 24, 27, 7, 8, 30, 8, 42, 30, 23, 0, 24, 30, 41, 7, 7, 41, 6, 24, 40, 27, 27, 40, 30, 24, 36, 40, 24, 0, 36, 30, 40, 41, 40, 36, 3, 0, 1, 36, 1, 2, 36, 40, 3, 41, 36, 2, 3, 41, 4, 6, 41, 3, 4, 6, 4, 5], "vertices": [4, 8, -71.42, -9.01, 0.19922, 9, 9.7, -68.1, 0.51839, 10, -22.16, -67.21, 0.2208, 11, -54.39, -65.77, 0.06158, 3, 8, -64.05, 10.63, 0.32136, 9, -11.1, -65.32, 0.49514, 10, -42.83, -63.61, 0.1835, 3, 8, -47.48, 12.59, 0.47523, 9, -16.72, -49.62, 0.40307, 10, -47.82, -47.7, 0.1217, 3, 8, -10.3, 8.14, 0.61256, 9, -20.72, -12.38, 0.3139, 10, -50.35, -10.34, 0.07354, 2, 8, 25.03, 10.17, 0.71927, 9, -30.62, 21.59, 0.28073, 3, 8, 62.12, 12.06, 0.63193, 9, -40.79, 57.31, 0.29857, 10, -67.64, 60.1, 0.0695, 3, 8, 61.2, -8.79, 0.48664, 9, -20.26, 61.09, 0.40198, 10, -46.98, 63.06, 0.11139, 3, 8, 55.31, -27.56, 0.311, 9, -0.64, 59.56, 0.46267, 10, -27.44, 60.76, 0.22633, 4, 8, 53.89, -47.96, 0.15982, 9, 19.55, 62.76, 0.3211, 10, -7.14, 63.15, 0.39837, 11, -35.28, 64.06, 0.12071, 4, 9, 37.55, 68.86, 0.2105, 10, 11.09, 68.54, 0.45133, 11, -16.9, 68.87, 0.24079, 12, -43.28, 72.81, 0.09738, 4, 9, 51.48, 72.29, 0.11398, 10, 25.15, 71.41, 0.30517, 11, -2.75, 71.31, 0.3666, 12, -28.98, 74.01, 0.21425, 4, 9, 67.41, 75.98, 0.06133, 10, 41.21, 74.47, 0.21192, 11, 13.39, 73.85, 0.34248, 12, -12.68, 75.16, 0.38427, 3, 10, 57.55, 74.68, 0.1544, 11, 29.74, 73.55, 0.2615, 12, 3.58, 73.45, 0.5841, 3, 10, 72.56, 71.46, 0.15972, 11, 44.63, 69.86, 0.25839, 12, 18.1, 68.48, 0.58189, 3, 10, 86.26, 49.58, 0.11057, 11, 57.64, 47.57, 0.19478, 12, 29.13, 45.15, 0.69465, 3, 10, 101.65, 17.61, 0.05811, 11, 72.02, 15.13, 0.13319, 12, 40.66, 11.59, 0.8087, 2, 11, 77.68, -21.16, 0.10506, 12, 43.17, -25.04, 0.89494, 2, 11, 82.32, -57.07, 0.12718, 12, 44.7, -61.22, 0.87282, 2, 11, 64.02, -59.3, 0.199, 12, 26.27, -61.87, 0.801, 3, 10, 75.09, -54.58, 0.09513, 11, 43.2, -56.2, 0.34248, 12, 5.8, -56.98, 0.56238, 4, 9, 87.96, -55.81, 0.05669, 10, 56.53, -58.04, 0.20747, 11, 24.54, -59.07, 0.43085, 12, -13.04, -58.23, 0.30499, 4, 9, 67.91, -63.28, 0.13425, 10, 36.2, -64.7, 0.39067, 11, 4.02, -65.09, 0.32983, 12, -34.01, -62.46, 0.14526, 4, 9, 48.11, -66.22, 0.28011, 10, 16.29, -66.86, 0.44142, 11, -15.95, -66.62, 0.21855, 12, -54.03, -62.26, 0.05992, 4, 8, -75.22, -27.48, 0.1004, 9, 28.56, -67.66, 0.4694, 10, -3.3, -67.52, 0.30881, 11, -35.55, -66.67, 0.12139, 4, 8, -47.49, -27.14, 0.10099, 9, 22, -40.72, 0.5792, 10, -8.78, -40.34, 0.24218, 11, -40.18, -39.33, 0.07763, 4, 9, 60.74, -31.82, 0.10974, 10, 30.28, -32.98, 0.52099, 11, -0.9, -33.2, 0.28166, 12, -36.16, -30.26, 0.08761, 3, 10, 69.34, -25.62, 0.08322, 11, 38.37, -27.07, 0.37559, 12, 3.5, -27.54, 0.54119, 3, 8, -11, -27.16, 0.1472, 9, 13.84, -5.15, 0.64873, 10, -15.53, -4.48, 0.20407, 4, 9, 52.58, 3.76, 0.11818, 10, 23.53, 2.89, 0.58493, 11, -6.52, 2.87, 0.23848, 12, -38.65, 6.16, 0.05841, 3, 10, 62.59, 10.25, 0.10547, 11, 32.75, 8.99, 0.3944, 12, 1, 8.87, 0.50013, 3, 8, 19.19, -26.26, 0.22092, 9, 6.19, 24.07, 0.59351, 10, -22.01, 25.02, 0.18557, 4, 9, 45.29, 33.88, 0.1594, 10, 17.45, 33.27, 0.56248, 11, -11.65, 33.43, 0.21653, 12, -41.12, 37.04, 0.06159, 3, 10, 53.95, 40.83, 0.15601, 11, 25.07, 39.83, 0.3821, 12, -3.98, 40.25, 0.4619, 2, 11, 59.53, -23.61, 0.15185, 12, 24.88, -25.93, 0.84815, 3, 10, 50.67, -29.57, 0.18548, 11, 19.58, -30.43, 0.58615, 12, -15.51, -29.27, 0.22837, 3, 9, 42.62, -35.83, 0.24758, 10, 12.01, -36.28, 0.58035, 11, -19.27, -35.92, 0.17206, 3, 8, -47.36, -10.52, 0.25508, 9, 5.77, -44.31, 0.57704, 10, -25.14, -43.29, 0.16788, 2, 11, 53.31, 12.48, 0.14052, 12, 21.8, 10.57, 0.85948, 3, 10, 43.71, 7.1, 0.2088, 11, 13.78, 6.44, 0.61685, 12, -18.11, 7.96, 0.17434, 3, 9, 34.89, -0.59, 0.25689, 10, 5.69, -0.76, 0.61727, 11, -24.47, -0.22, 0.12584, 3, 8, -11.28, -10.89, 0.33923, 9, -1.95, -9.07, 0.54818, 10, -31.46, -7.77, 0.1126, 3, 8, 20.81, -9.06, 0.42572, 9, -10.94, 21.8, 0.48141, 10, -39.22, 23.43, 0.09287, 4, 8, 20.73, -47.91, 0.09789, 9, 26.94, 30.43, 0.27355, 10, -1.03, 30.56, 0.52393, 11, -30.2, 31.29, 0.10463, 4, 9, 63.4, 38.25, 0.0725, 10, 35.71, 36.92, 0.2459, 11, 6.72, 36.5, 0.51803, 12, -22.55, 38.52, 0.16357, 3, 10, 69.45, 42.75, 0.0871, 11, 40.62, 41.26, 0.18925, 12, 11.64, 40.34, 0.72365], "hull": 24, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 2, 0, 0, 46, 34, 36, 36, 38, 18, 20, 20, 22, 14, 16, 16, 18, 10, 12, 12, 14, 36, 66, 66, 74, 74, 88, 38, 52, 52, 58, 58, 64, 64, 22, 40, 68, 68, 76, 76, 86, 86, 20, 42, 50, 50, 56, 56, 62, 62, 18, 44, 70, 70, 78, 78, 84, 84, 16, 46, 48, 48, 54, 54, 60, 60, 14, 0, 72, 72, 80, 80, 82, 82, 12, 26, 28, 22, 24, 24, 26, 88, 24], "width": 146, "height": 159}}, "chain-ball": {"chain-ball": {"x": 34.46, "scaleX": 1.0961, "scaleY": 1.0961, "width": 64, "height": 68}}, "chain-flat": {"chain-flat": {"x": 7.5, "width": 16, "height": 5}}, "chain-flat2": {"chain-flat": {"x": 7.5, "width": 16, "height": 5}}, "chain-flat3": {"chain-flat": {"x": 7.5, "width": 16, "height": 5}}, "chain-flat4": {"chain-flat": {"x": 7.5, "width": 16, "height": 5}}, "chain-round": {"chain-round": {"x": 8, "width": 17, "height": 14}}, "chain-round2": {"chain-round": {"x": 8, "width": 17, "height": 14}}, "chain-round3": {"chain-round": {"x": 8, "width": 17, "height": 14}}, "eyes": {"eyes": {"type": "mesh", "uvs": [1, 1, 0.7185, 1, 0.5355, 1, 0, 1, 0, 0, 0.5355, 0, 0.7275, 0, 1, 0], "triangles": [1, 6, 0, 6, 7, 0, 6, 1, 5, 3, 5, 2, 1, 2, 5, 3, 4, 5], "vertices": [47.52, 35.34, 24.43, 35.29, 9.43, 35.26, -34.48, 35.16, -34.55, 66.16, 9.36, 66.26, 25.11, 66.29, 47.45, 66.34], "hull": 8, "edges": [6, 8, 0, 14, 4, 6, 8, 10, 4, 10, 0, 2, 2, 4, 10, 12, 12, 14, 2, 12], "width": 82, "height": 31}}, "fingers": {"fingers": {"x": 16.22, "y": 1.37, "rotation": 82.36, "width": 31, "height": 33}}, "foot1": {"foot1": {"x": 12.55, "y": 0.12, "rotation": 90.96, "width": 50, "height": 42}}, "foot2": {"foot2": {"x": 8.83, "y": 2.92, "rotation": 90.42, "width": 53, "height": 38}}, "forearm1": {"forearm1": {"x": 11.88, "y": -5.04, "rotation": 106.62, "width": 41, "height": 49}}, "forearm2": {"forearm2": {"x": 13.09, "y": -0.64, "rotation": 83.72, "width": 31, "height": 32}}, "hand1": {"hand1": {"x": 11.93, "y": -0.7, "rotation": 90.37, "width": 37, "height": 48}}, "hand2": {"hand2": {"x": 14.24, "y": 1.64, "rotation": 82.36, "width": 31, "height": 37}}, "handle": {"handle": {"x": 38.91, "width": 134, "height": 14}}, "head": {"head": {"type": "mesh", "uvs": [0.89191, 0.07465, 0.88062, 0.30104, 0.99999, 0.35067, 0.85131, 0.46335, 0.74578, 0.50611, 0.76615, 0.75424, 0.77424, 1, 0.52642, 1, 0.25139, 1, 0.15427, 0.83409, 0.05901, 0.67136, 0, 0.57054, 0.10296, 0.27407, 0.23, 0.14511, 0.45598, 0.0454, 0.71273, 0, 0.62503, 0.49225, 0.4167, 0.41377, 0.25215, 0.44757, 0.60099, 0.22733, 0.43093, 0.21786, 0.55163, 0.49889, 0.54698, 0.72069, 0.2887, 0.55971, 0.30157, 0.73525, 0.67864, 0.75067, 0.77674, 0.36167, 0.69014, 0.298], "triangles": [6, 25, 5, 6, 7, 25, 8, 24, 7, 7, 22, 25, 7, 24, 22, 8, 9, 24, 9, 10, 24, 25, 4, 5, 4, 25, 16, 10, 23, 24, 24, 23, 22, 16, 25, 22, 22, 21, 16, 22, 23, 21, 21, 23, 17, 10, 11, 23, 11, 18, 23, 11, 12, 18, 23, 18, 17, 21, 17, 19, 16, 21, 27, 17, 18, 20, 4, 16, 26, 19, 17, 20, 27, 21, 19, 20, 18, 12, 27, 15, 0, 27, 19, 15, 12, 13, 20, 20, 14, 19, 19, 14, 15, 20, 13, 14, 3, 4, 26, 16, 27, 26, 3, 1, 2, 3, 26, 1, 26, 27, 1, 1, 27, 0], "vertices": [2, 27, 26.13, 60.51, 0.65472, 6, 48.58, 23.14, 0.34528, 2, 27, 15.9, 18.31, 0.87962, 6, 59.17, -18.96, 0.12038, 2, 27, 36.06, 4.6, 1.00135, 6, 83.39, -21.72, -0.00135, 1, 27, 9.19, -9.02, 1, 2, 27, -22.73, -7.86, 0.432, 5, 51.71, 70.79, 0.568, 1, 5, 55.31, 27.87, 1, 1, 5, 56.79, -14.64, 1, 1, 5, 14.16, -14.73, 1, 1, 5, -33.14, -14.83, 1, 1, 5, -49.91, 13.84, 1, 1, 5, -66.35, 41.95, 1, 2, 6, -89.13, -35.05, 0.144, 5, -76.54, 59.37, 0.856, 2, 6, -69.88, 1.34, 0.62777, 5, -68.29, 99.7, 0.37223, 1, 6, -49.02, 22.53, 1, 1, 6, -11.73, 38.09, 1, 2, 27, 8.75, 82.55, 0.38932, 6, 22.95, 34.56, 0.61068, 2, 6, 17.89, -52.01, 0.136, 5, 30.94, 73.15, 0.864, 1, 5, -5.12, 86.7, 1, 1, 5, -33.78, 79.78, 1, 2, 27, -15.53, 57.97, 0.17778, 6, 12.89, 1.5, 0.82222, 1, 6, -15.12, 9.72, 1, 1, 5, 18.32, 71.97, 1, 1, 5, 17.6, 33.6, 1, 1, 5, -26.88, 61.35, 1, 1, 5, -24.61, 30.99, 1, 1, 5, 40.25, 28.46, 1, 2, 27, -1.22, 14.24, 0.70399, 6, 45.91, -30.54, 0.29601, 2, 27, -10.26, 33.74, 0.44624, 6, 28.83, -17.48, 0.55376], "hull": 16, "edges": [0, 30, 30, 28, 28, 26, 26, 24, 22, 24, 8, 32, 34, 36, 36, 22, 4, 6, 6, 8, 38, 40, 40, 24, 12, 14, 14, 16, 32, 42, 42, 34, 42, 38, 38, 30, 14, 44, 44, 42, 8, 10, 10, 12, 0, 2, 2, 4, 20, 22, 20, 46, 16, 18, 18, 20, 18, 48], "width": 172, "height": 173}}, "mantles": {"mantles": {"type": "mesh", "uvs": [1, 1, 0.73691, 1, 0.27506, 0.80976, 0, 0.63798, 0, 0, 0.27235, 0, 0.74584, 0, 1, 0], "triangles": [1, 6, 0, 6, 7, 0, 1, 2, 6, 3, 5, 2, 2, 5, 6, 3, 4, 5], "vertices": [-15.29, -51.11, -15.02, -15.33, -4.09, 47.41, 5.64, 84.74, 40.73, 84.48, 40.45, 47.44, 39.97, -16.95, 39.71, -51.52], "hull": 8, "edges": [0, 14, 6, 8, 4, 6, 8, 10, 4, 10, 0, 2, 10, 12, 12, 14, 2, 12, 2, 4], "width": 136, "height": 55}}, "mouth": {"mouth": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [42.59, 4.33, -18.41, 4.2, -18.44, 17.2, 42.56, 17.33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 61, "height": 13}}, "shin1": {"shin1": {"x": 8.96, "y": 0.67, "rotation": 96.81, "width": 53, "height": 57}}, "shin2": {"shin2": {"x": 2.25, "y": -2.94, "rotation": 87.72, "width": 51, "height": 54}}, "thigh1": {"thigh1": {"x": 11.99, "y": -1.02, "rotation": 107.07, "width": 60, "height": 63}}, "thigh2": {"thigh2": {"x": 15.71, "y": -4.62, "rotation": 85.46, "width": 57, "height": 64}}, "upper-arm1": {"upper-arm1": {"x": 6.6, "y": 0.06, "rotation": 137.18, "width": 50, "height": 56}}, "upper-arm2": {"upper-arm2": {"x": 13.33, "y": -2.22, "rotation": 90.45, "width": 39, "height": 59}}, "weapon-morningstar-path": {"weapon-morningstar-path": {"type": "path", "lengths": [0, 0, 0, 0, 0], "vertexCount": 15, "vertices": [1, 40, -9.12, 0.07, 1, 1, 40, -0.02, 0, 1, 1, 40, 7.9, -0.06, 1, 2, 40, 8.01, 0, 0.5, 41, -7.93, 0, 0.5, 1, 41, -0.03, 0, 1, 1, 41, 8.03, 0.01, 1, 2, 41, 8.17, -0.01, 0.5, 42, -7.76, -0.01, 0.5, 1, 42, -0.05, 0, 1, 1, 42, 8.13, 0.01, 1, 2, 42, 8.26, 0.03, 0.5, 43, -7.68, 0.03, 0.5, 1, 42, 15.92, -0.02, 1, 1, 43, 8.11, -0.08, 1, 1, 43, 8.08, -0.08, 1, 1, 43, 15.13, 0, 1, 1, 40, 72.74, 0.11, 1]}}, "weapon-sword": {"sword": {"x": 76, "rotation": 0.19, "width": 216, "height": 69}}}}, {"name": "weapon/morningstar", "bones": ["chain1", "chain2", "chain3", "chain4", "chain5", "chain6", "chain7", "chain8", "weapon-morningstar", "weapon-morningstar-physics1", "weapon-morningstar-physics2", "weapon-morningstar-physics3", "weapon-morningstar-physics4"], "path": ["weapon-morningstar"]}, {"name": "weapon/sword", "bones": ["weapon-sword"]}], "events": {"footstep": {}}, "animations": {"attack": {"bones": {"hip": {"translate": [{"x": -12.6, "y": -11.34}, {"time": 0.0667, "x": -20.76, "y": -23.09}, {"time": 0.1333, "x": -15.51, "y": -16.91}, {"time": 0.2, "x": -5.89, "y": -21.85}, {"time": 0.2667, "x": -7.08, "y": -25.29}, {"time": 0.4, "x": -12.6, "y": -11.34}]}, "left-ground": {"translate": [{"x": 51.95}]}, "right-ground": {"translate": [{"x": -67.98}]}, "body": {"rotate": [{"value": 2.2}, {"time": 0.0667, "value": 13.22}, {"time": 0.1333, "value": -9.59}, {"time": 0.2, "value": -23.59}, {"time": 0.2667, "value": -32.15}, {"time": 0.4, "value": 2.2}], "translate": [{"x": -0.77, "y": -1.89}, {"time": 0.1333, "x": 4.86, "y": -0.24}, {"time": 0.2, "x": 8.05, "y": -2.44}, {"time": 0.4, "x": -0.77, "y": -1.89}]}, "thigh1": {"translate": [{"x": 14.51, "y": -3.79}]}, "thigh2": {"translate": [{"x": -22.58, "y": 1.52}, {"time": 0.2, "x": -13.27, "y": -2.4}, {"time": 0.4, "x": -22.58, "y": 1.52}]}, "head": {"rotate": [{"value": -19.21}, {"time": 0.0667, "value": -19.17}, {"time": 0.1333, "value": 4.77}, {"time": 0.2, "value": 12.47}, {"time": 0.2667, "value": 12.58}, {"time": 0.4, "value": -19.21}], "translate": [{}, {"time": 0.0667, "x": 3.47, "y": 5.7}, {"time": 0.2, "x": -4.56, "y": -12.28}, {"time": 0.4}]}, "mantles": {"rotate": [{"value": -8.28}, {"time": 0.0667, "value": 5.33}, {"time": 0.1333, "value": 4.81}, {"time": 0.2, "value": -2.43}, {"time": 0.2667, "value": -12.35}, {"time": 0.4, "value": -8.28}], "translate": [{}, {"time": 0.0667, "x": 5.51, "y": -5.45}, {"time": 0.1333, "x": 1.54, "y": -5.87}, {"time": 0.2, "x": 0.47, "y": -6.12}, {"time": 0.2667, "x": -6.55, "y": -4.72}, {"time": 0.4}]}, "upper-arm1": {"rotate": [{"value": -4.05}, {"time": 0.0667, "value": 33.65}, {"time": 0.1333, "value": -3.75}, {"time": 0.2667, "value": -49.65}, {"time": 0.3333, "value": -42.31}, {"time": 0.4, "value": -4.05}], "translate": [{}, {"time": 0.2, "x": -2.5, "y": -6.51}, {"time": 0.4}]}, "upper-arm2": {"rotate": [{"value": -35.53}, {"time": 0.0667, "value": 101.05}, {"time": 0.1333, "value": 49.06}, {"time": 0.2, "value": 15.11}, {"time": 0.2667, "value": -46.82}, {"time": 0.4, "value": -35.53}], "translate": [{}, {"time": 0.1333, "x": -2.1, "y": -8.13}, {"time": 0.2, "x": -10.56, "y": 1.26}, {"time": 0.4}]}, "bone2": {"rotate": [{}, {"time": 0.1333, "value": 15.7}, {"time": 0.3333, "value": -8.11}, {"time": 0.4}], "translate": [{}, {"time": 0.1333, "x": -13.54, "y": 2.05}, {"time": 0.3333, "x": 4.33, "y": -12.82}, {"time": 0.4}]}, "foot1": {"rotate": [{"value": 8.9}]}, "foot2": {"rotate": [{"value": -13.12}]}, "forearm1": {"rotate": [{"value": 7.94}, {"time": 0.0667, "value": 42.31}, {"time": 0.1333, "value": 21.87}, {"time": 0.3333, "value": 43.27}, {"time": 0.4, "value": 7.94}]}, "forearm2": {"rotate": [{"value": 56.31}, {"time": 0.0667, "value": 87.63}, {"time": 0.1333, "value": 6.52}, {"time": 0.2, "value": 1}, {"time": 0.2667, "value": 45.15}, {"time": 0.4, "value": 56.31}]}, "hair01": {"rotate": [{}, {"time": 0.2, "value": 19.95}, {"time": 0.3333, "value": -8.11}, {"time": 0.4}], "translate": [{}, {"time": 0.2, "x": -11.68, "y": 11.17}, {"time": 0.3333, "x": 3.76, "y": -20.94}, {"time": 0.4}]}, "hand1": {"rotate": [{}, {"time": 0.1333, "value": -8.94}, {"time": 0.2667, "value": -20.8}, {"time": 0.4}]}, "hand2": {"rotate": [{"value": 17.89}, {"time": 0.0667, "value": -12.89}, {"time": 0.1333, "value": -11.39}, {"time": 0.2, "value": 6.2}, {"time": 0.2667, "value": 18.97}, {"time": 0.4, "value": 17.89}]}}, "attachments": {"default": {"body": {"body": {"deform": [{"curve": [0.05, 0, 0.15, 1]}, {"time": 0.2, "offset": 12, "vertices": [-0.41783, -9.98458, -2.40063, -7.62489, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -2.40062, -7.62489, -0.41784, -9.98458, -1e-05, 0, -0.41783, -9.98458, -0.41783, -9.98458, -2.40062, -7.62489, -2.40062, -7.62489, -2.40062, -7.62489, -0.41781, -9.98458], "curve": [0.25, 0, 0.35, 1]}, {"time": 0.4}]}}, "eyes": {"eyes": {"deform": [{"curve": [0.042, 0.78, 0.15, 1]}, {"time": 0.2, "vertices": [6.7696, 1.0625, 15.00201, 0.58574, 19.83157, 0.7739, 23.31314, -0.40104, 22.66579, -0.49446, 19.83157, 0.7739, 15.00201, 0.58574, 6.12215, 0.96938], "curve": [0.307, 0, 0.35, 1]}, {"time": 0.4}]}}, "head": {"head": {"deform": [{"offset": 15, "vertices": [-3.84663, 0, -3.84666, 0, 0, 0, 0, -6.48674, 0.51688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.48674, 0.51688, 0, 0, -4.54436, -0.64838, -4.54436, -0.64838], "curve": [0.042, 0.78, 0.15, 1]}, {"time": 0.2, "offset": 12, "vertices": [2.2914, -5.91936, 6.0395, -1.21213, 6.27038, 0.1022, 1.63067, 0.06915, 1.12625, 1.37998, 19.62174, 1.25847, 2.16658, 0.74579, -4.25158, -0.00014, -5.02677, -9e-05, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.70107, 0.27222, 11.70107, 0.27221, 4.49132, 0.19015, 11.44339, 0.18923, 0, 0, 0, 0, 0, 0, 13.0799, 0.52394, 20.62553, 0.49708, 10.15124, 0.32587, 10.15124, 0.32587, 9.14707, 1.88179], "curve": [0.263, 0, 0.302, 0.6]}, {"time": 0.3333, "offset": 14, "vertices": [3.07036, -5.06854, 2.50973, -4.58881, 0.65269, 0.02768, 0.45079, 0.55235, 3.96337, 0.81371, 0.86719, 0.29851, -1.70172, -5e-05, -2.012, -4e-05, -1.11318, -0.04721, -1.11318, -0.0472, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.68344, 0.10896, 4.68344, 0.10895, 1.79768, 0.07611, 4.5803, 0.07574, 0, 0, 0, 0, 0, 0, 1.34495, 0.51971, 8.25552, 0.19896, 1.33766, -0.25843, 1.33766, -0.25843, 3.66118, 0.7532], "curve": [0.357, 0.57, 0.378, 1]}, {"time": 0.4, "offset": 22, "vertices": [-6.48674, 0.51688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.48674, 0.51688, 0, 0, -4.54436, -0.64838, -4.54436, -0.64838]}]}}, "mantles": {"mantles": {"deform": [{"curve": [0.05, 0, 0.15, 1]}, {"time": 0.2, "vertices": [4.09309, -3.31009, 0.20662, -12.49304, -5.29249, -11.21297, -2.35192, -2.26423, -2.35192, -2.26423, -5.2925, -11.21297, -0.07825, -5.05688, -3.74411, 9.53745], "curve": [0.25, 0, 0.35, 1]}, {"time": 0.4}]}}, "mouth": {"mouth": {"deform": [{"curve": [0.042, 0.78, 0.15, 1]}, {"time": 0.2, "vertices": [9.24917, -1.3452, 25.71696, -0.48061, 25.58308, -0.49992, 9.1152, -1.36452], "curve": [0.307, 0, 0.35, 1]}, {"time": 0.4}]}}}}}, "crouch": {"bones": {"upper-arm2": {"rotate": [{"value": 38.04}, {"time": 0.5, "value": 30.8}, {"time": 1, "value": 38.04}]}, "body": {"rotate": [{"value": -15.51, "curve": [0.125, -15.51, 0.375, -11.54]}, {"time": 0.5, "value": -11.54, "curve": [0.625, -11.54, 0.875, -15.51]}, {"time": 1, "value": -15.51}], "translate": [{"x": -0.41, "y": -2.33, "curve": [0.125, -0.41, 0.375, -2.21, 0.125, -2.33, 0.375, -0.53]}, {"time": 0.5, "x": -2.21, "y": -0.53, "curve": [0.625, -2.21, 0.875, -0.41, 0.625, -0.53, 0.875, -2.33]}, {"time": 1, "x": -0.41, "y": -2.33}]}, "head": {"rotate": [{"value": 8.73}, {"time": 0.5, "value": 3.25}, {"time": 1, "value": 8.73}], "translate": [{"x": -4.43, "y": -2.86, "curve": [0.06, -4.7, 0.116, -4.85, 0.06, -2.99, 0.116, -3.06]}, {"time": 0.1667, "x": -4.85, "y": -3.06, "curve": [0.292, -4.85, 0.542, -2.36, 0.292, -3.06, 0.542, -1.86]}, {"time": 0.6667, "x": -2.36, "y": -1.86, "curve": [0.787, -3.27, 0.901, -4.02, 0.787, -2.3, 0.901, -2.66]}, {"time": 1, "x": -4.43, "y": -2.86}]}, "hip": {"translate": [{"x": -11.23, "y": -42.01}]}, "mantles": {"rotate": [{"value": 9.05}, {"time": 0.5, "value": 7.13}, {"time": 1, "value": 9.05}], "translate": [{"x": -0.1}]}, "left-ground": {"translate": [{"x": -29.07, "y": 1.18}]}, "right-ground": {"translate": [{"x": 16.23}]}, "upper-arm1": {"rotate": [{"value": -5.42}, {"time": 0.5, "value": -11.1}, {"time": 1, "value": -5.42}], "translate": [{"x": -6.95, "y": 5.86}]}, "forearm1": {"rotate": [{"value": 25.39}, {"time": 0.5, "value": 32.07}, {"time": 1, "value": 25.39}]}, "forearm2": {"rotate": [{"value": 18.6}, {"time": 0.5, "value": 26.16}, {"time": 1, "value": 18.6}]}, "thigh2": {"translate": [{"x": -2.75}]}, "thigh1": {"translate": [{"x": -0.93, "y": 6.68}]}, "foot1": {"rotate": [{"value": -89.87}]}, "hand2": {"rotate": [{"value": -13.4}, {"time": 0.5, "value": -17.29}, {"time": 1, "value": -13.4}]}, "hand1": {"rotate": [{"value": -9.3}, {"time": 0.5, "value": -1.97}, {"time": 1, "value": -9.3}]}, "right-ankle": {"translate": [{"y": -4.93}]}, "left-ankle": {"translate": [{"y": -12.23}]}}, "attachments": {"default": {"body": {"body": {"deform": [{"offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "offset": 8, "vertices": [-0.16332, -0.55323, 0.56806, -2.69373, 1.7576, 3.93866, -1.93257, 0.76876, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -1.97895, 1.32549, -0.70129, 3.17348, -1e-05, 0, -0.70129, 3.17348, -0.70129, 3.17348, -1.97895, 1.32549, -1.97895, 1.32549, -1.93256, 0.76876, -0.70127, 3.17348], "curve": [0.625, 0, 0.875, 1]}, {"time": 1, "offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544]}]}}, "eyes": {"eyes": {"deform": [{"vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "vertices": [6.7696, 1.0625, 13.34377, 0.58217, 15.22793, 0.76399, 17.05129, -0.41451, 16.40394, -0.50793, 15.22793, 0.76398, 13.34377, 0.58217, 6.12215, 0.96938], "curve": [0.794, -0.02, 0.875, 1]}, {"time": 1, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533]}]}}, "head": {"head": {"deform": [{"offset": 14, "vertices": [1.38039, -4.88211, 1.38039, -4.88215, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "offset": 14, "vertices": [3.50356, -2.74406, 3.50352, -2.74412, 1.63067, 0.06915, 1.12625, 1.37998, 9.63888, 1.16068, 2.16658, 0.74579, 0, 0, 0, 0, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.70107, 0.27222, 11.70107, 0.27221, 9.41118, -0.53924, 9.4911, 0.18932, 0, 0, 0, 0, 0, 0, 7.79456, 0.42606, 15.34019, 0.39919, 6.02913, 0.32602, 6.02913, 0.32602, 9.14707, 1.88179], "curve": [0.794, -0.02, 0.875, 1]}, {"time": 1, "offset": 14, "vertices": [1.38039, -4.88211, 1.38039, -4.88215, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741]}]}}, "mantles": {"mantles": {"deform": [{"offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "vertices": [1.3394, -2.09791, 0.11258, -3.18363, 3.04376, 1.23967, -0.03921, 0.77839, -0.03921, 0.77839, 3.04375, 1.23967, -0.01005, -0.95502, -0.04588, 0.86383], "curve": [0.625, 0, 0.875, 1]}, {"time": 1, "offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224]}]}}, "mouth": {"mouth": {"deform": [{"vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "vertices": [9.24917, -1.3452, 17.81878, -0.49759, 17.68489, -0.51691, 9.1152, -1.36452], "curve": [0.794, -0.02, 0.875, 1]}, {"time": 1, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885]}]}}}}}, "crouch-from fall": {"bones": {"upper-arm2": {"rotate": [{"value": 22.23}, {"time": 0.0333, "value": 6.7}, {"time": 0.5333, "value": 30.8}, {"time": 1.0333, "value": 38.04}]}, "body": {"rotate": [{"value": 2.71}, {"time": 0.0667, "value": -20.15, "curve": [0.183, -20.15, 0.417, -11.54]}, {"time": 0.5333, "value": -11.54, "curve": [0.658, -11.54, 0.908, -15.51]}, {"time": 1.0333, "value": -15.51}], "translate": [{"x": -0.82, "y": -2.69}, {"time": 0.0333, "x": -1.64, "y": -5.39, "curve": [0.062, -1.64, 0.096, -1.54, 0.062, -5.39, 0.096, -7.5]}, {"time": 0.1333, "x": -1.37, "y": -10.93, "curve": [0.261, -1.61, 0.436, -2.21, 0.261, -7.91, 0.436, -0.53]}, {"time": 0.5333, "x": -2.21, "y": -0.53, "curve": [0.658, -2.21, 0.908, -0.41, 0.658, -0.53, 0.908, -2.33]}, {"time": 1.0333, "x": -0.41, "y": -2.33}]}, "head": {"rotate": [{"value": 28.81}, {"time": 0.1, "value": -4.21}, {"time": 0.2667, "value": 3.25}, {"time": 1.0333, "value": 8.73}], "translate": [{"x": -2.21, "y": -1.43}, {"time": 0.0333, "x": -4.43, "y": -2.86, "curve": [0.079, -7.06, 0.123, -9.34, 0.079, -2.96, 0.123, -3.05]}, {"time": 0.1667, "x": -11.35, "y": -3.13, "curve": [0.25, -7.12, 0.328, -4.85, 0.25, -3.08, 0.328, -3.06]}, {"time": 0.4, "x": -4.85, "y": -3.06, "curve": [0.5, -4.85, 0.7, -2.36, 0.5, -3.06, 0.7, -1.86]}, {"time": 0.8, "x": -2.36, "y": -1.86, "curve": [0.884, -3.27, 0.964, -4.02, 0.884, -2.3, 0.964, -2.66]}, {"time": 1.0333, "x": -4.43, "y": -2.86}], "scale": [{"y": 1.139}, {"time": 0.0667, "x": 1.038, "y": 1.037}, {"time": 0.2}]}, "hip": {"translate": [{"x": -5.61, "y": -21.01}, {"time": 0.0333, "x": -11.23, "y": -42.01}]}, "mantles": {"rotate": [{"value": 4.52}, {"time": 0.0333, "value": 9.05}, {"time": 0.5333, "value": 7.13}, {"time": 1.0333, "value": 9.05}], "translate": [{"x": -0.05}, {"time": 0.0333, "x": -0.1}]}, "left-ground": {"translate": [{"x": 18.67, "y": 8.42}, {"time": 0.1333, "x": -29.07, "y": 1.18}]}, "right-ground": {"translate": [{"x": 15.37, "y": 2.29}, {"time": 0.0333, "x": 16.23}]}, "upper-arm1": {"rotate": [{"value": -40.37}, {"time": 0.0333, "value": -55.04}, {"time": 0.1, "value": -26.73}, {"time": 0.2333, "value": 23.12}, {"time": 0.3667, "value": -11.1}, {"time": 1.0333, "value": -5.42}], "translate": [{"x": -3.47, "y": 2.93}, {"time": 0.0333, "x": -6.95, "y": 5.86}]}, "forearm1": {"rotate": [{"value": 36.8}, {"time": 0.0333, "value": 47.41}, {"time": 0.1333, "value": -6.15}, {"time": 0.3333, "value": 39.73}, {"time": 0.6333, "value": 32.07}, {"time": 1.0333, "value": 25.39}]}, "forearm2": {"rotate": [{"value": 12.02}, {"time": 0.0333, "value": 11.73}, {"time": 0.2333, "value": 26.16}, {"time": 1.0333, "value": 18.6}]}, "thigh2": {"rotate": [{"value": 60.45}, {"time": 0.0333}], "translate": [{"x": -1.38}, {"time": 0.0333, "x": -2.75}]}, "thigh1": {"rotate": [{"value": 83.27}, {"time": 0.0333}], "translate": [{"x": -0.46, "y": 3.34}, {"time": 0.0333, "x": -0.93, "y": 6.68}]}, "foot1": {"rotate": [{"value": -35.12}, {"time": 0.0333, "value": -89.87}]}, "foot2": {"rotate": [{"value": 19.58}, {"time": 0.0333}]}, "hand2": {"rotate": [{"value": 37.56}, {"time": 0.0333, "value": 40.84, "curve": [0.033, -20.79, 0.133, -28.41]}, {"time": 0.1667, "value": -28.41, "curve": [0.383, -28.41, 0.571, -13.4]}, {"time": 1.0333, "value": -13.4}]}, "hand1": {"rotate": [{"value": -4.65}, {"time": 0.0333, "value": -9.3}, {"time": 0.1333, "value": -36.61}, {"time": 0.3333, "value": 38.83}, {"time": 0.6333, "value": -1.97}, {"time": 1.0333, "value": -9.3}]}, "shin2": {"rotate": [{"value": -66.41}, {"time": 0.0333}]}, "shin1": {"rotate": [{"value": -110.05}, {"time": 0.0333}]}, "right-ankle": {"translate": [{"y": -2.46}, {"time": 0.0333, "y": -4.93}]}, "left-ankle": {"translate": [{"y": -6.11}, {"time": 0.0333, "y": -12.23}]}, "root": {"scale": [{"y": 0.97}, {"time": 0.0333, "y": 0.94}, {"time": 0.1667}]}}, "attachments": {"default": {"body": {"body": {"deform": [{}, {"time": 0.0333, "offset": 12, "vertices": [1.29363, 5.08191, -0.08312, 2.25328, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.11549, 2.64195, -0.42297, 4.54772, -1e-05, 0, -0.42297, 4.54772, -0.42297, 4.54772, -0.11549, 2.64195, -0.11549, 2.64195, -0.08311, 2.25329, -0.42295, 4.54772]}, {"time": 0.0667, "offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544], "curve": [0.192, 0, 0.442, 1]}, {"time": 0.5667, "offset": 8, "vertices": [-0.16332, -0.55323, 0.56806, -2.69373, 1.7576, 3.93866, -1.93257, 0.76876, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -1.97895, 1.32549, -0.70129, 3.17348, -1e-05, 0, -0.70129, 3.17348, -0.70129, 3.17348, -1.97895, 1.32549, -1.97895, 1.32549, -1.93256, 0.76876, -0.70127, 3.17348], "curve": [0.692, 0, 0.942, 1]}, {"time": 1.0667, "offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544]}]}}, "eyes": {"eyes": {"deform": [{}, {"time": 0.0333, "vertices": [1.93199, 0.30323, 3.80821, 0.16614, 4.34593, 0.21804, 4.8663, -0.11831, 4.68156, -0.14496, 4.34593, 0.21803, 3.80821, 0.16615, 1.74722, 0.27665]}, {"time": 0.0667, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533], "curve": [0.192, 0, 0.442, 1]}, {"time": 0.5667, "vertices": [6.7696, 1.0625, 13.34377, 0.58217, 15.22793, 0.76399, 17.05129, -0.41451, 16.40394, -0.50793, 15.22793, 0.76398, 13.34377, 0.58217, 6.12215, 0.96938], "curve": [0.86, -0.02, 0.942, 1]}, {"time": 1.0667, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533]}]}}, "head": {"head": {"deform": [{}, {"time": 0.0333, "offset": 14, "vertices": [0.69019, 0.02921, 0.6902, 0.02921, 0.46538, 0.01973, 0.32142, 0.39384, 1.35876, 0.44217, 0.61833, 0.21284, 0, 0, 0, 0, -0.79372, -0.03366, -0.79372, -0.03366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.33939, 0.0777, 3.33939, 0.07769, 1.28179, 0.05427, 2.08944, 0.05405, 0, 0, 0, 0, 0, 0, 0.8324, 0.23252, 4.37797, 0.11393, 0.74541, -0.0461, 0.74541, -0.0461, 2.6105, 0.53705]}, {"time": 0.0667, "offset": 14, "vertices": [1.38039, 0.05844, 1.38039, 0.05842, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741], "curve": [0.192, 0, 0.442, 1]}, {"time": 0.5667, "offset": 14, "vertices": [2.41842, 0.10236, 2.41842, 0.10235, 1.63067, 0.06915, 1.12625, 1.37998, 9.63888, 1.16068, 2.16658, 0.74579, 0, 0, 0, 0, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.70107, 0.27222, 11.70107, 0.27221, 4.49132, 0.19015, 7.32128, 0.18938, 0, 0, 0, 0, 0, 0, 7.79456, 0.42606, 15.34019, 0.39919, 6.02913, 0.32602, 6.02913, 0.32602, 9.14707, 1.88179], "curve": [0.86, -0.02, 0.942, 1]}, {"time": 1.0667, "offset": 14, "vertices": [1.38039, 0.05844, 1.38039, 0.05842, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741]}]}}, "mantles": {"mantles": {"deform": [{}, {"time": 0.0333, "offset": 2, "vertices": [-0.14301, 1.1612, 1.56465, 2.6563, 0.0679, 1.16074, 0.0679, 1.16074, 1.56464, 2.6563, -0.143, 1.1612]}, {"time": 0.0667, "offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224], "curve": [0.192, 0, 0.442, 1]}, {"time": 0.5667, "vertices": [1.3394, -2.09791, 0.11258, -3.18363, 3.04376, 1.23967, -0.03921, 0.77839, -0.03921, 0.77839, 3.04375, 1.23967, -0.01005, -0.95502, -0.04588, 0.86383], "curve": [0.692, 0, 0.942, 1]}, {"time": 1.0667, "offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224]}]}}, "mouth": {"mouth": {"deform": [{}, {"time": 0.0333, "vertices": [2.63964, -0.38391, 5.08534, -0.14201, 5.04713, -0.14752, 2.60141, -0.38942]}, {"time": 0.0667, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885], "curve": [0.192, 0, 0.442, 1]}, {"time": 0.5667, "vertices": [9.24917, -1.3452, 17.81878, -0.49759, 17.68489, -0.51691, 9.1152, -1.36452], "curve": [0.86, -0.02, 0.942, 1]}, {"time": 1.0667, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885]}]}}}}}, "fall": {"bones": {"right-ground": {"translate": [{"x": 14.5, "y": 4.58}, {"time": 0.3333, "x": 13.83, "y": 12.64}, {"time": 0.6, "x": 13.56, "y": 16.8}, {"time": 0.8, "x": 10.68, "y": 4.32}, {"time": 1, "x": 14.5, "y": 4.58}]}, "left-ground": {"translate": [{"x": 30.61, "y": 10.23}, {"time": 0.2333, "x": 23.51, "y": 14.29}, {"time": 0.4667, "x": 28.58, "y": 14.96}, {"time": 0.8333, "x": 33.02, "y": 13.4}, {"time": 1, "x": 30.61, "y": 10.23}]}, "foot1": {"rotate": [{"value": 19.63}]}, "foot2": {"rotate": [{"value": 39.17}]}, "body": {"rotate": [{"value": 14.14}, {"time": 0.2667, "value": 15.62}, {"time": 0.5333, "value": 11.42}, {"time": 0.7, "value": 14.03}, {"time": 1, "value": 14.14}]}, "head": {"rotate": [{"value": -17.81}, {"time": 0.3333, "value": -15.89}, {"time": 0.6667, "value": -20.37}, {"time": 1, "value": -17.81}]}, "upper-arm1": {"rotate": [{"value": -25.71}, {"time": 0.3, "value": -30.42}, {"time": 0.5667, "value": -30}, {"time": 0.7333, "value": -24.31}, {"time": 1, "value": -25.71}]}, "forearm1": {"rotate": [{"value": 26.18}, {"time": 0.3, "value": 33.54}, {"time": 0.5667, "value": 33.75}, {"time": 0.7333, "value": 16.9}, {"time": 1, "value": 26.18}]}, "upper-arm2": {"rotate": [{"value": 37.75}, {"time": 0.3, "value": 44.17}, {"time": 0.5667, "value": 34.42}, {"time": 0.7333, "value": 35.72}, {"time": 1, "value": 37.75}]}, "forearm2": {"rotate": [{"value": 12.31}, {"time": 0.3, "value": 12.8}, {"time": 0.5667, "value": 31.01}, {"time": 0.7333, "value": 21.52}, {"time": 1, "value": 12.31}]}}}, "head-turn": {"bones": {"upper-arm2": {"rotate": [{"value": 0.46}, {"time": 0.5, "value": -1.2}, {"time": 1, "value": 0.46}, {"time": 1.5, "value": -1.2}, {"time": 2, "value": 0.46}], "translate": [{}, {"time": 0.5, "x": -3.13, "y": 8.02}, {"time": 1}, {"time": 1.5, "x": -3.13, "y": 8.02}, {"time": 2}]}, "body": {"rotate": [{}, {"time": 0.5, "value": 1.67}, {"time": 1}, {"time": 1.5, "value": 1.67}, {"time": 2}], "translate": [{"curve": [0.125, 0, 0.375, 0, 0.125, 0, 0.375, -3.7]}, {"time": 0.5, "y": -3.7, "curve": [0.625, 0, 0.875, 0, 0.625, -3.7, 0.875, 0]}, {"time": 1, "curve": [1.125, 0, 1.375, 0, 1.125, 0, 1.375, -3.7]}, {"time": 1.5, "y": -3.7, "curve": [1.625, 0, 1.875, 0, 1.625, -3.7, 1.875, 0]}, {"time": 2}]}, "head": {"rotate": [{"value": -4.18}, {"time": 0.5, "value": -8.26}, {"time": 1, "value": -4.18}, {"time": 1.5, "value": -8.26}, {"time": 2, "value": -4.18}], "translate": [{"x": -1.06, "curve": [0.038, -0.46, 0.073, 0, 0.038, 0, 0.073, 0]}, {"time": 0.1, "curve": [0.242, 0, 0.525, -3.75, 0.242, 0, 0.525, 0]}, {"time": 0.6667, "x": -3.75, "curve": [0.748, -3.75, 0.883, -2.14, 0.748, 0, 0.883, 0]}, {"time": 1, "x": -1.06, "curve": [1.038, -0.46, 1.074, 0, 1.038, 0, 1.074, 0]}, {"time": 1.1, "curve": [1.242, 0, 1.525, -3.75, 1.242, 0, 1.525, 0]}, {"time": 1.6667, "x": -3.75, "curve": [1.748, -3.75, 1.883, -2.14, 1.748, 0, 1.883, 0]}, {"time": 2, "x": -1.06}], "scale": [{"time": 0.1, "x": -1, "curve": "stepped"}, {"time": 1.1}]}, "hip": {"translate": [{"y": -2.92, "curve": [0.125, 0, 0.375, -3.18, 0.125, -2.92, 0.375, -4.04]}, {"time": 0.5, "x": -3.18, "y": -4.04, "curve": [0.625, -3.18, 0.875, 0, 0.625, -4.04, 0.875, -2.92]}, {"time": 1, "y": -2.92, "curve": [1.125, 0, 1.375, -3.18, 1.125, -2.92, 1.375, -4.04]}, {"time": 1.5, "x": -3.18, "y": -4.04, "curve": [1.625, -3.18, 1.875, 0, 1.625, -4.04, 1.875, -2.92]}, {"time": 2, "y": -2.92}]}, "mantles": {"rotate": [{}, {"time": 0.4333, "value": -4.84, "curve": [0.575, -4.84, 0.858, 0]}, {"time": 1}, {"time": 1.4333, "value": -4.84, "curve": [1.575, -4.84, 1.858, 0]}, {"time": 2}], "translate": [{"x": -0.1, "curve": [0.13, -0.38, 0.328, -1.42, 0.13, 0, 0.328, 0]}, {"time": 0.4333, "x": -1.42, "curve": [0.558, -1.42, 0.808, 0, 0.558, 0, 0.808, 0]}, {"time": 0.9333}, {"time": 1, "x": -0.1, "curve": [1.13, -0.38, 1.328, -1.42, 1.13, 0, 1.328, 0]}, {"time": 1.4333, "x": -1.42, "curve": [1.558, -1.42, 1.808, 0, 1.558, 0, 1.808, 0]}, {"time": 1.9333}, {"time": 2, "x": -0.1}]}, "left-ground": {"translate": [{"x": -7.15}]}, "right-ground": {"translate": [{"x": 5.94}]}, "upper-arm1": {"rotate": [{"curve": [0.202, 0, 0.375, -15.4]}, {"time": 0.5, "value": -15.4, "curve": [0.759, -15.4, 0.875, 0]}, {"time": 1, "curve": [1.202, 0, 1.375, -15.4]}, {"time": 1.5, "value": -15.4, "curve": [1.759, -15.4, 1.875, 0]}, {"time": 2}], "translate": [{"curve": [0.202, 0, 0.375, 1.84, 0.202, 0, 0.375, -5.01]}, {"time": 0.5, "x": 1.84, "y": -5.01, "curve": [0.759, 1.84, 0.875, 0, 0.759, -5.01, 0.875, 0]}, {"time": 1, "curve": [1.202, 0, 1.375, 1.84, 1.202, 0, 1.375, -5.01]}, {"time": 1.5, "x": 1.84, "y": -5.01, "curve": [1.759, 1.84, 1.875, 0, 1.759, -5.01, 1.875, 0]}, {"time": 2}]}, "forearm1": {"rotate": [{"curve": [0.202, 0, 0.375, 9.63]}, {"time": 0.5, "value": 9.63, "curve": [0.759, 9.63, 0.875, 0]}, {"time": 1, "curve": [1.202, 0, 1.375, 9.63]}, {"time": 1.5, "value": 9.63, "curve": [1.759, 9.63, 1.875, 0]}, {"time": 2}]}, "forearm2": {"rotate": [{}, {"time": 0.5, "value": 13.64}, {"time": 1}, {"time": 1.5, "value": 13.64}, {"time": 2}]}, "hand2": {"rotate": [{}, {"time": 0.5, "value": -8.65}, {"time": 1}, {"time": 1.5, "value": -8.65}, {"time": 2}]}, "hand1": {"rotate": [{"curve": [0.202, 0, 0.375, 12.37]}, {"time": 0.5, "value": 12.37, "curve": [0.759, 12.37, 0.875, 0]}, {"time": 1, "curve": [1.202, 0, 1.375, 12.37]}, {"time": 1.5, "value": 12.37, "curve": [1.759, 12.37, 1.875, 0]}, {"time": 2}]}, "bone2": {"rotate": [{}, {"time": 0.3, "value": 2.36}, {"time": 1}, {"time": 1.2333, "value": 3.15}, {"time": 1.6667, "value": 2.36}, {"time": 2}], "translate": [{"curve": [0.027, 0, 0.062, -4.09, 0.027, 0, 0.062, -7.43]}, {"time": 0.1, "x": -9.73, "y": -17.7, "curve": [0.165, -6.94, 0.239, -2.97, 0.165, -12.45, 0.239, -5.01]}, {"time": 0.3, "x": -0.72, "y": -0.78}, {"time": 1, "curve": [1.025, 0, 1.075, -9.73, 1.025, 0, 1.075, -17.7]}, {"time": 1.1, "x": -9.73, "y": -17.7, "curve": [1.144, -7.1, 1.193, -3.36, 1.144, -11.58, 1.193, -2.89]}, {"time": 1.2333, "x": -1.24, "y": 2.05}, {"time": 1.6667, "x": -0.72, "y": -0.78}, {"time": 2}]}, "hair01": {"rotate": [{}, {"time": 0.3, "value": 5.81}, {"time": 1}, {"time": 1.2333, "value": 7.75}, {"time": 1.6667, "value": 5.81}, {"time": 2}], "translate": [{"curve": [0.027, 0, 0.062, -3.49, 0.027, 0, 0.062, -4.17]}, {"time": 0.1, "x": -8.32, "y": -9.93, "curve": [0.165, -5.51, 0.239, -1.53, 0.165, -7.29, 0.239, -3.55]}, {"time": 0.3, "x": 0.74, "y": -1.42}, {"time": 1, "curve": [1.025, 0, 1.075, -8.32, 1.025, 0, 1.075, -9.93]}, {"time": 1.1, "x": -8.32, "y": -9.93, "curve": [1.144, -6.13, 1.193, -3.01, 1.144, -6.22, 1.193, -0.94]}, {"time": 1.2333, "x": -1.24, "y": 2.05}, {"time": 1.6667, "x": 0.74, "y": -1.42}, {"time": 2}]}}, "attachments": {"default": {"body": {"body": {"deform": [{"curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "offset": 12, "vertices": [-0.75589, -3.68988, -1.01899, -2.97405, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -1.01898, -2.97405, -0.7559, -3.68988, -1e-05, 0, -0.75589, -3.68988, -0.75589, -3.68988, -1.01898, -2.97405, -1.01898, -2.97405, -1.01898, -2.97405, -0.75587, -3.68988], "curve": [0.625, 0, 0.875, 1]}, {"time": 1, "curve": [1.125, 0, 1.375, 1]}, {"time": 1.5, "offset": 12, "vertices": [-0.75589, -3.68988, -1.01899, -2.97405, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -1.01898, -2.97405, -0.7559, -3.68988, -1e-05, 0, -0.75589, -3.68988, -0.75589, -3.68988, -1.01898, -2.97405, -1.01898, -2.97405, -1.01898, -2.97405, -0.75587, -3.68988], "curve": [1.625, 0, 1.875, 1]}, {"time": 2}]}}, "eyes": {"eyes": {"deform": [{"vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533], "curve": [0.012, 0.44, 0.075, 1]}, {"time": 0.1, "vertices": [-8.40292, -7.08714, -21.63906, -8.70874, -16.35854, -9.80267, -11.15096, -9.57028, -11.74202, -9.65559, -16.35854, -9.80268, -21.63906, -8.70874, -8.99406, -7.17217], "curve": [0.117, 0.39, 0.225, 1]}, {"time": 0.2667, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533], "curve": "stepped"}, {"time": 1, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533], "curve": [1.012, 0.44, 1.075, 1]}, {"time": 1.1, "vertices": [-8.40292, -7.08714, -21.63906, -8.70874, -16.35854, -9.80267, -11.15096, -9.57028, -11.74202, -9.65559, -16.35854, -9.80268, -21.63906, -8.70874, -8.99406, -7.17217], "curve": [1.117, 0.39, 1.225, 1]}, {"time": 1.2667, "vertices": [6.7696, 1.0625, 13.34377, 0.58217, 15.22793, 0.76399, 17.05129, -0.41451, 16.40394, -0.50793, 15.22793, 0.76398, 13.34377, 0.58217, 6.12215, 0.96938], "curve": [1.697, -0.02, 1.817, 1]}, {"time": 2, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533]}]}}, "head": {"head": {"deform": [{"offset": 14, "vertices": [1.38039, 0.05844, 1.38039, 0.05842, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741], "curve": [0.012, 0.44, 0.075, 1]}, {"time": 0.1, "vertices": [-28.25413, 0.58983, -28.29793, -0.849, 0, 0, 0, 0, -8.54648, 2.15753, -9.02655, 0.97842, -1.19273, 1.64783, 8.69878, -1.74019, 8.71585, -1.38528, -4.34675, -10.72729, -8.58519, 8.46599, -10.54067, -12.01434, -1.8362, -10.84667, 0, 0, 0, 0, 7.20288, 0.91037, 7.20288, 0.91038, 1.87616, -7.83395, 1.98415, -7.80763, -9.94567, -3.80405, -40.97641, -5.18958, -41.84467, -1.62019, -41.73163, -3.60776, -15.38478, -8.74435, -15.38478, -8.74435, -14.91351, -13.12096, -5.60619, -6.39686, 0, 0, 0, 0, 0, 0, -18.34148, -11.65996, -10.8627, -12.31054, -18.1491, -6.56942, -15.89555, -8.6677, -7.04251, -15.24861], "curve": [0.117, 0.39, 0.225, 1]}, {"time": 0.2667, "offset": 14, "vertices": [9.37443, -0.66393, 9.40689, 0.0588, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741], "curve": "stepped"}, {"time": 1, "offset": 14, "vertices": [1.38039, 0.05844, 1.38039, 0.05842, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741], "curve": [1.012, 0.44, 1.075, 1]}, {"time": 1.1, "vertices": [-28.25413, 0.58983, -28.29793, -0.849, 0, 0, 0, 0, -23.08535, 11.20282, -23.74395, 9.72906, -8.07306, 0.62723, -1.79153, -1.05692, -1.79153, -1.05691, -4.34675, -10.72724, -8.58519, 8.46599, -10.54067, -12.01434, -1.8362, -10.84667, 0, 0, 0, 0, 7.20288, 0.91037, 7.20288, 0.91038, 1.87616, -7.83395, 1.98415, -7.80763, -9.94567, -3.80405, -40.97641, -5.18958, -41.84467, -1.62019, -41.73163, -3.60776, -15.50945, -1.09537, -15.50945, -1.09534, -15.03819, -5.47194, -5.60619, -6.39686, 0, 0, 0, 0, 0, 0, -18.46617, -4.01095, -10.8627, -12.31054, -18.1491, -6.56942, -15.89555, -8.6677, -7.04251, -15.24861], "curve": [1.117, 0.39, 1.225, 1]}, {"time": 1.2667, "offset": 14, "vertices": [2.41842, 0.10236, 2.41842, 0.10235, 1.63067, 0.06915, 1.12625, 1.37998, 9.63888, 1.16068, 2.16658, 0.74579, 0, 0, 0, 0, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.70107, 0.27222, 11.70107, 0.27221, 4.49132, 0.19015, 7.32128, 0.18938, 0, 0, 0, 0, 0, 0, 7.79456, 0.42606, 15.34019, 0.39919, 6.02913, 0.32602, 6.02913, 0.32602, 9.14707, 1.88179], "curve": [1.697, -0.02, 1.817, 1]}, {"time": 2, "offset": 14, "vertices": [1.38039, 0.05844, 1.38039, 0.05842, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741]}]}}, "mantles": {"mantles": {"deform": [{"curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "vertices": [-1.3883, 0.54368, 0.15339, -6.90907, -1.2541, -4.50875, -1.22915, -1.35892, -1.22924, -1.35755, -1.25382, -4.47607, -0.06354, -5.18391, -0.43389, 3.45036], "curve": [0.625, 0, 0.875, 1]}, {"time": 1, "curve": [1.125, 0, 1.375, 1]}, {"time": 1.5, "vertices": [-1.3883, 0.54368, 0.15339, -6.90907, -1.2541, -4.50875, -1.22915, -1.35892, -1.22924, -1.35755, -1.25382, -4.47607, -0.06354, -5.18391, -0.43389, 3.45036], "curve": [1.625, 0, 1.875, 1]}, {"time": 2}]}}, "mouth": {"mouth": {"deform": [{"vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885], "curve": [0.012, 0.44, 0.075, 1]}, {"time": 0.1, "vertices": [-19.44883, -10.36464, -11.62436, -9.59074, -11.74661, -9.60837, -19.57115, -10.38228], "curve": [0.117, 0.39, 0.225, 1]}, {"time": 0.2667, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885], "curve": "stepped"}, {"time": 1, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885], "curve": [1.012, 0.44, 1.075, 1]}, {"time": 1.1, "vertices": [-19.44883, -10.36464, -11.62436, -9.59074, -11.74661, -9.60837, -19.57115, -10.38228], "curve": [1.117, 0.39, 1.225, 1]}, {"time": 1.2667, "vertices": [9.24917, -1.3452, 17.81878, -0.49759, 17.68489, -0.51691, 9.1152, -1.36452], "curve": [1.697, -0.02, 1.817, 1]}, {"time": 2, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885]}]}}}}}, "idle": {"bones": {"upper-arm2": {"rotate": [{"value": 0.46}, {"time": 0.5, "value": -1.2}, {"time": 1, "value": 0.46}], "translate": [{}, {"time": 0.5, "x": -3.13, "y": 8.02}, {"time": 1}]}, "body": {"rotate": [{}, {"time": 0.5, "value": 1.67}, {"time": 1}], "translate": [{"curve": [0.125, 0, 0.375, 0, 0.125, 0, 0.375, -3.7]}, {"time": 0.5, "y": -3.7, "curve": [0.625, 0, 0.875, 0, 0.625, -3.7, 0.875, 0]}, {"time": 1}]}, "head": {"rotate": [{"value": -4.18}, {"time": 0.5, "value": -8.26}, {"time": 1, "value": -4.18}], "translate": [{"x": -1.06, "curve": [0.064, -0.46, 0.123, 0, 0.064, 0, 0.123, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, -3.75, 0.292, 0, 0.542, 0]}, {"time": 0.6667, "x": -3.75, "curve": [0.748, -3.75, 0.883, -2.14, 0.748, 0, 0.883, 0]}, {"time": 1, "x": -1.06}]}, "hip": {"translate": [{"y": -2.92, "curve": [0.125, 0, 0.375, -3.18, 0.125, -2.92, 0.375, -4.04]}, {"time": 0.5, "x": -3.18, "y": -4.04, "curve": [0.625, -3.18, 0.875, 0, 0.625, -4.04, 0.875, -2.92]}, {"time": 1, "y": -2.92}]}, "mantles": {"rotate": [{}, {"time": 0.4333, "value": -4.84, "curve": [0.575, -4.84, 0.858, 0]}, {"time": 1}], "translate": [{"x": -0.1, "curve": [0.13, -0.38, 0.328, -1.42, 0.13, 0, 0.328, 0]}, {"time": 0.4333, "x": -1.42, "curve": [0.558, -1.42, 0.808, 0, 0.558, 0, 0.808, 0]}, {"time": 0.9333}, {"time": 1, "x": -0.1}]}, "left-ground": {"translate": [{"x": -7.15}]}, "right-ground": {"translate": [{"x": 5.94}]}, "upper-arm1": {"rotate": [{"curve": [0.202, 0, 0.375, -15.4]}, {"time": 0.5, "value": -15.4, "curve": [0.759, -15.4, 0.875, 0]}, {"time": 1}], "translate": [{"curve": [0.202, 0, 0.375, 1.84, 0.202, 0, 0.375, -5.01]}, {"time": 0.5, "x": 1.84, "y": -5.01, "curve": [0.759, 1.84, 0.875, 0, 0.759, -5.01, 0.875, 0]}, {"time": 1}]}, "forearm1": {"rotate": [{"curve": [0.202, 0, 0.375, 9.63]}, {"time": 0.5, "value": 9.63, "curve": [0.759, 9.63, 0.875, 0]}, {"time": 1}]}, "forearm2": {"rotate": [{}, {"time": 0.5, "value": 13.64}, {"time": 1}]}, "hand2": {"rotate": [{}, {"time": 0.5, "value": -8.65}, {"time": 1}]}, "hand1": {"rotate": [{"curve": [0.202, 0, 0.375, 12.37]}, {"time": 0.5, "value": 12.37, "curve": [0.759, 12.37, 0.875, 0]}, {"time": 1}]}, "bone2": {"rotate": [{}, {"time": 0.4667, "value": 3.15}, {"time": 0.6667, "value": 2.36}, {"time": 1}], "translate": [{"curve": [0.117, 0, 0.35, -1.24, 0.117, 0, 0.35, 2.05]}, {"time": 0.4667, "x": -1.24, "y": 2.05}, {"time": 0.6667, "x": -0.72, "y": -0.78}, {"time": 1}]}, "hair01": {"rotate": [{}, {"time": 0.4667, "value": 7.75}, {"time": 0.6667, "value": 5.81}, {"time": 1}], "translate": [{"curve": [0.117, 0, 0.35, -1.24, 0.117, 0, 0.35, 2.05]}, {"time": 0.4667, "x": -1.24, "y": 2.05}, {"time": 0.6667, "x": 0.74, "y": -1.42}, {"time": 1}]}}, "attachments": {"default": {"body": {"body": {"deform": [{"curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "offset": 12, "vertices": [-0.75589, -3.68988, -1.01899, -2.97405, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -1.01898, -2.97405, -0.7559, -3.68988, -1e-05, 0, -0.75589, -3.68988, -0.75589, -3.68988, -1.01898, -2.97405, -1.01898, -2.97405, -1.01898, -2.97405, -0.75587, -3.68988], "curve": [0.625, 0, 0.875, 1]}, {"time": 1}]}}, "eyes": {"eyes": {"deform": [{"vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "vertices": [6.7696, 1.0625, 13.34377, 0.58217, 15.22793, 0.76399, 17.05129, -0.41451, 16.40394, -0.50793, 15.22793, 0.76398, 13.34377, 0.58217, 6.12215, 0.96938], "curve": [0.794, -0.02, 0.875, 1]}, {"time": 1, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533]}]}}, "head": {"head": {"deform": [{"offset": 14, "vertices": [1.38038, -3.88539, 1.38039, -3.88566, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "offset": 14, "vertices": [2.07188, -2.55623, 2.41842, -2.57897, 1.63067, 0.06915, 1.12625, 1.37998, 9.63888, 1.16068, 2.16658, 0.74579, 0, 0, 0, 0, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.70107, 0.27222, 11.70107, 0.27221, 4.49132, 0.19015, 7.32128, 0.18938, 0, 0, 0, 0, 0, 0, 7.79456, 0.42606, 15.34019, 0.39919, 6.02913, 0.32602, 6.02913, 0.32602, 9.14707, 1.88179], "curve": [0.794, -0.02, 0.875, 1]}, {"time": 1, "offset": 14, "vertices": [1.38038, -3.88539, 1.38039, -3.88566, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741]}]}}, "mantles": {"mantles": {"deform": [{"curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "vertices": [-1.3883, 0.54368, 0.15339, -6.90907, -1.2541, -4.50875, -1.22915, -1.35892, -1.22924, -1.35755, -1.25382, -4.47607, -0.06354, -5.18391, -0.43389, 3.45036], "curve": [0.625, 0, 0.875, 1]}, {"time": 1}]}}, "mouth": {"mouth": {"deform": [{"vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "vertices": [9.24917, -1.3452, 17.81878, -0.49759, 17.68489, -0.51691, 9.1152, -1.36452], "curve": [0.794, -0.02, 0.875, 1]}, {"time": 1, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885]}]}}}}}, "idle-from fall": {"bones": {"upper-arm2": {"rotate": [{"value": 33.69}, {"time": 0.0333, "value": 29.63}, {"time": 0.2, "value": -0.65}, {"time": 0.5333, "value": 0.46}], "translate": [{"x": -1.56, "y": 4.01}, {"time": 0.0333, "x": -3.13, "y": 8.02}, {"time": 0.2, "x": -2.08, "y": 5.35}, {"time": 0.5333}]}, "body": {"rotate": [{"value": 7.9}, {"time": 0.0333, "value": 1.67}, {"time": 0.1, "value": -5.11}, {"time": 0.2, "value": 1.11}, {"time": 0.5333}], "translate": [{"y": -1.85}, {"time": 0.0333, "y": -3.7, "curve": [0.158, 0, 0.408, 0, 0.158, -3.7, 0.408, 0]}, {"time": 0.5333}]}, "head": {"rotate": [{"value": -4.66}, {"time": 0.0333, "value": 8.49}, {"time": 0.1, "value": -17.41}, {"time": 0.3, "value": -6.9}, {"time": 0.5667, "value": -4.18}], "translate": [{"x": -1.34}, {"time": 0.0333, "x": -2.69, "curve": [0.056, -5.27, 0.079, -7.65, 0.056, 0.23, 0.079, 0.45]}, {"time": 0.1, "x": -9.49, "y": 0.61, "curve": [0.175, -5.93, 0.243, -3.75, 0.175, 0.23, 0.243, 0]}, {"time": 0.3, "x": -3.75, "curve": [0.365, -3.75, 0.473, -2.14, 0.365, 0, 0.473, 0]}, {"time": 0.5667, "x": -1.06}]}, "hip": {"rotate": [{"value": -1.86}, {"time": 0.0333, "value": -3.72}, {"time": 0.2}], "translate": [{"x": -2.97, "y": -9.61}, {"time": 0.0333, "x": -5.94, "y": -19.22, "curve": [0.115, -5.94, 0.204, -3.8, 0.115, -19.22, 0.204, -13.35]}, {"time": 0.3, "y": -2.92}]}, "mantles": {"rotate": [{"value": -2.28}, {"time": 0.0333, "value": -4.56, "curve": [0.181, -3.69, 0.411, 0]}, {"time": 0.5333}], "translate": [{"x": -0.66}, {"time": 0.0333, "x": -1.32, "curve": [0.163, -1.04, 0.361, 0, 0.163, 0, 0.361, 0]}, {"time": 0.4667}, {"time": 0.5333, "x": -0.1}]}, "left-ground": {"translate": [{"x": 11.73, "y": 5.12}, {"time": 0.0333, "x": -7.15}]}, "right-ground": {"translate": [{"x": 10.22, "y": 2.29}, {"time": 0.0333, "x": 5.94}]}, "upper-arm1": {"rotate": [{"value": -46.85}, {"time": 0.1333, "value": 7.07}, {"time": 0.3333, "value": -13.9, "curve": [0.422, -9.03, 0.476, 0]}, {"time": 0.5333}], "translate": [{"x": 3.95, "y": -0.8}, {"time": 0.0333, "x": 7.89, "y": -1.61, "curve": [0.11, 7.89, 0.176, 5.34, 0.11, -1.61, 0.176, -2.8]}, {"time": 0.2333, "x": 1.66, "y": -4.52, "curve": [0.366, 1.08, 0.448, 0, 0.366, -2.94, 0.448, 0]}, {"time": 0.5333}]}, "forearm1": {"rotate": [{"value": 7.54}, {"time": 0.0667, "value": -29.76, "curve": [0.108, -29.76, 0.192, 45.6]}, {"time": 0.2333, "value": 45.6, "curve": [0.308, 45.6, 0.458, 0]}, {"time": 0.5333}]}, "forearm2": {"rotate": [{"value": 34.52}, {"time": 0.0333, "value": 56.72, "curve": [0.033, -0.73, 0.158, -10.87]}, {"time": 0.2, "value": -10.87}, {"time": 0.5333}]}, "thigh2": {"rotate": [{"value": 44.27}, {"time": 0.0333, "value": 20.94}, {"time": 0.5333, "value": 16.55}]}, "thigh1": {"rotate": [{"value": 64.42}, {"time": 0.0333, "value": 17.95}, {"time": 0.5333, "value": 6.6}]}, "foot1": {"rotate": [{"value": 9.81}, {"time": 0.0333}]}, "foot2": {"rotate": [{"value": 19.58}, {"time": 0.0333}]}, "hand2": {"rotate": [{"value": -4.33}, {"time": 0.0333, "value": -8.65, "curve": [0.033, -15.61, 0.133, -18.06]}, {"time": 0.1667, "value": -18.06, "curve": [0.225, -18.06, 0.342, 0]}, {"time": 0.4}]}, "hand1": {"rotate": [{"value": 6.18}, {"time": 0.0333, "value": 12.37, "curve": [0.144, 12.37, 0.21, 28.81]}, {"time": 0.2667, "value": 37.67, "curve": [0.359, 15.44, 0.445, 0]}, {"time": 0.5333}]}, "shin2": {"rotate": [{"value": -57.8}, {"time": 0.0333, "value": -25.6}, {"time": 0.5333, "value": -23.54}]}, "shin1": {"rotate": [{"value": -98.69}, {"time": 0.0333, "value": -48.2}, {"time": 0.5333, "value": -30.35}]}, "bone2": {"rotate": [{"value": 8.01}, {"time": 0.0333, "value": 16.02}, {"time": 0.1667, "value": -10.45}, {"time": 0.3333, "value": 2.36}, {"time": 0.5333}], "translate": [{"x": -3.51, "y": 7.69}, {"time": 0.0333, "x": -7.02, "y": 15.39}, {"time": 0.1667, "x": 6.98, "y": -6.57}, {"time": 0.3333, "x": -0.72, "y": -0.78}, {"time": 0.5333}]}, "hair01": {"rotate": [{"value": 8.34}, {"time": 0.0333, "value": 16.67}, {"time": 0.1333, "value": -8}, {"time": 0.3333, "value": 5.81}, {"time": 0.5333}], "translate": [{"x": -1, "y": 6.5}, {"time": 0.0333, "x": -1.99, "y": 13.01}, {"time": 0.1333, "x": 1.46, "y": -7.98}, {"time": 0.3333, "x": 0.74, "y": -1.42}, {"time": 0.5333}]}}, "attachments": {"default": {"body": {"body": {"deform": [{}, {"time": 0.0333, "offset": 12, "vertices": [-0.37794, -1.84494, -0.5095, -1.48702, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.50949, -1.48702, -0.37795, -1.84494, -1e-05, 0, -0.37795, -1.84494, -0.37795, -1.84494, -0.50949, -1.48702, -0.50949, -1.48702, -0.50949, -1.48702, -0.37793, -1.84494]}, {"time": 0.0667, "offset": 12, "vertices": [-0.75589, -3.68988, -1.01899, -2.97405, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -1.01898, -2.97405, -0.7559, -3.68988, -1e-05, 0, -0.75589, -3.68988, -0.75589, -3.68988, -1.01898, -2.97405, -1.01898, -2.97405, -1.01898, -2.97405, -0.75587, -3.68988], "curve": [0.192, 0, 0.442, 1]}, {"time": 0.5667}]}}, "eyes": {"eyes": {"deform": [{}, {"time": 0.0333, "vertices": [3.3848, 0.53125, 6.67189, 0.29108, 7.61397, 0.382, 8.52564, -0.20725, 8.20197, -0.25397, 7.61397, 0.382, 6.67189, 0.29108, 3.06108, 0.48469]}, {"time": 0.0667, "vertices": [6.7696, 1.0625, 13.34377, 0.58217, 15.22793, 0.76399, 17.05129, -0.41451, 16.40394, -0.50793, 15.22793, 0.76398, 13.34377, 0.58217, 6.12215, 0.96938], "curve": [0.091, -0.07, 0.113, 0.3]}, {"time": 0.1333, "vertices": [6.72558, 1.0556, 13.25701, 0.57839, 15.12892, 0.75903, 16.94042, -0.41182, 16.29729, -0.50463, 15.12892, 0.75901, 13.25701, 0.57839, 6.08234, 0.96307], "curve": [0.172, 0.21, 0.205, 0.57]}, {"time": 0.2333, "vertices": [6.41825, 1.00736, 12.65124, 0.55196, 14.43761, 0.72434, 16.16634, -0.39299, 15.55259, -0.48157, 14.43761, 0.72433, 12.65124, 0.55196, 5.80442, 0.91907], "curve": [0.383, 0.39, 0.463, 1]}, {"time": 0.5667, "vertices": [3.86398, 0.60646, 7.61642, 0.33229, 8.69186, 0.43607, 9.73261, -0.2366, 9.36311, -0.28992, 8.69186, 0.43607, 7.61642, 0.33229, 3.49443, 0.5533]}]}}, "head": {"head": {"deform": [{}, {"time": 0.0333, "offset": 14, "vertices": [1.20921, 0.05115, 1.20921, 0.05117, 0.81534, 0.03457, 0.56313, 0.68999, 4.81944, 0.58034, 1.08329, 0.37289, 0, 0, 0, 0, -1.39058, -0.05896, -1.39058, -0.05896, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.85053, 0.13611, 5.85053, 0.1361, 2.24566, 0.09507, 3.66064, 0.09469, 0, 0, 0, 0, 0, 0, 3.89728, 0.21303, 7.67009, 0.1996, 3.01457, 0.16301, 3.01457, 0.16301, 4.57353, 0.9409]}, {"time": 0.0667, "offset": 14, "vertices": [2.00027, -2.71393, 2.41841, -2.74482, 1.63067, 0.06915, 1.12625, 1.37998, 9.63888, 1.16068, 2.16658, 0.74579, 0, 0, 0, 0, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.70107, 0.27222, 11.70107, 0.27221, 4.49132, 0.19015, 7.32128, 0.18938, 0, 0, 0, 0, 0, 0, 7.79456, 0.42606, 15.34019, 0.39919, 6.02913, 0.32602, 6.02913, 0.32602, 9.14707, 1.88179], "curve": [0.091, -0.07, 0.113, 0.3]}, {"time": 0.1333, "offset": 14, "vertices": [2.86001, -3.15265, 2.40266, -3.1847, 1.62007, 0.0687, 1.11893, 1.37101, 9.53403, 1.15649, 2.15249, 0.74094, 0, 0, 0, 0, -2.76307, -0.11716, -2.76307, -0.11716, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.62499, 0.27045, 11.62499, 0.27044, 4.46211, 0.18891, 7.27368, 0.18815, 0, 0, 0, 0, 0, 0, 7.7017, 0.42665, 15.24045, 0.3966, 5.96039, 0.31969, 5.96039, 0.31969, 9.08759, 1.86955], "curve": [0.172, 0.21, 0.205, 0.57]}, {"time": 0.2333, "offset": 14, "vertices": [2.3668, -3.76697, 2.2929, -3.76766, 1.54604, 0.06556, 1.0678, 1.30836, 8.80197, 1.12727, 2.05414, 0.70708, 0, 0, 0, 0, -2.63681, -0.11182, -2.63681, -0.11181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.09379, 0.25809, 11.09379, 0.25808, 4.25822, 0.18028, 6.94132, 0.17955, 0, 0, 0, 0, 0, 0, 7.05337, 0.43077, 14.54404, 0.37847, 5.48038, 0.27545, 5.48038, 0.27545, 8.67234, 1.78413], "curve": [0.383, 0.39, 0.463, 1]}, {"time": 0.5667, "offset": 14, "vertices": [1.38038, -3.57954, 1.38039, -3.57972, 0.93076, 0.03947, 0.64285, 0.78767, 2.71751, 0.88435, 1.23665, 0.42568, 0, 0, 0, 0, -1.58744, -0.06729, -1.58744, -0.06731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.67879, 0.1554, 6.67879, 0.15537, 2.56357, 0.10853, 4.17887, 0.1081, 0, 0, 0, 0, 0, 0, 1.6648, 0.46504, 8.75594, 0.22785, 1.49083, -0.09221, 1.49083, -0.09221, 5.221, 1.0741]}]}}, "mantles": {"mantles": {"deform": [{}, {"time": 0.0333, "vertices": [-0.69416, 0.27184, 0.07669, -3.45453, -0.62705, -2.25438, -0.61458, -0.67946, -0.61463, -0.67877, -0.62692, -2.23804, -0.03177, -2.59196, -0.21695, 1.72519]}, {"time": 0.0667, "vertices": [-1.3883, 0.54368, 0.15339, -6.90907, -1.2541, -4.50875, -1.22915, -1.35892, -1.22924, -1.35755, -1.25382, -4.47607, -0.06354, -5.18391, -0.43389, 3.45036], "curve": [0.192, 0, 0.442, 1]}, {"time": 0.5667}]}}, "mouth": {"mouth": {"deform": [{}, {"time": 0.0333, "vertices": [4.62458, -0.6726, 8.90939, -0.24879, 8.84245, -0.25845, 4.5576, -0.68226]}, {"time": 0.0667, "vertices": [9.24917, -1.3452, 17.81878, -0.49759, 17.68489, -0.51691, 9.1152, -1.36452], "curve": [0.091, -0.07, 0.113, 0.3]}, {"time": 0.1333, "vertices": [9.18903, -1.33645, 17.70293, -0.49435, 17.56991, -0.51355, 9.05593, -1.35565], "curve": [0.172, 0.21, 0.205, 0.57]}, {"time": 0.2333, "vertices": [8.76914, -1.27538, 16.894, -0.47176, 16.76706, -0.49008, 8.64213, -1.2937], "curve": [0.383, 0.39, 0.463, 1]}, {"time": 0.5667, "vertices": [5.27928, -0.76782, 10.17068, -0.28402, 10.09426, -0.29504, 5.20281, -0.77885]}]}}}}}, "jump": {"bones": {"right-ground": {"translate": [{"x": -6.83, "y": 12.06}, {"time": 0.5, "x": -1.96, "y": 18.55}, {"time": 1, "x": -6.83, "y": 12.06}]}, "left-ground": {"translate": [{"x": 2.41, "y": 1.21}, {"time": 0.6333, "x": -0.29, "y": 4.45}, {"time": 1, "x": 2.41, "y": 1.21}]}, "foot1": {"rotate": [{"value": -32.24}]}, "foot2": {"rotate": [{"value": -33.31}]}, "body": {"rotate": [{"value": -10.4}, {"time": 0.5, "value": -8.9}, {"time": 1, "value": -10.4}]}, "head": {"rotate": [{"value": 4.89}, {"time": 0.3333, "value": 7.07}, {"time": 1, "value": 4.89}]}, "upper-arm1": {"rotate": [{"value": 22.69}, {"time": 0.5, "value": 7.75}, {"time": 1, "value": 22.69}]}, "forearm1": {"rotate": [{"value": 56.42}, {"time": 0.5, "value": 75.53}, {"time": 1, "value": 56.42}]}, "upper-arm2": {"rotate": [{"value": 23.72}, {"time": 0.5, "value": 14.69}, {"time": 1, "value": 23.72}]}, "forearm2": {"rotate": [{"value": 20.06}, {"time": 0.5, "value": 35.02}, {"time": 1, "value": 20.06}]}}}, "morningstar pose": {"bones": {"upper-arm1": {"rotate": [{"value": -7.09}], "translate": [{"x": 4.87, "y": 0.85}]}, "body": {"rotate": [{"value": -12.99}], "translate": [{"x": 4.98, "y": -0.91}]}, "upper-arm2": {"rotate": [{"value": 52.48}]}, "forearm2": {"rotate": [{"value": 27.54}]}, "head": {"rotate": [{"value": 8.92}]}, "right-ground": {"translate": [{"x": 35.9, "y": 4.02}]}, "right-ankle": {"rotate": [{"value": 8.94}], "translate": [{"x": -23.47, "y": -10.78}]}, "foot2": {"rotate": [{"value": 1.59}]}, "left-ground": {"translate": [{"x": -12.33, "y": 6.27}]}, "foot1": {"rotate": [{"value": -5.51}]}, "cape1": {"rotate": [{"value": -16.72}]}, "cape2": {"rotate": [{"value": -16.72}]}, "cape3": {"rotate": [{"value": -16.72}]}, "cape4": {"rotate": [{"value": -16.72}]}, "hand2": {"rotate": [{"value": -26.12}]}, "chain1": {"rotate": [{"value": -111.95}]}, "chain3": {"rotate": [{"value": -30.04}]}, "chain5": {"rotate": [{"value": 5.57}]}, "chain7": {"rotate": [{"value": 17.43}]}, "left-ankle": {"translate": [{"x": -9.94, "y": -8.62}]}, "hip": {"translate": [{"x": -4.98, "y": -14.06}]}, "weapon-morningstar": {"translate": [{"x": -2.55, "y": -9.13}]}, "hand1": {"rotate": [{"value": 32.29}]}, "thigh1": {"translate": [{"x": -0.88, "y": 4.42}]}, "weapon-morningstar-physics1": {"rotate": [{"value": -109.67}]}, "weapon-morningstar-physics2": {"rotate": [{"value": -22.2}]}, "weapon-morningstar-physics3": {"rotate": [{"value": -22.35}]}, "weapon-morningstar-physics4": {"rotate": [{"value": 22.11}]}}, "attachments": {"default": {"eyes": {"eyes": {"deform": [{"offset": 2, "vertices": [9.12951, 0.00041, 6.71509, 0.0004, 3.75744, 0.00018, 3.75744, 0.00018, 6.71509, 0.0004, 9.12951]}]}}, "head": {"head": {"deform": [{"offset": 46, "vertices": [4.89935, 0.38196, 4.89929, 0.38176, 0, 0, 0, 0, 7.88389, -1.00815, 7.88392, -1.0079, 0, 0, 9.84631, -0.95363, 13.40236, 0.79156, 0, 0, 0, 0, 2.31737, 1.33926]}]}}, "mouth": {"mouth": {"deform": [{"vertices": [-1.06702, 3.68677, 5.16507, 9e-05, 5.16507, 9e-05, -1.06702, 3.68677]}]}}}}}, "run": {"bones": {"upper-arm1": {"rotate": [{"value": -40.45}, {"time": 0.0441, "value": -34.97}, {"time": 0.0881, "value": 1.8}, {"time": 0.1322, "value": 42.59}, {"time": 0.3117, "value": 77.45}, {"time": 0.3526, "value": 48.22}, {"time": 0.3967, "value": 26.18}, {"time": 0.4407, "value": -22.81}, {"time": 0.5333, "value": -40.45}], "translate": [{}, {"time": 0.0441, "x": 1.34, "y": 0.57}, {"time": 0.1763, "x": -2.75, "y": -5.59}, {"time": 0.3117, "x": -3.11, "y": -2.46}, {"time": 0.4407, "x": -0.75, "y": 4.05}, {"time": 0.5333}]}, "forearm1": {"rotate": [{"value": 27.41}, {"time": 0.0441, "value": 21.5}, {"time": 0.0881, "value": 16.71}, {"time": 0.1322, "value": 30.62}, {"time": 0.3117, "value": 38.33}, {"time": 0.3526, "value": 37.81}, {"time": 0.3967, "value": 16.47}, {"time": 0.4407, "value": 17.71}, {"time": 0.5333, "value": 27.41}]}, "hand1": {"rotate": [{"value": -20.2}, {"time": 0.0441, "value": -24.83}, {"time": 0.1763, "value": -14.29}, {"time": 0.3526, "value": 21.85}, {"time": 0.3967, "value": 9.46}, {"time": 0.4407, "value": 12.08}, {"time": 0.5333, "value": -20.2}]}, "left-ground": {"translate": [{"x": 49.53, "y": 0.29}, {"time": 0.0441, "x": 39.67, "y": 0.29}, {"time": 0.0881, "x": -3.64, "y": 0.29}, {"time": 0.1322, "x": -10.76, "y": 0.29}, {"time": 0.1763, "x": -16.66, "y": 22.67}, {"time": 0.2204, "x": -10.01, "y": 27.65}, {"time": 0.2667, "x": -11.26, "y": 17.21}, {"time": 0.3117, "x": 12.42, "y": 8.33}, {"time": 0.3526, "x": 37.47, "y": 7.87}, {"time": 0.3967, "x": 52.34, "y": 6.1}, {"time": 0.4407, "x": 68.7, "y": 20}, {"time": 0.4848, "x": 68.7, "y": 8.54}, {"time": 0.5333, "x": 49.53, "y": 0.29}]}, "foot1": {"rotate": [{"value": 20.27}, {"time": 0.0441, "value": -0.61}, {"time": 0.1322, "value": -31.14}, {"time": 0.1763, "value": -97.65}, {"time": 0.2204, "value": -71.21}, {"time": 0.2667, "value": -84.96}, {"time": 0.3117, "value": -63.21}, {"time": 0.3526, "value": -8.37}, {"time": 0.3967, "value": -18.34}, {"time": 0.4407, "value": 34.08}, {"time": 0.5333, "value": 20.27}]}, "right-ground": {"translate": [{"x": -58.45, "y": 26.31}, {"time": 0.0441, "x": -42.01, "y": 21.37}, {"time": 0.0881, "x": -5.28, "y": 9.86}, {"time": 0.1322, "x": 11.16, "y": 12.05}, {"time": 0.1763, "x": 19.38, "y": 26.85}, {"time": 0.2204, "x": 23.89, "y": 17.18}, {"time": 0.2667, "x": 21.52, "y": 0.15}, {"time": 0.3117, "x": -19.32, "y": 0.15}, {"time": 0.3526, "x": -53.26, "y": -0.03}, {"time": 0.3967, "x": -65.63, "y": 6.75}, {"time": 0.4407, "x": -75.71, "y": 39.78}, {"time": 0.5333, "x": -58.45, "y": 26.31}]}, "foot2": {"rotate": [{"value": -97.04}, {"time": 0.0881, "value": -74.34}, {"time": 0.1322, "value": -4.27}, {"time": 0.1763, "value": 13.39}, {"time": 0.2204, "value": 28.96}, {"time": 0.2667, "value": 35.05}, {"time": 0.3117, "value": 7.53}, {"time": 0.3526, "value": -31.42}, {"time": 0.4407, "value": -99.94}, {"time": 0.5333, "value": -97.04}]}, "hip": {"translate": [{"y": -7.82}, {"time": 0.0441, "x": 1.64, "y": -12.3}, {"time": 0.0881, "x": 1.64, "y": -10.66, "curve": [0.11, 1.64, 0.154, 1.64, 0.11, -10.66, 0.154, 9.55]}, {"time": 0.1763, "x": 1.64, "y": 9.55}, {"time": 0.2204, "x": 1.64, "y": -0.53}, {"time": 0.2667, "y": -7.82}, {"time": 0.3117, "y": -9.6}, {"time": 0.3526, "y": -8.91, "curve": [0.375, 0, 0.419, 0, 0.375, -8.91, 0.419, 6.36]}, {"time": 0.4407, "y": 6.36}, {"time": 0.4848, "x": 1.64, "y": -0.53}, {"time": 0.5333, "y": -7.82}]}, "left-ankle": {"translate": [{}, {"time": 0.1763, "x": -1.45, "y": 9.68}, {"time": 0.5333}]}, "body": {"rotate": [{"value": -11.15}, {"time": 0.0441, "value": -16.76}, {"time": 0.1763, "value": -8.61}, {"time": 0.3117, "value": -20.18}, {"time": 0.4407, "value": -8.61}, {"time": 0.5333, "value": -11.15}], "translate": [{"x": 3.37, "y": -2.53}, {"time": 0.0441, "x": 4.99, "y": 0.87}, {"time": 0.1763, "x": 2.61, "y": 4.03}, {"time": 0.3117, "x": 5.73, "y": 0.47}, {"time": 0.4407, "x": 2.7, "y": 5.11}, {"time": 0.5333, "x": 3.37, "y": -2.53}]}, "thigh1": {"translate": [{"x": 6.03}, {"time": 0.1322, "x": -3.58, "y": 1.43}, {"time": 0.1763, "x": 0.24, "y": 2.72}, {"time": 0.2667, "x": 6.14, "y": -0.59}, {"time": 0.3306, "x": 8.82, "y": 5.78}, {"time": 0.3967, "x": 31, "y": 0.36}, {"time": 0.4848, "x": 22.13, "y": 0.18}, {"time": 0.5333, "x": 6.03}]}, "thigh2": {"translate": [{"x": -8.32, "y": -2.38}, {"time": 0.1322, "x": -3.93, "y": -2.38}, {"time": 0.2667, "x": 3.23, "y": -1.53}, {"time": 0.3526, "x": -18.12, "y": 0.18}, {"time": 0.3967, "x": -28.39, "y": 0.18}, {"time": 0.5333, "x": -8.32, "y": -2.38}]}, "head": {"rotate": [{"value": 8.09}, {"time": 0.0441, "value": 1.34, "curve": [0.077, 1.34, 0.157, 5.49]}, {"time": 0.1763, "value": 6.46}, {"time": 0.2667, "value": 10.37}, {"time": 0.3117, "value": 3.57, "curve": [0.344, 3.57, 0.42, 6.84]}, {"time": 0.4407, "value": 7.82}, {"time": 0.5333, "value": 8.09}], "translate": [{"x": 2.88, "y": 0.24}, {"time": 0.0441, "x": -3.51, "y": -1.82}, {"time": 0.1763, "x": 4.61, "y": 0.76}, {"time": 0.2667, "x": 1.55, "y": -6.16}, {"time": 0.3117, "x": -7.08, "y": -8.67}, {"time": 0.4407, "x": 7.04, "y": 0.05}, {"time": 0.5333, "x": 2.88, "y": 0.24}]}, "mantles": {"rotate": [{}, {"time": 0.0441, "value": -2.18}, {"time": 0.2667, "value": 2.9}, {"time": 0.3117, "value": -0.95}, {"time": 0.5333}], "translate": [{}, {"time": 0.0441, "x": -2.6, "y": -0.65}, {"time": 0.2667, "x": 1.4, "y": -0.59}, {"time": 0.3117, "x": -2.6, "y": -0.65}, {"time": 0.5333}]}, "upper-arm2": {"rotate": [{"value": 30.8}, {"time": 0.0441, "value": 38.3}, {"time": 0.1322, "value": 5.43}, {"time": 0.2204, "value": -25.39, "curve": [0.243, -25.39, 0.289, -31.62]}, {"time": 0.3117, "value": -31.62}, {"time": 0.4407, "value": 21.28}, {"time": 0.5333, "value": 30.8}], "translate": [{"x": -5.34, "y": -1.56}, {"time": 0.1763, "x": -6.44, "y": 2.12}, {"time": 0.5333, "x": -5.34, "y": -1.56}]}, "forearm2": {"rotate": [{"value": 35.9}, {"time": 0.0881, "value": 25.71}, {"time": 0.1763, "value": 29.3}, {"time": 0.2204, "value": 34.63, "curve": [0.243, 34.63, 0.289, 38.95]}, {"time": 0.3117, "value": 38.95}, {"time": 0.4407, "value": 30.1}, {"time": 0.5333, "value": 35.9}]}, "hand2": {"rotate": [{"value": 8.06}, {"time": 0.0441, "value": 12.46}, {"time": 0.1763, "value": 33.85}, {"time": 0.4407, "value": 22.92}, {"time": 0.5333, "value": 8.06}]}, "hair01": {"rotate": [{"value": 44.05}, {"time": 0.0441, "value": 14.22, "curve": [0.066, 14.22, 0.11, 5.91]}, {"time": 0.1322, "value": 5.91, "curve": [0.154, 5.91, 0.198, 31.55]}, {"time": 0.2204, "value": 31.55}, {"time": 0.2667, "value": 44.05}, {"time": 0.3117, "curve": [0.333, 0, 0.375, 5.91]}, {"time": 0.3967, "value": 5.91, "curve": [0.419, 5.91, 0.463, 31.55]}, {"time": 0.4848, "value": 31.55}, {"time": 0.5333, "value": 44.05}], "translate": [{"x": 2.05, "y": 5.53}, {"time": 0.0441, "curve": [0.055, 0, 0.077, 0.4, 0.055, 0, 0.077, -10.84]}, {"time": 0.0881, "x": 0.4, "y": -10.84}, {"time": 0.1322, "x": 4.4, "y": -15.88, "curve": [0.154, 4.4, 0.198, 5.94, 0.154, -15.88, 0.198, -2.13]}, {"time": 0.2204, "x": 5.94, "y": -2.13}, {"time": 0.2667, "x": 2.05, "y": 5.53}, {"time": 0.3117, "curve": [0.322, 0, 0.342, 0.4, 0.322, 0, 0.342, -10.84]}, {"time": 0.3526, "x": 0.4, "y": -10.84}, {"time": 0.3967, "x": 4.4, "y": -15.88, "curve": [0.419, 4.4, 0.463, 5.94, 0.419, -15.88, 0.463, -2.13]}, {"time": 0.4848, "x": 5.94, "y": -2.13}, {"time": 0.5333, "x": 2.05, "y": 5.53}]}, "bone2": {"rotate": [{"value": 16.31}, {"time": 0.0441, "curve": [0.055, 0, 0.077, -10.17]}, {"time": 0.0881, "value": -10.17}, {"time": 0.1322, "value": -14.57, "curve": [0.154, -14.57, 0.198, 9.84]}, {"time": 0.2204, "value": 9.84}, {"time": 0.2667, "value": 16.31}, {"time": 0.3117, "curve": [0.322, 0, 0.342, -10.17]}, {"time": 0.3526, "value": -10.17}, {"time": 0.3967, "value": -14.57, "curve": [0.419, -14.57, 0.463, 9.84]}, {"time": 0.4848, "value": 9.84}, {"time": 0.5333, "value": 16.31}], "translate": [{"x": -13.75, "y": 7.39}, {"time": 0.0441, "curve": [0.055, 0, 0.077, 3.59, 0.055, 0, 0.077, -1.16]}, {"time": 0.0881, "x": 3.59, "y": -1.16}, {"time": 0.1322, "x": 8.09, "y": -5.72, "curve": [0.154, 8.09, 0.198, -8.03, 0.154, -5.72, 0.198, 1.86]}, {"time": 0.2204, "x": -8.03, "y": 1.86}, {"time": 0.2667, "x": -13.75, "y": 7.39}, {"time": 0.3117, "x": -9.98, "y": 3.28, "curve": [0.322, -9.98, 0.342, 3.59, 0.322, 3.28, 0.342, -1.16]}, {"time": 0.3526, "x": 3.59, "y": -1.16}, {"time": 0.3967, "x": 8.09, "y": -5.72, "curve": [0.419, 8.09, 0.463, -8.03, 0.419, -5.72, 0.463, 1.86]}, {"time": 0.4848, "x": -8.03, "y": 1.86}, {"time": 0.5333, "x": -13.75, "y": 7.39}]}}, "attachments": {"default": {"body": {"body": {"deform": [{"offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544], "curve": [0.067, 0, 0.2, 1]}, {"time": 0.2667, "offset": 8, "vertices": [-0.57551, -1.94941, 2.00168, -9.49192, -0.33626, -11.77182, -6.3903, -8.66438, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -6.3903, -8.66438, -0.33627, -11.77181, -1e-05, 0, -0.33626, -11.77182, -0.33626, -11.77182, -6.3903, -8.66438, -6.3903, -8.66438, -6.3903, -8.66438, -0.33624, -11.77182], "curve": [0.333, 0, 0.467, 1]}, {"time": 0.5333, "offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544]}]}}, "eyes": {"eyes": {"deform": [{}, {"time": 0.2667, "vertices": [1.74138, 1.06249, 15.00201, 0.58574, 19.83157, 0.7739, 23.31314, -0.40104, 22.66579, -0.49446, 19.83157, 0.7739, 15.00201, 0.58574, 1.09394, 0.96937]}, {"time": 0.5333}]}}, "head": {"head": {"deform": [{}, {"time": 0.0881, "offset": 14, "vertices": [-0.63123, -3.55455, 5e-05, -3.61035, -0.74866, -6e-05, 2.23631, -3e-05, 6.21925, -4e-05, 3.0556, -2e-05, -2.05153, -2e-05, -2.05153, -2e-05, -1.74058, 0.50937, -1.81357, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.00372, -1.17111, 4.17159, -1e-05, 5.00946, -5e-05, 5.47429, -0.0001, 1.60961, -1.55718, 2.14941, -0.6288, 0, 0, 5.07674, -1e-05, 5.18583, -6e-05, 4.4898, -9e-05, 4.4898, -9e-05, 2.63893, 0.56216, 1.37451, -1.32975, 1.83551, -0.53701, 1.67599, -1.6214, 2.23808, -0.65477]}, {"time": 0.1322, "offset": 14, "vertices": [-0.68909, -4.76839, 3e-05, -4.82112, -1.10721, -9e-05, 3.30735, -5e-05, 9.19785, -6e-05, 4.51902, -2e-05, -3.03408, -2e-05, -3.03408, -2e-05, -2.5742, 0.75332, -2.68214, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.92124, -1.73199, 6.1695, -1e-05, 7.40866, -7e-05, 8.09611, -0.00015, 2.3805, -2.30297, 3.17884, -0.92995, 0, 0, 7.50817, -1e-05, 7.6695, -9e-05, 6.64012, -0.00013, 6.64012, -0.00013, 3.9028, 0.83139, 2.03282, -1.96661, 2.71459, -0.79421, 2.47868, -2.39795, 3.30997, -0.96836]}, {"time": 0.2667, "offset": 18, "vertices": [-2.14141, -0.00017, 6.39661, -9e-05, 17.78916, -0.00011, 8.74004, -5e-05, -5.86807, -5e-05, -5.86807, -5e-05, -4.97865, 1.45697, -5.18741, -2e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.45201, -3.34976, 11.93216, -2e-05, 14.32876, -0.00014, 15.65833, -0.00029, 4.60402, -4.45406, 6.14805, -1.79858, 0, 0, 14.52121, -2e-05, 14.83325, -0.00018, 12.84236, -0.00026, 12.84236, -0.00026, 7.54823, 1.60796, 3.93158, -3.80354, 5.25018, -1.53604, 4.79391, -4.63776, 6.40166, -1.87286]}, {"time": 0.3117, "offset": 14, "vertices": [-0.03494, -5.18138, 4e-05, -5.18165, -1.93269, -0.00015, 5.77313, -8e-05, 16.05525, -0.0001, 7.88815, -4e-05, -5.29611, -4e-05, -5.29611, -4e-05, -4.49339, 1.31496, -4.6818, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.33579, -3.02326, 10.76914, -1e-05, 12.93214, -0.00012, 14.13212, -0.00026, 4.15527, -4.01993, 5.5488, -1.62328, 0, 0, 13.10583, -1e-05, 13.38746, -0.00017, 11.59062, -0.00023, 11.59062, -0.00023, 6.81251, 1.45123, 3.54837, -3.43281, 4.73844, -1.38632, 4.32665, -4.18572, 5.7777, -1.69032]}, {"time": 0.3333, "offset": 14, "vertices": [-0.0662, -3.38727, -1e-05, -3.38785, -1.74639, -0.00014, 5.21665, -7e-05, 14.50766, -9e-05, 7.1278, -4e-05, -4.78561, -4e-05, -4.78561, -4e-05, -4.06026, 1.18821, -4.23051, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.3395, -2.73184, 9.73108, -1e-05, 11.68559, -0.00011, 12.7699, -0.00024, 3.75473, -3.63244, 5.01394, -1.46681, 0, 0, 11.84254, -1e-05, 12.09702, -0.00015, 10.47338, -0.00021, 10.47338, -0.00021, 6.15584, 1.31134, 3.20634, -3.10192, 4.2817, -1.25269, 3.9096, -3.78225, 5.22078, -1.52738]}, {"time": 0.3526, "offset": 14, "vertices": [-0.10253, -3.91882, -1e-05, -3.9203, -1.56741, -0.00012, 4.68201, -7e-05, 13.0208, -8e-05, 6.39729, -3e-05, -4.29515, -3e-05, -4.29515, -3e-05, -3.64413, 1.06643, -3.79694, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.38232, -2.45186, 8.73376, -1e-05, 10.48796, -0.0001, 11.46114, -0.00021, 3.36992, -3.26016, 4.50007, -1.31648, 0, 0, 10.62883, -1e-05, 10.85722, -0.00013, 9.39999, -0.00019, 9.39999, -0.00019, 5.52494, 1.17695, 2.87773, -2.78401, 3.84288, -1.12431, 3.50891, -3.39462, 4.68571, -1.37085]}, {"time": 0.3967, "offset": 14, "vertices": [-0.22822, -4.28248, -1e-05, -4.29148, -1.11217, -9e-05, 3.32218, -5e-05, 9.23907, -6e-05, 4.53928, -2e-05, -3.04768, -2e-05, -3.04768, -2e-05, -2.58574, 0.7567, -2.69416, -1e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.94778, -1.73975, 6.19715, -1e-05, 7.44187, -7e-05, 8.1324, -0.00015, 2.39117, -2.31329, 3.19308, -0.93412, 0, 0, 7.54182, -1e-05, 7.70388, -0.0001, 6.66988, -0.00013, 6.66988, -0.00013, 3.92029, 0.83512, 2.04193, -1.97543, 2.72676, -0.79777, 2.48979, -2.40869, 3.3248, -0.9727]}, {"time": 0.5333}]}}, "mantles": {"mantles": {"deform": [{"offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224], "curve": [0.067, 0, 0.2, 1]}, {"time": 0.2667, "vertices": [4.71967, -7.39242, 1.11851, -17.07923, 2.82789, -9.03923, -0.48091, -3.11594, -0.48091, -3.11594, 2.82788, -9.03923, 0.68637, -9.22626, -0.1616, 3.04387], "curve": [0.333, 0, 0.467, 1]}, {"time": 0.5333, "offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224]}]}}, "mouth": {"mouth": {"deform": [{}, {"time": 0.2667, "vertices": [9.24917, -1.3452, 25.71696, -0.48061, 25.58308, -0.49992, 9.1152, -1.36452]}, {"time": 0.5333}]}}}}, "events": [{"time": 0.2667, "name": "footstep"}, {"time": 0.5333, "name": "footstep"}]}, "run-from fall": {"bones": {"upper-arm1": {"rotate": [{"value": -25.71, "curve": [0, 53.98, 0.019, 62.83]}, {"time": 0.0333, "value": 62.83}, {"time": 0.0667, "value": 48.22}, {"time": 0.1, "value": 26.18}, {"time": 0.1333, "value": -22.81}, {"time": 0.2333, "value": -40.45}, {"time": 0.2667, "value": -34.97}, {"time": 0.3333, "value": 1.8}, {"time": 0.3667, "value": 42.59}, {"time": 0.5333, "value": 77.45}, {"time": 0.6, "value": 48.22}, {"time": 0.6333, "value": 26.18}, {"time": 0.6667, "value": -22.81}, {"time": 0.7667, "value": -40.45}], "translate": [{"curve": [0, 9, 0.019, 10, 0, 0.99, 0.019, 1.1]}, {"time": 0.0333, "x": 10, "y": 1.1}, {"time": 0.1333, "x": -0.75, "y": 4.05}, {"time": 0.2333}, {"time": 0.2667, "x": 1.34, "y": 0.57}, {"time": 0.4, "x": -2.75, "y": -5.59}, {"time": 0.5333, "x": -3.11, "y": -2.46}, {"time": 0.6667, "x": -0.75, "y": 4.05}, {"time": 0.7667}]}, "forearm1": {"rotate": [{"value": 26.18, "curve": [0, 36.88, 0.019, 38.07]}, {"time": 0.0333, "value": 38.07}, {"time": 0.0667, "value": 37.81}, {"time": 0.1, "value": 16.47}, {"time": 0.1333, "value": 17.71}, {"time": 0.2333, "value": 27.41}, {"time": 0.2667, "value": 21.5}, {"time": 0.3333, "value": 16.71}, {"time": 0.3667, "value": 30.62}, {"time": 0.5333, "value": 38.33}, {"time": 0.6, "value": 37.81}, {"time": 0.6333, "value": 16.47}, {"time": 0.6667, "value": 17.71}, {"time": 0.7667, "value": 27.41}]}, "hand1": {"rotate": [{"curve": [0, 14.24, 0.019, 15.82]}, {"time": 0.0333, "value": 15.82}, {"time": 0.0667, "value": 21.85}, {"time": 0.1, "value": 9.46}, {"time": 0.1333, "value": 12.08}, {"time": 0.2333, "value": -20.2}, {"time": 0.2667, "value": -24.83}, {"time": 0.4, "value": -14.29}, {"time": 0.6, "value": 21.85}, {"time": 0.6333, "value": 9.46}, {"time": 0.6667, "value": 12.08}, {"time": 0.7667, "value": -20.2}]}, "left-ground": {"translate": [{"x": 35.34, "y": 4.15, "curve": [0, 25.98, 0.019, 24.94, 0, 7.7, 0.019, 8.1]}, {"time": 0.0333, "x": 24.94, "y": 8.1}, {"time": 0.0667, "x": 37.47, "y": 7.87}, {"time": 0.1, "x": 52.34, "y": 6.1}, {"time": 0.1333, "x": 68.7, "y": 20}, {"time": 0.2, "x": 68.7, "y": 8.54}, {"time": 0.2333, "x": 49.53, "y": 0.29}, {"time": 0.2667, "x": 39.67, "y": 0.29}, {"time": 0.3333, "x": -3.64, "y": 0.29}, {"time": 0.3667, "x": -10.76, "y": 0.29}, {"time": 0.4, "x": -16.66, "y": 22.67}, {"time": 0.4667, "x": -10.01, "y": 27.65}, {"time": 0.5, "x": -11.26, "y": 17.21}, {"time": 0.5333, "x": 12.42, "y": 8.33}, {"time": 0.6, "x": 37.47, "y": 7.87}, {"time": 0.6333, "x": 52.34, "y": 6.1}, {"time": 0.6667, "x": 68.7, "y": 20}, {"time": 0.7333, "x": 68.7, "y": 8.54}, {"time": 0.7667, "x": 49.53, "y": 0.29}]}, "foot1": {"rotate": [{"value": 19.63, "curve": [0, -30.25, 0.019, -35.79]}, {"time": 0.0333, "value": -35.79}, {"time": 0.0667, "value": -8.37}, {"time": 0.1, "value": -18.34}, {"time": 0.1333, "value": 34.08}, {"time": 0.2333, "value": 20.27}, {"time": 0.2667, "value": -0.61}, {"time": 0.3667, "value": -31.14}, {"time": 0.4, "value": -97.65}, {"time": 0.4667, "value": -71.21}, {"time": 0.5, "value": -84.96}, {"time": 0.5333, "value": -63.21}, {"time": 0.6, "value": -8.37}, {"time": 0.6333, "value": -18.34}, {"time": 0.6667, "value": 34.08}, {"time": 0.7667, "value": 20.27}]}, "right-ground": {"translate": [{"x": 13.16, "y": 7.94, "curve": [0, -31.35, 0.019, -36.29, 0, 0.85, 0.019, 0.06]}, {"time": 0.0333, "x": -36.29, "y": 0.06}, {"time": 0.0667, "x": -53.26, "y": -0.03}, {"time": 0.1, "x": -65.63, "y": 6.75}, {"time": 0.1333, "x": -75.71, "y": 39.78}, {"time": 0.2333, "x": -58.45, "y": 26.31}, {"time": 0.2667, "x": -42.01, "y": 21.37}, {"time": 0.3333, "x": -5.28, "y": 9.86}, {"time": 0.3667, "x": 11.16, "y": 12.05}, {"time": 0.4, "x": 19.38, "y": 26.85}, {"time": 0.4667, "x": 23.89, "y": 17.18}, {"time": 0.5, "x": 21.52, "y": 0.15}, {"time": 0.5333, "x": -19.32, "y": 0.15}, {"time": 0.6, "x": -53.26, "y": -0.03}, {"time": 0.6333, "x": -65.63, "y": 6.75}, {"time": 0.6667, "x": -75.71, "y": 39.78}, {"time": 0.7667, "x": -58.45, "y": 26.31}]}, "foot2": {"rotate": [{"value": 39.17, "curve": [0, -6.83, 0.019, -11.94]}, {"time": 0.0333, "value": -11.94}, {"time": 0.0667, "value": -31.42}, {"time": 0.1333, "value": -99.94}, {"time": 0.2333, "value": -97.04}, {"time": 0.3333, "value": -74.34}, {"time": 0.3667, "value": -4.27}, {"time": 0.4, "value": 13.39}, {"time": 0.4667, "value": 28.96}, {"time": 0.5, "value": 35.05}, {"time": 0.5333, "value": 7.53}, {"time": 0.6, "value": -31.42}, {"time": 0.6667, "value": -99.94}, {"time": 0.7667, "value": -97.04}]}, "hip": {"translate": [{"curve": [0, -6.07, 0.019, -6.75, 0, -31.96, 0.019, -35.51]}, {"time": 0.0333, "x": -6.75, "y": -35.51}, {"time": 0.1333, "x": 2.16, "y": 12.25}, {"time": 0.2333, "y": -7.82}, {"time": 0.2667, "x": 1.64, "y": -12.3}, {"time": 0.3333, "x": 1.64, "y": -10.66, "curve": [0.35, 1.64, 0.383, 1.64, 0.35, -10.66, 0.383, 9.55]}, {"time": 0.4, "x": 1.64, "y": 9.55}, {"time": 0.4667, "x": 1.64, "y": -0.53}, {"time": 0.5, "y": -7.82}, {"time": 0.5333, "y": -9.6}, {"time": 0.6, "y": -8.91, "curve": [0.617, 0, 0.65, 0, 0.617, -8.91, 0.65, 6.36]}, {"time": 0.6667, "y": 6.36}, {"time": 0.7333, "x": 1.64, "y": -0.53}, {"time": 0.7667, "y": -7.82}], "scale": [{"curve": [0, 1, 0.019, 1, 0, 0.982, 0.019, 0.98]}, {"time": 0.0333, "y": 0.98}, {"time": 0.2667}]}, "left-ankle": {"translate": [{"curve": [0, -7.57, 0.019, -8.41, 0, -13.2, 0.019, -14.67]}, {"time": 0.0333, "x": -8.41, "y": -14.67}, {"time": 0.2333}, {"time": 0.4, "x": -1.45, "y": 9.68}, {"time": 0.7667}]}, "body": {"rotate": [{"value": 14.14, "curve": [0, -18.21, 0.019, -21.8]}, {"time": 0.0333, "value": -21.8}, {"time": 0.1333, "value": -8.61}, {"time": 0.2333, "value": -11.15}, {"time": 0.2667, "value": -16.76}, {"time": 0.4, "value": -8.61}, {"time": 0.5333, "value": -20.18}, {"time": 0.6667, "value": -8.61}, {"time": 0.7667, "value": -11.15}], "translate": [{"curve": [0, 2.53, 0.019, 2.81, 0, 4.16, 0.019, 4.62]}, {"time": 0.0333, "x": 2.81, "y": 4.62}, {"time": 0.1333, "x": 2.7, "y": 5.11}, {"time": 0.2333, "x": 3.37, "y": -2.53}, {"time": 0.2667, "x": 4.99, "y": 0.87}, {"time": 0.4, "x": 2.61, "y": 4.03}, {"time": 0.5333, "x": 5.73, "y": 0.47}, {"time": 0.6667, "x": 2.7, "y": 5.11}, {"time": 0.7667, "x": 3.37, "y": -2.53}]}, "thigh1": {"rotate": [{"value": 53.96, "curve": [0, 65.62, 0.019, 66.91]}, {"time": 0.0333, "value": 66.91}, {"time": 0.2333}], "translate": [{"curve": [0, 7.94, 0.019, 8.82, 0, 5.2, 0.019, 5.78]}, {"time": 0.0333, "x": 8.82, "y": 5.78}, {"time": 0.1, "x": 31, "y": 0.36}, {"time": 0.2, "x": 22.13, "y": 0.18}, {"time": 0.2333, "x": 6.03}, {"time": 0.3667, "x": -3.58, "y": 1.43}, {"time": 0.4, "x": 0.24, "y": 2.72}, {"time": 0.5, "x": 6.14, "y": -0.59}, {"time": 0.5667, "x": 8.82, "y": 5.78}, {"time": 0.6333, "x": 31, "y": 0.36}, {"time": 0.7333, "x": 22.13, "y": 0.18}, {"time": 0.7667, "x": 6.03}]}, "thigh2": {"rotate": [{"value": 32.44, "curve": [0, -3.53, 0.019, -7.53]}, {"time": 0.0333, "value": -7.53}, {"time": 0.2333}], "translate": [{"curve": [0, -9.91, 0.019, -11.01, 0, -0.35, 0.019, -0.39]}, {"time": 0.0333, "x": -11.01, "y": -0.39}, {"time": 0.0667, "x": -18.12, "y": 0.18}, {"time": 0.1, "x": -28.39, "y": 0.18}, {"time": 0.2333, "x": -8.32, "y": -2.38}, {"time": 0.3667, "x": -3.93, "y": -2.38}, {"time": 0.5, "x": 3.23, "y": -1.53}, {"time": 0.6, "x": -18.12, "y": 0.18}, {"time": 0.6333, "x": -28.39, "y": 0.18}, {"time": 0.7667, "x": -8.32, "y": -2.38}]}, "head": {"rotate": [{"value": -17.81, "curve": [0, 6.9, 0.019, 9.64]}, {"time": 0.0333, "value": 9.64, "curve": [0.07, 6.07, 0.118, -1.48]}, {"time": 0.1333, "value": -4.09}, {"time": 0.2333, "value": 8.09}, {"time": 0.3333, "value": 1.34, "curve": [0.35, 1.34, 0.39, 5.49]}, {"time": 0.4, "value": 6.46}, {"time": 0.5, "value": 10.37}, {"time": 0.5333, "value": 3.57, "curve": [0.567, 3.57, 0.646, 6.84]}, {"time": 0.6667, "value": 7.82}, {"time": 0.7667, "value": 8.09}], "translate": [{"curve": [0, -2.27, 0.019, -2.52, 0, -4.27, 0.019, -4.74]}, {"time": 0.0333, "x": -2.52, "y": -4.74}, {"time": 0.1333, "x": 7.04, "y": 0.05}, {"time": 0.2333, "x": 2.88, "y": 0.24}, {"time": 0.2667, "x": -3.51, "y": -1.82}, {"time": 0.4, "x": 4.61, "y": 0.76}, {"time": 0.5, "x": 1.55, "y": -6.16}, {"time": 0.5333, "x": -7.08, "y": -8.67}, {"time": 0.6667, "x": 7.04, "y": 0.05}, {"time": 0.7667, "x": 2.88, "y": 0.24}]}, "mantles": {"rotate": [{"curve": [0, -0.74, 0.019, -0.82]}, {"time": 0.0333, "value": -0.82}, {"time": 0.2333}, {"time": 0.2667, "value": -2.18}, {"time": 0.5, "value": 2.9}, {"time": 0.5333, "value": -0.95}, {"time": 0.7667}], "translate": [{"curve": [0, -2.01, 0.019, -2.23, 0, -0.5, 0.019, -0.56]}, {"time": 0.0333, "x": -2.23, "y": -0.56}, {"time": 0.2333}, {"time": 0.2667, "x": -2.6, "y": -0.65}, {"time": 0.5, "x": 1.4, "y": -0.59}, {"time": 0.5333, "x": -2.6, "y": -0.65}, {"time": 0.7667}]}, "shin1": {"rotate": [{"value": -37.59, "curve": [0, -99.31, 0.019, -106.17]}, {"time": 0.0333, "value": -106.17}, {"time": 0.2333}]}, "shin2": {"rotate": [{"value": -39.7, "curve": [0, -47.2, 0.019, -48.03]}, {"time": 0.0333, "value": -48.03}, {"time": 0.2333}]}, "upper-arm2": {"rotate": [{"value": 37.75, "curve": [0, -12.78, 0.019, -18.39]}, {"time": 0.0333, "value": -18.39}, {"time": 0.1333, "value": 21.28}, {"time": 0.2333, "value": 30.8}, {"time": 0.2667, "value": 38.3}, {"time": 0.3667, "value": 5.43}, {"time": 0.4667, "value": -25.39, "curve": [0.483, -25.39, 0.517, -31.62]}, {"time": 0.5333, "value": -31.62}, {"time": 0.6667, "value": 21.28}, {"time": 0.7667, "value": 30.8}], "translate": [{"curve": [0, -5.35, 0.019, -5.94, 0, 0.4, 0.019, 0.45]}, {"time": 0.0333, "x": -5.94, "y": 0.45}, {"time": 0.2333, "x": -5.34, "y": -1.56}, {"time": 0.4, "x": -6.44, "y": 2.12}, {"time": 0.7667, "x": -5.34, "y": -1.56}]}, "forearm2": {"rotate": [{"value": 12.31, "curve": [0, 34.3, 0.019, 36.74]}, {"time": 0.0333, "value": 36.74}, {"time": 0.1333, "value": 30.1}, {"time": 0.2333, "value": 35.9}, {"time": 0.3333, "value": 25.71}, {"time": 0.4, "value": 29.3}, {"time": 0.4667, "value": 34.63, "curve": [0.483, 34.63, 0.517, 38.95]}, {"time": 0.5333, "value": 38.95}, {"time": 0.6667, "value": 30.1}, {"time": 0.7667, "value": 35.9}]}, "hand2": {"rotate": [{"curve": [0, -4.55, 0.019, -5.05]}, {"time": 0.0333, "value": -5.05}, {"time": 0.1333, "value": -32.27}, {"time": 0.2667, "value": 8.06}, {"time": 0.4, "value": 33.85}, {"time": 0.6667, "value": 22.92}, {"time": 0.7667, "value": 8.06}]}, "hair01": {"rotate": [{"value": 14.89, "curve": [0, 3, 0.019, 1.68]}, {"time": 0.0333, "value": 1.68, "curve": [0.057, 3.37, 0.084, 5.91]}, {"time": 0.1, "value": 5.91, "curve": [0.125, 5.91, 0.175, 31.55]}, {"time": 0.2, "value": 31.55}, {"time": 0.2333, "value": 44.05}, {"time": 0.2667, "value": 14.22, "curve": [0.292, 14.22, 0.342, 5.91]}, {"time": 0.3667, "value": 5.91, "curve": [0.392, 5.91, 0.442, 31.55]}, {"time": 0.4667, "value": 31.55}, {"time": 0.5, "value": 44.05}, {"time": 0.5333, "curve": [0.558, 0, 0.608, 5.91]}, {"time": 0.6333, "value": 5.91, "curve": [0.658, 5.91, 0.708, 31.55]}, {"time": 0.7333, "value": 31.55}, {"time": 0.7667, "value": 44.05}], "translate": [{"x": -1.16, "y": 11.64, "curve": [0, 0.06, 0.019, 0.2, 0, -3.71, 0.019, -5.42]}, {"time": 0.0333, "x": 0.2, "y": -5.42, "curve": [0.046, 0.3, 0.058, 0.4, 0.046, -8.13, 0.058, -10.84]}, {"time": 0.0667, "x": 0.4, "y": -10.84}, {"time": 0.1, "x": 4.4, "y": -15.88, "curve": [0.125, 4.4, 0.175, 5.94, 0.125, -15.88, 0.175, -2.13]}, {"time": 0.2, "x": 5.94, "y": -2.13}, {"time": 0.2333, "x": 2.05, "y": 5.53}, {"time": 0.2667, "curve": [0.283, 0, 0.317, 0.4, 0.283, 0, 0.317, -10.84]}, {"time": 0.3333, "x": 0.4, "y": -10.84}, {"time": 0.3667, "x": 4.4, "y": -15.88, "curve": [0.392, 4.4, 0.442, 5.94, 0.392, -15.88, 0.442, -2.13]}, {"time": 0.4667, "x": 5.94, "y": -2.13}, {"time": 0.5, "x": 2.05, "y": 5.53}, {"time": 0.5333, "curve": [0.55, 0, 0.583, 0.4, 0.55, 0, 0.583, -10.84]}, {"time": 0.6, "x": 0.4, "y": -10.84}, {"time": 0.6333, "x": 4.4, "y": -15.88, "curve": [0.658, 4.4, 0.708, 5.94, 0.658, -15.88, 0.708, -2.13]}, {"time": 0.7333, "x": 5.94, "y": -2.13}, {"time": 0.7667, "x": 2.05, "y": 5.53}]}, "bone2": {"rotate": [{"value": 11.13, "curve": [0, -3.46, 0.019, -5.08]}, {"time": 0.0333, "value": -5.08, "curve": [0.046, -7.62, 0.058, -10.17]}, {"time": 0.0667, "value": -10.17}, {"time": 0.1, "value": -14.57, "curve": [0.125, -14.57, 0.175, 9.84]}, {"time": 0.2, "value": 9.84}, {"time": 0.2333, "value": 16.31}, {"time": 0.2667, "curve": [0.283, 0, 0.317, -10.17]}, {"time": 0.3333, "value": -10.17}, {"time": 0.3667, "value": -14.57, "curve": [0.392, -14.57, 0.442, 9.84]}, {"time": 0.4667, "value": 9.84}, {"time": 0.5, "value": 16.31}, {"time": 0.5333, "curve": [0.55, 0, 0.583, -10.17]}, {"time": 0.6, "value": -10.17}, {"time": 0.6333, "value": -14.57, "curve": [0.658, -14.57, 0.708, 9.84]}, {"time": 0.7333, "value": 9.84}, {"time": 0.7667, "value": 16.31}], "translate": [{"x": -7.56, "y": 11.64, "curve": [0, -3.64, 0.019, -3.2, 0, 2.12, 0.019, 1.06]}, {"time": 0.0333, "x": -3.2, "y": 1.06, "curve": [0.046, 0.19, 0.058, 3.59, 0.046, -0.05, 0.058, -1.16]}, {"time": 0.0667, "x": 3.59, "y": -1.16}, {"time": 0.1, "x": 8.09, "y": -5.72, "curve": [0.125, 8.09, 0.175, -8.03, 0.125, -5.72, 0.175, 1.86]}, {"time": 0.2, "x": -8.03, "y": 1.86}, {"time": 0.2333, "x": -13.75, "y": 7.39}, {"time": 0.2667, "curve": [0.283, 0, 0.317, 3.59, 0.283, 0, 0.317, -1.16]}, {"time": 0.3333, "x": 3.59, "y": -1.16}, {"time": 0.3667, "x": 8.09, "y": -5.72, "curve": [0.392, 8.09, 0.442, -8.03, 0.392, -5.72, 0.442, 1.86]}, {"time": 0.4667, "x": -8.03, "y": 1.86}, {"time": 0.5, "x": -13.75, "y": 7.39}, {"time": 0.5333, "x": -9.98, "y": 3.28, "curve": [0.55, -9.98, 0.583, 3.59, 0.55, 3.28, 0.583, -1.16]}, {"time": 0.6, "x": 3.59, "y": -1.16}, {"time": 0.6333, "x": 8.09, "y": -5.72, "curve": [0.658, 8.09, 0.708, -8.03, 0.658, -5.72, 0.708, 1.86]}, {"time": 0.7333, "x": -8.03, "y": 1.86}, {"time": 0.7667, "x": -13.75, "y": 7.39}]}, "root": {"scale": [{"curve": [0, 1, 0.019, 1, 0, 0.982, 0.019, 0.98]}, {"time": 0.0333, "y": 0.98, "curve": [0.067, 1, 0.167, 1, 0.067, 0.98, 0.167, 1.025]}, {"time": 0.1667, "y": 1.05}, {"time": 0.3}]}}, "attachments": {"default": {"body": {"body": {"deform": [{"curve": [0, 0.9, 0.019, 1]}, {"time": 0.0333, "offset": 8, "vertices": [-0.46935, -1.58981, 1.63244, -7.74099, 0.20303, -7.72544, -5.24218, -6.23479, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -5.25412, -6.0914, -0.43027, -7.92252, -1e-05, 0, -0.43027, -7.92252, -0.43027, -7.92252, -5.25412, -6.0914, -5.25412, -6.0914, -5.24217, -6.23479, -0.43025, -7.92252], "curve": [0.1, 0.33, 0.185, 1]}, {"time": 0.2333, "offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544], "curve": [0.3, 0, 0.433, 1]}, {"time": 0.5, "offset": 8, "vertices": [-0.57551, -1.94941, 2.00168, -9.49192, -0.33626, -11.77182, -6.3903, -8.66438, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -6.3903, -8.66438, -0.33627, -11.77181, -1e-05, 0, -0.33626, -11.77182, -0.33626, -11.77182, -6.3903, -8.66438, -6.3903, -8.66438, -6.3903, -8.66438, -0.33624, -11.77182], "curve": [0.567, 0, 0.7, 1]}, {"time": 0.7667, "offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544]}]}}, "eyes": {"eyes": {"deform": [{"curve": [0, 0.9, 0.019, 1]}, {"time": 0.0333, "vertices": [5.07719, 0.79688, 11.2515, 0.43931, 14.87367, 0.58043, 17.48485, -0.30077, 16.99935, -0.37085, 14.87367, 0.58041, 11.2515, 0.43932, 4.59162, 0.72703]}, {"time": 0.2333}, {"time": 0.5, "vertices": [6.7696, 1.0625, 15.00201, 0.58574, 19.83157, 0.7739, 23.31314, -0.40104, 22.66579, -0.49446, 19.83157, 0.7739, 15.00201, 0.58574, 6.12215, 0.96938]}, {"time": 0.7667}]}}, "head": {"head": {"deform": [{"curve": [0, 0.9, 0.019, 1]}, {"time": 0.0333, "offset": 14, "vertices": [0.78538, 0.03323, 0.78538, 0.03324, 0.52956, 0.02246, 2.83194, -2.88789, 2.93262, -2.5783, 0.70359, 0.24219, 0, 0, -0.84392, 6e-05, -0.90317, -0.03827, -0.90317, -0.0383, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.04986, 0.37032, 6.06242, 0.08871, 1.45855, 0.06175, 5.72176, 0.06192, 0, 0, 0, 0, 0, 0, 3.14171, 0.34994, 8.34537, 0.39374, 3.39029, -0.33179, 1.98376, -0.3318, 3.78743, 0.86661]}, {"time": 0.2333, "offset": 22, "vertices": [-6.48674, 0.51688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.48674, 0.51688, 0, 0, -4.54436, -0.64838, -4.54436, -0.64838]}, {"time": 0.2667, "offset": 14, "vertices": [0.99863, 0.0423, 0.99863, 0.04226, 0.67335, 0.02855, 3.60092, -3.67206, 5.49033, -3.41875, 0.89464, 0.30796, 0, 0, 0, 0, -1.14842, -0.04868, -1.14842, -0.0487, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.66313, 0.1127, 3.66313, 0.11273, 1.8546, 0.07852, 1.85459, 0.07852, 0, 0, 0, 0, 0, 0, -0.13307, 0.30454, 5.89205, 0.17004, -0.17825, -0.24602, -0.17825, -0.24602]}, {"time": 0.3333, "offset": 14, "vertices": [1.41243, -2.60362, 1.80192, -2.63201, 1.21497, 0.05152, 2.20083, -0.81378, 10.82771, -0.77248, 1.61426, 0.55567, 0, 0, 0, 0, -2.07217, -0.08785, -2.07217, -0.08787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.24382, -3.17786, 8.24383, -3.17786, 3.34636, 0.14168, 3.34636, 0.14168, 0, 0, 0, 0, 0, 0, 4.97759, 0.13375, 10.63141, 0.30681, 3.33368, 0.07762, 3.33368, 0.07762, 2.51448, -0.06769]}, {"time": 0.3667, "offset": 14, "vertices": [1.79039, -2.79256, 2.08866, -2.80796, 1.40831, 0.05972, 1.70105, 0.20653, 12.73297, 0.17215, 1.87114, 0.64409, 0, 0, 0, 0, -2.40191, -0.10184, -2.40191, -0.10185, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.87897, -4.35248, 9.87897, -4.35248, 3.87887, 0.16422, 3.87887, 0.16423, 0, 0, 0, 0, 0, 0, 6.80192, 0.07278, 12.32319, 0.35563, 4.58732, 0.19315, 4.58732, 0.19315, 3.41206, -0.09185]}, {"time": 0.4, "offset": 14, "vertices": [2.41842, 0.10236, 2.41842, 0.10235, 1.63067, 0.06915, 1.12625, 1.37998, 14.92422, 1.25856, 2.16658, 0.74579, 0, 0, 0, 0, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.87107, 0.27301, 8.87107, 0.273, 4.49132, 0.19015, 4.49131, 0.19016, 0, 0, 0, 0, 0, 0, 8.90008, 0.00266, 14.26892, 0.41178, 6.02913, 0.32602, 6.02913, 0.32602, 4.44437, -0.11964]}, {"time": 0.5, "offset": 14, "vertices": [2.79754, 0.11841, 2.79754, 0.11839, 1.88631, 0.07999, 0, 0, 16.97795, -0.22148, 2.50623, 0.8627, 0, 0, 0, 0, -3.21715, -0.13641, -3.21715, -0.13641, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.97684, -2.44116, 9.97684, -2.44119, 5.19541, 0.21996, 5.1954, 0.21997, 0, 0, 0, 0, 0, 0, 11.31223, -0.07796, 16.50582, 0.47633, 7.68671, 0.47878, 10.35958, 0.20314, 8.39172, 0.32814]}, {"time": 0.5333, "offset": 14, "vertices": [0.99863, -2.24777, 0.99864, -2.24781, 0.67335, 0.02855, 3.60092, -3.67206, 5.49033, -3.41875, 0.89464, 0.30796, 0, 0, 0, 0, -1.14842, -0.04868, -1.14842, -0.0487, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.66313, 0.1127, 3.66313, 0.11273, 1.8546, 0.07852, 1.85459, 0.07852, 0, 0, 0, 0, 0, 0, -0.13307, 0.30454, 7.3368, 0.50062, -0.17825, -0.24602, -0.17825, -0.24602, 4.81586, 1.10193]}, {"time": 0.7667, "offset": 22, "vertices": [-6.48674, 0.51688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.48674, 0.51688, 0, 0, -4.54436, -0.64838, -4.54436, -0.64838]}]}}, "mantles": {"mantles": {"deform": [{"curve": [0, 0.9, 0.019, 1]}, {"time": 0.0333, "vertices": [3.84905, -6.02877, 0.85942, -13.50029, 2.88349, -6.3918, -0.36714, -2.11292, -0.36714, -2.11292, 2.88348, -6.3918, 0.507, -7.09592, -0.13179, 2.48238], "curve": [0.1, 0.33, 0.185, 1]}, {"time": 0.2333, "offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224], "curve": [0.3, 0, 0.433, 1]}, {"time": 0.5, "vertices": [4.71967, -7.39242, 1.11851, -17.07923, 2.82789, -9.03923, -0.48091, -3.11594, -0.48091, -3.11594, 2.82788, -9.03923, 0.68637, -9.22626, -0.1616, 3.04387], "curve": [0.567, 0, 0.7, 1]}, {"time": 0.7667, "offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224]}]}}, "mouth": {"mouth": {"deform": [{"curve": [0, 0.9, 0.019, 1]}, {"time": 0.0333, "vertices": [6.93687, -1.0089, 19.28772, -0.36045, 19.18731, -0.37494, 6.8364, -1.02339]}, {"time": 0.2333}, {"time": 0.5, "vertices": [9.24917, -1.3452, 25.71696, -0.48061, 25.58308, -0.49992, 9.1152, -1.36452]}, {"time": 0.7667}]}}}}, "events": [{"time": 0.2333, "name": "footstep"}, {"time": 0.5, "name": "footstep"}, {"time": 0.7667, "name": "footstep"}]}, "walk": {"bones": {"left-ground": {"translate": [{"x": 68.76}, {"time": 0.1333, "x": 57.43}, {"time": 0.2667, "x": 41.05}, {"time": 0.4, "x": 21.33}, {"time": 0.5333, "x": 5.9}, {"time": 0.6667, "x": 4.92}, {"time": 0.8, "x": 5.9, "y": 10.31}, {"time": 0.9333, "x": 44.69, "y": 10.8}, {"time": 1.0667, "x": 68.76}]}, "thigh1": {"translate": [{"x": 24.93}, {"time": 0.2667, "x": 22.21}, {"time": 0.4, "x": 21.75, "y": -1.02}, {"time": 0.5333, "x": 21.3}, {"time": 0.6667, "x": 16.11, "y": 1.01}, {"time": 0.9333, "x": 21.7, "y": -3.93}, {"time": 1.0667, "x": 24.93}]}, "hip": {"translate": [{"y": -7.4}, {"time": 0.1333, "y": -6.33}, {"time": 0.2667, "y": -1.45}, {"time": 0.3333, "y": 1.49}, {"time": 0.4, "y": 0.87}, {"time": 0.5333, "y": -9.6}, {"time": 0.6667, "y": -8.45}, {"time": 0.8, "y": -1.45}, {"time": 0.8667, "y": 1.49}, {"time": 0.9333, "y": 0.87}, {"time": 1.0667, "y": -7.4}]}, "foot1": {"rotate": [{"value": 17.4}, {"time": 0.1333, "value": -0.28}, {"time": 0.4, "value": -10.64}, {"time": 0.5333, "value": -8.72}, {"time": 0.6667, "value": -24.71}, {"time": 0.8, "value": -43.25}, {"time": 0.9333, "value": -35.21}, {"time": 1.0667, "value": 17.4}]}, "thigh2": {"translate": [{"x": -33.53, "y": 1.88}, {"time": 0.2, "x": -30.69, "y": 0.18}, {"time": 0.2333, "x": -33.24, "y": 1.44}, {"time": 0.4, "x": -9.43, "y": -3.48}, {"time": 0.5333, "x": -4.25, "y": -1.42}, {"time": 0.6667, "x": -6.64, "y": 0.05}, {"time": 0.8, "x": -11.55, "y": -2.12}, {"time": 0.9333, "x": -22.85, "y": -3.88}, {"time": 1.0667, "x": -33.53, "y": 1.88}]}, "right-ground": {"translate": [{"x": -53.9}, {"time": 0.1333, "x": -55.01, "y": 2.56}, {"time": 0.2667, "x": -56.01, "y": 14.28}, {"time": 0.4, "x": -9.55, "y": 9.64}, {"time": 0.5333, "x": 18.48, "y": -0.26}, {"time": 0.6667, "x": 6.23, "y": -0.26}, {"time": 0.8, "x": -20.75, "y": -0.26}, {"time": 0.9333, "x": -36.46, "y": -3.45}, {"time": 1.0667, "x": -53.9}]}, "foot2": {"rotate": [{"value": -20}, {"time": 0.1333, "value": -36.59}, {"time": 0.2667, "value": -69.51}, {"time": 0.4, "value": -13.92}, {"time": 0.5333, "value": 33.26}, {"time": 0.6667, "value": 0.41}, {"time": 0.9333, "value": -1.16}, {"time": 1.0667, "value": -20}]}, "upper-arm1": {"rotate": [{"value": 13.97}, {"time": 0.1333, "value": 2.64}, {"time": 0.5333, "value": 66.2}, {"time": 0.6667, "value": 57.62}, {"time": 0.8, "value": 41.56}, {"time": 0.9333, "value": 14.48}, {"time": 1.0667, "value": 13.97}], "translate": [{}, {"time": 0.1333, "x": 4.42, "y": 3.58}, {"time": 0.5333, "x": -0.23, "y": -4.78}, {"time": 0.6667, "x": 0.25, "y": -2.4}, {"time": 1.0667}]}, "forearm1": {"rotate": [{"value": -11.7}, {"time": 0.1333, "value": -5.74}, {"time": 0.5333, "value": 24.76}, {"time": 0.6667, "value": 33.27, "curve": [0.7, 33.27, 0.767, 3.09]}, {"time": 0.8, "value": 3.09}, {"time": 0.9333, "value": 5.39}, {"time": 1.0667, "value": -11.7}]}, "hand1": {"rotate": [{"value": -19.63}, {"time": 0.1333, "value": -25.07}, {"time": 0.2667, "value": -27.67}, {"time": 0.4, "value": -34.31}, {"time": 0.5333, "value": -24.05}, {"time": 0.8, "value": 16.76}, {"time": 0.9333, "value": 7.44}, {"time": 1.0667, "value": -19.63}]}, "body": {"rotate": [{"value": 6.02}, {"time": 0.1333, "value": 1.27, "curve": [0.226, 1.27, 0.283, 8.58]}, {"time": 0.3333, "value": 8.58}, {"time": 0.5333, "value": 4.14}, {"time": 0.6667, "value": 1.27, "curve": [0.76, 1.27, 0.817, 7.43]}, {"time": 0.8667, "value": 7.43}, {"time": 1.0667, "value": 6.02}], "translate": [{"x": 4.41, "y": -1.14}, {"time": 0.1333, "x": 10.49, "y": -2.93}, {"time": 0.3333, "x": 1.79, "y": 5.09}, {"time": 0.5333, "x": 12.04, "y": -2.64}, {"time": 0.6667, "x": 10.49, "y": -2.93}, {"time": 0.8667, "x": 1.79, "y": 6.43}, {"time": 1.0667, "x": 4.41, "y": -0.47}]}, "head": {"rotate": [{"value": -11.71}, {"time": 0.1333, "value": -2.88}, {"time": 0.3333, "value": -10.46}, {"time": 0.5333, "value": -6.47}, {"time": 0.6667, "value": -2.86}, {"time": 0.8667, "value": -8.52}, {"time": 1.0667, "value": -11.71}], "translate": [{}, {"time": 0.1333, "x": -2.96, "y": -3.43}, {"time": 0.3333, "x": 1.05, "y": 0.33}, {"time": 0.5333}, {"time": 0.6667, "x": -2.96, "y": -3.43}, {"time": 0.8667, "x": 1.05, "y": 0.33}, {"time": 1.0667}]}, "upper-arm2": {"rotate": [{"value": -10.34}, {"time": 0.1333, "value": -10.21}, {"time": 0.2667, "value": -16.46}, {"time": 0.4, "value": -25.91}, {"time": 0.5333, "value": -32.21}, {"time": 0.8, "value": -21.46}, {"time": 0.9333, "value": -5.31}, {"time": 1.0667, "value": -10.34}], "translate": [{}, {"time": 0.1333, "x": -0.76, "y": -1.45}, {"time": 0.5333, "x": -0.2, "y": 8.7}, {"time": 1.0667}]}, "forearm2": {"rotate": [{"value": 21.05}, {"time": 0.1333, "value": 24.41}, {"time": 0.2667, "value": -0.81}, {"time": 0.4, "value": 23.68}, {"time": 0.5333, "value": 23.62}, {"time": 0.6667, "value": 25.56}, {"time": 0.8, "value": 12.56}, {"time": 0.9333, "value": -0.27}, {"time": 1.0667, "value": 21.05}]}, "hand2": {"rotate": [{"value": -12.11}, {"time": 0.1333, "value": -8.88}, {"time": 0.2667, "value": 13.76}, {"time": 0.4, "value": -3.02}, {"time": 0.5333, "value": 11.48}, {"time": 0.6667, "value": 5.24}, {"time": 0.8, "value": 3.93}, {"time": 0.9333, "value": 0.31}, {"time": 1.0667, "value": -12.11}]}, "right-ankle": {"translate": [{"y": -1.36}]}, "mantles": {"rotate": [{"value": -3.56}, {"time": 0.3333, "value": -8.55}, {"time": 0.5333, "value": -6.74}, {"time": 0.8667, "value": -8.55}, {"time": 1.0667, "value": -3.56}], "translate": [{}, {"time": 0.1333, "x": -1.9, "y": -1.47}, {"time": 0.3333, "x": -0.66, "y": -0.2}, {"time": 0.6667, "x": -1.9, "y": -1.47}, {"time": 0.8667, "x": -0.66, "y": -0.2}, {"time": 1.0667}]}, "bone2": {"rotate": [{"value": 1.6}, {"time": 0.1333, "value": 4.44}, {"time": 0.2667, "value": -11.58}, {"time": 0.4, "value": 4.79}, {"time": 0.5333, "value": 1.6}, {"time": 0.6667, "value": 4.44}, {"time": 0.8, "value": -11.58}, {"time": 0.9333, "value": 4.79}, {"time": 1.0667, "value": 1.6}], "translate": [{"x": -1.7, "y": 2.56}, {"time": 0.1333, "x": -9.99, "y": 4.3}, {"time": 0.2667, "x": -2.47, "y": 3.65}, {"time": 0.4, "x": -5.1, "y": 7.68}, {"time": 0.5333, "x": -1.7, "y": 2.56}, {"time": 0.6667, "x": -9.99, "y": 4.3}, {"time": 0.8, "x": -2.47, "y": 3.65}, {"time": 0.9333, "x": -5.1, "y": 7.68}, {"time": 1.0667, "x": -1.7, "y": 2.56}]}, "hair01": {"rotate": [{"value": 3.13}, {"time": 0.1333, "value": 9.9}, {"time": 0.2667, "value": 1.75}, {"time": 0.4, "value": 9.39}, {"time": 0.5333, "value": 3.13}, {"time": 0.6667, "value": 9.9}, {"time": 0.8, "value": 1.75}, {"time": 0.9333, "value": 9.39}, {"time": 1.0667, "value": 3.13}], "translate": [{"x": -1.39, "y": -0.92}, {"time": 0.1333, "x": -0.8, "y": -1.35}, {"time": 0.2667, "x": 2.13, "y": -10.12}, {"time": 0.4, "x": -4.17, "y": -2.76}, {"time": 0.5333, "x": -1.39, "y": -0.92}, {"time": 0.6667, "x": -0.8, "y": -1.35}, {"time": 0.8, "x": 2.13, "y": -10.12}, {"time": 0.9333, "x": -4.17, "y": -2.76}, {"time": 1.0667, "x": -1.39, "y": -0.92}]}}, "attachments": {"default": {"body": {"body": {"deform": [{"offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544], "curve": [0.133, 0, 0.4, 1]}, {"time": 0.5333, "offset": 8, "vertices": [-0.57551, -1.94941, 2.00168, -9.49192, -0.33626, -11.77182, -6.3903, -8.66438, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -6.3903, -8.66438, -0.33627, -11.77181, -1e-05, 0, -0.33626, -11.77182, -0.33626, -11.77182, -6.3903, -8.66438, -6.3903, -8.66438, -6.3903, -8.66438, -0.33624, -11.77182], "curve": [0.667, 0, 0.933, 1]}, {"time": 1.0667, "offset": 12, "vertices": [2.58727, 10.16383, -0.16622, 4.50658, 0, 0, 0, 0, -2e-05, 0, 1e-05, 0, 0, 0, -0.23098, 5.2839, -0.84593, 9.09544, -1e-05, 0, -0.84593, 9.09544, -0.84593, 9.09544, -0.23098, 5.2839, -0.23098, 5.2839, -0.16621, 4.50658, -0.84591, 9.09544]}]}}, "eyes": {"eyes": {"deform": [{"curve": [0.133, 0, 0.4, 1]}, {"time": 0.5333, "vertices": [6.7696, 1.0625, 15.00201, 0.58574, 19.83157, 0.7739, 23.31314, -0.40104, 22.66579, -0.49446, 19.83157, 0.7739, 15.00201, 0.58574, 6.12215, 0.96938], "curve": [0.667, 0, 0.933, 1]}, {"time": 1.0667}]}}, "head": {"head": {"deform": [{"offset": 22, "vertices": [-6.48674, 0.51688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.54038, -0.15547, -4.54038, -0.15547, 0, 0, 0, 0, 0, 0, -6.48674, 0.51688, 0, 0, -11.14854, -0.87456, -8.67199, -0.78973], "curve": [0.173, 0.07, 0.306, 0.68]}, {"time": 0.5333, "offset": 14, "vertices": [2.26378, -2.72514, 2.41842, -2.72951, 1.63067, 0.06915, 1.12625, 1.37998, 15.33712, 1.2489, 2.16658, 0.74579, 0, 0, 0, 0, -2.78115, -0.11795, -2.78115, -0.11793, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.21351, 0.25793, 12.21371, 0.27221, 9.71989, 0.1901, 13.32494, 0.18936, 0, 0, 0, 0, 0, 0, 15.65543, 0.52389, 21.03843, 0.48742, 9.11117, 0.32602, 9.11117, 0.32602, 9.14707, 1.88179], "curve": [0.752, 0.18, 0.935, 1]}, {"time": 1.0667, "offset": 22, "vertices": [-6.48674, 0.51688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.54038, -0.15547, -4.54038, -0.15547, 0, 0, 0, 0, 0, 0, -6.48674, 0.51688, 0, 0, -11.14854, -0.87456, -8.67199, -0.78973]}]}}, "mantles": {"mantles": {"deform": [{"offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224], "curve": [0.133, 0, 0.4, 1]}, {"time": 0.5333, "vertices": [4.71967, -7.39242, 1.11851, -17.07923, 2.82789, -9.03923, -0.48091, -3.11594, -0.48091, -3.11594, 2.82788, -9.03923, 0.68637, -9.22626, -0.1616, 3.04387], "curve": [0.667, 0, 0.933, 1]}, {"time": 1.0667, "offset": 2, "vertices": [-0.28601, 2.3224, 3.1293, 5.31261, 0.13581, 2.32149, 0.13581, 2.32149, 3.12929, 5.31261, -0.286, 2.3224]}]}}, "mouth": {"mouth": {"deform": [{"curve": [0.133, 0, 0.4, 1]}, {"time": 0.5333, "vertices": [9.24917, -1.3452, 25.71696, -0.48061, 25.58308, -0.49992, 9.1152, -1.36452], "curve": [0.667, 0, 0.933, 1]}, {"time": 1.0667}]}}}}, "events": [{"time": 0.5333, "name": "footstep"}, {"time": 1.0667, "name": "footstep"}]}}}