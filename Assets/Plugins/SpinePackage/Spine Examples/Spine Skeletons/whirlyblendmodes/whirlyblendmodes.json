{"skeleton": {"hash": "tfwPbA2MCzE", "spine": "4.2.22", "x": -252.71, "y": -232.55, "width": 456.71, "height": 360.69, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "_rotation", "parent": "root", "color": "abe323ff"}, {"name": "additive", "parent": "root", "x": -37.18, "y": -104.42}, {"name": "multiply", "parent": "root", "x": 75.86}, {"name": "normal", "parent": "root"}, {"name": "screen", "parent": "root", "x": -124.58}], "slots": [{"name": "normal", "bone": "normal", "color": "ff9100ff", "attachment": "whirly"}, {"name": "multiply", "bone": "multiply", "color": "905e9eff", "attachment": "whirly", "blend": "multiply"}, {"name": "screen", "bone": "screen", "color": "0670c6ff", "attachment": "whirly", "blend": "screen"}, {"name": "additive", "bone": "additive", "color": "0670c6ff", "attachment": "whirly", "blend": "additive"}], "transform": [{"name": "rotation", "bones": ["additive", "multiply", "normal", "screen"], "target": "_rotation", "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"additive": {"whirly": {"rotation": -0.06, "width": 256, "height": 256}}, "multiply": {"whirly": {"rotation": -0.06, "width": 256, "height": 256}}, "normal": {"whirly": {"rotation": -0.06, "width": 256, "height": 256}}, "screen": {"whirly": {"rotation": -0.06, "width": 256, "height": 256}}}}], "animations": {"animation": {"bones": {"_rotation": {"rotate": [{}, {"time": 0.3333, "value": -120}, {"time": 0.6667, "value": -240}, {"time": 1, "value": -360}]}}}}}