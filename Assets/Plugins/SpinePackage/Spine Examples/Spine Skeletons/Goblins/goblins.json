{"skeleton": {"hash": "r8iwnU9Tvmc", "spine": "4.2.22", "x": -134.12, "y": -3.28, "width": 266.94, "height": 349.61, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "hip", "parent": "root", "x": 0.65, "y": 114.41, "color": "ffcf00ff"}, {"name": "torso", "parent": "hip", "length": 85.83, "rotation": 93.93, "x": -6.42, "y": 1.98, "color": "ffcf00ff"}, {"name": "neck", "parent": "torso", "length": 18.38, "rotation": -1.52, "x": 81.68, "y": -6.35, "color": "ffcf00ff"}, {"name": "head", "parent": "neck", "length": 68.29, "rotation": -13.92, "x": 20.94, "y": 11.59, "color": "ffcf00ff"}, {"name": "left-shoulder", "parent": "torso", "length": 35.43, "rotation": -156.96, "x": 74.05, "y": -20.39, "color": "ff0000ff"}, {"name": "left-arm", "parent": "left-shoulder", "length": 35.62, "rotation": 28.17, "x": 37.86, "y": -2.35, "color": "ff0000ff"}, {"name": "left-upper-leg", "parent": "hip", "length": 50.4, "rotation": -89.1, "x": 14.45, "y": 2.81, "color": "ff0000ff"}, {"name": "left-lower-leg", "parent": "left-upper-leg", "length": 49.9, "rotation": -16.66, "x": 56.34, "y": 0.99, "color": "ff0000ff"}, {"name": "left-foot", "parent": "left-lower-leg", "length": 46.5, "rotation": 102.43, "x": 58.94, "y": -7.61, "color": "ff0000ff"}, {"name": "left-hand", "parent": "left-arm", "length": 11.52, "rotation": 2.7, "x": 35.62, "y": 0.08, "color": "ff0000ff"}, {"name": "pelvis", "parent": "hip", "x": 1.41, "y": -6.58, "color": "ffcf00ff"}, {"name": "right-shoulder", "parent": "torso", "length": 37.25, "rotation": 133.89, "x": 76.02, "y": 18.15, "color": "62ff00ff"}, {"name": "right-arm", "parent": "right-shoulder", "length": 36.75, "rotation": 36.33, "x": 37.61, "y": 0.31, "color": "62ff00ff"}, {"name": "right-upper-leg", "parent": "hip", "length": 42.46, "rotation": -97.5, "x": -20.08, "y": -6.84, "color": "62ff00ff"}, {"name": "right-lower-leg", "parent": "right-upper-leg", "length": 58.53, "rotation": -14.34, "x": 43, "y": -0.62, "color": "62ff00ff"}, {"name": "right-foot", "parent": "right-lower-leg", "length": 45.46, "rotation": 110.31, "x": 64.89, "y": 0.04, "color": "62ff00ff"}, {"name": "right-hand", "parent": "right-arm", "length": 15.32, "rotation": 2.36, "x": 36.9, "y": 0.35, "color": "62ff00ff"}, {"name": "spear1", "parent": "left-hand", "length": 65.07, "rotation": 102.43, "x": 0.48, "y": 17.03, "color": "ffcf00ff"}, {"name": "spear2", "parent": "spear1", "length": 61.42, "rotation": 0.9, "x": 65.06, "y": 0.04, "color": "ffcf00ff"}, {"name": "spear3", "parent": "spear2", "length": 76.8, "rotation": -0.9, "x": 61.89, "y": 0.57, "color": "ffcf00ff"}], "slots": [{"name": "left-shoulder", "bone": "left-shoulder", "attachment": "left-shoulder"}, {"name": "left-arm", "bone": "left-arm", "attachment": "left-arm"}, {"name": "left-hand-item", "bone": "left-hand", "attachment": "spear"}, {"name": "left-hand", "bone": "left-hand", "attachment": "left-hand"}, {"name": "left-foot", "bone": "left-foot", "attachment": "left-foot"}, {"name": "left-lower-leg", "bone": "left-lower-leg", "attachment": "left-lower-leg"}, {"name": "left-upper-leg", "bone": "left-upper-leg", "attachment": "left-upper-leg"}, {"name": "neck", "bone": "neck", "attachment": "neck"}, {"name": "torso", "bone": "torso", "attachment": "torso"}, {"name": "pelvis", "bone": "pelvis", "attachment": "pelvis"}, {"name": "right-foot", "bone": "right-foot", "attachment": "right-foot"}, {"name": "right-lower-leg", "bone": "right-lower-leg", "attachment": "right-lower-leg"}, {"name": "undie-straps", "bone": "pelvis", "attachment": "undie-straps"}, {"name": "undies", "bone": "pelvis", "attachment": "undies"}, {"name": "right-upper-leg", "bone": "right-upper-leg", "attachment": "right-upper-leg"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eyes", "bone": "head"}, {"name": "right-shoulder", "bone": "right-shoulder", "attachment": "right-shoulder"}, {"name": "right-arm", "bone": "right-arm", "attachment": "right-arm"}, {"name": "right-hand-thumb", "bone": "right-hand", "attachment": "right-hand-thumb"}, {"name": "right-hand-item", "bone": "right-hand", "attachment": "dagger"}, {"name": "right-hand", "bone": "right-hand", "attachment": "right-hand"}, {"name": "right-hand-item2", "bone": "right-hand", "attachment": "shield"}], "skins": [{"name": "default", "attachments": {"left-hand-item": {"dagger": {"x": 7.88, "y": -23.46, "rotation": 10.48, "width": 26, "height": 108}, "spear": {"type": "mesh", "uvs": [1, 0.11236, 0.77096, 0.13278, 0.76608, 0.21781, 0.75642, 0.386, 0.74723, 0.54607, 0.72117, 1, 0.28838, 1, 0.24208, 0.54328, 0.2259, 0.38362, 0.20891, 0.21605, 0.20043, 0.13243, 0, 0.1152, 0.4527, 0, 0.58399, 0], "triangles": [4, 7, 3, 6, 7, 4, 5, 6, 4, 10, 11, 12, 1, 13, 0, 12, 13, 1, 10, 12, 1, 9, 10, 1, 2, 9, 1, 8, 9, 2, 3, 8, 2, 7, 8, 3], "vertices": [1, 20, 38.54, -10.89, 1, 1, 20, 30.97, -5.93, 1, 2, 19, 61.48, -5.59, 0.5116, 20, -0.31, -6.16, 0.4884, 2, 18, 64.73, -5.03, 0.50272, 19, -0.4, -5.07, 0.49728, 1, 10, 4.57, 23.91, 1, 1, 10, 41.7, -138.95, 1, 1, 10, 32.43, -141.1, 1, 1, 10, -6.49, 22.41, 1, 2, 18, 65.48, 6.65, 0.50272, 19, 0.53, 6.6, 0.49728, 2, 19, 62.19, 6.67, 0.5116, 20, 0.2, 6.1, 0.4884, 1, 20, 30.97, 6.62, 1, 1, 20, 37.26, 11.09, 1, 1, 20, 79.75, 1.6, 1, 1, 20, 79.78, -1.29, 1], "hull": 14, "edges": [24, 22, 22, 20, 10, 12, 2, 0, 24, 26, 0, 26, 8, 10, 12, 14, 6, 8, 14, 16, 2, 4, 4, 6, 16, 18, 18, 20, 20, 2], "width": 22, "height": 368}}, "right-hand-item": {"dagger": {"type": "mesh", "uvs": [0.78092, 0.38453, 1, 0.38406, 1, 0.44882, 0.73954, 0.4687, 0.74642, 0.81344, 0.34023, 1, 0.15434, 1, 0.11304, 0.78858, 0.23007, 0.47368, 0, 0.45047, 0, 0.38622, 0.22368, 0.38573, 0.24384, 0, 1, 0], "triangles": [0, 12, 13, 11, 12, 0, 0, 1, 2, 9, 10, 11, 3, 11, 0, 3, 0, 2, 8, 11, 3, 9, 11, 8, 5, 6, 7, 4, 5, 8, 4, 8, 3, 5, 7, 8], "vertices": [15.49, -12.83, 21.14, -13.57, 20.16, -20.5, 13.16, -21.68, 8.13, -58.57, -5.14, -77.04, -9.92, -76.37, -7.8, -53.61, -0.03, -20.36, -5.61, -17.04, -4.64, -10.17, 1.13, -10.93, 7.47, 30.24, 26.93, 27.5], "hull": 14, "edges": [22, 20, 24, 26, 22, 24, 2, 0, 0, 22, 0, 26, 12, 14, 14, 16, 18, 20, 16, 18, 2, 4, 4, 6, 6, 8, 10, 12, 8, 10], "width": 26, "height": 108}}, "right-hand-item2": {"shield": {"rotation": 93.5, "width": 70, "height": 72}}}}, {"name": "goblin", "attachments": {"eyes": {"eyes-closed": {"name": "goblin/eyes-closed", "x": 29.19, "y": -24.9, "rotation": -88.93, "width": 34, "height": 12}}, "head": {"head": {"name": "goblin/head", "type": "mesh", "uvs": [0, 0.60495, 0.14172, 0.51451, 0.24218, 0.55229, 0.32668, 0.67807, 0.37969, 0.79352, 0.53505, 0.93015, 0.86057, 1, 0.94071, 0.94169, 0.92099, 0.69924, 0.9888, 0.65498, 0.99003, 0.51643, 0.89633, 0.43562, 0.94487, 0.41917, 1, 0.39713, 1, 0.2836, 0.94017, 0.27027, 0.87906, 0.25666, 0.80755, 0.16045, 0.66699, 0.01998, 0.4734, 0.01806, 0.29215, 0.19893, 0.25393, 0.31824, 0.09117, 0.32401, 0, 0.44331, 0.43271, 0.69154, 0.46601, 0.47795, 0.35997, 0.31246, 0.73474, 0.68594, 0.72216, 0.57426, 0.8818, 0.5583, 0.80268, 0.51016], "triangles": [5, 27, 6, 7, 27, 8, 7, 6, 27, 4, 24, 5, 5, 24, 27, 4, 3, 24, 27, 29, 8, 8, 29, 9, 24, 28, 27, 24, 25, 28, 24, 3, 25, 29, 28, 30, 29, 27, 28, 25, 2, 26, 25, 3, 2, 9, 29, 10, 0, 23, 1, 28, 25, 30, 29, 11, 10, 29, 30, 11, 2, 21, 26, 2, 1, 21, 23, 22, 1, 1, 22, 21, 30, 16, 11, 30, 17, 16, 30, 25, 17, 17, 26, 18, 18, 26, 19, 26, 17, 25, 11, 15, 12, 11, 16, 15, 12, 15, 13, 15, 14, 13, 21, 20, 26, 26, 20, 19], "vertices": [14.56, 50.42, 23.12, 35.48, 17.47, 26.36, 11.58, 16.87, 3.75, 11.71, -5.9, -3.92, -11.83, -37.24, -8.32, -45.64, 7.75, -44.24, 10.4, -51.34, 19.53, -51.83, 25.21, -43.16, 26.13, -47.43, 27.36, -53.16, 34.84, -53.46, 35.97, -47.34, 37.11, -41.09, 43.75, -33.98, 53.59, -19.88, 54.51, 0.04, 43.32, 19.17, 35.61, 23.41, 35.89, 40.18, 28.4, 49.87, 10.26, 5.99, 24.21, 2, 35.55, 12.48, 9.39, -25.11, 16.8, -24.31, 17.21, -40.66, 20.69, -33.02], "hull": 24, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 6, 48, 48, 50, 50, 52, 52, 42, 2, 4, 4, 6, 4, 52, 2, 44, 22, 32, 22, 24, 24, 26, 28, 30, 30, 32, 24, 30, 16, 54, 54, 56, 20, 58, 58, 54, 16, 58, 22, 60, 60, 56, 58, 60], "width": 103, "height": 66}}, "left-arm": {"left-arm": {"name": "goblin/left-arm", "type": "mesh", "uvs": [0.68993, 0.29284, 1, 0.46364, 1, 0.74644, 0.8409, 1, 0.66344, 1, 0.33766, 0.64284, 0, 0.44124, 0, 0, 0.34296, 0], "triangles": [3, 4, 2, 4, 5, 2, 5, 0, 2, 0, 1, 2, 0, 5, 8, 5, 6, 8, 6, 7, 8], "vertices": [18.6, 8.81, 32.2, 10.32, 38.02, 1.63, 38.08, -9.63, 32.32, -13.5, 14.37, -9.62, -0.76, -10.78, -9.85, 2.78, 1.29, 10.25], "hull": 9, "edges": [14, 16, 16, 0, 0, 2, 2, 4, 6, 4, 6, 8, 8, 10, 12, 14, 10, 12], "width": 37, "height": 35}}, "left-foot": {"left-foot": {"name": "goblin/left-foot", "type": "mesh", "uvs": [0.15734, 0.31874, 0.08195, 0.78503, 0.15884, 0.99367, 0.41633, 0.96805, 0.68823, 0.97637, 1, 0.96388, 0.99386, 0.73501, 0.85295, 0.51863, 0.61479, 0.31056, 0.46992, 0, 0.48033, 0.75604, 0.75995, 0.77706], "triangles": [0, 9, 8, 10, 0, 8, 10, 8, 7, 11, 10, 7, 11, 7, 6, 1, 0, 10, 11, 6, 5, 3, 1, 10, 4, 10, 11, 4, 11, 5, 3, 10, 4, 2, 1, 3], "vertices": [2.28, 13.07, -1.77, -1.64, 3.6, -7.81, 20.26, -6.04, 37.92, -5.28, 58.13, -3.71, 57.32, 3.35, 47.78, 9.51, 31.95, 15.05, 21.99, 24.12, 24.03, 0.76, 42.21, 1.16], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 6, 20, 20, 16, 2, 20, 8, 22, 22, 14, 20, 22, 22, 10], "width": 65, "height": 31}}, "left-hand": {"left-hand": {"name": "goblin/left-hand", "type": "mesh", "uvs": [0.51801, 0.12578, 1, 0.16286, 0.99789, 0.50578, 0.69745, 1, 0.37445, 1, 0, 0.80051, 0, 0.42793, 0.17601, 0, 0.43568, 0], "triangles": [2, 0, 1, 0, 5, 6, 6, 7, 0, 0, 7, 8, 3, 4, 0, 4, 5, 0, 2, 3, 0], "vertices": [-3.11, 15.43, 10.84, 22.27, 15.5, 14.56, 18.36, -8.96, 9.48, -14.33, -4.59, -14.3, -11.64, -2.64, -14.89, 13.68, -7.76, 18], "hull": 9, "edges": [16, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 12, 14], "width": 36, "height": 41}}, "left-lower-leg": {"left-lower-leg": {"name": "goblin/left-lower-leg", "type": "mesh", "uvs": [0.95509, 0.2075, 0.81927, 0.65214, 0.94754, 0.77308, 0.67842, 0.97347, 0.46464, 1, 0.26845, 1, 0.04964, 0.90707, 0.21061, 0.60115, 0.07479, 0.40195, 0.18545, 0, 0.28858, 0], "triangles": [10, 8, 9, 1, 7, 10, 7, 8, 10, 0, 1, 10, 1, 4, 7, 3, 1, 2, 5, 6, 7, 7, 4, 5, 1, 3, 4], "vertices": [-0.2, 6.82, 30.97, 10.96, 37.97, 17.34, 53.88, 12.6, 57.59, 6.32, 59.35, 0.09, 55.05, -8.63, 33, -9.34, 20.8, -17.44, -7.28, -21.57, -8.2, -18.29], "hull": 11, "edges": [20, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 16, 18], "width": 33, "height": 70}}, "left-shoulder": {"left-shoulder": {"name": "goblin/left-shoulder", "type": "mesh", "uvs": [0.7377, 0.40692, 1, 0.75238, 1, 1, 0.62046, 1, 0.26184, 0.56602, 0, 0.29783, 0, 0, 0.44115, 0], "triangles": [3, 1, 2, 3, 0, 1, 3, 4, 0, 4, 7, 0, 4, 5, 7, 5, 6, 7], "vertices": [15.18, 5.74, 32.17, 5.33, 41.79, 0.22, 36.63, -9.5, 14.89, -9.73, 0.9, -10.9, -10.67, -4.75, -4.67, 6.55], "hull": 8, "edges": [12, 14, 14, 0, 4, 2, 0, 2, 4, 6, 6, 8, 10, 12, 8, 10], "width": 29, "height": 44}}, "left-upper-leg": {"left-upper-leg": {"name": "goblin/left-upper-leg", "type": "mesh", "uvs": [1, 0.12168, 1, 0.54873, 0.91067, 0.78907, 0.76568, 1, 0.30871, 0.95791, 0, 0.68777, 0, 0.21901, 0.51962, 0, 0.87552, 0], "triangles": [7, 8, 0, 5, 6, 7, 0, 1, 7, 4, 5, 7, 1, 4, 7, 2, 4, 1, 3, 4, 2], "vertices": [2.34, 13.07, 33.51, 12.57, 51, 9.34, 66.32, 4.31, 63.01, -10.71, 43.13, -20.59, 8.91, -20.04, -6.8, -2.64, -6.61, 9.1], "hull": 9, "edges": [10, 8, 8, 6, 6, 4, 4, 2, 10, 12, 12, 14, 14, 16, 2, 0, 16, 0], "width": 33, "height": 73}}, "neck": {"neck": {"name": "goblin/neck", "type": "mesh", "uvs": [0.81968, 0.27365, 0.92101, 0.82048, 0.47135, 1, 0.15679, 0.93541, 0, 0.7556, 0.19268, 0.51834, 0.15468, 0.35707, 0, 0.2199, 0.13568, 0, 0.68879, 0, 0.70146, 0.53873], "triangles": [3, 5, 2, 2, 10, 1, 2, 5, 10, 3, 4, 5, 10, 0, 1, 0, 10, 6, 10, 5, 6, 7, 8, 6, 6, 9, 0, 6, 8, 9], "vertices": [18.63, -11.66, -3.98, -13.86, -10.29, 2.77, -6.92, 13.9, 0.8, 19.06, 10.06, 11.51, 16.75, 12.45, 22.72, 17.65, 31.4, 12.2, 30.12, -7.68, 8.05, -6.71], "hull": 10, "edges": [14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 20, 20, 0, 0, 18, 16, 18, 14, 16, 0, 2], "width": 36, "height": 41}}, "pelvis": {"pelvis": {"name": "goblin/pelvis", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [25.38, -20.73, -36.62, -20.73, -36.62, 22.27, 25.38, 22.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 62, "height": 43}}, "right-arm": {"right-arm": {"name": "goblin/right-arm", "type": "mesh", "uvs": [1, 0.09223, 1, 0.8501, 0.72058, 1, 0.24385, 1, 0, 0.86559, 0.20823, 0.1092, 0.50903, 0, 0.85342, 0], "triangles": [1, 2, 6, 6, 2, 5, 1, 6, 0, 4, 5, 3, 2, 3, 5, 6, 7, 0], "vertices": [-4.75, 8.89, 33.03, 11.74, 40.99, 5.9, 41.82, -5.03, 35.54, -11.13, -2.54, -9.2, -8.5, -2.72, -9.1, 5.18], "hull": 8, "edges": [8, 6, 4, 6, 4, 2, 12, 14, 2, 0, 14, 0, 10, 12, 8, 10], "width": 23, "height": 50}}, "right-foot": {"right-foot": {"name": "goblin/right-foot", "type": "mesh", "uvs": [0.40851, 0.00471, 0.59088, 0.33404, 0.75959, 0.48311, 0.88907, 0.59751, 0.97533, 0.89392, 0.90386, 1, 0.6722, 1, 0.38633, 1, 0.08074, 1, 0, 0.88921, 0, 0.65985, 0, 0.46578, 0.0906, 0.0988, 0.305, 0, 0.47461, 0.71258, 0.71501, 0.74681], "triangles": [1, 10, 11, 1, 13, 0, 14, 1, 2, 1, 12, 13, 12, 1, 11, 14, 10, 1, 15, 14, 2, 15, 2, 3, 9, 10, 14, 15, 3, 4, 7, 8, 9, 14, 7, 9, 6, 14, 15, 5, 6, 15, 7, 14, 6, 4, 5, 15], "vertices": [17.36, 26, 29.14, 15.44, 39.89, 10.81, 48.15, 7.25, 53.84, -2.38, 49.43, -6, 34.85, -6.39, 16.84, -6.87, -2.4, -7.38, -7.59, -3.87, -7.79, 3.7, -7.96, 10.1, -2.57, 22.36, 10.84, 25.98, 22.15, 2.76, 37.32, 2.03], "hull": 14, "edges": [0, 2, 6, 8, 8, 10, 16, 18, 22, 24, 24, 26, 0, 26, 10, 12, 2, 4, 4, 6, 12, 14, 14, 16, 18, 20, 20, 22, 2, 28, 28, 14, 20, 28, 4, 30, 30, 12, 28, 30, 30, 8], "width": 63, "height": 33}}, "right-hand": {"right-hand": {"name": "goblin/right-hand", "type": "mesh", "uvs": [0.17957, 0, 0, 0.44772, 0, 0.79734, 0.20057, 0.94264, 0.55057, 1, 0.8539, 1, 0.89824, 0.82005, 0.8259, 0.74286, 0.84224, 0.49994, 0.96357, 0.34102, 0.66024, 0], "triangles": [8, 10, 9, 0, 10, 1, 8, 2, 1, 8, 1, 10, 7, 3, 8, 3, 2, 8, 4, 3, 7, 5, 7, 6, 4, 7, 5], "vertices": [-10.83, -9.45, 5.95, -15.35, 18.88, -14.9, 24.01, -7.5, 25.69, 5.16, 25.32, 16.08, 18.61, 17.44, 15.85, 14.74, 6.84, 15.02, 0.82, 19.19, -11.42, 7.84], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 36, "height": 37}}, "right-hand-thumb": {"right-hand-thumb": {"name": "goblin/right-hand", "type": "mesh", "uvs": [0.88538, 0.22263, 0.76168, 0.3594, 0.75089, 0.78308, 0.95326, 0.84981, 1, 0.60303], "triangles": [1, 0, 4, 2, 1, 4, 3, 2, 4], "vertices": [-2.82, 15.98, 2.4, 11.72, 18.08, 11.91, 20.28, 19.28, 11.09, 20.62], "hull": 5, "edges": [2, 4, 4, 6, 6, 8, 2, 0, 0, 8], "width": 36, "height": 37}}, "right-lower-leg": {"right-lower-leg": {"name": "goblin/right-lower-leg", "type": "mesh", "uvs": [1, 0.27261, 0.81312, 0.52593, 0.79587, 0.71796, 0.95544, 0.80989, 0.85194, 0.95493, 0.47242, 1, 0.14034, 1, 0, 0.8773, 0.14896, 0.67914, 0.1619, 0.30326, 0.60611, 0], "triangles": [1, 10, 0, 9, 10, 1, 8, 9, 1, 2, 8, 1, 4, 2, 3, 6, 7, 8, 5, 6, 8, 2, 5, 8, 4, 5, 2], "vertices": [6.27, 8.46, 23.32, 8.05, 37.1, 12.9, 41.45, 20.83, 53.07, 21.47, 61.33, 10.06, 65.78, -1.04, 59, -9.19, 43.02, -9.82, 16.33, -20.01, -12.8, -9.26], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 20, 18, 20], "width": 36, "height": 76}}, "right-shoulder": {"right-shoulder": {"name": "goblin/right-shoulder", "type": "mesh", "uvs": [0.62008, 0.03709, 0.92131, 0.09049, 1, 0.3832, 0.72049, 0.69371, 0.31657, 1, 0, 1, 0, 0.75106, 0.28234, 0.49989], "triangles": [4, 6, 7, 4, 7, 3, 4, 5, 6, 7, 0, 3, 2, 0, 1, 2, 3, 0], "vertices": [-3.17, -11.05, -9, -0.58, -1.01, 10.34, 16.69, 11.17, 37.41, 8.2, 45.46, -1.16, 36.96, -8.46, 21.21, -7.48], "hull": 8, "edges": [10, 12, 12, 14, 14, 0, 0, 2, 2, 4, 4, 6, 8, 10, 6, 8], "width": 39, "height": 45}}, "right-upper-leg": {"right-upper-leg": {"name": "goblin/right-upper-leg", "type": "mesh", "uvs": [0.27019, 0, 0.11619, 0.18177, 0, 0.70688, 0, 0.89577, 0.26669, 1, 0.48719, 1, 0.67619, 0.83533, 1, 0.5161, 1, 0.25544, 0.74619, 0.0571], "triangles": [9, 8, 7, 9, 1, 0, 6, 9, 7, 6, 1, 9, 2, 1, 6, 4, 3, 2, 6, 4, 2, 5, 4, 6], "vertices": [-9.86, -10.37, 2.18, -14.07, 35.49, -13.67, 47.29, -12.11, 52.62, -2.27, 51.64, 5.17, 40.51, 10.18, 19.14, 18.47, 2.85, 16.33, -8.41, 6.14], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 34, "height": 63}}, "torso": {"torso": {"name": "goblin/torso", "type": "mesh", "uvs": [0, 0.33288, 0.15946, 0.46489, 0.15762, 0.60314, 0.15502, 0.79806, 0.32808, 0.93479, 0.68751, 1, 0.80732, 1, 1, 0.77763, 1, 0.66148, 1, 0.56704, 0.93208, 0.4771, 0.86944, 0.39417, 0.83838, 0.22601, 0.68085, 0, 0.14836, 0, 0, 0.07199, 0.78735, 0.8625, 0.43679, 0.79649, 0.76738, 0.61733, 0.44345, 0.58747, 0.54329, 0.38317, 0.77692, 0.73447, 0.66479, 0.51012], "triangles": [5, 16, 6, 6, 16, 7, 4, 17, 5, 5, 17, 16, 4, 3, 17, 17, 21, 16, 16, 21, 7, 3, 2, 17, 21, 19, 18, 21, 17, 19, 17, 2, 19, 21, 8, 7, 21, 18, 8, 18, 9, 8, 19, 22, 18, 18, 10, 9, 18, 22, 10, 2, 1, 19, 19, 20, 22, 19, 1, 20, 22, 11, 10, 22, 20, 11, 20, 1, 14, 20, 12, 11, 1, 0, 14, 20, 13, 12, 20, 14, 13, 0, 15, 14], "vertices": [56.93, 27.95, 43.37, 18.24, 30.16, 19.5, 11.53, 21.29, -2.55, 10.69, -10.89, -13.12, -11.59, -21.24, 8.55, -36.13, 19.66, -37.09, 28.69, -37.86, 37.69, -34.01, 45.99, -30.45, 56.4, -29.07, 84.79, -20.92, 87.9, 15.15, 81.89, 25.8, 1.67, -21.02, 10.04, 2.19, 25.23, -18.25, 29.99, 0.01, 48.54, -8.4, 13.98, -21.37, 35.91, -15.6], "hull": 16, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 14, 32, 32, 34, 34, 6, 18, 36, 36, 38, 2, 4, 4, 6, 38, 4, 2, 40, 40, 22, 40, 38, 38, 34, 32, 10, 34, 8, 40, 28, 14, 16, 16, 18, 32, 42, 42, 36, 16, 42, 42, 34, 18, 20, 20, 22, 36, 44, 44, 40, 20, 44], "width": 68, "height": 96}}, "undie-straps": {"undie-straps": {"name": "goblin/undie-straps", "type": "mesh", "uvs": [0.36098, 0.4496, 0.66298, 0.60591, 1, 0.19486, 1, 0.57118, 0.75898, 1, 0.38698, 1, 0, 0.26433, 0, 0, 0.12498, 0], "triangles": [6, 7, 8, 6, 8, 0, 3, 1, 2, 5, 0, 1, 6, 0, 5, 4, 1, 3, 5, 1, 4], "vertices": [-10.56, 12.88, 6.54, 9.91, 25.62, 17.72, 25.62, 10.57, 11.97, 2.42, -9.09, 2.42, -31, 16.4, -31, 21.42, -23.93, 21.42], "hull": 9, "edges": [14, 16, 16, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 10, 12, 0, 10, 2, 8], "width": 55, "height": 19}}, "undies": {"undies": {"name": "goblin/undies", "type": "mesh", "uvs": [0, 0.3203, 0.14893, 0.59457, 0.22438, 1, 0.3591, 1, 0.50999, 1, 0.7956, 0.58454, 0.98421, 0.28016, 1, 0.00588, 0.46957, 0.17647, 0, 0.03933, 0.48843, 0.59123, 0.48115, 0.431], "triangles": [6, 8, 7, 0, 9, 8, 11, 8, 6, 0, 8, 11, 5, 11, 6, 10, 11, 5, 1, 0, 11, 1, 11, 10, 3, 2, 1, 10, 3, 1, 4, 10, 5, 3, 10, 4], "vertices": [-13.22, 5.57, -8, -2.48, -5.49, -14.28, -0.64, -14.36, 4.79, -14.46, 15.28, -2.59, 22.22, 6.12, 22.93, 14.06, 3.75, 9.45, -13.08, 13.72, 4.22, -2.59, 4.04, 2.06], "hull": 10, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 4, 6, 6, 8, 6, 20, 16, 22, 22, 20, 0, 22, 22, 12, 2, 20, 20, 10], "width": 36, "height": 29}}}}, {"name": "goblingirl", "attachments": {"eyes": {"eyes-closed": {"name": "goblingirl/eyes-closed", "x": 28, "y": -25.55, "rotation": -87.05, "width": 37, "height": 21}}, "head": {"head": {"name": "goblingirl/head", "x": 27.72, "y": -4.32, "rotation": -85.58, "width": 103, "height": 81}}, "left-arm": {"left-arm": {"name": "goblingirl/left-arm", "x": 19.64, "y": -2.43, "rotation": 33.05, "width": 37, "height": 35}}, "left-foot": {"left-foot": {"name": "goblingirl/left-foot", "type": "<PERSON><PERSON><PERSON>", "skin": "goblin", "parent": "left-foot", "width": 65, "height": 31}}, "left-hand": {"left-hand": {"name": "goblingirl/left-hand", "x": 4.34, "y": 2.39, "scaleX": 0.8965, "scaleY": 0.8965, "rotation": 30.35, "width": 35, "height": 40}}, "left-lower-leg": {"left-lower-leg": {"name": "goblingirl/left-lower-leg", "x": 25.02, "y": -0.61, "rotation": 105.76, "width": 33, "height": 70}}, "left-shoulder": {"left-shoulder": {"name": "goblingirl/left-shoulder", "x": 19.81, "y": -0.43, "rotation": 61.22, "width": 28, "height": 46}}, "left-upper-leg": {"left-upper-leg": {"name": "goblingirl/left-upper-leg", "x": 30.22, "y": -2.95, "rotation": 89.1, "width": 33, "height": 70}}, "neck": {"neck": {"name": "goblingirl/neck", "x": 6.16, "y": -3.15, "rotation": -98.86, "width": 35, "height": 41}}, "pelvis": {"pelvis": {"name": "goblingirl/pelvis", "x": -3.88, "y": 3.19, "width": 62, "height": 43}}, "right-arm": {"right-arm": {"name": "goblingirl/right-arm", "x": 16.85, "y": -0.66, "rotation": 93.53, "width": 28, "height": 50}}, "right-foot": {"right-foot": {"name": "goblingirl/right-foot", "type": "<PERSON><PERSON><PERSON>", "skin": "goblin", "parent": "right-foot", "width": 63, "height": 33}}, "right-hand": {"right-hand": {"name": "goblingirl/right-hand", "x": 7.22, "y": 3.44, "rotation": 91.17, "width": 36, "height": 37}}, "right-hand-thumb": {"right-hand-thumb": {"name": "goblingirl/right-hand", "x": 7.22, "y": 3.44, "rotation": 91.17, "width": 36, "height": 37}}, "right-lower-leg": {"right-lower-leg": {"name": "goblingirl/right-lower-leg", "x": 26.15, "y": -3.28, "rotation": 111.84, "width": 36, "height": 76}}, "right-shoulder": {"right-shoulder": {"name": "goblingirl/right-shoulder", "x": 14.46, "y": 0.46, "rotation": 129.85, "width": 39, "height": 45}}, "right-upper-leg": {"right-upper-leg": {"name": "goblingirl/right-upper-leg", "x": 19.7, "y": 2.13, "rotation": 97.5, "width": 34, "height": 63}}, "torso": {"torso": {"name": "goblingirl/torso", "x": 36.28, "y": -5.14, "rotation": -95.75, "width": 68, "height": 96}}, "undie-straps": {"undie-straps": {"name": "goblingirl/undie-straps", "x": -1.52, "y": 14.19, "width": 55, "height": 19}}, "undies": {"undies": {"name": "goblingirl/undies", "x": 5.4, "y": 1.71, "width": 36, "height": 29}}}}], "animations": {"walk": {"slots": {"eyes": {"attachment": [{"time": 0.7, "name": "eyes-closed"}, {"time": 0.8}]}}, "bones": {"left-upper-leg": {"rotate": [{"value": -26.56}, {"time": 0.1333, "value": -8.79}, {"time": 0.2333, "value": 9.51}, {"time": 0.3667, "value": 30.74}, {"time": 0.5, "value": 25.34}, {"time": 0.6333, "value": 26.12}, {"time": 0.7333, "value": 7.45}, {"time": 0.8667, "value": -21.19}, {"time": 1, "value": -26.56}], "translate": [{"x": -1.32, "y": 1.71}, {"time": 0.3667, "x": -0.06, "y": 2.43}, {"time": 1, "x": -1.32, "y": 1.71}]}, "right-upper-leg": {"rotate": [{"value": 42.45}, {"time": 0.1333, "value": 49.86, "curve": [0.175, 49.86, 0.204, 22.69]}, {"time": 0.2333, "value": 22.51}, {"time": 0.5, "value": -16.94}, {"time": 0.6333, "value": 1.9}, {"time": 0.7333, "value": 34.87, "curve": [0.795, 37.71, 0.867, 58.69]}, {"time": 0.8667, "value": 58.69, "curve": [0.933, 58.35, 1, 42.45]}, {"time": 1, "value": 42.45}], "translate": [{"x": 6.24}, {"time": 0.2333, "x": 2.14, "y": 2.4}, {"time": 0.5, "x": 2.44, "y": 4.8}, {"time": 1, "x": 6.24}]}, "left-lower-leg": {"rotate": [{"value": -18.05}, {"time": 0.1333, "value": -63.51}, {"time": 0.2333, "value": -83.02}, {"time": 0.5, "value": 5.12}, {"time": 0.6333, "value": -28.3}, {"time": 0.7333, "value": -27.52}, {"time": 0.8667, "value": 3.53}, {"time": 1, "value": -18.05}], "translate": [{}, {"time": 0.2333, "x": 2.56, "y": -0.47}, {"time": 0.5}]}, "left-foot": {"rotate": [{"value": -14.57}, {"time": 0.1333, "value": -10.42}, {"time": 0.2333, "value": -5.01}, {"time": 0.3, "value": 6.67}, {"time": 0.3667, "value": 3.87}, {"time": 0.5, "value": -3.88}, {"time": 0.6333, "value": 2.78}, {"time": 0.7333, "value": -12}, {"time": 0.8667, "value": -12.45}, {"time": 1, "value": -14.57}]}, "right-shoulder": {"rotate": [{"value": 5.29, "curve": [0.167, 5.29, 0.475, 6.65]}, {"time": 0.6333, "value": 6.65}, {"time": 1, "value": 5.29}]}, "right-arm": {"rotate": [{"value": -4.03, "curve": [0.169, -3.91, 0.51, 19.66]}, {"time": 0.6333, "value": 19.79, "curve": [0.746, 19.75, 0.922, -3.91]}, {"time": 1, "value": -4.03}]}, "right-hand": {"rotate": [{"value": 8.99}, {"time": 0.6333, "value": 0.51}, {"time": 1, "value": 8.99}]}, "left-shoulder": {"rotate": [{"value": 6.26, "curve": [0.17, 6.26, 0.342, -11.79]}, {"time": 0.5, "value": -11.79, "curve": [0.641, -11.79, 0.843, 6.16]}, {"time": 1, "value": 6.26}], "translate": [{"x": 1.15, "y": 0.24}]}, "left-hand": {"rotate": [{"value": -21.24, "curve": [0.148, -21.24, 0.378, -27.21]}, {"time": 0.5, "value": -27.28, "curve": [0.621, -27.28, 0.875, -21.4]}, {"time": 1, "value": -21.24}]}, "left-arm": {"rotate": [{"value": 28.38, "curve": [0.17, 28.38, 0.342, 60.09]}, {"time": 0.5, "value": 60.09, "curve": [0.641, 60.09, 0.843, 28.54]}, {"time": 1, "value": 28.38}]}, "torso": {"rotate": [{"value": -10.28}, {"time": 0.1333, "value": -15.39, "curve": [0.261, -15.36, 0.324, -9.78]}, {"time": 0.3667, "value": -9.78, "curve": [0.521, -10.8, 0.545, -15.72]}, {"time": 0.6333, "value": -15.75, "curve": [0.688, -15.66, 0.819, -7.07]}, {"time": 0.8667, "value": -7.07, "curve": [0.895, -7.07, 0.975, -10.25]}, {"time": 1, "value": -10.28}]}, "right-foot": {"rotate": [{"value": -5.25}, {"time": 0.2333, "value": -17.77}, {"time": 0.3667, "value": -20.1}, {"time": 0.5, "value": -19.74}, {"time": 0.7333, "value": -11.69}, {"time": 0.8, "value": 4.47}, {"time": 0.8667, "value": 0.46}, {"time": 1, "value": -5.25}]}, "right-lower-leg": {"rotate": [{"value": -3.39, "curve": [0.042, -4.01, 0.099, -42.81]}, {"time": 0.1333, "value": -43.22, "curve": [0.175, -43.22, 0.204, -26.09]}, {"time": 0.2333, "value": -25.98}, {"time": 0.5, "value": -19.53}, {"time": 0.6333, "value": -64.8}, {"time": 0.7333, "value": -89.54, "curve": [0.882, -74, 1, -3.39]}, {"time": 1, "value": -3.39}], "translate": [{"time": 0.5}, {"time": 0.6333, "x": 2.19, "y": 0.21}, {"time": 1}]}, "hip": {"translate": [{"y": -8.4}, {"time": 0.1333, "y": -9.35, "curve": [0.166, 0, 0.201, 0, 0.166, -8.91, 0.201, -1.14]}, {"time": 0.2333, "y": -0.59, "curve": [0.277, 0, 0.319, 0, 0.277, -1.92, 0.319, -2.96]}, {"time": 0.3667, "y": -3.97}, {"time": 0.5, "y": -8.4}, {"time": 0.6333, "y": -10.01, "curve": [0.669, 0, 0.698, 0, 0.669, -7.79, 0.698, -6.49]}, {"time": 0.7333, "y": -5.3, "curve": [0.756, 0, 0.778, 0, 0.756, -4.27, 0.778, -3.35]}, {"time": 0.8, "y": -2.5, "curve": [0.821, 0, 0.843, 0, 0.821, -3.02, 0.843, -3.5]}, {"time": 0.8667, "y": -3.97}, {"time": 1, "y": -8.4}]}, "neck": {"rotate": [{"value": 3.6}, {"time": 0.1333, "value": 17.5}, {"time": 0.2333, "value": 6.11}, {"time": 0.3667, "value": 3.46}, {"time": 0.5, "value": 5.18}, {"time": 0.6333, "value": 18.36}, {"time": 0.7333, "value": 6.09}, {"time": 0.8667, "value": 2.29}, {"time": 1, "value": 3.6}]}, "head": {"rotate": [{"value": 3.6, "curve": [0, 3.6, 0.094, -0.89]}, {"time": 0.1333, "value": -0.21}, {"time": 0.2333, "value": 6.11}, {"time": 0.3667, "value": 3.46}, {"time": 0.5, "value": 5.18, "curve": [0.5, 5.18, 0.617, -1.4]}, {"time": 0.6667, "value": 1.11}, {"time": 0.7333, "value": 6.09}, {"time": 0.8667, "value": 2.29}, {"time": 1, "value": 3.6}]}, "pelvis": {"rotate": [{"value": -1.34}], "translate": [{"x": 0.39, "y": -0.78}]}, "spear1": {"rotate": [{"value": 1.85}, {"time": 0.2, "value": -5.39}, {"time": 0.5, "value": 2.95}, {"time": 0.7333, "value": -3.67}, {"time": 1, "value": 1.85}]}, "spear2": {"rotate": [{"value": 1.85}, {"time": 0.2, "value": -5.39}, {"time": 0.5, "value": 2.95}, {"time": 0.7333, "value": -3.67}, {"time": 1, "value": 1.85}]}, "spear3": {"rotate": [{"value": 3.64}, {"time": 0.2, "value": -3.6}, {"time": 0.5, "value": 4.74}, {"time": 0.7333, "value": -1.88}, {"time": 1, "value": 3.64}]}}, "attachments": {"default": {"right-hand-item": {"dagger": {"deform": [{"offset": 26, "vertices": [2.34755, 0.1447], "curve": [0.125, 0, 0.375, 1]}, {"time": 0.5, "offset": 8, "vertices": [-1.19415, 4.31532, 0.07279, 6.41351, 1.66048, 6.18883, 1.75233, 3.59555], "curve": [0.625, 0, 0.875, 1]}, {"time": 1, "offset": 26, "vertices": [2.34755, 0.1447]}]}}}, "goblin": {"head": {"head": {"deform": [{"curve": [0.127, 0, 0.15, 1]}, {"time": 0.2, "vertices": [-10.97827, -6.68962, -4.68015, -2.46175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08534, 0.08392, -1.08534, 0.08392, -1.08534, 0.08392, 0, 0, -2.22325, 2.66465, -4.83295, 2.70085, -5.70553, -0.51941, -3.15962, -1.61502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.64742, 0.81612, -11.82286, -1.34955, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08534, 0.08392], "curve": [0.242, 0, 0.325, 1]}, {"time": 0.3667, "vertices": [10.69276, 4.05949, 3.66373, 1.85427, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.47305, 0.09018, 1.47305, 0.09018, 1.47305, 0.09018, 0, 0, 2.69653, -0.22738, 3.77135, 0.11418, 3.6893, 1.55352, 2.49595, 1.65501, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.45881, -3.9113, 9.19594, -1.66854, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.47305, 0.09018], "curve": [0.574, 0, 0.617, 1]}, {"time": 0.7, "vertices": [-10.97827, -6.68962, -4.68015, -2.46175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.17551, -0.17183, -1.17551, -0.17183, -1.17551, -0.17183, 0, 0, -2.22325, 2.66465, -4.83295, 2.70085, -5.70553, -0.51941, -3.15962, -1.61502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.64742, 0.81612, -11.82286, -1.34955, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.17551, -0.17183], "curve": [0.742, 0, 0.825, 1]}, {"time": 0.8667, "vertices": [10.69276, 4.05949, 3.66373, 1.85427, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.38687, 0.08446, 0.38687, 0.08446, 0.38687, 0.08446, 0, 0, 2.69653, -0.22738, 3.77135, 0.11418, 3.6893, 1.55352, 2.49595, 1.65501, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.45881, -3.9113, 9.19594, -1.66854, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.38687, 0.08446], "curve": [0.9, 0, 0.967, 1]}, {"time": 1}]}}, "left-foot": {"left-foot": {"deform": [{"offset": 8, "vertices": [3.69298, 2.37573, -7.16969, 18.79733, -12.78162, 14.7778, -12.75776, 6.50514, -3.13476, 1.98906, -0.44402, 0.36629, 0, 0, -3.80085, 2.98474]}, {"time": 0.1333}, {"time": 0.2333, "offset": 8, "vertices": [-3.96073, -2.34594, -5.80446, -12.47629, -2.2313, -12.99038, 2.02942, -9.1036, 0, 0, 0, 0, 0, 0, -1.35254, -5.2883]}, {"time": 0.3667, "offset": 8, "vertices": [0.66505, 0.33548, 0.33902, 2.69014, -0.48171, 2.54524, -1.13593, 1.38562, 0, 0, 0, 0, 0, 0, -0.11908, 0.79273]}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6333}, {"time": 0.7333, "offset": 8, "vertices": [-2.97738, 9.40254, -6.91661, 19.92794, -10.55287, 18.41085, -12.37161, 12.38473, -4.72607, 6.30799, 0, 0, -1.48902, 4.88944, -7.06773, 10.70102]}, {"time": 0.8333, "offset": 6, "vertices": [1.05319, 1.56362, -2.52723, 7.9974, -5.52031, 17.14137, -8.93317, 15.79635, -10.73748, 10.22056, -4.23801, 5.36992, 0, 0, 0, 0, -5.83148, 8.55532]}, {"time": 1, "offset": 8, "vertices": [3.69298, 2.37573, -7.16969, 18.79733, -12.78162, 14.7778, -12.75776, 6.50514, -3.13476, 1.98906, -0.44402, 0.36629, 0, 0, -3.80085, 2.98474]}]}}, "pelvis": {"pelvis": {"deform": [{}, {"time": 0.1333, "offset": 6, "vertices": [-0.6899, -4.13284]}, {"time": 0.3333, "offset": 6, "vertices": [-1.04945, -3.10477]}, {"time": 0.7, "offset": 6, "vertices": [-1.4245, -6.30617]}, {"time": 0.8667, "offset": 6, "vertices": [-1.13542, -1.79036]}, {"time": 1}]}}, "right-foot": {"right-foot": {"deform": [{}, {"time": 0.1333, "offset": 2, "vertices": [-2.81259, 2.63115, -2.35238, 3.89441, -1.99921, 4.8639, -0.93273, 5.57982, -0.48886, 5.09855, -0.34813, 3.42912, -0.17446, 1.36899, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.31305, 1.91372, -1.32986, 3.65703]}, {"time": 0.2333, "offset": 2, "vertices": [-6.39088, 6.41246, -7.74575, 8.27192, -7.02471, 11.35894, -4.03471, 13.93454, -2.50399, 12.62963, -1.46125, 7.58915, -0.17446, 1.36899, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.84766, 2.61216, -4.53956, 7.92358]}, {"time": 0.3, "offset": 2, "vertices": [-8.27185, 6.68822, -9.29764, 10.13797, -8.62231, 14.7134, -4.5863, 18.81939, -2.20304, 17.10709, -0.07795, 9.9046, 2.54452, 1.01642, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.94625, 2.38008, -4.59399, 10.01888]}, {"time": 0.3667, "offset": 2, "vertices": [-10.47684, 9.44176, -13.36883, 12.40983, -14.32569, 16.94392, -9.24463, 23.55674, -5.51712, 21.51378, -1.19582, 11.53193, 2.54452, 1.01642, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.14848, 2.29389, -6.63419, 11.37127]}, {"time": 0.5, "offset": 2, "vertices": [-5.42474, 4.36854, -10.59004, 7.04468, -11.64251, 11.55845, -6.19665, 20.12806, -1.45498, 18.05411, 4.8662, 6.41679, 2.81463, 0.27601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.96412, 4.9483]}, {"time": 0.6333}, {"time": 0.7333, "offset": 4, "vertices": [1.31462, -6.84099, -0.87905, -12.54479, -5.9851, -14.08368, -7.15892, -11.63194, -5.6792, -4.83545, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.06164, -6.93844]}, {"time": 0.8, "offset": 4, "vertices": [0.65731, -3.4205, -0.43953, -6.2724, -2.99255, -7.04184, -3.57946, -5.81597, -2.8396, -2.41772, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.79688, -1.28021, 0, 0, 0, 0, -1.03082, -3.46922]}, {"time": 0.8667}]}}, "right-hand": {"right-hand": {"deform": [{"offset": 4, "vertices": [-1.48417, 0.34736, 0, 0, 1.31152, 0.08085, 1.60296, 0.09882, 0.13673, 0.15471, 0, 0, 0, 0, -0.72862, -0.0449]}, {"time": 0.5}, {"time": 1, "offset": 4, "vertices": [-1.48417, 0.34736, 0, 0, 1.31152, 0.08085, 1.60296, 0.09882, 0.13673, 0.15471, 0, 0, 0, 0, -0.72862, -0.0449]}]}}, "right-lower-leg": {"right-lower-leg": {"deform": [{}, {"time": 0.6, "offset": 6, "vertices": [1.80396, -1.56553]}, {"time": 1}]}}, "right-upper-leg": {"right-upper-leg": {"deform": [{"vertices": [-6.03857, -1.46325, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34685, -1.93102, -1.86047, -5.05266, -2.5014, -3.09985]}, {"time": 0.3333}, {"time": 0.8667, "offset": 14, "vertices": [0.13425, -2.35378, -1.33318, -5.99573, -1.35862, -4.43324]}, {"time": 1, "vertices": [-6.03857, -1.46325, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34685, -1.93102, -1.86047, -5.05266, -2.5014, -3.09985]}]}}, "torso": {"torso": {"deform": [{"offset": 2, "vertices": [0.24821, 2.86673, 0.24821, 2.86673, 0.24821, 2.86673, 0.24821, 2.86673, 0.24821, 2.86673, 0.24821, 2.86673, -1.24131, 2.62652, -2.47492, 0.71183, -0.26363, -0.5308, 0.24821, 2.86673, 0.24821, 2.86673, 0, 0, 0, 0, 0, 0, 0, 0, 1.34461, 0.25215, 0.24821, 2.86673, 0.82507, 1.61798, 0.24821, 2.86673, 0, 0, -1.86431, -0.4326, 0.24821, 2.86673]}, {"time": 0.1333, "offset": 2, "vertices": [0.35589, 4.10914, 0.35589, 4.10914, 0.35589, 4.10914, 0.35589, 4.10914, 0.35589, 4.10914, 0.35589, 4.10914, 1.66908, 3.51187, -0.62355, 2.47979, 1.1045, 3.49684, -1.09009, 6.08429, 0.35589, 4.10914, 0, 0, 0, 0, 0, 0, 0, 0, 3.01291, 0.15693, 0.35589, 4.10914, -1.11398, 3.7954, 0.35589, 4.10914, 0, 0, -2.96167, 0.55563, -2.20741, 4.40587]}, {"time": 0.3, "offset": 2, "vertices": [0.2884, 3.32948, 0.2884, 3.32948, 0.2884, 3.32948, 0.2884, 3.32948, 0.2884, 3.32948, 0.2884, 3.32948, 6.32601, 0.19387, 7.84315, 1.94837, 7.08587, 3.64119, 4.52343, 4.46961, 0.2884, 3.32948, 0, 0, 0, 0, 0, 0, 0, 0, 4.36416, -1.83876, 0.2884, 3.32948, 4.2925, 3.60194, 0.2884, 3.32948, 0, 0, 3.72601, -0.19338, 0.2884, 3.32948]}, {"time": 0.5, "offset": 2, "vertices": [0.3133, 3.61659, 0.3133, 3.61659, 0.3133, 3.61659, 0.3133, 3.61659, 0.3133, 3.61659, 0.3133, 3.61659, 2.57273, 2.74457, 2.88831, 3.04797, 3.48442, 3.04655, 1.80035, 4.609, 0.3133, 3.61659, 0, 0, 0, 0, 0, 0, 0, 0, 3.53782, -0.82203, 0.3133, 3.61659, 1.80022, 3.63246, 0.3133, 3.61659, 0, 0, 0.62718, 0.33564, -1.22467, 3.79463]}, {"time": 0.6333, "offset": 2, "vertices": [0.44398, 5.125, 0.44398, 5.125, 0.44398, 5.125, 0.44398, 5.125, 0.44398, 5.125, 0.44398, 5.125, 1.19401, 3.60798, -0.53546, 3.49565, 1.1926, 4.5127, -1.002, 7.10015, 0.44398, 5.125, 0, 0, 0, 0, 0, 0, 0, 0, 3.101, 1.17278, 0.44398, 5.125, -1.02589, 4.81126, 0.44398, 5.125, 0, 0, -2.87358, 1.57149, -2.11931, 5.42173]}, {"time": 0.8667, "offset": 2, "vertices": [0.30385, 3.50647, 0.30385, 3.50647, 0.30385, 3.50647, 0.30385, 3.50647, 0.30385, 3.50647, 0.30385, 3.50647, 0.92587, 2.24385, 0.68874, 1.29945, 3.55433, 3.00604, 2.71494, 5.89962, 0.30385, 3.50647, 0, 0, 0, 0, 0, 0, 0, 0, 1.96775, 0.40548, 0.30385, 3.50647, 2.6104, 2.3545, 0.30385, 3.50647, 0, 0, 0.22709, -0.12851, -0.62826, 3.61437]}, {"time": 1, "offset": 2, "vertices": [0.32802, 3.78826, 0.32802, 3.78826, 0.32802, 3.78826, 0.32802, 3.78826, 0.32802, 3.78826, 0.32802, 3.78826, -1.1615, 3.54805, -2.39511, 1.63336, -0.18382, 0.39073, 0.32802, 3.78826, 0.32802, 3.78826, 0, 0, 0, 0, 0, 0, 0, 0, 1.42442, 1.17368, 0.32802, 3.78826, 0.90488, 2.53951, 0.32802, 3.78826, 0, 0, -1.7845, 0.48894, 0.32802, 3.78826]}]}}, "undie-straps": {"undie-straps": {"deform": [{"offset": 2, "vertices": [-1.77697, 0.5476, -0.96145, -1.03793, -0.39148, -0.24072, -1.77697, 0.5476]}, {"time": 0.1333, "offset": 2, "vertices": [-2.25684, -1.03177, -1.49719, -4.23862, -0.7447, -2.84907, -1.90072, 0.54478]}, {"time": 0.3333, "offset": 2, "vertices": [-2.37974, -0.05432, -0.49433, 0.19437, -0.90861, 1.16519, -1.60956, 2.70799, 0.96186, 0.80615]}, {"time": 0.7, "offset": 2, "vertices": [-0.91715, -2.76567, -0.62215, -3.63489, -0.84941, -2.26772, -2.56077, 0.52971]}, {"time": 0.8667, "offset": 2, "vertices": [-2.56077, 0.52971, -1.58065, 0.32031, -1.3847, 0.32476, -2.56077, 0.52971]}, {"time": 1, "offset": 2, "vertices": [-1.77697, 0.5476, -0.80128, 0.53413, -0.80128, 0.53413, -1.77697, 0.5476]}]}}, "undies": {"undies": {"deform": [{"vertices": [0.43099, 0.722, 10.60295, -0.117, 2.29599, 0, 2.29599, 0, 2.29599, 0, 0.58799, 0.244, -2.40018, -0.65335, -2.2782, -0.77534, 2.29599, 0, 0.58799, -0.488, 4.98698, -0.117, 6.50797, -0.23399]}, {"time": 0.1333, "vertices": [0.72659, 0.4332, 7.20417, -0.1638, 1.37759, 0, 1.37759, 0, 1.37759, 0, 1.25279, 0.0464, -0.99862, -2.95085, -1.37543, -3.07404, 1.37759, 0, 0.35279, -0.2928, 2.99219, -0.0702, 3.90478, -0.1404]}, {"time": 0.3333, "vertices": [1.16999, 0, 2.10599, -0.23401, 0, 0, 0, 0, 0, 0, 2.24999, -0.24999, -0.4344, 0.60551, -1.55939, 0.48051]}, {"time": 0.5333, "vertices": [1.16999, 0, -0.234, -0.936, -2.92499, 0.351, 0, 0, 0, 0, 0.5, -0.24999, -0.64079, -2.07915, -0.64079, -2.07915]}, {"time": 0.7, "vertices": [1.86271, -0.11514, 4.66327, -0.091, -1.76428, 0.21171, 0, 0, -0.56833, 0.32833, -1.13833, -1.15111, -2.19996, -3.47068, -1.29719, -3.47068, 0, 0, 0, 0, 1.58785, -0.04643, 2.65942, 0.16715]}, {"time": 0.8333, "vertices": [2.41688, -0.20726, 8.58108, 0.585, -0.83571, 0.10029, 0, 0, -1.02299, 0.59099, -2.449, -1.872, -1.625, 0, 0, 0, 0, 0, 0, 0, 2.85813, -0.08357, 4.78695, 0.30086]}, {"time": 0.8667, "vertices": [2.0197, -0.02141, 8.98546, 0.4446, -0.20937, 0.08023, 0.4592, 0, -0.3592, 0.47279, -1.8416, -1.4488, -0.79153, 1.26421, 0.53286, 1.23981, 0.4592, 0, 0.1176, -0.0976, 3.2839, -0.09025, 5.13116, 0.19389]}, {"time": 1, "vertices": [0.43099, 0.722, 10.60295, -0.117, 2.29599, 0, 2.29599, 0, 2.29599, 0, 0.58799, 0.244, -2.40018, -0.65335, -2.2782, -0.77534, 2.29599, 0, 0.58799, -0.488, 4.98698, -0.117, 6.50797, -0.23399]}]}}}}}}}