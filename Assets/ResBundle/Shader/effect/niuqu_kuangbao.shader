// Shader created with Shader Forge v1.38 
// Shader Forge (c) <PERSON><PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:0,lgpr:1,limd:1,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:6,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:False,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:True,fgod:False,fgor:False,fgmd:0,fgcr:0.5,fgcg:0.5,fgcb:0.5,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:False,fsmp:False;n:type:ShaderForge.SFN_Final,id:4795,x:33264,y:32946,varname:node_4795,prsc:2|emission-8963-OUT;n:type:ShaderForge.SFN_Tex2d,id:6074,x:31801,y:32800,ptovrint:False,ptlb:wenli,ptin:_wenli,varname:_wenli,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:2,isnm:False|UVIN-3588-OUT;n:type:ShaderForge.SFN_Multiply,id:2393,x:32095,y:32907,varname:node_2393,prsc:2|A-6074-RGB,B-797-RGB;n:type:ShaderForge.SFN_Color,id:797,x:31774,y:33125,ptovrint:True,ptlb:Color,ptin:_TintColor,varname:_TintColor,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_VertexColor,id:9695,x:31774,y:33277,varname:node_9695,prsc:2;n:type:ShaderForge.SFN_Multiply,id:488,x:32054,y:33109,varname:node_488,prsc:2|A-797-A,B-9695-A;n:type:ShaderForge.SFN_Multiply,id:1977,x:32289,y:33029,varname:node_1977,prsc:2|A-2393-OUT,B-488-OUT;n:type:ShaderForge.SFN_TexCoord,id:6086,x:30229,y:32793,varname:node_6086,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Tex2d,id:8188,x:30639,y:32793,ptovrint:False,ptlb:niuqu,ptin:_niuqu,varname:_niuqu,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-4624-UVOUT;n:type:ShaderForge.SFN_TexCoord,id:6698,x:30979,y:32601,varname:node_6698,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Add,id:4198,x:30998,y:32793,varname:node_4198,prsc:2|A-6698-UVOUT,B-7899-OUT;n:type:ShaderForge.SFN_Panner,id:4624,x:30414,y:32793,varname:node_4624,prsc:2,spu:-0.2,spv:0|UVIN-6086-UVOUT,DIST-8193-OUT;n:type:ShaderForge.SFN_Multiply,id:7899,x:30822,y:32793,varname:node_7899,prsc:2|A-1933-OUT,B-8188-G;n:type:ShaderForge.SFN_Slider,id:1933,x:30620,y:32649,ptovrint:False,ptlb:niuqufudu,ptin:_niuqufudu,varname:_niuqusudu,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:0,max:1;n:type:ShaderForge.SFN_TexCoord,id:6930,x:31232,y:32421,varname:node_6930,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Panner,id:4313,x:31437,y:32521,varname:node_4313,prsc:2,spu:-7,spv:0|UVIN-6930-UVOUT;n:type:ShaderForge.SFN_Add,id:3588,x:31599,y:32777,varname:node_3588,prsc:2|A-4313-UVOUT,B-4198-OUT;n:type:ShaderForge.SFN_Time,id:1611,x:30205,y:32563,varname:node_1611,prsc:2;n:type:ShaderForge.SFN_Multiply,id:8193,x:30414,y:32589,varname:node_8193,prsc:2|A-1611-T,B-9889-OUT;n:type:ShaderForge.SFN_ValueProperty,id:9889,x:30205,y:32716,ptovrint:False,ptlb:niuqusudu,ptin:_niuqusudu,varname:node_9889,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Tex2d,id:1803,x:32379,y:33346,ptovrint:False,ptlb:zhezhao,ptin:_zhezhao,varname:node_1803,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Multiply,id:8963,x:32790,y:33100,varname:node_8963,prsc:2|A-1977-OUT,B-1803-R,C-1803-A;proporder:6074-797-8188-1933-9889-1803;pass:END;sub:END;*/

Shader "Shader Forge/niuqu_kuangbao" {
    Properties {
        _wenli ("wenli", 2D) = "black" {}
        _TintColor ("Color", Color) = (0.5,0.5,0.5,1)
        _niuqu ("niuqu", 2D) = "white" {}
        _niuqufudu ("niuqufudu", Range(0, 1)) = 0
        _niuqusudu ("niuqusudu", Float ) = 0
        _zhezhao ("zhezhao", 2D) = "white" {}
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend One One
            Cull Off
            ZTest Always
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase 
            #pragma target 3.0
            uniform sampler2D _wenli; uniform float4 _wenli_ST;
            uniform float4 _TintColor;
            uniform sampler2D _niuqu; uniform float4 _niuqu_ST;
            uniform float _niuqufudu;
            uniform float _niuqusudu;
            uniform sampler2D _zhezhao; uniform float4 _zhezhao_ST;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.pos = UnityObjectToClipPos( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float4 node_7400 = _Time;
                float4 node_1611 = _Time;
                float2 node_4624 = (i.uv0+(node_1611.g*_niuqusudu)*float2(-0.2,0));
                float4 _niuqu_var = tex2D(_niuqu,TRANSFORM_TEX(node_4624, _niuqu));
                float2 node_3588 = ((i.uv0+node_7400.g*float2(-7,0))+(i.uv0+(_niuqufudu*_niuqu_var.g)));
                float4 _wenli_var = tex2D(_wenli,TRANSFORM_TEX(node_3588, _wenli));
                float4 _zhezhao_var = tex2D(_zhezhao,TRANSFORM_TEX(i.uv0, _zhezhao));
                float3 emissive = (((_wenli_var.rgb*_TintColor.rgb)*(_TintColor.a*i.vertexColor.a))*_zhezhao_var.r*_zhezhao_var.a);
                float3 finalColor = emissive;
                return fixed4(finalColor,1);
            }
            ENDCG
        }
    }
    FallBack "Diffuse"
    CustomEditor "ShaderForgeMaterialInspector"
}
