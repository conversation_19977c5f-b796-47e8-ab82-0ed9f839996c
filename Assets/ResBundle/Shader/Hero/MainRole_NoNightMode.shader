// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

// Unlit shader. Simplest possible textured shader.
// - no lighting
// - no lightmap support
// - no per-material color

Shader "EZ/Unlit/MainRole_NoNightMode" {
	Properties{
		_MainTex("Base (RGB)", 2D) = "white" {}
		_FuryColor("FuryColor", Color) = (1,1,1,1)
		_MinX("Min X", Float) = -100000
		_MaxX("Max X", Float) = 100000
		_MinY("Min Y", Float) = -100000
		_MaxY("Max Y", Float) = 100000
	}

		CGINCLUDE

	#include "UnityCG.cginc"

	struct appdata_t {
		float4 vertex : POSITION;
		float2 texcoord : TEXCOORD0;
		UNITY_VERTEX_INPUT_INSTANCE_ID
	};

	struct v2f {
		float4 vertex : SV_POSITION;
		float2 texcoord : TEXCOORD0;
		float3 vpos : TEXCOORD1;
		float3 worldPos:TEXCOORD3;
		UNITY_VERTEX_INPUT_INSTANCE_ID
	};

	sampler2D _MainTex;
	float4 _MainTex_ST;

	v2f vert(appdata_t v)
	{
		v2f o;
		UNITY_SETUP_INSTANCE_ID(v);
		UNITY_TRANSFER_INSTANCE_ID(v, o);

		o.vpos = mul(unity_ObjectToWorld, v.vertex).xyz; 
		o.vertex = UnityWorldToClipPos(o.vpos);
		o.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);
o.worldPos = mul(UNITY_MATRIX_M,v.vertex).xyz;
		return o;
	}
	fixed4 _FuryColor;
	float _MinX;
	float _MaxX;
	float _MinY;
	float _MaxY;

	fixed4 frag(v2f i) : SV_Target
	{
		UNITY_SETUP_INSTANCE_ID(i);
		float newAlpha = 1;
		newAlpha *= (i.vpos.x >= _MinX);
		newAlpha *= (i.vpos.x <= _MaxX);
		newAlpha *= (i.vpos.y >= _MinY);
		newAlpha *= (i.vpos.y <= _MaxY);
		clip(newAlpha - 0.5f);
		fixed4 col = tex2D(_MainTex, i.texcoord) *_FuryColor;

		
		return col;
	}
		ENDCG

		SubShader{
			Tags { "RenderType" = "Opaque" }
			LOD 100

			Pass {
				CGPROGRAM
				#pragma multi_compile_instancing
				#pragma vertex vert
				#pragma fragment frag
				ENDCG
			}
			Pass
			{
				Tags { "LightMode" = "ShadowCaster" }

				 ZWrite On ZTest Less Cull Off

				CGPROGRAM
				#pragma multi_compile_instancing
				#pragma multi_compile_shadowcaster
				#pragma vertex vert
				#pragma fragment frag
				ENDCG
		}
	}
}
