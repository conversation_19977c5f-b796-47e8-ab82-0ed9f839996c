Shader "ONEMT/Character/CreatureLit_Jelly"
{
    Properties
    {   
        // [Toggle(_GRAY_ON)]_GRAY_ON("置灰",float) = 0
        _NormalTex ("_NormalTex", 2D) = "white" {}
        // _ReflectionTex("_ReflectionTex", CUBE) = "white" {}
        _Matcap("Matcap", 2D) = "white" {}
        _ReflectMap("ReflectMap", 2D) = "white" {}
        // _distortion ("_distortion", 2D) = "white" {}
        // _Dist("_Dist",Range(0,1)) = 0.5
        // _ColorUnd("_ColorUnd",color) = (1,1,1,1)
        // _FresnePow("_FresnePow",float) = 1
        _FresnelColor("_FresnelColor",color) = (1,1,1,1)
        // _specularColor("_specularColor",color) = (1,1,1,1)
        // _pow("_pow",float) = 1
        _alpha("_alpha",float) = 1
        

    }
    SubShader
    {
        Tags{"Queue"="Transparent" "RenderPipeline" = "UniversalPipeline"}
        Blend SrcAlpha OneMinusSrcAlpha
        
        // ZWrite off

        // Pass
        // {   
        //     Tags{"LightMode" = "UniversalForward"}
        //     // Cull off
        //     HLSLPROGRAM
        //     #pragma vertex vert
        //     #pragma fragment frag
        //     #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
        //     #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        //     #pragma multi_compile _ _GRAY_ON

        //     CBUFFER_START(UnityPerMaterial)
        //         half4 _FresnelColor,_specularColor; 
        //         half _Dist,_FresnePow,_pow,_alpha;       
        //     CBUFFER_END
        //     TEXTURE2D (_Matcap);SAMPLER(sampler_Matcap);
        //     TEXTURE2D(_CameraOpaqueTexture);SAMPLER(sampler_CameraOpaqueTexture);
        //     TEXTURECUBE (_ReflectionTex);SAMPLER(sampler_ReflectionTex);
        //     TEXTURE2D(_NormalTex);      SAMPLER(sampler_NormalTex);
        //     TEXTURE2D(_distortion);SAMPLER(sampler_distortion);

        //     inline float3 ComputeBumpNormal (float3 BumpMap , float BumapScale , float4 TtoW0 , float4 TtoW1, float4 TtoW2)
        //     {
        //         half3 normalTangent = BumpMap.rgb;
        //         normalTangent.xy *= BumapScale;
        //         normalTangent.z = sqrt(1.0 - saturate(dot(normalTangent.xy,normalTangent.xy)));
        //         return normalize(half3(dot(TtoW0.xyz,normalTangent),dot(TtoW1.xyz,normalTangent),dot(TtoW2.xyz,normalTangent)));			
        //     }

        //     struct appdata
        //     {
        //         half4 positionOS : POSITION;
        //         half2 uv : TEXCOORD0;
        //         half3 normalOS : NORMAL;
        //         half4 tangentOS : TANGENT;
        //     };

        //     struct v2f
        //     {
        //         half2 uv : TEXCOORD0;
        //         half4 positionCS : SV_POSITION;
        //         half3 positionWS : TEXCOORD1;
        //         half3 normalWS : TEXCOORD2;
        //         half3 positionOS : TEXCOORD3;
        //         half4 tangentWS : TEXCOORD4;
        //         half4 bitangentWS : TEXCOORD5;
                
        //     };

        //     v2f vert (appdata v)
        //     {
        //         v2f o = (v2f)0;
        //         o.positionCS = TransformObjectToHClip(v.positionOS.xyz);
        //         o.uv = v.uv;
        //         o.positionWS = TransformObjectToWorld(v.positionOS.xyz);
        //         o.normalWS = TransformObjectToWorldNormal(v.normalOS);
        //         o.positionOS.xyz = v.positionOS.xyz;
        //         o.tangentWS.xyz = TransformObjectToWorldDir(v.tangentOS.xyz);
        //         real sign = v.tangentOS.w * GetOddNegativeScale();
        //         o.bitangentWS.xyz = cross(o.normalWS.xyz, o.tangentWS.xyz) * sign;
              
        //         return o;
        //     }

        //     half4 frag (v2f i) : SV_Target
        //     {   
        //         Light light = GetMainLight();
        //         half4 TtoW0 = half4(i.tangentWS.x,i.bitangentWS.x,i.normalWS.x,i.positionWS.x);
        //         half4 TtoW1 = half4(i.tangentWS.y,i.bitangentWS.y,i.normalWS.y,i.positionWS.y);
        //         half4 TtoW2 = half4(i.tangentWS.z,i.bitangentWS.z,i.normalWS.z,i.positionWS.z);
        //         // half Distort = SAMPLE_TEXTURE2D(_Distort, sampler_Distort, (i.uv.xy*0.3)).r;
        //         float3 NormalTex = UnpackNormal(SAMPLE_TEXTURE2D(_NormalTex,sampler_NormalTex,half2(i.uv.x * 0.5,i.uv.y * 0.5 + _Time.y * 0.02)));
        //         half3 NormalWS = ComputeBumpNormal(NormalTex, 2, TtoW0, TtoW1, TtoW2);
        //         half3 L = light.direction;               
        //         half3 V = normalize(_WorldSpaceCameraPos.xyz - i.positionWS);
        //         half3 H = normalize(L + V); 
        //         half3 reflectUV = reflect(-V,NormalWS);
        //         half3 ReflectionTex = SAMPLE_TEXTURE2D(_ReflectionTex, sampler_ReflectionTex, reflectUV).rgb;
        //         half NdV = saturate(dot(NormalWS,V));
                
        //         half3 specular = _specularColor.rgb * 3 * pow(max(0,dot(NormalWS,H)),_pow); 

        //         half4 c;
        //         c.a = pow((1-NdV) ,_alpha);
        //         // return c.a;
        //         //屏幕UV
        //         float2 screenUV = i.positionCS.xy/_ScreenParams.xy;
        //         // return screenUV.y;
        //         half4 distortion = SAMPLE_TEXTURE2D(_distortion, sampler_distortion, screenUV);
        //         // return distortion;
        //         float2 disUV = lerp(screenUV,distortion,_Dist);
        //         half3 Opaque = SAMPLE_TEXTURE2D(_CameraOpaqueTexture,sampler_CameraOpaqueTexture,disUV) ;
                
        //         // return half4(Opaque,1);

        //         half3 normalVS = TransformWorldToViewDir(NormalWS);
        //         half3 Matcap = SAMPLE_TEXTURE2D(_Matcap,sampler_Matcap,normalVS.xy * 0.5 + 0.5).rgb;
        //         c.rgb = Matcap * 1.5;
        //         // c.rgb += Opaque*0.3;
        //         c.rgb +=  ReflectionTex.rgb * 0.1 + specular.rgb;
        //         c.rgb +=  pow((1-NdV) ,_FresnePow) * _FresnelColor.rgb;
        //         // #ifdef _GRAY_ON
        //         // // half gray = 0.2125 * finColor.r + 0.7154 * finColor.g + 0.0721*finColor.b;
        //         // // half3 grayColor = float3(gray, gray, gray);
        //         // c.rgb = 0.2125 * c.r + 0.7154 * c.g + 0.0721*c.b;
        //         // #endif
        //         return c;
        //     }
        //     ENDHLSL
      
        // }
        //   Pass
        // {   
        //     Tags{"LightMode" = "SRPDefaultUnlit"}
        //     Cull Back
        //     ZWrite on
        //     HLSLPROGRAM
        //     #pragma vertex vert
        //     #pragma fragment frag
        //     #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
        //     #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        //     #pragma multi_compile _ _GRAY_ON

        //     CBUFFER_START(UnityPerMaterial)
        //         half4 _FresnelColor,_specularColor; 
        //         half _Dist,_FresnePow,_pow,_alpha;       
        //     CBUFFER_END
        //     TEXTURE2D (_Matcap);SAMPLER(sampler_Matcap);
        //     TEXTURE2D(_CameraOpaqueTexture);SAMPLER(sampler_CameraOpaqueTexture);
        //     TEXTURECUBE (_ReflectionTex);SAMPLER(sampler_ReflectionTex);
        //     TEXTURE2D(_NormalTex);      SAMPLER(sampler_NormalTex);
        //     TEXTURE2D(_distortion);SAMPLER(sampler_distortion);

        //     inline float3 ComputeBumpNormal (float3 BumpMap , float BumapScale , float4 TtoW0 , float4 TtoW1, float4 TtoW2)
        //     {
        //         half3 normalTangent = BumpMap.rgb;
        //         normalTangent.xy *= BumapScale;
        //         normalTangent.z = sqrt(1.0 - saturate(dot(normalTangent.xy,normalTangent.xy)));
        //         return normalize(half3(dot(TtoW0.xyz,normalTangent),dot(TtoW1.xyz,normalTangent),dot(TtoW2.xyz,normalTangent)));			
        //     }

        //     struct appdata
        //     {
        //         half4 positionOS : POSITION;
        //         half2 uv : TEXCOORD0;
        //         half3 normalOS : NORMAL;
        //         half4 tangentOS : TANGENT;
        //     };

        //     struct v2f
        //     {
        //         half2 uv : TEXCOORD0;
        //         half4 positionCS : SV_POSITION;
        //         half3 positionWS : TEXCOORD1;
        //         half3 normalWS : TEXCOORD2;
        //         half3 positionOS : TEXCOORD3;
        //         half4 tangentWS : TEXCOORD4;
        //         half4 bitangentWS : TEXCOORD5;
                
        //     };

        //     v2f vert (appdata v)
        //     {
        //         v2f o = (v2f)0;
        //         o.positionCS = TransformObjectToHClip(v.positionOS.xyz);
        //         o.uv = v.uv;
        //         o.positionWS = TransformObjectToWorld(v.positionOS.xyz);
        //         o.normalWS = TransformObjectToWorldNormal(v.normalOS);
        //         o.positionOS.xyz = v.positionOS.xyz;
        //         o.tangentWS.xyz = TransformObjectToWorldDir(v.tangentOS.xyz);
        //         real sign = v.tangentOS.w * GetOddNegativeScale();
        //         o.bitangentWS.xyz = cross(o.normalWS.xyz, o.tangentWS.xyz) * sign;
              
        //         return o;
        //     }

        //     half4 frag (v2f i) : SV_Target
        //     {   
        //         Light light = GetMainLight();
        //         half4 TtoW0 = half4(i.tangentWS.x,i.bitangentWS.x,i.normalWS.x,i.positionWS.x);
        //         half4 TtoW1 = half4(i.tangentWS.y,i.bitangentWS.y,i.normalWS.y,i.positionWS.y);
        //         half4 TtoW2 = half4(i.tangentWS.z,i.bitangentWS.z,i.normalWS.z,i.positionWS.z);
        //         // half Distort = SAMPLE_TEXTURE2D(_Distort, sampler_Distort, (i.uv.xy*0.3)).r;
        //         float3 NormalTex = UnpackNormal(SAMPLE_TEXTURE2D(_NormalTex,sampler_NormalTex,half2(i.uv.x * 0.5,i.uv.y * 0.5 + _Time.y * 0.02)));
        //         half3 NormalWS = ComputeBumpNormal(NormalTex, 2, TtoW0, TtoW1, TtoW2);
        //         half3 L = light.direction;               
        //         half3 V = normalize(_WorldSpaceCameraPos.xyz - i.positionWS);
        //         half3 H = normalize(L + V); 
        //         half3 reflectUV = reflect(-V,NormalWS);
        //         half3 ReflectionTex = SAMPLE_TEXTURE2D(_ReflectionTex, sampler_ReflectionTex, reflectUV).rgb;
        //         half NdV = saturate(dot(NormalWS,V));
                
        //         half3 specular = _specularColor.rgb * 3 * pow(max(0,dot(NormalWS,H)),_pow); 

        //         half4 c;
        //         c.a = pow((1-NdV) ,_alpha);
  
        //         //屏幕UV
        //         float2 screenUV = i.positionCS.xy/_ScreenParams.xy;
        //         // return screenUV.y;
        //         half4 distortion = SAMPLE_TEXTURE2D(_distortion, sampler_distortion, screenUV);
        //         // return distortion;
        //         float2 disUV = lerp(screenUV,distortion,_Dist);
        //         half3 Opaque = SAMPLE_TEXTURE2D(_CameraOpaqueTexture,sampler_CameraOpaqueTexture,disUV) ;
        //         c.rgb += Opaque;
        //         // return half4(Opaque,1);

        //         half3 normalVS = TransformWorldToViewDir(NormalWS);
        //         half3 Matcap = SAMPLE_TEXTURE2D(_Matcap,sampler_Matcap,normalVS.xy * 0.5 + 0.5).rgb;
        //         c.rgb = Matcap;
        //         c.rgb +=  ReflectionTex.rgb * 0.1 + specular.rgb;
        //         c.rgb +=  pow((1-NdV) ,_FresnePow) * _FresnelColor.rgb;
        //         // #ifdef _GRAY_ON
        //         // // half gray = 0.2125 * finColor.r + 0.7154 * finColor.g + 0.0721*finColor.b;
        //         // // half3 grayColor = float3(gray, gray, gray);
        //         // c.rgb = 0.2125 * c.r + 0.7154 * c.g + 0.0721*c.b;
        //         // #endif
        //         return c;
        //     }
        //     ENDHLSL
        // }
         Pass
        {   
            Cull Back
            Tags{"LightMode" = "UniversalForward"}
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #pragma multi_compile _ _GRAY_ON

            CBUFFER_START(UnityPerMaterial)
                half4 _FresnelColor; 
                half _alpha;       
            CBUFFER_END
            TEXTURE2D (_Matcap);SAMPLER(sampler_Matcap);
            TEXTURE2D (_ReflectMap);SAMPLER(sampler_ReflectMap);
            TEXTURE2D(_NormalTex);      SAMPLER(sampler_NormalTex);


            inline float3 ComputeBumpNormal (float3 BumpMap , float BumapScale , float4 TtoW0 , float4 TtoW1, float4 TtoW2)
            {
                half3 normalTangent = BumpMap.rgb;
                normalTangent.xy *= BumapScale;
                normalTangent.z = sqrt(1.0 - saturate(dot(normalTangent.xy,normalTangent.xy)));
                return normalize(half3(dot(TtoW0.xyz,normalTangent),dot(TtoW1.xyz,normalTangent),dot(TtoW2.xyz,normalTangent)));			
            }

            struct appdata
            {
                half4 positionOS : POSITION;
                half2 uv : TEXCOORD0;
                half3 normalOS : NORMAL;
                half4 tangentOS : TANGENT;
            };

            struct v2f
            {
                half2 uv : TEXCOORD0;
                half4 positionCS : SV_POSITION;
                half3 positionWS : TEXCOORD1;
                half3 normalWS : TEXCOORD2;
                half3 positionOS : TEXCOORD3;
                half4 tangentWS : TEXCOORD4;
                half4 bitangentWS : TEXCOORD5;
                
            };

            v2f vert (appdata v)
            {
                v2f o = (v2f)0;
                o.positionCS = TransformObjectToHClip(v.positionOS.xyz);
                o.uv = v.uv;
                o.positionWS = TransformObjectToWorld(v.positionOS.xyz);
                o.normalWS = TransformObjectToWorldNormal(v.normalOS);
                o.positionOS.xyz = v.positionOS.xyz;
                o.tangentWS.xyz = TransformObjectToWorldDir(v.tangentOS.xyz);
                real sign = v.tangentOS.w * GetOddNegativeScale();
                o.bitangentWS.xyz = cross(o.normalWS.xyz, o.tangentWS.xyz) * sign;
              
                return o;
            }

            half4 frag (v2f i) : SV_Target
            {   
                Light light = GetMainLight();
                half4 TtoW0 = half4(i.tangentWS.x,i.bitangentWS.x,i.normalWS.x,i.positionWS.x);
                half4 TtoW1 = half4(i.tangentWS.y,i.bitangentWS.y,i.normalWS.y,i.positionWS.y);
                half4 TtoW2 = half4(i.tangentWS.z,i.bitangentWS.z,i.normalWS.z,i.positionWS.z);
                // half Distort = SAMPLE_TEXTURE2D(_Distort, sampler_Distort, (i.uv.xy*0.3)).r;
                float3 NormalTex = UnpackNormal(SAMPLE_TEXTURE2D(_NormalTex,sampler_NormalTex,half2(i.uv.x * 0.5,i.uv.y * 0.5 + _Time.y * 0.02)));
                half3 NormalWS = ComputeBumpNormal(NormalTex, 2, TtoW0, TtoW1, TtoW2);
                half3 L = light.direction;               
                half3 V = normalize(_WorldSpaceCameraPos.xyz - i.positionWS);
                half3 H = normalize(L + V); 
                half3 reflectUV = reflect(-V,NormalWS);
                // half3 ReflectionTex = SAMPLE_TEXTURE2D(_ReflectionTex, sampler_ReflectionTex, reflectUV).rgb;
                half NdV = saturate(dot(NormalWS,V));
                // return half4(refract,1);
                // float2 reflectUV = V.xz + NormalWS.xy;
                half4 c;
                half3 reflectMap = SAMPLE_TEXTURE2D(_ReflectMap,sampler_ReflectMap,reflectUV.xy).rgb * _FresnelColor.rgb;
                c.a = pow((1-NdV) ,_alpha);

                half3 normalVS = TransformWorldToViewDir(NormalWS);
                half3 Matcap = SAMPLE_TEXTURE2D(_Matcap,sampler_Matcap,normalVS.xy * 0.5 + 0.5).rgb;

                 //屏幕UV
                float2 screenUV = i.positionCS.xy/_ScreenParams.xy;
                // return screenUV.y;
                // half4 distortion = SAMPLE_TEXTURE2D(_distortion, sampler_distortion, screenUV);
                // return distortion;
                // float2 disUV = lerp(screenUV,distortion.xy,_Dist);
                // half3 Opaque = SAMPLE_TEXTURE2D(_CameraOpaqueTexture,sampler_CameraOpaqueTexture,disUV).xyz;
                c.rgb = reflectMap * 1.2;
                c.rgb += Matcap;
                // c.rgb += Opaque * 0.2;

                // half3 r = reflect(-V,NormalWS);
                // half ud = dot(i.bitangentWS.xyz, V);
                // half rl = dot(i.tangentWS.xyz,V);
                // half taichi = rl-ud;
                // half3 taichiColor = cos(r * float3(25, 30, 35));
                // c.rgb += taichiColor * 0.03;
                // return half4(Opaque,1);
                return c;
                
                // c.rgb += reflectMap  * Matcap.rgb ;
                // c.rgb += refract + Matcap01;

                // half3 V = normalize(i.viewWS.xyz);
                // half ud = dot(i.bitangentWS.xyz, V);
                // half rl = dot(i.tangentWS.xyz,V);
                // half taichi = rl-ud;
                // // anisotropy = abs(anisotropy) * _FilmThickness - _FilmIOR;
                // half3 taichiColor = cos(taichi * float3(25, 30, 35));
                // // return half4(taichiColor,1);
                // c.rgb += taichiColor * _ReflectDir.w * 0.1;

                // return c;
            }
            ENDHLSL
        }
        Pass
        {   
            Cull Front
            Tags{"LightMode" = "SRPDefaultUnlit"}
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #pragma multi_compile _ _GRAY_ON

            CBUFFER_START(UnityPerMaterial)
                half4 _FresnelColor; 
                half _alpha;       
            CBUFFER_END
            TEXTURE2D (_Matcap);SAMPLER(sampler_Matcap);
            TEXTURE2D (_ReflectMap);SAMPLER(sampler_ReflectMap);
            TEXTURE2D(_NormalTex);      SAMPLER(sampler_NormalTex);

            inline float3 ComputeBumpNormal (float3 BumpMap , float BumapScale , float4 TtoW0 , float4 TtoW1, float4 TtoW2)
            {
                half3 normalTangent = BumpMap.rgb;
                normalTangent.xy *= BumapScale;
                normalTangent.z = sqrt(1.0 - saturate(dot(normalTangent.xy,normalTangent.xy)));
                return normalize(half3(dot(TtoW0.xyz,normalTangent),dot(TtoW1.xyz,normalTangent),dot(TtoW2.xyz,normalTangent)));			
            }

            struct appdata
            {
                half4 positionOS : POSITION;
                half2 uv : TEXCOORD0;
                half3 normalOS : NORMAL;
                half4 tangentOS : TANGENT;
            };

            struct v2f
            {
                half2 uv : TEXCOORD0;
                half4 positionCS : SV_POSITION;
                half3 positionWS : TEXCOORD1;
                half3 normalWS : TEXCOORD2;
                half3 positionOS : TEXCOORD3;
                half4 tangentWS : TEXCOORD4;
                half4 bitangentWS : TEXCOORD5;
                
            };

            v2f vert (appdata v)
            {
                v2f o = (v2f)0;
                o.positionCS = TransformObjectToHClip(v.positionOS.xyz);
                o.uv = v.uv;
                o.positionWS = TransformObjectToWorld(v.positionOS.xyz);
                o.normalWS = TransformObjectToWorldNormal(v.normalOS);
                o.positionOS.xyz = v.positionOS.xyz;
                o.tangentWS.xyz = TransformObjectToWorldDir(v.tangentOS.xyz);
                real sign = v.tangentOS.w * GetOddNegativeScale();
                o.bitangentWS.xyz = cross(o.normalWS.xyz, o.tangentWS.xyz) * sign;
              
                return o;
            }

            half4 frag (v2f i) : SV_Target
            {   
                Light light = GetMainLight();
                half4 TtoW0 = half4(i.tangentWS.x,i.bitangentWS.x,i.normalWS.x,i.positionWS.x);
                half4 TtoW1 = half4(i.tangentWS.y,i.bitangentWS.y,i.normalWS.y,i.positionWS.y);
                half4 TtoW2 = half4(i.tangentWS.z,i.bitangentWS.z,i.normalWS.z,i.positionWS.z);
                // half Distort = SAMPLE_TEXTURE2D(_Distort, sampler_Distort, (i.uv.xy*0.3)).r;
                float3 NormalTex = UnpackNormal(SAMPLE_TEXTURE2D(_NormalTex,sampler_NormalTex,half2(i.uv.x * 0.5,i.uv.y * 0.5 + _Time.y * 0.02)));
                half3 NormalWS = ComputeBumpNormal(NormalTex, 2, TtoW0, TtoW1, TtoW2);
                half3 L = light.direction;               
                half3 V = normalize(_WorldSpaceCameraPos.xyz - i.positionWS);
                half3 H = normalize(L + V); 
                half3 reflectUV = reflect(-V,NormalWS);
                // half3 ReflectionTex = SAMPLE_TEXTURE2D(_ReflectionTex, sampler_ReflectionTex, reflectUV).rgb;
                half NdV = saturate(dot(-NormalWS,V));
                // return half4(refract,1);
                // float2 reflectUV = V.xz + NormalWS.xy;
                half3 reflectMap = SAMPLE_TEXTURE2D(_ReflectMap,sampler_ReflectMap,reflectUV.xy).rgb * _FresnelColor.rgb;
                half a = max(0,pow((1-NdV) ,_alpha));
                // return a;
                return half4(reflectMap,a);
                
                // c.rgb += reflectMap  * Matcap.rgb ;
                // c.rgb += refract + Matcap01;

                // half3 V = normalize(i.viewWS.xyz);
                // half ud = dot(i.bitangentWS.xyz, V);
                // half rl = dot(i.tangentWS.xyz,V);
                // half taichi = rl-ud;
                // // anisotropy = abs(anisotropy) * _FilmThickness - _FilmIOR;
                // half3 taichiColor = cos(taichi * float3(25, 30, 35));
                // // return half4(taichiColor,1);
                // c.rgb += taichiColor * _ReflectDir.w * 0.1;

                // return c;
            }
            ENDHLSL
        }
    }
}
