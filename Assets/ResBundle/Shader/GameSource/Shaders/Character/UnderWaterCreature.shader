//水底生物材质
//选中描边在管线上开启相应的RenderFeature
//by taecg
Shader "ONEMT/Character/UnderWaterCreature"
{
    Properties
    {
        //: { "type": "group","label":"基础"}
        //: { "type": "default","prop":"_UVType", "label":"类型"}
        [KeywordEnum(NPC,Part,Procedual)]_UVType("UV展开方式",int) = 0
        //: { "type": "texture2","prop":"_BaseMap","prop2":"_BaseColor", "label":"基础纹理(RGB)"}
        [HDR]_BaseColor("Color", Color) = (1,1,1,1)
        _BaseMap("Albedo", 2D) = "white" {}
        //: { "type": "toggle","prop":"_BaseValue02","vectorComponent1":"x", "label":"是否在半透明Rim时保留Alpha(X)"}
        //: { "type": "st","prop":"_BaseMap"}
        _BaseValue02("在半透明Rim时保留Alpha(X)",vector) = (1,1,1,1)
        //: { "type": "texture2","prop":"_BumpMap","prop2":"_BumpScale", "label":"法线纹理"}
        _BumpScale("Normal Scale", range(0,1)) = 1.0
        [Normal]_BumpMap("Normal Map", 2D) = "bump" {}
        //: { "type": "texture1","prop":"_MetallicGlossMap","label":"金属(R)粗糙(G)自发光(B)AO(A)"}
        //: { "type":"float","prop":"_MixValue1","label":"微调曲线","vectorComponent1":"z","min":"0.0001","max":"1"}
        _MetallicGlossMap("Metallic(R)Roughness(G)Emission(B)AO(A)", 2D) = "white" {}
        //: { "type": "float","prop":"_MixValue1", "label":"金属度","vectorComponent1":"x","min":"0","max":"1"}
        //: { "type": "float","prop":"_MixValue1", "label":"光滑度","vectorComponent1":"y","min":"0","max":"1"}
        //: { "type": "float","prop":"_MixValue1", "label":"AO强度","vectorComponent1":"w","min":"0","max":"1"}
        //: { "type": "default","prop":"_EmissionColor", "label":"自发光"}
        [HDR] _EmissionColor("Emission Color", Color) = (0,0,0)
        _MixValue1("金属度(X)光滑度(Y)AO(W)",vector) = (1,1,1,1)

        //: { "type": "group","label":"外轮廓半透明"}
        //: { "type": "minmax","prop":"_RimAlpha", "label":"衰减","vectorComponent1":"x","vectorComponent2":"y","min":"0","max":"2"}
        //: { "type": "toggle","prop":"_RimAlpha", "label":"反向","vectorComponent1":"w"}
        _RimAlpha("Min(X) Max(Y) Intensity(Z) Invert(W)",vector) = (0,1,1,0)
        //: { "type": "vector3","prop":"_RimValue01", "label":"半透方向"}
        _RimValue01("半透方向(XYZ)",vector) = (0,0,0,0)

        //: { "type": "groupVariant","label":"表面皮肤","keyword":"_SURFACEENABLED_ON"}
        [Toggle]_SurfaceEnabled("SurfaceEnabled",int) = 0
        //: { "type": "texture2","prop":"_SurfaceMap","prop2":"_SurfaceColor", "label":"表面纹理(RGB)透明(A)"}
        _SurfaceColor("SurfaceColor", Color) = (1,1,1,1)
        _SurfaceMap("SurfaceMap", 2D) = "white" {}
        //: { "type": "toggle","prop":"_SurfaceValue01", "label":"是否在半透明Rim时保留Alpha","vectorComponent1":"x"}
        //: { "type": "st","prop":"_SurfaceMap"}
        _SurfaceValue01("在半透明Rim时保留Alpha(X)",vector) = (1,1,1,1)
        //: { "type": "texture2","prop":"_SurfaceBumpMap","prop2":"_SurfaceBumpScale", "label":"法线纹理"}
        _SurfaceBumpScale("Normal Scale", range(0,1)) = 1.0
        [Normal]_SurfaceBumpMap("Surface BumpMap", 2D) = "bump" {}

        //: { "type": "groupVariant","label":"半透明透射","keyword":"_TRANSLUCENTENABLED_ON"}
        [Toggle]_TranslucentEnabled("TranslucentEnabled",int) = 0
        //: { "type": "toggle","prop":"_TranslucentToggle", "label":"是否显示透射(测试)"}
        [Toggle]_TranslucentToggle("仅显示透射(测试)",int) = 0
        //: { "type": "color","prop":"_TranslucentColor", "label":"颜色"}
        _TranslucentColor("TranslucentColor(RGB)",color) = (1,0.18,0.18,1)
        //: { "type": "minmax","prop":"_Translucent", "label":"范围","vectorComponent1":"x","vectorComponent2":"y","min":"-5","max":"5"}
        //: { "type": "float","prop":"_Translucent", "label":"强度","vectorComponent1":"z"}
        //: { "type": "float","prop":"_Translucent", "label":"衰减","vectorComponent1":"w"}
        _Translucent("Near(X)Far(Y)Strength(Z)Attenuation(W)",vector) = (0,1,1,1)

        //: { "type": "groupVariant","label":"外发光","keyword":"_RIMENABLED_ON"}
        [Toggle]_RimEnabled("RimEnabled",int) = 0
        //: { "type": "color","prop":"_RimColor", "label":"颜色"}
        _RimColor("RimColor",color) = (1,1,1,1)
        //: { "type": "minmax","prop":"_Rim", "label":"衰减","vectorComponent1":"x","vectorComponent2":"y","min":"0","max":"2"}
        //: { "type": "float","prop":"_Rim", "label":"强度","vectorComponent1":"z"}
        //: { "type": "toggle","prop":"_Rim", "label":"是否内发光","vectorComponent1":"w"}
        _Rim("Min(X) Max(Y) Intensity(Z) Invert(W)",vector) = (0,1,1,0)

        //: { "type": "group","label":"渲染状态"}
        //: { "type": "renderType","prop":"_RenderType"}
        //: { "type": "cull","prop":"_Cull"}
        //: { "type": "zwrite","prop":"_ZWrite"}
        //: { "type": "queue"}
        [Enum(Opaque,0,Transparent,1)]_RenderType("RenderType",int) = 0
        [Enum(UnityEngine.Rendering.BlendMode)]_BlendSrc("BlendSrc",int) = 1
        [Enum(UnityEngine.Rendering.BlendMode)]_BlendDst("BlendDst",int) = 0
        [Enum(UnityEngine.Rendering.CullMode)]_Cull("CullMode",int) = 2
        [Enum(Off,0,On,1)]_ZWrite("ZWrite",int) = 1

        //C#传参
        _BaseMap02("_BaseMap02", 2D) = "white" {}
        [Normal]_BumpMap02("_BumpMap02", 2D) = "bump" {}
        [HDR]_BaseColor02("_BaseColor02",color) = (0.8,0.8,0.8,1)
        _BaseColor03("_BaseColor03",color) = (0,0,0,0)
    }

    HLSLINCLUDE
    #pragma target 3.5
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

    CBUFFER_START(UnityPerMaterial)
        float4 _BaseMap_ST;
        float4 _SurfaceMap_ST;
        float4 _LightDirection;
        half4 _BaseColor;
        half4 _BaseValue02;
        half4 _EmissionColor;
        half4 _MixValue1;
        half4 _SurfaceColor;
        half4 _SurfaceValue01;
        half4 _SurfaceMetallicGlossMap,_SurfaceMixValue1;
        half4 _TranslucentColor,_Translucent;
        half4 _RimAlpha,_RimColor,_Rim,_RimValue01;
        half4 _BaseColor02;
        half4 _BaseColor03;
        half _RenderType;
        half _Smoothness;
        half _Metallic;
        half _BumpScale;
        half _OcclusionStrength;
        half _ShowArea;
        half _SurfaceBumpScale;
        half _TranslucentToggle;
    CBUFFER_END
    TEXTURE2D(_BaseMap);            SAMPLER(sampler_BaseMap);
    TEXTURE2D(_BumpMap);            SAMPLER(sampler_BumpMap);
    TEXTURE2D(_MetallicGlossMap);   SAMPLER(sampler_MetallicGlossMap);
    TEXTURE2D(_SurfaceMap);         SAMPLER(sampler_SurfaceMap);
    TEXTURE2D(_SurfaceBumpMap);     SAMPLER(sampler_SurfaceBumpMap);
    TEXTURE2D(_BaseMap02);          SAMPLER(sampler_BaseMap02);
    TEXTURE2D(_BumpMap02);          SAMPLER(sampler_BumpMap02);
    float _BaseMap02_Repeat;


    //程序化计算UV
    half4 SampleProcedualUV(TEXTURE2D_PARAM(map,sampler_map),float3 worldUV,half3 normalOS)
    {
        half4 c = 1;
        half3 xyz = step(0,normalOS.xyz)*2-1;
        half4 map01 = SAMPLE_TEXTURE2D(map, sampler_map, worldUV.zy * half2(xyz.x,1));
        half4 map02 = SAMPLE_TEXTURE2D(map, sampler_map, worldUV.xy * half2(-xyz.z,1));
        half4 map03 = SAMPLE_TEXTURE2D(map, sampler_map, worldUV.xz * half2(1,xyz.y));
        half2 faceXY = saturate((abs(normalOS.xy)-0.5)/0.1);//smoothstep(0.5,0.6,abs(normalOS.x));
        c = lerp(map02,map01,faceXY.x);
        c = lerp(c,map03,faceXY.y);
        return c;
    }
    ENDHLSL

    SubShader
    {
        Tags{"Queue"="Transparent" "RenderPipeline" = "UniversalPipeline"}
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            Name "ForwardLit"
            Tags{"LightMode" = "UniversalForward"}

            // Blend[_BlendSrc][_BlendDst]
            ZWrite[_ZWrite]
            Cull[_Cull]

            HLSLPROGRAM
            #pragma shader_feature_local _ _TRANSLUCENTENABLED_ON
            #pragma shader_feature_local _ _RIMENABLED_ON
            #pragma shader_feature_local _ _UVTYPE_NPC _UVTYPE_PART _UVTYPE_PROCEDUAL
            #pragma multi_compile_local _ _SURFACEENABLED_ON
            #pragma multi_compile _ _ADDITIONAL_LIGHTS
            #pragma vertex vert
            #pragma fragment frag

            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS     : NORMAL;
                float4 tangentOS    : TANGENT;
                half4 color         : COLOR;        //程序化模型：
                //由于骨骼动画会改变模型的本地坐标，为了保证动画时不发生偏移，所以另外存储数据来用
                float4 texcoord     : TEXCOORD0;    //程序化模型：模型空间的位置坐标
                float4 texcoord2    : TEXCOORD1;    //程序化模型：法线
            };

            struct Varyings
            {
                float4 positionCS               : SV_POSITION;
                float4 uv                       : TEXCOORD0;
                float3 positionWS               : TEXCOORD1;
                float4 normalWS                 : TEXCOORD2;
                float4 tangentWS                : TEXCOORD3;
                float4 bitangentWS              : TEXCOORD4;
                float4 viewDirWS                : TEXCOORD5; //视向量(XYZ) 顶点到相机的距离(W)
                float2 viewDirLength            : TEXCOORD6; //每个顶点到相机的距离(X) 模型中心点到相机的距离(Y)
                half3 vertexSH                  : TEXCOORD7;
                float3 normalOS                 : TEXCOORD8;
                half4 color                     : TEXCOORD9;
            };

            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;
                o.positionWS = TransformObjectToWorld(v.positionOS.xyz);
                o.positionCS = TransformWorldToHClip(o.positionWS);

                #if _UVTYPE_NPC || _UVTYPE_PART //美术自己展的UV
                    o.uv.xy = TRANSFORM_TEX(v.texcoord, _BaseMap);
                    o.uv.zw = TRANSFORM_TEX(v.texcoord,_SurfaceMap);
                    o.normalOS = v.normalOS;
                    o.normalWS.xyz = TransformObjectToWorldNormal(v.normalOS);
                    o.tangentWS.xyz = TransformObjectToWorldDir(v.tangentOS.xyz);
                    real sign = v.tangentOS.w * GetOddNegativeScale();
                    o.bitangentWS.xyz = cross(o.normalWS.xyz, o.tangentWS.xyz) * sign;
                    o.color = v.color;
                #elif  _UVTYPE_PROCEDUAL //程序化UV
                    o.uv.xyz = v.texcoord.xyz;
                    o.normalOS = v.texcoord2.xyz;
                    o.normalWS.xyz = TransformObjectToWorldNormal(v.texcoord2.xyz);
                    half3 tangentOS = half3(-o.normalOS.z,o.normalOS.y,o.normalOS.x);
                    o.tangentWS.xyz = TransformObjectToWorldDir(tangentOS);
                    o.bitangentWS.xyz =  cross(o.tangentWS.xyz,o.normalWS.xyz);
                    o.color = v.positionOS;
                #endif
                o.tangentWS.w = o.positionWS.x;
                o.bitangentWS.w = o.positionWS.y;
                o.normalWS.w = o.positionWS.z;

                o.viewDirWS.xyz = _WorldSpaceCameraPos-o.positionWS;
                o.viewDirLength.x = length(o.viewDirWS.xyz);
                o.viewDirLength.y = length(_WorldSpaceCameraPos - unity_ObjectToWorld._14_24_34);
                o.vertexSH = SampleSHVertex(o.normalWS.xyz);
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                half alpha = 1;

                //材质表面数据
                SurfaceData surfaceData = (SurfaceData)0;
                {
                    half4 albedoAlpha = 1;
                    half4 metallicMap = 1;
                    
                    #if _UVTYPE_NPC //美术PNC
                        {
                            half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv.xy);
                            albedoAlpha = baseMap * _BaseColor;
                            surfaceData.normalTS = UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, i.uv.xy), _BumpScale);
                            metallicMap = SAMPLE_TEXTURE2D(_MetallicGlossMap, sampler_MetallicGlossMap, i.uv.xy);
                        }
                    #elif _UVTYPE_PART  //主角美术部件
                        {
                            half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv.xy);
                            half4 baseMap02 = SAMPLE_TEXTURE2D(_BaseMap02, sampler_BaseMap02, i.uv.xy * _BaseMap02_Repeat);
                            albedoAlpha.rgb = lerp(baseMap02.rgb * _BaseColor02.rgb, baseMap.rgb * _BaseColor.rgb * lerp(1,_BaseColor02.rgb,0.2),i.color.rgb);
                            albedoAlpha.a = baseMap.a;
                            alpha = albedoAlpha.a * _BaseValue02.x;

                            half3 normalMap01 = UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, i.uv.xy), _BumpScale);
                            half3 normalMap02 = UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap02, sampler_BumpMap02, i.uv.xy), _BumpScale);
                            surfaceData.normalTS=normalize(lerp(normalMap02,normalMap01,i.color.rgb));
                            metallicMap = SAMPLE_TEXTURE2D(_MetallicGlossMap, sampler_MetallicGlossMap, i.uv.xy);

                            #if _SURFACEENABLED_ON
                                half4 surfaceMap = SAMPLE_TEXTURE2D(_SurfaceMap,sampler_SurfaceMap,i.uv.zw);
                                surfaceMap *= _SurfaceColor;
                                half mask = lerp(surfaceMap.a,0,i.color.r); 
                                albedoAlpha.rgb = lerp(albedoAlpha.rgb,surfaceMap.rgb,mask);
                                alpha += surfaceMap.a * _SurfaceValue01.x;

                                half3 surfaceNormal = UnpackNormalScale(SAMPLE_TEXTURE2D(_SurfaceBumpMap,sampler_SurfaceBumpMap,i.uv.zw),_SurfaceBumpScale);
                                surfaceNormal = BlendNormalRNM(surfaceData.normalTS, surfaceNormal);
                                surfaceData.normalTS = lerp(surfaceData.normalTS,surfaceNormal,mask);
                            #endif
                        }
                    #elif _UVTYPE_PROCEDUAL //程序化UV
                        {
                            float3 positionOS = i.uv.xyz;
                            float3 worldUV = positionOS *_BaseMap_ST.x;

                            albedoAlpha = SampleProcedualUV(TEXTURE2D_ARGS(_BaseMap,sampler_BaseMap),worldUV,i.normalOS);
                            albedoAlpha.rgb *= _BaseColor.rgb;
                            alpha = albedoAlpha.a * _BaseValue02.x;

                            surfaceData.normalTS = UnpackNormalScale(SampleProcedualUV(TEXTURE2D_ARGS(_BumpMap,sampler_BumpMap),worldUV,i.normalOS),_BumpScale);
                            metallicMap = SampleProcedualUV(TEXTURE2D_ARGS(_MetallicGlossMap,sampler_MetallicGlossMap),worldUV,i.normalOS);
                            
                            #if _SURFACEENABLED_ON
                                float3 surfaceWorldUV = positionOS *_SurfaceMap_ST.x;
                                half4 surfaceMap = SampleProcedualUV(TEXTURE2D_ARGS(_SurfaceMap,sampler_SurfaceMap),surfaceWorldUV,i.normalOS);
                                surfaceMap *= _SurfaceColor;
                                half mask = surfaceMap.a;
                                albedoAlpha.rgb = lerp(albedoAlpha.rgb,surfaceMap.rgb,mask);
                                alpha += surfaceMap.a * _SurfaceValue01.x;

                                half3 surfaceNormal = UnpackNormalScale(SampleProcedualUV(TEXTURE2D_ARGS(_SurfaceBumpMap,sampler_SurfaceBumpMap),surfaceWorldUV,i.normalOS),_SurfaceBumpScale);
                                surfaceNormal = BlendNormalRNM(surfaceData.normalTS, surfaceNormal);
                                surfaceData.normalTS = lerp(surfaceData.normalTS,surfaceNormal,mask);
                            #endif
                        }
                    #else
                    #endif
                    
                    surfaceData.albedo = albedoAlpha.rgb;
                    surfaceData.alpha = albedoAlpha.a;
                    metallicMap = PositivePow(metallicMap,_MixValue1.z);
                    surfaceData.metallic = metallicMap.r * _MixValue1.r;
                    surfaceData.specular = half3(0.0h, 0.0h, 0.0h);
                    surfaceData.smoothness = (1-metallicMap.g)*_MixValue1.g;
                    surfaceData.occlusion = lerp(1,metallicMap.a,_MixValue1.a);
                    surfaceData.emission = _EmissionColor.rgb * metallicMap.b;
                }

                //额外数据
                InputData inputData = (InputData)0;
                { 
                    inputData.positionWS = i.positionWS;
                    inputData.normalWS = TransformTangentToWorld(surfaceData.normalTS, half3x3(i.tangentWS.xyz, i.bitangentWS.xyz, i.normalWS.xyz));
                    inputData.normalWS = SafeNormalize(inputData.normalWS);
                    inputData.viewDirectionWS = SafeNormalize(i.viewDirWS.xyz);
                    inputData.shadowCoord = TransformWorldToShadowCoord(inputData.positionWS);
                    // inputData.vertexLighting = i.fogFactorAndVertexLight.yzw;
                    inputData.bakedGI = SAMPLE_GI(i.lightmapUV, i.vertexSH, inputData.normalWS);
                    // inputData.normalizedScreenSpaceUV = GetNormalizedScreenSpaceUV(i.positionCS);
                }

                half4 c = half4(0,0,0,1);
                BRDFData brdfData = (BRDFData)0;
                {//BRDF数据
                    half oneMinusReflectivity = OneMinusReflectivityMetallic(surfaceData.metallic);
                    half reflectivity = 1.0 - oneMinusReflectivity;
                    brdfData.diffuse = surfaceData.albedo * oneMinusReflectivity;
                    brdfData.specular = lerp(kDieletricSpec.rgb, surfaceData.albedo, surfaceData.metallic);
                    brdfData.reflectivity = reflectivity;
                    brdfData.perceptualRoughness = PerceptualSmoothnessToPerceptualRoughness(surfaceData.smoothness);
                    brdfData.roughness           = max(PerceptualRoughnessToRoughness(brdfData.perceptualRoughness), HALF_MIN_SQRT);
                    brdfData.roughness2          = max(brdfData.roughness * brdfData.roughness, HALF_MIN);
                    brdfData.grazingTerm         = saturate(surfaceData.smoothness + reflectivity);
                    brdfData.normalizationTerm   = brdfData.roughness * 4.0h + 2.0h;
                    brdfData.roughness2MinusOne  = brdfData.roughness2 - 1.0h;
                }

                Light mainLight = GetMainLight(inputData.shadowCoord);
                half3 color = 0;
                {//间接光
                    half3 reflectVector = reflect(-inputData.viewDirectionWS, inputData.normalWS);
                    half NoV = saturate(dot(inputData.normalWS, inputData.viewDirectionWS));
                    half fresnelTerm = Pow4(1.0 - NoV);
                    half3 indirectDiffuse = inputData.bakedGI * surfaceData.occlusion;
                    half3 indirectSpecular = GlossyEnvironmentReflection(reflectVector, brdfData.perceptualRoughness, surfaceData.occlusion);
                    color = EnvironmentBRDF(brdfData, indirectDiffuse, indirectSpecular, fresnelTerm);
                }

                BRDFData brdfDataClearCoat = (BRDFData)0;
                color += LightingPhysicallyBased(brdfData, brdfDataClearCoat,mainLight,inputData.normalWS, inputData.viewDirectionWS,surfaceData.clearCoatMask, false);

                #ifdef _ADDITIONAL_LIGHTS
                    uint pixelLightCount = GetAdditionalLightsCount();
                    for (uint lightIndex = 0u; lightIndex < pixelLightCount; ++lightIndex)
                    {
                        Light light = GetAdditionalLight(lightIndex, inputData.positionWS);
                        color += LightingPhysicallyBased(brdfData, brdfDataClearCoat,light,inputData.normalWS, inputData.viewDirectionWS,surfaceData.clearCoatMask, false);
                    }
                #endif

                #if _TRANSLUCENTENABLED_ON
                    half scaleFactor = UNITY_MATRIX_M[0][0];
                    float2 nearFar = _Translucent.xy * 1 + i.viewDirLength.y;
                    half3 translucent = _Translucent.z * pow(smoothstep(nearFar.x,nearFar.y,i.viewDirLength.x),_Translucent.w) * _TranslucentColor.rgb;
                    color += translucent * surfaceData.albedo;
                    if(_TranslucentToggle==1)return half4(translucent,1);
                #endif

                c.rgb += color + surfaceData.emission;

                #if _RIMENABLED_ON
                    half3 rim;
                    {//Rim
                        half NoV = saturate(dot(inputData.normalWS,inputData.viewDirectionWS));
                        NoV = smoothstep(_Rim.x,_Rim.y,NoV);
                        NoV = lerp(1-NoV,NoV,_Rim.w);
                        rim = NoV * _Rim.z * _RimColor.rgb;
                        #if _UVTYPE_NPC || _UVTYPE_PROCEDUAL
                            c.rgb += rim*rim;
                        #elif _UVTYPE_PART
                            c.rgb += lerp(rim*rim,0,i.color.rgb);
                        #endif
                    }
                #endif

                //#if _RIMALPHAENABLED_ON
                {//外轮廓半透明
                    half rimAlpha;
                    half3 VH = inputData.viewDirectionWS+_RimValue01.xyz;
                    half NoV = saturate(dot(inputData.normalWS,VH));
                    NoV = smoothstep(_RimAlpha.x,_RimAlpha.y,NoV);
                    NoV = lerp(1-NoV,NoV,_RimAlpha.w);
                    #if _UVTYPE_NPC
                        // c.a = saturate(NoV + i.color);  //外轮廓半透明,并通过顶点色区分区域
                        // c.a = saturate(c.a + surfaceData.alpha);
                        c.a = max(max(NoV,i.color.r),surfaceData.alpha);
                    #elif _UVTYPE_PART || _UVTYPE_PROCEDUAL
                        // c.a = lerp(NoV,saturate(surfaceData.alpha+NoV),_RimValue01.w);
                        // return half4(surfaceData.alpha.rrr,1);
                        c.a = saturate(alpha+NoV);
                    #endif
                }

                c.rgb += _BaseColor03.rgb;  //在水底升级长大时的表面泛白效果
                c.a *= lerp(1,_BaseColor.a,_RenderType);
                // c.rgb = c.aaa;

                return c;
            }
            ENDHLSL
        }

        Pass
        {
            Stencil
            {
                Ref 11
                Comp Never
                Fail Replace
            }

            Name "Outline0"
            Tags{"LightMode" = "Outline0"}
            ZWrite Off
            ZTest Always

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            struct Attributes
            {
                float4 positionOS   : POSITION;
                #if _UVTYPE_PROCEDUAL
                    half4 texcoord3 : TEXCOORD2;    //Metaball模型: 上下渐变(R) 部件选择区域(G)
                #endif
            };

            struct Varyings
            {
                float4 positionCS               : SV_POSITION;
            };

            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;
                o.positionCS = TransformObjectToHClip(v.positionOS.xyz);
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                return 0;
            }
            ENDHLSL
        }
        Pass
        {
            Stencil
            {
                Ref 11
                Comp NotEqual
            }
            
            Name "Outline1"
            Tags{"LightMode" = "Outline1"}
            ZWrite Off
            ZTest Always

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS     : NORMAL;
            };

            struct Varyings
            {
                float4 positionCS               : SV_POSITION;
            };

            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;
                float3 positionOS = v.positionOS.xyz;
                half3 dir = normalize(v.normalOS.xyz);
                positionOS += dir * 0.01;
                o.positionCS = TransformObjectToHClip(positionOS);
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                return 1;
            }
            ENDHLSL
        }

        // Pass
        // {
            //     Name "ShadowCaster"
            //     Tags{"LightMode" = "ShadowCaster"}

            //     ZWrite On
            //     ZTest LEqual
            //     ColorMask 0
            //     Cull[_Cull]

            //     HLSLPROGRAM
            //     #pragma target 2.0
            //     #pragma vertex ShadowPassVertex
            //     #pragma fragment ShadowPassFragment

            //     // #include "Packages/com.unity.render-pipelines.universal/Shaders/LitInput.hlsl"
            //     #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            //     #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"

            //     struct Attributes
            //     {
                //         float4 positionOS   : POSITION;
                //         float3 normalOS     : NORMAL;
                //         float2 texcoord     : TEXCOORD0;
            //     };

            //     struct Varyings
            //     {
                //         float2 uv           : TEXCOORD0;
                //         float4 positionCS   : SV_POSITION;
            //     };

            //     float4 GetShadowPositionHClip(Attributes input)
            //     {
                //         float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                //         float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

                //         float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, _LightDirection));

                //         #if UNITY_REVERSED_Z
                //             positionCS.z = min(positionCS.z, positionCS.w * UNITY_NEAR_CLIP_VALUE);
                //         #else
                //             positionCS.z = max(positionCS.z, positionCS.w * UNITY_NEAR_CLIP_VALUE);
                //         #endif

                //         return positionCS;
            //     }

            //     Varyings ShadowPassVertex(Attributes input)
            //     {
                //         Varyings output;
                //         UNITY_SETUP_INSTANCE_ID(input);

                //         output.uv = TRANSFORM_TEX(input.texcoord, _BaseMap);
                //         output.positionCS = GetShadowPositionHClip(input);
                //         return output;
            //     }

            //     half4 ShadowPassFragment(Varyings input) : SV_TARGET
            //     {
                //         // Alpha(SampleAlbedoAlpha(input.uv, TEXTURE2D_ARGS(_BaseMap, sampler_BaseMap)).a, _BaseColor, _Cutoff);
                //         return 0;
            //     }
            //     ENDHLSL
        // }

    }

    CustomEditor "taecg.tools.CustomShaderGUI"
}
