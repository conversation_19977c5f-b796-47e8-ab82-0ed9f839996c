Shader "ONEMT/Character/CreatureSimpleLit_MonsterWave"
{
    Properties
    {
        _BaseMap("基础纹理", 2D) = "white" {} 
        _ShadowHight("_ShadowHight", float) = 0.03   
    }
    SubShader
    {
        Tags {"Queue"="Geometry+1" "RenderPipeline" = "UniversalPipeline"}

        Pass
        {
            Tags{"LightMode" = "MonsterWave"}
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            CBUFFER_START(UnityPerMaterial)
                half4 _BaseMap_ST; 
                half _ShadowHight;//Story系列由程序传值控制阴影高度
            CBUFFER_END
            
            half4 _duskColor, _nightColor;
            float _dayToDusk, _duskToNight, _nightToDay;
            
            TEXTURE2D(_BaseMap);SAMPLER(sampler_BaseMap);
            
            struct appdata
            {
                half4 positionOS : POSITION;
                half4 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 positionCS : SV_POSITION;
            };

            v2f vert (appdata v)
            {
                // v2f o;
                v2f o;
                o.positionCS = TransformObjectToHClip(v.positionOS.xyz);

                o.uv = TRANSFORM_TEX(v.uv, _BaseMap);
                return o;
            }

            half4 frag (v2f i) : SV_Target
            {
                half4 BaseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv);
                return BaseMap;
            }
            ENDHLSL
        }

        Pass
        {
            Tags{"LightMode" = "MonsterWaveMeshShadow"}
            Blend DstColor Zero
            Stencil
            {
                Ref 21
                Comp NotEqual
                Pass Replace
            }
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #pragma multi_compile _ DITHERENABLED_ON


            CBUFFER_START(UnityPerMaterial)
                half4 _BaseMap_ST; 
                half _ShadowHight;//Story系列由程序传值控制阴影高度
            CBUFFER_END
            TEXTURE2D(_BaseMap);SAMPLER(sampler_BaseMap);

            struct a2v
            {
                float4 positionOS : POSITION;
                half2 uv : TEXCOORD0;
            };
            struct v2f
            {
                
                float4 positionCS : SV_POSITION;
                half4 color : TEXCOORD0;
                half4 positionOS : TEXCOORD1;
                float2 uv : TEXCOORD2;
            };

            v2f vert(a2v v)
            {
                v2f o;
                Light light = GetMainLight();
                float3 L = normalize(light.direction);
                float3 positionWS = TransformObjectToWorld(v.positionOS.xyz);
                positionWS.xz -= L.xz * (positionWS.y - _ShadowHight);
                positionWS.y = _ShadowHight;
                o.positionCS = TransformWorldToHClip(positionWS);
                o.uv = v.uv;
                o.color = half4(0.21,0.26,0.34,1);
                o.positionOS = v.positionOS;
                return o;
            }
            half4 frag(v2f i) : SV_Target
            {
                return i.color;
            }

            ENDHLSL


            
        }
    }
}
