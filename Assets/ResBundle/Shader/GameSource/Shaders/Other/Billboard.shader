Shader "ONEMT/Other/Billboard"
{
    Properties
    {
        _BaseColor("基础颜色(RGB)透明度(A)",color) = (1,1,1,1)
        _BaseMap("基础纹理(RGB)透明(A)",2D) = "white"{}
    }

    HLSLINCLUDE
    #pragma prefer_hlslcc gles
    #pragma exclude_renderers d3d11_9x
    #pragma vertex vert
    #pragma fragment frag
    #pragma target 2.0
    #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
    #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/UnityInstancing.hlsl"

    CBUFFER_START(UnityPerMaterial)
        half4 _BaseColor;
    CBUFFER_END
    TEXTURE2D(_BaseMap); SAMPLER(sampler_BaseMap);
    ENDHLSL

    SubShader
    {
        Tags { "Queue"="Transparent" "RenderPipeline" = "UniversalPipeline"}
        Blend SrcAlpha OneMinusSrcAlpha
        ZWrite Off
        ZTest Always

        Pass
        {
            HLSLPROGRAM

            struct Attributes
            {
                float4 positionOS       : POSITION;
                float2 texcoord         : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS       : SV_POSITION;
                float2 uv               : TEXCOORD0;
            };

            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;
                o.uv = v.texcoord;

                //BillBoard
                float3 viewDir=mul(GetWorldToObjectMatrix(),float4(_WorldSpaceCameraPos,1)).xyz;
                viewDir = normalize(viewDir);
                float3 upDir = float3(0,1,0);
                float3 rightDir = normalize(cross(viewDir,upDir));
                upDir = cross(rightDir,viewDir);
                float3 positionOS = rightDir * v.positionOS.x + upDir * v.positionOS.y + viewDir * v.positionOS.z;
                o.positionCS = TransformObjectToHClip(positionOS);
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                half4 c;
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap,sampler_BaseMap,i.uv);
                c = baseMap * _BaseColor;
                return c;
            }
            ENDHLSL
        }
    }
}
