#ifndef UNIVERSAL_MYBRDF_INCLUDED
#define UNIVERSAL_MYBRDF_INCLUDED

// #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"
// #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/EntityLighting.hlsl"
// #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/ImageBasedLighting.hlsl"
// #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
// #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"
#include "MyBRDFCore.hlsl"
#include "MySSS.hlsl"



struct MyBRDFData
{
    half3 diffuse;
    half3 specular;
    half perceptualRoughness;
    half roughness;
    half roughness2;
    half grazingTerm;

    // We save some light invariant BRDF terms so we don't have to recompute
    // them in the light loop. Take a look at DirectBRDF function for detailed explaination.
    half normalizationTerm;     // roughness * 4.0 + 2.0
    half roughness2MinusOne;    // roughness^2 - 1.0
};
//Standard PBR
half3 MyDirectBRDF(MyBRDFData brdfData, half3 normalWS, half3 lightDirectionWS, half3 viewDirectionWS)
{
    #ifndef _SPECULARHIGHLIGHTS_OFF
    float3 halfDir = normalize(float3(lightDirectionWS) + float3(viewDirectionWS));

    float NoH = saturate(dot(normalWS, halfDir));
    half LoH = saturate(dot(lightDirectionWS, halfDir));

    // GGX Distribution multiplied by combined approximation of Visibility and Fresnel
    // BRDFspec = (D * V * F) / 4.0
    // D = roughness^2 / ( NoH^2 * (roughness^2 - 1) + 1 )^2
    // V * F = 1.0 / ( LoH^2 * (roughness + 0.5) )
    // See "Optimizing PBR for Mobile" from Siggraph 2015 moving mobile graphics course
    // https://community.arm.com/events/1155

    // Final BRDFspec = roughness^2 / ( NoH^2 * (roughness^2 - 1) + 1 )^2 * (LoH^2 * (roughness + 0.5) * 4.0)
    // We further optimize a few light invariant terms
    // brdfData.normalizationTerm = (roughness + 0.5) * 4.0 rewritten as roughness * 4.0 + 2.0 to a fit a MAD.
    float d = NoH * NoH * brdfData.roughness2MinusOne + 1.00001f;

    half LoH2 = LoH * LoH;
    half specularTerm = brdfData.roughness2 / ((d * d) * max(0.1h, LoH2) * brdfData.normalizationTerm);

    // On platforms where half actually means something, the denominator has a risk of overflow
    // clamp below was added specifically to "fix" that, but dx compiler (we convert bytecode to metal/gles)
    // sees that specularTerm have only non-negative terms, so it skips max(0,..) in clamp (leaving only min(100,...))
    #if defined (SHADER_API_MOBILE) || defined (SHADER_API_SWITCH)
    specularTerm = specularTerm - HALF_MIN;
    specularTerm = clamp(specularTerm, 0.0, 100.0); // Prevent FP16 overflow on mobiles
    #endif

    half3 color = specularTerm * brdfData.specular + brdfData.diffuse;
    return color;
    #else
    return brdfData.diffuse;
    #endif
}
//Aniso PBR
half3 DirectBRDF_GGXAniso(MyBRDFData brdfData,float aniso,half3 tangent,half3 bnormal, half3 normalWS, half3 lightDirectionWS, half3 viewDirectionWS)
{
    #ifndef _SPECULARHIGHLIGHTS_OFF
    float3 halfDir = normalize(float3(lightDirectionWS) + float3(viewDirectionWS));
    float NoH = saturate(dot(normalWS, halfDir));


    half LoH = saturate(dot(lightDirectionWS, halfDir));
    float3 X = normalize(tangent).xyz;
    float3 Y = normalize(bnormal).xyz;
    half NoV = abs(dot(normalWS,viewDirectionWS));
    half NoL = saturate(dot(normalWS,lightDirectionWS));
    half XoH = dot(X,halfDir);
    half YoH = dot(Y,halfDir);
    half VoH = dot(viewDirectionWS,halfDir);
    half aspect = sqrt(1.0f-aniso*0.99f);
    half ax = max(0.01f,brdfData.roughness/aspect);
    half ay = max(0.01f,brdfData.roughness*aspect);

    float d = D_UE_GGXAniso(ax,ay,NoH,XoH,YoH);
    float vis = Vis_Cloth(NoV,NoL);
    float3 F = F_UE_Schlick(float3(1,1,1),VoH);
    half specularTerm = d*vis*F.r* brdfData.normalizationTerm;
 
    #if defined (SHADER_API_MOBILE) || defined (SHADER_API_SWITCH)
    specularTerm = specularTerm - HALF_MIN;
    specularTerm = clamp(specularTerm, 0.0, 100.0); // Prevent FP16 overflow on mobiles
    #endif

    half3 color = specularTerm * brdfData.specular + brdfData.diffuse;
    return color;
    #else
    return brdfData.diffuse;
    #endif
}
//Cloth PBR
half3 DirectBRDF_Cloth(MyBRDFData brdfData,float aniso,half Cloth,half3 tangent,half3 bnormal, half3 normalWS, half3 lightDirectionWS, half3 viewDirectionWS)
{
    #ifndef _SPECULARHIGHLIGHTS_OFF
    float3 halfDir = normalize(float3(lightDirectionWS) + float3(viewDirectionWS));

    float NoH = saturate(dot(normalWS, halfDir));
    half LoH = saturate(dot(lightDirectionWS, halfDir));
    half NoV = abs(dot(normalWS,viewDirectionWS)+0.00001f);
    half NoL = saturate(dot(normalWS,lightDirectionWS));

    half VoH =saturate(dot(viewDirectionWS,halfDir));
    half aspect = sqrt(1.0f-aniso*0.99f);
    float d = NoH * NoH * brdfData.roughness2MinusOne + 1.00001f;

    half LoH2 = LoH * LoH;
    half specularTerm = brdfData.roughness2 / ((d * d) * max(0.1h, LoH2) * brdfData.normalizationTerm);

    float d2 = D_UE_InvGGX(brdfData.roughness2,NoH);
    
    float vis2 = Vis_Cloth(NoV,NoL);
    float3 F2 = F_UE_Schlick(float3(1,1,1),VoH);
    half specularTerm2 = d2*vis2*F2.r;

    #if defined (SHADER_API_MOBILE) || defined (SHADER_API_SWITCH)
    specularTerm = specularTerm - HALF_MIN;
    specularTerm = clamp(specularTerm, 0.0, 100.0); // Prevent FP16 overflow on mobiles
    #endif

    half3 color = lerp(specularTerm* brdfData.specular,specularTerm2,Cloth) + brdfData.diffuse;
    return color;
    #else
    return brdfData.diffuse;
    #endif
}

//Clera Coat PBR
half3 DirectBRDF_ClearCoat(MyBRDFData brdfData, half3 normalWS, half3 normalNoMap,half3 lightDirectionWS, half3 viewDirectionWS,half clearCoat,half clearCoatSmoothness)
{
    #ifndef _SPECULARHIGHLIGHTS_OFF
    float3 halfDir = normalize(float3(lightDirectionWS) + float3(viewDirectionWS));

    float NoH = saturate(dot(normalWS, halfDir));
    half LoH = saturate(dot(lightDirectionWS, halfDir));
    float VoH = saturate(dot(viewDirectionWS,halfDir));

    float d = NoH * NoH * brdfData.roughness2MinusOne + 1.00001f;
    half LoH2 = LoH * LoH;
    half specularTerm = brdfData.roughness2 / ((d * d) * max(0.1h, LoH2) * brdfData.normalizationTerm);

//clearCoat
    half NoV = abs(dot(normalNoMap,viewDirectionWS));
    half NoL = saturate(dot(normalNoMap,lightDirectionWS));
    half NoH2 = saturate(dot(normalNoMap,halfDir));
    float clearCoatPreRouness = 1- clearCoatSmoothness;
    float clearCoatRouness = clearCoatPreRouness*clearCoatPreRouness;
    float clearCoatRouness2 = clearCoatRouness * clearCoatRouness;
    float dr = D_GGX2(NoH2,clearCoatRouness2);
    float gr = Vis_GGX(clearCoatRouness,NoV,NoL);
    float fr = F_UE_Schlick(float3(1,1,1),VoH).r;
    half specularTerm2 = dr*clearCoat*3.14f;
    
//endif
    specularTerm += specularTerm2;
    //return specularTerm2;
    #if defined (SHADER_API_MOBILE) || defined (SHADER_API_SWITCH)
    specularTerm = specularTerm - HALF_MIN;
    specularTerm = clamp(specularTerm, 0.0, 100.0); // Prevent FP16 overflow on mobiles
    #endif

    half3 color = specularTerm * brdfData.specular + brdfData.diffuse;
    return color;
    #else
    return brdfData.diffuse;
    #endif
}

half3 My_LightingPhysicallyBased(MyBRDFData brdfData, half3 lightColor, half3 lightDirectionWS, half lightAttenuation, half3 normalWS, half3 viewDirectionWS)
{
    half NdotL = saturate(dot(normalWS, lightDirectionWS));
    half3 radiance = lightColor * (lightAttenuation * NdotL);
    return MyDirectBRDF(brdfData, normalWS, lightDirectionWS, viewDirectionWS) * radiance;
}
//Clear Coat
half3 My_LightingPhysicallyClearCoat(MyBRDFData brdfData, half3 lightColor, half3 lightDirectionWS, half lightAttenuation, half3 normalWS,half3 normalNoMap, half3 viewDirectionWS,half clearCoat,half clearCoatSmoothness)
{
    half NdotL = saturate(dot(normalWS, lightDirectionWS));
    half3 radiance = lightColor * (lightAttenuation * NdotL);
    return DirectBRDF_ClearCoat(brdfData, normalWS, normalNoMap,lightDirectionWS, viewDirectionWS,clearCoat,clearCoatSmoothness) * radiance;
}
half3 My_LightingPhysicallySSS(MyBRDFData brdfData, half3 lightColor, half3 lightDirectionWS, half lightAttenuation, half3 normalWS, half3 viewDirectionWS,half3 ScatterAmt)
{
    #if _ENABLESGSSS
    half3 NdotL = CreateSphereGaussians(lightDirectionWS,ScatterAmt.xyz,normalWS,lightColor.r);
    #else
    half NdotL = saturate(dot(normalWS, lightDirectionWS));

    #endif    
    half3 radiance = lightColor * (lightAttenuation * NdotL);
    
    return MyDirectBRDF(brdfData, normalWS, lightDirectionWS, viewDirectionWS) * radiance;
}
half3 My_LightingPhysicallyAniso(MyBRDFData brdfData,float aniso,half3 tangent,half3 bnormal,half3 lightColor, half3 lightDirectionWS, half lightAttenuation, half3 normalWS, half3 viewDirectionWS)
{
    half NdotL = saturate(dot(normalWS, lightDirectionWS));
    half3 radiance = lightColor * (lightAttenuation * NdotL);
    return DirectBRDF_GGXAniso(brdfData, aniso,tangent,bnormal,normalWS, lightDirectionWS, viewDirectionWS) * radiance;

}
half3 My_LightingPhysicallyCloth(MyBRDFData brdfData,float aniso,half Cloth,half3 tangent,half3 bnormal,half3 lightColor, half3 lightDirectionWS, half lightAttenuation, half3 normalWS, half3 viewDirectionWS)
{
    half NdotL = saturate(dot(normalWS, lightDirectionWS));
    half3 radiance = lightColor * (lightAttenuation * NdotL);
    return DirectBRDF_Cloth(brdfData, aniso,Cloth,tangent,bnormal,normalWS, lightDirectionWS, viewDirectionWS) * radiance;
}

half3 My_LightingPhysicallyBased(MyBRDFData brdfData, Light light, half3 normalWS, half3 viewDirectionWS)
{
    return My_LightingPhysicallyBased(brdfData, light.color, light.direction, light.distanceAttenuation * light.shadowAttenuation, normalWS, viewDirectionWS);
}
half3 My_LightingPhysicallyAniso(MyBRDFData brdfData, float aniso,half3 tangent,half3 bnormal,Light light, half3 normalWS, half3 viewDirectionWS)
{
    return My_LightingPhysicallyAniso(brdfData,aniso,tangent,bnormal,light.color, light.direction, light.distanceAttenuation * light.shadowAttenuation, normalWS, viewDirectionWS);
}

half3 My_LightingPhysicallyCloth(MyBRDFData brdfData, float aniso,half Cloth,half3 tangent,half3 bnormal,Light light, half3 normalWS, half3 viewDirectionWS)
{
    return My_LightingPhysicallyCloth(brdfData,aniso,Cloth,tangent,bnormal,light.color, light.direction, light.distanceAttenuation * light.shadowAttenuation, normalWS, viewDirectionWS);
}
half3 My_LightingPhysicallySSS(MyBRDFData brdfData, Light light, half3 normalWS, half3 viewDirectionWS,half3 ScatterAmt)
{
    return My_LightingPhysicallySSS(brdfData, light.color, light.direction, light.distanceAttenuation * light.shadowAttenuation, normalWS, viewDirectionWS,ScatterAmt);
}
//Clear Coat
half3 My_LightingPhysicallyClearCoat(MyBRDFData brdfData, Light light, half3 normalWS,half3 normalNoMap, half3 viewDirectionWS,float clearCoat,float clearCoatSmoothness)
{
    return My_LightingPhysicallyClearCoat(brdfData, light.color, light.direction, light.distanceAttenuation * light.shadowAttenuation, normalWS, normalNoMap, viewDirectionWS,clearCoat,clearCoatSmoothness);
}

#endif
