// Simplified Diffuse shader. Differences from regular Diffuse one:
// - no Main Color
// - fully supports only 1 directional light. Other lights can affect it, but it will be per-vertex/SH.

Shader "Inu/Scene/Inu_Mobile_Diffuse_customLightMap" 
{
	Properties {
		_Color("Main Color", Color) = (1,1,1,1)
		_Power ("Power", Float ) = 1
		_MainTex ("Base (RGB)", 2D) = "white" {}
	}
	SubShader {
		Tags { "RenderType"="Opaque" }
		LOD 100
//#surface-start
//#		CGPROGRAM
//#		#pragma surface surf Lambert 
//#
//#		sampler2D _MainTex;
//#		fixed4 _Color;
//#		fixed _Power;
//#		struct Input {
//#			float2 uv_MainTex;
//#		};
//#
//#		void surf (Input IN, inout SurfaceOutput o) {
//#			fixed4 c = tex2D(_MainTex, IN.uv_MainTex);
//#			o.Albedo = c.rgb * _Color.rgb * _Power;
//#			o.Alpha = c.a;
//#		}
//#		ENDCG
//#surface-end

//#vertfrag-start
		Pass
		{
			Tags { "RenderType"="Opaque" }
			LOD 100
			CGPROGRAM
			#pragma multi_compile_instancing
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
			// make fog work
			#pragma multi_compile_fog
			#pragma multi_compile _ _NIGHT_MODE
			#pragma multi_compile LIGHTMAP_OFF LIGHTMAP_ON

			struct appdata
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
				float2 texcoord1 : TEXCOORD1;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float2 uv : TEXCOORD0;
				UNITY_FOG_COORDS(2)
				float4 pos : SV_POSITION;
				//#ifndef LIGHTMAP_OFF
		 			half2 lmap : TEXCOORD4;
				//#endif
				float3 worldPos:TEXCOORD3;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			sampler2D _MainTex;
			float4 _MainTex_ST;
			sampler2D _LightMapMainTex;
			float4 _LightMapMainTex_ST;
			fixed4 _Color;
			fixed _Power;
			uniform fixed _Global_Power;
			uniform fixed4 _Global_Black_Color;
			half4 _CharaPosition;
            half4 _ShadowColor;
            half4 _lightmapScaleOffset;
            float _ShadowMaxDistance;
			v2f vert (appdata v)
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				o.pos = UnityObjectToClipPos(v.vertex);
				o.uv = TRANSFORM_TEX(v.uv, _MainTex);
				UNITY_TRANSFER_FOG(o,o.pos);
				#ifndef LIGHTMAP_OFF
		 			o.lmap.xy = v.texcoord1.xy * unity_LightmapST.xy + unity_LightmapST.zw;
				#else
		 			o.lmap.xy = v.texcoord1.xy * _lightmapScaleOffset.xy + _lightmapScaleOffset.zw;
				#endif
				o.worldPos = mul(UNITY_MATRIX_M,v.vertex).xyz;
				return o;
			}
			
			fixed4 frag (v2f i) : SV_Target
			{
				// sample the texture
				UNITY_SETUP_INSTANCE_ID(i);

				fixed4 col = tex2D(_MainTex, i.uv)*_Color*_Power;
				#ifndef LIGHTMAP_OFF
		 			fixed4 bakedColorTex = UNITY_SAMPLE_TEX2D(unity_Lightmap, i.lmap.xy);
		 			half3 bakedColor = DecodeLightmap(bakedColorTex);
					col.rgb *= bakedColor;
				#else
					//half4 decodeInstructions = half4(0,0,0,0);
					//half4 transformCoords = half4(1,1,0,0);
				//	half3 bakedcolor = SampleSingleLightmap(TEXTURE2D_ARGS(_LightMapMainTex, sampler_LightMapMainTex),
						//i.lmap.xy, transformCoords, true, decodeInstructions);
					//col.rgb *= bakedColor.rgb;
					fixed4 bakedColorTex = tex2D(_LightMapMainTex, i.lmap.xy);
					half3 bakedColor = DecodeLightmap(bakedColorTex);
					col.rgb *= bakedColor;


				#endif

//night Mode
				#ifdef _NIGHT_MODE
				float4 charaPos = _CharaPosition;
				float3 worldPos = i.worldPos.xyz;
				float dis = 1-pow(saturate(distance(charaPos.xyz,worldPos)/_ShadowMaxDistance),charaPos.a);
				float3 shadowColor = lerp(1,_ShadowColor.rgb,1-dis);
				col.rgb =col.rgb*shadowColor;
				#endif
				
				// apply fog
				UNITY_APPLY_FOG(i.fogCoord, col);
				return col ;//* (1-_Global_Power) + _Global_Power *col*_Global_Black_Color;
			}
			ENDCG
		}
		UsePass "Hidden/EZ/SimplerShadowCaster/SIMPLERSHADOWCASTER"
//#vertfrag-end
	}
	Fallback "Inu/Scene/VertexLit"
}
